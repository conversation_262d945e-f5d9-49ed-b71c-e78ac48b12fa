{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IndexComponent } from './index.component';\nimport { ChartModule } from 'primeng/chart';\nimport { MenuModule } from 'primeng/menu';\nimport { TableModule } from 'primeng/table';\nimport { ButtonModule } from 'primeng/button';\nimport { StyleClassModule } from 'primeng/styleclass';\nimport { PanelMenuModule } from 'primeng/panelmenu';\nimport { IndexRoutingModule } from './index-routing.module';\nimport { DataViewModule } from 'primeng/dataview';\nimport { KnobModule } from 'primeng/knob';\nimport * as i0 from \"@angular/core\";\nexport class IndexModule {\n  static #_ = this.ɵfac = function IndexModule_Factory(t) {\n    return new (t || IndexModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: IndexModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, FormsModule, ChartModule, MenuModule, TableModule, StyleClassModule, PanelMenuModule, ButtonModule, IndexRoutingModule, DataViewModule, KnobModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(IndexModule, {\n    declarations: [IndexComponent],\n    imports: [CommonModule, FormsModule, ChartModule, MenuModule, TableModule, StyleClassModule, PanelMenuModule, ButtonModule, IndexRoutingModule, DataViewModule, KnobModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "IndexComponent", "ChartModule", "MenuModule", "TableModule", "ButtonModule", "StyleClassModule", "PanelMenuModule", "IndexRoutingModule", "DataViewModule", "KnobModule", "IndexModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\index\\index.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IndexComponent } from './index.component';\r\nimport { ChartModule } from 'primeng/chart';\r\nimport { MenuModule } from 'primeng/menu';\r\nimport { TableModule } from 'primeng/table';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { StyleClassModule } from 'primeng/styleclass';\r\nimport { PanelMenuModule } from 'primeng/panelmenu';\r\nimport { IndexRoutingModule } from './index-routing.module';\r\nimport { DataViewModule } from 'primeng/dataview';\r\nimport { KnobModule } from 'primeng/knob';\r\n\r\n@NgModule({\r\n    imports: [\r\n        CommonModule,\r\n        FormsModule,\r\n        ChartModule,\r\n        MenuModule,\r\n        TableModule,\r\n        StyleClassModule,\r\n        PanelMenuModule,\r\n        ButtonModule,\r\n        IndexRoutingModule,\r\n        DataViewModule,\r\n        KnobModule\r\n    ],\r\n    declarations: [IndexComponent]\r\n})\r\nexport class IndexModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,UAAU,QAAQ,cAAc;;AAkBzC,OAAM,MAAOC,WAAW;EAAA,QAAAC,CAAA,G;qBAAXD,WAAW;EAAA;EAAA,QAAAE,EAAA,G;UAAXF;EAAW;EAAA,QAAAG,EAAA,G;cAdhBf,YAAY,EACZC,WAAW,EACXE,WAAW,EACXC,UAAU,EACVC,WAAW,EACXE,gBAAgB,EAChBC,eAAe,EACfF,YAAY,EACZG,kBAAkB,EAClBC,cAAc,EACdC,UAAU;EAAA;;;2EAILC,WAAW;IAAAI,YAAA,GAFLd,cAAc;IAAAe,OAAA,GAZzBjB,YAAY,EACZC,WAAW,EACXE,WAAW,EACXC,UAAU,EACVC,WAAW,EACXE,gBAAgB,EAChBC,eAAe,EACfF,YAAY,EACZG,kBAAkB,EAClBC,cAAc,EACdC,UAAU;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}