{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let CacheService = /*#__PURE__*/(() => {\n  class CacheService {\n    constructor() {\n      this.stationsStorageKey = 'stationsList';\n      this.dataStorageKey = 'stationsDataList';\n      this.dataDaySumStorageKey = 'stationsSumDataList';\n    }\n    // Αποθήκευση δεδομένων\n    setStations(data) {\n      sessionStorage.setItem(this.stationsStorageKey, JSON.stringify(data));\n    }\n    // Ανάκτηση δεδομένων\n    getStations() {\n      const data = sessionStorage.getItem(this.stationsStorageKey);\n      console.log(JSON.parse(data));\n      return data ? JSON.parse(data) : null;\n    }\n    static #_ = this.ɵfac = function CacheService_Factory(t) {\n      return new (t || CacheService)();\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CacheService,\n      factory: CacheService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return CacheService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}