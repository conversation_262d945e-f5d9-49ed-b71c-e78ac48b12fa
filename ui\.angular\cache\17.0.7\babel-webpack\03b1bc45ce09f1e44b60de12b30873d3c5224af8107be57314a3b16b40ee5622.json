{"ast": null, "code": "import { finalize, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./loading.service\";\nexport let LoadingInterceptor = /*#__PURE__*/(() => {\n  class LoadingInterceptor {\n    constructor(loadingService) {\n      this.loadingService = loadingService;\n    }\n    intercept(req, next) {\n      // Εξαιρούμε ορισμένα URLs από το loading (π.χ. assets, static files)\n      const skipLoading = this.shouldSkipLoading(req.url);\n      if (!skipLoading) {\n        this.loadingService.show();\n      }\n      return next.handle(req).pipe(catchError(error => {\n        // Σε περίπτωση error, σιγουρευόμαστε ότι το loading θα κρυφτεί\n        if (!skipLoading) {\n          this.loadingService.hide();\n        }\n        throw error;\n      }), finalize(() => {\n        if (!skipLoading) {\n          this.loadingService.hide();\n        }\n      }));\n    }\n    shouldSkipLoading(url) {\n      // URLs που δεν θέλουμε να δείχνουν loading\n      const skipUrls = ['/assets/', '.json', '/api/health', '/api/ping'];\n      return skipUrls.some(skipUrl => url.includes(skipUrl));\n    }\n    static #_ = this.ɵfac = function LoadingInterceptor_Factory(t) {\n      return new (t || LoadingInterceptor)(i0.ɵɵinject(i1.LoadingService));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: LoadingInterceptor,\n      factory: LoadingInterceptor.ɵfac\n    });\n  }\n  return LoadingInterceptor;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}