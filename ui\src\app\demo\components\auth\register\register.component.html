<div class="auth-container">
    <div class="auth-background">
        <div class="bg-element bg-element-1"></div>
        <div class="bg-element bg-element-2"></div>
        <div class="bg-element bg-element-3"></div>
    </div>

    <div class="auth-content">
        <div class="auth-card">
            <!-- Header -->
            <div class="text-center mb-4">
                <img src="assets/layout/images/logo-dark.png" alt="SolarKapital" height="50" class="mb-3">
                <h2 class="text-900 text-2xl font-semibold mb-2">Create Account</h2>
                <p class="text-600">Join us to manage your solar energy systems</p>
            </div>

                <!-- Register Form -->
                <form [formGroup]="registerForm" (ngSubmit)="register()">
                    <!-- First Name Field -->
                    <div class="mb-4">
                        <label for="firstName" class="block text-900 font-medium mb-2">First Name</label>
                        <input id="firstName"
                               type="text"
                               formControlName="firstName"
                               placeholder="Enter your first name"
                               pInputText
                               class="w-full p-3"
                               [class.p-invalid]="registerForm.get('firstName')?.invalid && registerForm.get('firstName')?.touched">
                        <small class="p-error" *ngIf="registerForm.get('firstName')?.invalid && registerForm.get('firstName')?.touched">
                            <span *ngIf="registerForm.get('firstName')?.errors?.['required']">First name is required</span>
                            <span *ngIf="registerForm.get('firstName')?.errors?.['minlength']">First name must be at least 2 characters</span>
                            <span *ngIf="registerForm.get('firstName')?.errors?.['maxlength']">First name cannot exceed 50 characters</span>
                        </small>
                    </div>

                    <!-- Last Name Field -->
                    <div class="mb-4">
                        <label for="lastName" class="block text-900 font-medium mb-2">Last Name</label>
                        <input id="lastName"
                               type="text"
                               formControlName="lastName"
                               placeholder="Enter your last name"
                               pInputText
                               class="w-full p-3"
                               [class.p-invalid]="registerForm.get('lastName')?.invalid && registerForm.get('lastName')?.touched">
                        <small class="p-error" *ngIf="registerForm.get('lastName')?.invalid && registerForm.get('lastName')?.touched">
                            <span *ngIf="registerForm.get('lastName')?.errors?.['required']">Last name is required</span>
                            <span *ngIf="registerForm.get('lastName')?.errors?.['minlength']">Last name must be at least 2 characters</span>
                            <span *ngIf="registerForm.get('lastName')?.errors?.['maxlength']">Last name cannot exceed 50 characters</span>
                        </small>
                    </div>

                    <!-- Username Field -->
                    <div class="mb-4">
                        <label for="username" class="block text-900 font-medium mb-2">Username</label>
                        <input id="username"
                               type="text"
                               formControlName="username"
                               placeholder="Choose a username"
                               pInputText
                               class="w-full p-3"
                               [class.p-invalid]="registerForm.get('username')?.invalid && registerForm.get('username')?.touched">
                        <small class="p-error" *ngIf="registerForm.get('username')?.invalid && registerForm.get('username')?.touched">
                            <span *ngIf="registerForm.get('username')?.errors?.['required']">Username is required</span>
                            <span *ngIf="registerForm.get('username')?.errors?.['minlength']">Username must be at least 3 characters</span>
                            <span *ngIf="registerForm.get('username')?.errors?.['pattern']">Username can only contain letters, numbers, and underscores</span>
                        </small>
                    </div>

                    <!-- Email Field -->
                    <div class="mb-4">
                        <label for="email" class="block text-900 font-medium mb-2">Email Address</label>
                        <input id="email"
                               type="email"
                               formControlName="email"
                               placeholder="Enter your email address"
                               pInputText
                               class="w-full p-3"
                               [class.p-invalid]="registerForm.get('email')?.invalid && registerForm.get('email')?.touched">
                        <small class="p-error" *ngIf="registerForm.get('email')?.invalid && registerForm.get('email')?.touched">
                            <span *ngIf="registerForm.get('email')?.errors?.['required']">Email is required</span>
                            <span *ngIf="registerForm.get('email')?.errors?.['email']">Please enter a valid email address</span>
                        </small>
                    </div>

                    <!-- Password Field -->
                    <div class="mb-4">
                        <label for="password" class="block text-900 font-medium mb-2">Password</label>
                        <p-password id="password"
                                   formControlName="password"
                                   placeholder="Create a strong password"
                                   [toggleMask]="true"
                                   [feedback]="true"
                                   styleClass="w-full"
                                   inputStyleClass="w-full p-3"
                                   [class.p-invalid]="registerForm.get('password')?.invalid && registerForm.get('password')?.touched">
                        </p-password>
                        <small class="p-error" *ngIf="registerForm.get('password')?.invalid && registerForm.get('password')?.touched">
                            <span *ngIf="registerForm.get('password')?.errors?.['required']">Password is required</span>
                            <span *ngIf="registerForm.get('password')?.errors?.['minlength']">Password must be at least 8 characters</span>
                            <span *ngIf="registerForm.get('password')?.errors?.['pattern']">Password must contain uppercase, lowercase, number and special character</span>
                        </small>
                    </div>

                    <!-- Confirm Password Field -->
                    <div class="mb-4">
                        <label for="confirmPassword" class="block text-900 font-medium mb-2">Confirm Password</label>
                        <p-password id="confirmPassword"
                                   formControlName="confirmPassword"
                                   placeholder="Confirm your password"
                                   [toggleMask]="true"
                                   [feedback]="false"
                                   styleClass="w-full"
                                   inputStyleClass="w-full p-3"
                                   [class.p-invalid]="registerForm.get('confirmPassword')?.invalid && registerForm.get('confirmPassword')?.touched">
                        </p-password>
                        <small class="p-error" *ngIf="(registerForm.get('confirmPassword')?.invalid && registerForm.get('confirmPassword')?.touched) || (registerForm.errors?.['passwordMismatch'] && registerForm.get('confirmPassword')?.touched)">
                            <span *ngIf="registerForm.get('confirmPassword')?.errors?.['required']">Please confirm your password</span>
                            <span *ngIf="registerForm.errors?.['passwordMismatch']">Passwords do not match</span>
                        </small>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="mb-4">
                        <div class="flex align-items-center">
                            <p-checkbox id="acceptTerms"
                                       formControlName="acceptTerms"
                                       [binary]="true"
                                       styleClass="mr-2">
                            </p-checkbox>
                            <label for="acceptTerms" class="text-900">
                                I agree to the
                                <a class="font-medium no-underline cursor-pointer"
                                   style="color: var(--primary-color)"
                                   (click)="showTerms()">Terms of Service</a>
                                and
                                <a class="font-medium no-underline cursor-pointer"
                                   style="color: var(--primary-color)"
                                   (click)="showPrivacy()">Privacy Policy</a>
                            </label>
                        </div>
                        <small class="p-error" *ngIf="registerForm.get('acceptTerms')?.invalid && registerForm.get('acceptTerms')?.touched">
                            You must accept the terms and conditions
                        </small>
                    </div>

                    <!-- Error Message -->
                    <div class="p-message p-message-error mb-4" *ngIf="errorMessage">
                        <div class="p-message-wrapper">
                            <span class="p-message-icon pi pi-exclamation-triangle"></span>
                            <span class="p-message-text">{{ errorMessage }}</span>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit"
                            pButton
                            pRipple
                            label="Create Account"
                            class="w-full p-3 text-xl mb-4"
                            [disabled]="registerForm.invalid || isLoading"
                            [loading]="isLoading">
                    </button>

                    <!-- Login Link -->
                    <div class="text-center">
                        <span class="text-600">Already have an account? </span>
                        <a class="font-medium no-underline cursor-pointer"
                           style="color: var(--primary-color)"
                           (click)="redirectLogin()">
                            Sign in here
                        </a>
                    </div>
                </form>
        </div>
    </div>
</div>
