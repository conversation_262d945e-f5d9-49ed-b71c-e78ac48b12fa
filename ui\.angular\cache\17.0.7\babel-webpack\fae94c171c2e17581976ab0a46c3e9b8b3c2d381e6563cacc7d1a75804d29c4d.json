{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"primeng/button\";\nimport * as i5 from \"primeng/inputtext\";\nfunction HelpComponent_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function HelpComponent_button_19_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const category_r3 = restoredCtx.$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.filterByCategory(category_r3));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r3 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(\"category-btn \" + (ctx_r0.selectedCategory === category_r3 ? \"active\" : \"\"));\n    i0.ɵɵproperty(\"label\", category_r3);\n  }\n}\nfunction HelpComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 44);\n    i0.ɵɵelement(2, \"i\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No results found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Try adjusting your search terms or browse different categories.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HelpComponent_div_25_div_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵelement(1, \"img\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const faq_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", faq_r7.imageSrc, i0.ɵɵsanitizeUrl)(\"alt\", faq_r7.question);\n  }\n}\nfunction HelpComponent_div_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49);\n    i0.ɵɵlistener(\"click\", function HelpComponent_div_25_div_1_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const faq_r7 = restoredCtx.$implicit;\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.toggleFAQ(faq_r7));\n    });\n    i0.ɵɵelementStart(2, \"div\", 50)(3, \"span\", 51);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 52);\n    i0.ɵɵelement(8, \"i\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 54)(10, \"div\", 55);\n    i0.ɵɵelement(11, \"p\", 56);\n    i0.ɵɵtemplate(12, HelpComponent_div_25_div_1_div_12_Template, 2, 2, \"div\", 57);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const faq_r7 = ctx.$implicit;\n    i0.ɵɵclassProp(\"expanded\", faq_r7.expanded);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(faq_r7.category);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(faq_r7.question);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"pi-chevron-down\", !faq_r7.expanded)(\"pi-chevron-up\", faq_r7.expanded);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"show\", faq_r7.expanded);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", faq_r7.answer, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", faq_r7.imageSrc);\n  }\n}\nfunction HelpComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵtemplate(1, HelpComponent_div_25_div_1_Template, 13, 12, \"div\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.filteredFAQs);\n  }\n}\nexport class HelpComponent {\n  constructor() {\n    this.faqItems = [\n    // Getting Started\n    {\n      id: 'getting-started-1',\n      question: 'How do I get started with SolarKapital?',\n      answer: 'Welcome to SolarKapital! To get started, you need to add your solar energy providers first. Navigate to the Providers section from the main menu and follow the setup wizard to connect your solar installations.',\n      category: 'Getting Started'\n    }, {\n      id: 'getting-started-2',\n      question: 'What information do I need to set up my account?',\n      answer: 'You will need your solar provider credentials (username and password), and depending on your provider, additional information like Portfolio ID or FTP URL. Make sure you have this information ready before starting the setup process.',\n      category: 'Getting Started'\n    },\n    // Providers Management\n    {\n      id: 'providers-1',\n      question: 'How do I add a new solar energy provider?',\n      answer: 'To add a new provider: 1) Go to the Providers page from the main menu, 2) Click the \"Add Provider\" button, 3) Select your provider from the dropdown list, 4) Enter your credentials (username and password), 5) For some providers like Aurora, you may need to enter a Portfolio ID,  6) For some providers like SMA, you may need to enter the Ftp Url, 7) Get the list of stations, 6) Select which stations you want to monitor, 7) Click \"Save Providers\" to complete the setup.',\n      category: 'Providers'\n    }, {\n      id: 'providers-2',\n      question: 'What providers are currently supported?',\n      answer: 'SolarKapital currently supports major solar energy providers including SolarEdge, Huawei, Fronius, SMA, and others. The list of supported providers is constantly expanding. If your provider is not listed, please contact our support team.',\n      category: 'Providers'\n    }, {\n      id: 'providers-3',\n      question: 'Why do I need to provide my provider credentials?',\n      answer: 'Your provider credentials are required to securely access your solar energy data from your provider\\'s platform. This allows SolarKapital to retrieve real-time and historical data about your solar installations. Your credentials are encrypted and stored securely.',\n      category: 'Providers'\n    }, {\n      id: 'providers-4',\n      question: 'What provider credentials are required for Huawei?',\n      answer: 'The username and password that should be used are not the ones that you use to enter Huawei portal. You should go to System->Company Management->Northbound Management and create a new user that will be able to retrieve portal data.',\n      category: 'Providers',\n      imageSrc: '/assets/layout/images/huawei_example.png'\n    }, {\n      id: 'providers-5',\n      question: 'What is a Portfolio ID and when do I need it?',\n      answer: 'A Portfolio ID is required for certain providers like Aurora. It\\'s a unique identifier that groups your solar installations under your account. You can find this ID in your provider\\'s web portal, usually in the account settings or dashboard overview.',\n      category: 'Providers',\n      imageSrc: '/assets/layout/images/aurora_example.png'\n    }, {\n      id: 'providers-6',\n      question: 'What is an FTP url and when do I need it?',\n      answer: 'An FTP url is required for certain providers like SMA. It\\'s the url of the FTP that is used to identify your solar installations under your account. The FTP url should be like <em>ftp://ftp.server.com/additionalfolder/</em>. Please use the full path where the stations folders are located.',\n      category: 'Providers'\n    }, {\n      id: 'providers-7',\n      question: 'How do I edit or remove a provider configuration?',\n      answer: 'To edit a provider: click the edit (pencil) icon on the provider card and update the information. To remove a provider: click the delete (trash) icon and confirm the removal. Note that removing a provider will also remove all associated station data.',\n      category: 'Providers'\n    }, {\n      id: 'providers-8',\n      question: 'Can I add multiple configurations for the same provider?',\n      answer: 'Yes, you can add multiple configurations for the same provider if you have multiple accounts or different access credentials. Each configuration will be treated separately and can monitor different sets of stations.',\n      category: 'Providers'\n    },\n    // Stations & Monitoring\n    {\n      id: 'stations-1',\n      question: 'How do I view my solar station data?',\n      answer: 'After setting up your providers, your stations will automatically appear in the Stations section. Click on any station to view detailed energy production data, charts, and performance metrics.',\n      category: 'Stations'\n    }, {\n      id: 'stations-2',\n      question: 'What data can I see for each station?',\n      answer: 'For each station, you can view: real-time energy production, historical energy data, performance charts, weather information, inverter details, and system health status. The data is updated regularly based on your provider\\'s refresh rate.',\n      category: 'Stations'\n    }, {\n      id: 'stations-3',\n      question: 'How often is the data updated?',\n      answer: 'Data update frequency depends on your solar provider. Most providers update data every 15-30 minutes. SolarKapital automatically syncs with your providers to ensure you have the latest information available.',\n      category: 'Stations'\n    },\n    // Troubleshooting\n    {\n      id: 'troubleshooting-1',\n      question: 'My provider configuration is not working. What should I do?',\n      answer: 'First, verify that your credentials are correct by logging into your provider\\'s website directly. If that works, check if your provider requires special permissions for API access. Some providers may need to enable third-party access in their settings.',\n      category: 'Troubleshooting'\n    }, {\n      id: 'troubleshooting-2',\n      question: 'I don\\'t see any data for my stations. Why?',\n      answer: 'This could be due to several reasons: 1) The provider configuration is incorrect, 2) Your stations are not properly selected in the provider setup, 3) There might be a temporary issue with the provider\\'s API, 4) Your stations might not have recent data available.',\n      category: 'Troubleshooting'\n    }, {\n      id: 'troubleshooting-3',\n      question: 'How do I reset my provider configuration?',\n      answer: 'To reset a provider configuration: go to the Providers page, click the delete icon for the problematic provider, confirm the removal, then add the provider again with the correct information. This will create a fresh configuration.',\n      category: 'Troubleshooting'\n    },\n    // Account & Security\n    {\n      id: 'security-1',\n      question: 'Is my data secure?',\n      answer: 'Yes, SolarKapital takes security seriously. All provider credentials are encrypted before storage, data transmission uses secure HTTPS connections, and we follow industry best practices for data protection. Your solar data is never shared with third parties.',\n      category: 'Security'\n    }, {\n      id: 'security-2',\n      question: 'Can I change my account password?',\n      answer: 'Yes, you can change your account password from the Profile Settings section. Click on your profile icon in the top-right corner and select \"Profile Settings\" to update your password and other account information.',\n      category: 'Security'\n    }];\n    this.categories = [];\n    this.selectedCategory = 'All';\n    this.filteredFAQs = [];\n    this.searchQuery = '';\n  }\n  ngOnInit() {\n    // Extract unique categories\n    this.categories = ['All', ...Array.from(new Set(this.faqItems.map(item => item.category)))];\n    this.filteredFAQs = this.faqItems;\n  }\n  filterByCategory(category) {\n    this.selectedCategory = category;\n    this.applyFilters();\n  }\n  onSearch() {\n    this.applyFilters();\n  }\n  applyFilters() {\n    let filtered = this.faqItems;\n    // Filter by category\n    if (this.selectedCategory !== 'All') {\n      filtered = filtered.filter(item => item.category === this.selectedCategory);\n    }\n    // Filter by search query\n    if (this.searchQuery.trim()) {\n      const query = this.searchQuery.toLowerCase();\n      filtered = filtered.filter(item => item.question.toLowerCase().includes(query) || item.answer.toLowerCase().includes(query));\n    }\n    this.filteredFAQs = filtered;\n  }\n  toggleFAQ(faq) {\n    faq.expanded = !faq.expanded;\n  }\n  expandAll() {\n    this.filteredFAQs.forEach(faq => faq.expanded = true);\n  }\n  collapseAll() {\n    this.filteredFAQs.forEach(faq => faq.expanded = false);\n  }\n  static #_ = this.ɵfac = function HelpComponent_Factory(t) {\n    return new (t || HelpComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: HelpComponent,\n    selectors: [[\"app-help\"]],\n    decls: 80,\n    vars: 5,\n    consts: [[1, \"help-container\"], [1, \"col-12\", \"mb-4\"], [3, \"items\"], [1, \"help-header\"], [1, \"header-content\"], [1, \"header-icon\"], [1, \"pi\", \"pi-question-circle\"], [1, \"header-text\"], [1, \"help-controls\"], [1, \"search-section\"], [1, \"search-wrapper\"], [1, \"pi\", \"pi-search\", \"search-icon\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"Search for help topics...\", 1, \"search-input\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [1, \"filter-section\"], [1, \"category-filters\"], [\"pButton\", \"\", \"size\", \"small\", 3, \"label\", \"class\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"action-buttons\"], [\"pButton\", \"\", \"label\", \"Expand All\", \"icon\", \"pi pi-plus\", \"severity\", \"secondary\", \"size\", \"small\", 3, \"click\"], [\"pButton\", \"\", \"label\", \"Collapse All\", \"icon\", \"pi pi-minus\", \"severity\", \"secondary\", \"size\", \"small\", 3, \"click\"], [1, \"faq-content\"], [\"class\", \"no-results\", 4, \"ngIf\"], [\"class\", \"faq-list\", 4, \"ngIf\"], [1, \"quick-links-section\"], [1, \"quick-links-grid\"], [\"routerLink\", \"/app/providers\", 1, \"quick-link-card\"], [1, \"card-icon\"], [1, \"pi\", \"pi-server\"], [1, \"card-content\"], [1, \"card-arrow\"], [1, \"pi\", \"pi-arrow-right\"], [\"routerLink\", \"/app/stations\", 1, \"quick-link-card\"], [1, \"pi\", \"pi-chart-line\"], [\"routerLink\", \"/app/index\", 1, \"quick-link-card\"], [1, \"pi\", \"pi-home\"], [\"routerLink\", \"/app/communication\", 1, \"quick-link-card\"], [1, \"pi\", \"pi-comments\"], [1, \"support-section\"], [1, \"support-card\"], [1, \"support-icon\"], [1, \"pi\", \"pi-headphones\"], [1, \"support-content\"], [\"pButton\", \"\", \"label\", \"Contact Support\", \"icon\", \"pi pi-envelope\", 1, \"support-button\"], [\"pButton\", \"\", \"size\", \"small\", 3, \"label\", \"click\"], [1, \"no-results\"], [1, \"no-results-icon\"], [1, \"pi\", \"pi-search\"], [1, \"faq-list\"], [\"class\", \"faq-item\", 3, \"expanded\", 4, \"ngFor\", \"ngForOf\"], [1, \"faq-item\"], [1, \"faq-question\", 3, \"click\"], [1, \"question-content\"], [1, \"category-badge\"], [1, \"expand-icon\"], [1, \"pi\"], [1, \"faq-answer\"], [1, \"answer-content\"], [3, \"innerHTML\"], [\"class\", \"answer-image\", 4, \"ngIf\"], [1, \"answer-image\"], [3, \"src\", \"alt\"]],\n    template: function HelpComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-modern-breadcrumb\", 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n        i0.ɵɵelement(6, \"i\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"div\", 7)(8, \"h1\");\n        i0.ɵɵtext(9, \"Help & FAQ\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"p\");\n        i0.ɵɵtext(11, \"Find answers to common questions and learn how to make the most of SolarKapital\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(12, \"div\", 8)(13, \"div\", 9)(14, \"div\", 10);\n        i0.ɵɵelement(15, \"i\", 11);\n        i0.ɵɵelementStart(16, \"input\", 12);\n        i0.ɵɵlistener(\"ngModelChange\", function HelpComponent_Template_input_ngModelChange_16_listener($event) {\n          return ctx.searchQuery = $event;\n        })(\"input\", function HelpComponent_Template_input_input_16_listener() {\n          return ctx.onSearch();\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(17, \"div\", 13)(18, \"div\", 14);\n        i0.ɵɵtemplate(19, HelpComponent_button_19_Template, 1, 3, \"button\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"div\", 16)(21, \"button\", 17);\n        i0.ɵɵlistener(\"click\", function HelpComponent_Template_button_click_21_listener() {\n          return ctx.expandAll();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"button\", 18);\n        i0.ɵɵlistener(\"click\", function HelpComponent_Template_button_click_22_listener() {\n          return ctx.collapseAll();\n        });\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(23, \"div\", 19);\n        i0.ɵɵtemplate(24, HelpComponent_div_24_Template, 7, 0, \"div\", 20)(25, HelpComponent_div_25_Template, 2, 1, \"div\", 21);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(26, \"div\", 22)(27, \"h2\");\n        i0.ɵɵtext(28, \"Quick Links\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(29, \"div\", 23)(30, \"div\", 24)(31, \"div\", 25);\n        i0.ɵɵelement(32, \"i\", 26);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(33, \"div\", 27)(34, \"h3\");\n        i0.ɵɵtext(35, \"Manage Providers\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(36, \"p\");\n        i0.ɵɵtext(37, \"Add, edit, or remove your solar energy providers\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(38, \"div\", 28);\n        i0.ɵɵelement(39, \"i\", 29);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(40, \"div\", 30)(41, \"div\", 25);\n        i0.ɵɵelement(42, \"i\", 31);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(43, \"div\", 27)(44, \"h3\");\n        i0.ɵɵtext(45, \"View Stations\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(46, \"p\");\n        i0.ɵɵtext(47, \"Monitor your solar installations and energy data\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(48, \"div\", 28);\n        i0.ɵɵelement(49, \"i\", 29);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(50, \"div\", 32)(51, \"div\", 25);\n        i0.ɵɵelement(52, \"i\", 33);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(53, \"div\", 27)(54, \"h3\");\n        i0.ɵɵtext(55, \"Dashboard\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(56, \"p\");\n        i0.ɵɵtext(57, \"Overview of all your solar energy systems\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(58, \"div\", 28);\n        i0.ɵɵelement(59, \"i\", 29);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(60, \"div\", 34)(61, \"div\", 25);\n        i0.ɵɵelement(62, \"i\", 35);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(63, \"div\", 27)(64, \"h3\");\n        i0.ɵɵtext(65, \"Communication\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(66, \"p\");\n        i0.ɵɵtext(67, \"View system messages and notifications\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(68, \"div\", 28);\n        i0.ɵɵelement(69, \"i\", 29);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(70, \"div\", 36)(71, \"div\", 37)(72, \"div\", 38);\n        i0.ɵɵelement(73, \"i\", 39);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(74, \"div\", 40)(75, \"h3\");\n        i0.ɵɵtext(76, \"Still need help?\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(77, \"p\");\n        i0.ɵɵtext(78, \"Can't find what you're looking for? Our support team is here to help you get the most out of SolarKapital.\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(79, \"button\", 41);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"items\", ctx.breadcrumbItems);\n        i0.ɵɵadvance(14);\n        i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.filteredFAQs.length === 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.filteredFAQs.length > 0);\n      }\n    },\n    dependencies: [i1.NgForOf, i1.NgIf, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgModel, i3.RouterLink, i4.ButtonDirective, i5.InputText],\n    styles: [\".help-container[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.help-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-600) 100%);\\n  border-radius: 16px;\\n  padding: 3rem 2rem;\\n  margin-bottom: 2rem;\\n  color: white;\\n}\\n.help-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1.5rem;\\n}\\n.help-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  opacity: 0.9;\\n}\\n.help-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  margin: 0 0 0.5rem 0;\\n}\\n.help-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  margin: 0;\\n  opacity: 0.9;\\n}\\n\\n.help-controls[_ngcontent-%COMP%] {\\n  background: var(--surface-card);\\n  border-radius: 12px;\\n  padding: 1.5rem;\\n  margin-bottom: 2rem;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.help-controls[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n.help-controls[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  max-width: 500px;\\n}\\n.help-controls[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 1rem;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  color: var(--text-color-secondary);\\n  z-index: 1;\\n}\\n.help-controls[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0.75rem 1rem 0.75rem 3rem;\\n  border: 2px solid var(--surface-border);\\n  border-radius: 8px;\\n  font-size: 1rem;\\n  transition: all 0.3s ease;\\n}\\n.help-controls[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]:focus {\\n  border-color: var(--primary-color);\\n  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);\\n}\\n.help-controls[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  flex-wrap: wrap;\\n  gap: 1rem;\\n}\\n.help-controls[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]   .category-filters[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.5rem;\\n}\\n.help-controls[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]   .category-filters[_ngcontent-%COMP%]   .category-btn[_ngcontent-%COMP%] {\\n  border-radius: 20px;\\n  transition: all 0.3s ease;\\n}\\n.help-controls[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]   .category-filters[_ngcontent-%COMP%]   .category-btn.active[_ngcontent-%COMP%] {\\n  background: var(--primary-color) !important;\\n  border-color: var(--primary-color) !important;\\n  color: white !important;\\n}\\n.help-controls[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]   .category-filters[_ngcontent-%COMP%]   .category-btn[_ngcontent-%COMP%]:not(.active) {\\n  background: var(--surface-100);\\n  border-color: var(--surface-300);\\n  color: var(--text-color);\\n}\\n.help-controls[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]   .category-filters[_ngcontent-%COMP%]   .category-btn[_ngcontent-%COMP%]:not(.active):hover {\\n  background: var(--surface-200);\\n}\\n.help-controls[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n}\\n\\n.faq-content[_ngcontent-%COMP%] {\\n  margin-bottom: 3rem;\\n}\\n.faq-content[_ngcontent-%COMP%]   .no-results[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 3rem 2rem;\\n  color: var(--text-color-secondary);\\n}\\n.faq-content[_ngcontent-%COMP%]   .no-results[_ngcontent-%COMP%]   .no-results-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  margin-bottom: 1rem;\\n  opacity: 0.5;\\n}\\n.faq-content[_ngcontent-%COMP%]   .no-results[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  margin: 0 0 0.5rem 0;\\n}\\n.faq-content[_ngcontent-%COMP%]   .no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin: 0;\\n}\\n.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%] {\\n  background: var(--surface-card);\\n  border-radius: 12px;\\n  margin-bottom: 1rem;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n}\\n.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\\n}\\n.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-question[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  cursor: pointer;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  gap: 1rem;\\n  transition: background-color 0.3s ease;\\n}\\n.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-question[_ngcontent-%COMP%]:hover {\\n  background: var(--surface-50);\\n}\\n.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-question[_ngcontent-%COMP%]   .question-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-question[_ngcontent-%COMP%]   .question-content[_ngcontent-%COMP%]   .category-badge[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  background: var(--primary-100);\\n  color: var(--primary-700);\\n  padding: 0.25rem 0.75rem;\\n  border-radius: 12px;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  margin-bottom: 0.75rem;\\n}\\n.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-question[_ngcontent-%COMP%]   .question-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  margin: 0;\\n  color: var(--text-color);\\n  line-height: 1.4;\\n}\\n.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-question[_ngcontent-%COMP%]   .expand-icon[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  font-size: 1.25rem;\\n  transition: transform 0.3s ease;\\n}\\n.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item.expanded[_ngcontent-%COMP%]   .faq-question[_ngcontent-%COMP%]   .expand-icon[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-answer[_ngcontent-%COMP%] {\\n  max-height: 0;\\n  overflow: hidden;\\n  transition: max-height 0.3s ease;\\n}\\n.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-answer.show[_ngcontent-%COMP%] {\\n  max-height: 500px;\\n}\\n.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-answer[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%] {\\n  padding: 0 1.5rem 1.5rem 1.5rem;\\n  border-top: 1px solid var(--surface-border);\\n}\\n.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-answer[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  line-height: 1.6;\\n  color: var(--text-color-secondary);\\n  margin: 1rem 0 0 0;\\n}\\n.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-answer[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   .answer-image[_ngcontent-%COMP%] {\\n  margin: 1.5rem 0 0 0;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  background: var(--surface-50);\\n  border: 1px solid var(--surface-border);\\n}\\n.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-answer[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   .answer-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: auto;\\n  display: block;\\n  max-width: 100%;\\n  object-fit: contain;\\n  border-radius: 8px;\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  max-height: 400px;\\n  object-position: center;\\n}\\n.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-answer[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   .answer-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.02);\\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);\\n}\\n.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-answer[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   .answer-image[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background: var(--surface-100);\\n  display: none;\\n}\\n.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-answer[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   .answer-image[_ngcontent-%COMP%]   img[src=\\\"\\\"][_ngcontent-%COMP%], .faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-answer[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   .answer-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:not([src]) {\\n  display: none;\\n}\\n\\n.quick-links-section[_ngcontent-%COMP%] {\\n  margin-bottom: 3rem;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.75rem;\\n  font-weight: 600;\\n  margin: 0 0 1.5rem 0;\\n  color: var(--text-color);\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: 1.5rem;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%] {\\n  background: var(--surface-card);\\n  border-radius: 12px;\\n  padding: 1.5rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  text-decoration: none;\\n  color: inherit;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]:hover   .card-arrow[_ngcontent-%COMP%] {\\n  transform: translateX(4px);\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  width: 3rem;\\n  height: 3rem;\\n  background: var(--primary-100);\\n  color: var(--primary-700);\\n  border-radius: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 1.5rem;\\n  flex-shrink: 0;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n  margin: 0 0 0.25rem 0;\\n  color: var(--text-color);\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--text-color-secondary);\\n  margin: 0;\\n  line-height: 1.4;\\n}\\n.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .card-arrow[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  font-size: 1.25rem;\\n  transition: transform 0.3s ease;\\n}\\n\\n.support-section[_ngcontent-%COMP%]   .support-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--surface-100) 0%, var(--surface-200) 100%);\\n  border-radius: 16px;\\n  padding: 2rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 1.5rem;\\n}\\n.support-section[_ngcontent-%COMP%]   .support-card[_ngcontent-%COMP%]   .support-icon[_ngcontent-%COMP%] {\\n  width: 4rem;\\n  height: 4rem;\\n  background: var(--primary-color);\\n  color: white;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 2rem;\\n  flex-shrink: 0;\\n}\\n.support-section[_ngcontent-%COMP%]   .support-card[_ngcontent-%COMP%]   .support-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.support-section[_ngcontent-%COMP%]   .support-card[_ngcontent-%COMP%]   .support-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  margin: 0 0 0.5rem 0;\\n  color: var(--text-color);\\n}\\n.support-section[_ngcontent-%COMP%]   .support-card[_ngcontent-%COMP%]   .support-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: var(--text-color-secondary);\\n  margin: 0 0 1.5rem 0;\\n  line-height: 1.5;\\n}\\n.support-section[_ngcontent-%COMP%]   .support-card[_ngcontent-%COMP%]   .support-content[_ngcontent-%COMP%]   .support-button[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n  border-color: var(--primary-color);\\n}\\n\\n@media (max-width: 768px) {\\n  .help-container[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .help-header[_ngcontent-%COMP%] {\\n    padding: 2rem 1.5rem;\\n  }\\n  .help-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n    gap: 1rem;\\n  }\\n  .help-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .help-controls[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .help-controls[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n  .help-controls[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]   .category-filters[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  .help-controls[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  .quick-links-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .support-card[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n    padding: 1.5rem;\\n  }\\n  .faq-item[_ngcontent-%COMP%]   .faq-question[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .faq-item[_ngcontent-%COMP%]   .faq-question[_ngcontent-%COMP%]   .question-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n  .faq-item[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%] {\\n    padding: 0 1rem 1rem 1rem;\\n  }\\n  .faq-item[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   .answer-image[_ngcontent-%COMP%] {\\n    margin: 1rem 0 0 0;\\n    border-radius: 6px;\\n  }\\n  .faq-item[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   .answer-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    border-radius: 6px;\\n  }\\n}\\n.dark[_nghost-%COMP%]   .help-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .help-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);\\n}\\n.dark[_nghost-%COMP%]   .category-btn[_ngcontent-%COMP%]:not(.active), .dark   [_nghost-%COMP%]   .category-btn[_ngcontent-%COMP%]:not(.active) {\\n  background: var(--surface-700);\\n  border-color: var(--surface-600);\\n  color: var(--text-color);\\n}\\n.dark[_nghost-%COMP%]   .category-btn[_ngcontent-%COMP%]:not(.active):hover, .dark   [_nghost-%COMP%]   .category-btn[_ngcontent-%COMP%]:not(.active):hover {\\n  background: var(--surface-600);\\n}\\n.dark[_nghost-%COMP%]   .support-card[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .support-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--surface-700) 0%, var(--surface-800) 100%);\\n}\\n.dark[_nghost-%COMP%]   .faq-item[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   .answer-image[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .faq-item[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   .answer-image[_ngcontent-%COMP%] {\\n  background: var(--surface-800);\\n  border-color: var(--surface-600);\\n}\\n.dark[_nghost-%COMP%]   .faq-item[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   .answer-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .faq-item[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   .answer-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZGVtby9jb21wb25lbnRzL2hlbHAvaGVscC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7RUFDQSxpQkFBQTtFQUNBLGNBQUE7QUFDRjs7QUFJQTtFQUNFLHFGQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLG1CQUFBO0VBQ0EsWUFBQTtBQURGO0FBR0U7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxXQUFBO0FBREo7QUFHSTtFQUNFLGVBQUE7RUFDQSxZQUFBO0FBRE47QUFLTTtFQUNFLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxvQkFBQTtBQUhSO0FBTU07RUFDRSxtQkFBQTtFQUNBLFNBQUE7RUFDQSxZQUFBO0FBSlI7O0FBV0E7RUFDRSwrQkFBQTtFQUNBLG1CQUFBO0VBQ0EsZUFBQTtFQUNBLG1CQUFBO0VBQ0Esd0NBQUE7QUFSRjtBQVVFO0VBQ0UscUJBQUE7QUFSSjtBQVVJO0VBQ0Usa0JBQUE7RUFDQSxnQkFBQTtBQVJOO0FBVU07RUFDRSxrQkFBQTtFQUNBLFVBQUE7RUFDQSxRQUFBO0VBQ0EsMkJBQUE7RUFDQSxrQ0FBQTtFQUNBLFVBQUE7QUFSUjtBQVdNO0VBQ0UsV0FBQTtFQUNBLGtDQUFBO0VBQ0EsdUNBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtBQVRSO0FBV1E7RUFDRSxrQ0FBQTtFQUNBLHlEQUFBO0FBVFY7QUFlRTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0EsZUFBQTtFQUNBLFNBQUE7QUFiSjtBQWVJO0VBQ0UsYUFBQTtFQUNBLGVBQUE7RUFDQSxXQUFBO0FBYk47QUFlTTtFQUNFLG1CQUFBO0VBQ0EseUJBQUE7QUFiUjtBQWVRO0VBQ0UsMkNBQUE7RUFDQSw2Q0FBQTtFQUNBLHVCQUFBO0FBYlY7QUFnQlE7RUFDRSw4QkFBQTtFQUNBLGdDQUFBO0VBQ0Esd0JBQUE7QUFkVjtBQWdCVTtFQUNFLDhCQUFBO0FBZFo7QUFvQkk7RUFDRSxhQUFBO0VBQ0EsV0FBQTtBQWxCTjs7QUF3QkE7RUFDRSxtQkFBQTtBQXJCRjtBQXVCRTtFQUNFLGtCQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQ0FBQTtBQXJCSjtBQXVCSTtFQUNFLGVBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7QUFyQk47QUF3Qkk7RUFDRSxpQkFBQTtFQUNBLG9CQUFBO0FBdEJOO0FBeUJJO0VBQ0UsZUFBQTtFQUNBLFNBQUE7QUF2Qk47QUE0Qkk7RUFDRSwrQkFBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7RUFDQSx3Q0FBQTtFQUNBLGdCQUFBO0VBQ0EseUJBQUE7QUExQk47QUE0Qk07RUFDRSwwQ0FBQTtBQTFCUjtBQTZCTTtFQUNFLGVBQUE7RUFDQSxlQUFBO0VBQ0EsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsdUJBQUE7RUFDQSxTQUFBO0VBQ0Esc0NBQUE7QUEzQlI7QUE2QlE7RUFDRSw2QkFBQTtBQTNCVjtBQThCUTtFQUNFLE9BQUE7QUE1QlY7QUE4QlU7RUFDRSxxQkFBQTtFQUNBLDhCQUFBO0VBQ0EseUJBQUE7RUFDQSx3QkFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLHNCQUFBO0FBNUJaO0FBK0JVO0VBQ0UsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLFNBQUE7RUFDQSx3QkFBQTtFQUNBLGdCQUFBO0FBN0JaO0FBaUNRO0VBQ0UsMkJBQUE7RUFDQSxrQkFBQTtFQUNBLCtCQUFBO0FBL0JWO0FBb0NRO0VBQ0UseUJBQUE7QUFsQ1Y7QUFzQ007RUFDRSxhQUFBO0VBQ0EsZ0JBQUE7RUFDQSxnQ0FBQTtBQXBDUjtBQXNDUTtFQUNFLGlCQUFBO0FBcENWO0FBdUNRO0VBQ0UsK0JBQUE7RUFDQSwyQ0FBQTtBQXJDVjtBQXVDVTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGtDQUFBO0VBQ0Esa0JBQUE7QUFyQ1o7QUF3Q1U7RUFDRSxvQkFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSx5Q0FBQTtFQUNBLDZCQUFBO0VBQ0EsdUNBQUE7QUF0Q1o7QUF3Q1k7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGNBQUE7RUFDQSxlQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLHlCQUFBO0VBQ0EsZUFBQTtFQVFBLGlCQUFBO0VBQ0EsdUJBQUE7QUE3Q2Q7QUFzQ2M7RUFDRSxzQkFBQTtFQUNBLDBDQUFBO0FBcENoQjtBQTZDWTtFQUNFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSw4QkFBQTtFQUNBLGFBQUE7QUEzQ2Q7QUErQ1k7RUFDRSxhQUFBO0FBN0NkOztBQXVEQTtFQUNFLG1CQUFBO0FBcERGO0FBc0RFO0VBQ0Usa0JBQUE7RUFDQSxnQkFBQTtFQUNBLG9CQUFBO0VBQ0Esd0JBQUE7QUFwREo7QUF1REU7RUFDRSxhQUFBO0VBQ0EsMkRBQUE7RUFDQSxXQUFBO0FBckRKO0FBdURJO0VBQ0UsK0JBQUE7RUFDQSxtQkFBQTtFQUNBLGVBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0VBQ0EsZUFBQTtFQUNBLHlCQUFBO0VBQ0Esd0NBQUE7RUFDQSxxQkFBQTtFQUNBLGNBQUE7QUFyRE47QUF1RE07RUFDRSwyQkFBQTtFQUNBLDBDQUFBO0FBckRSO0FBdURRO0VBQ0UsMEJBQUE7QUFyRFY7QUF5RE07RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLDhCQUFBO0VBQ0EseUJBQUE7RUFDQSxtQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsaUJBQUE7RUFDQSxjQUFBO0FBdkRSO0FBMERNO0VBQ0UsT0FBQTtBQXhEUjtBQTBEUTtFQUNFLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxxQkFBQTtFQUNBLHdCQUFBO0FBeERWO0FBMkRRO0VBQ0UsbUJBQUE7RUFDQSxrQ0FBQTtFQUNBLFNBQUE7RUFDQSxnQkFBQTtBQXpEVjtBQTZETTtFQUNFLDJCQUFBO0VBQ0Esa0JBQUE7RUFDQSwrQkFBQTtBQTNEUjs7QUFtRUU7RUFDRSxtRkFBQTtFQUNBLG1CQUFBO0VBQ0EsYUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7QUFoRUo7QUFrRUk7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGdDQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxlQUFBO0VBQ0EsY0FBQTtBQWhFTjtBQW1FSTtFQUNFLE9BQUE7QUFqRU47QUFtRU07RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0Esb0JBQUE7RUFDQSx3QkFBQTtBQWpFUjtBQW9FTTtFQUNFLGVBQUE7RUFDQSxrQ0FBQTtFQUNBLG9CQUFBO0VBQ0EsZ0JBQUE7QUFsRVI7QUFxRU07RUFDRSxnQ0FBQTtFQUNBLGtDQUFBO0FBbkVSOztBQTBFQTtFQUNFO0lBQ0UsYUFBQTtFQXZFRjtFQTBFQTtJQUNFLG9CQUFBO0VBeEVGO0VBMEVFO0lBQ0Usc0JBQUE7SUFDQSxrQkFBQTtJQUNBLFNBQUE7RUF4RUo7RUEwRUk7SUFDRSxlQUFBO0VBeEVOO0VBNkVBO0lBQ0UsYUFBQTtFQTNFRjtFQTZFRTtJQUNFLHNCQUFBO0lBQ0Esb0JBQUE7RUEzRUo7RUE2RUk7SUFDRSx1QkFBQTtFQTNFTjtFQThFSTtJQUNFLHVCQUFBO0VBNUVOO0VBaUZBO0lBQ0UsMEJBQUE7RUEvRUY7RUFrRkE7SUFDRSxzQkFBQTtJQUNBLGtCQUFBO0lBQ0EsZUFBQTtFQWhGRjtFQW1GQTtJQUNFLGFBQUE7RUFqRkY7RUFtRkU7SUFDRSxlQUFBO0VBakZKO0VBcUZBO0lBQ0UseUJBQUE7RUFuRkY7RUFxRkU7SUFDRSxrQkFBQTtJQUNBLGtCQUFBO0VBbkZKO0VBcUZJO0lBQ0Usa0JBQUE7RUFuRk47QUFDRjtBQTBGRTtFQUNFLG1GQUFBO0FBeEZKO0FBMkZFO0VBQ0UsOEJBQUE7RUFDQSxnQ0FBQTtFQUNBLHdCQUFBO0FBekZKO0FBMkZJO0VBQ0UsOEJBQUE7QUF6Rk47QUE2RkU7RUFDRSxtRkFBQTtBQTNGSjtBQThGRTtFQUNFLDhCQUFBO0VBQ0EsZ0NBQUE7QUE1Rko7QUErRk07RUFDRSx5Q0FBQTtBQTdGUiIsInNvdXJjZXNDb250ZW50IjpbIi5oZWxwLWNvbnRhaW5lciB7XG4gIHBhZGRpbmc6IDJyZW07XG4gIG1heC13aWR0aDogMTIwMHB4O1xuICBtYXJnaW46IDAgYXV0bztcbn1cblxuXG4vLyBIZWFkZXIgU2VjdGlvblxuLmhlbHAtaGVhZGVyIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgdmFyKC0tcHJpbWFyeS1jb2xvcikgMCUsIHZhcigtLXByaW1hcnktNjAwKSAxMDAlKTtcbiAgYm9yZGVyLXJhZGl1czogMTZweDtcbiAgcGFkZGluZzogM3JlbSAycmVtO1xuICBtYXJnaW4tYm90dG9tOiAycmVtO1xuICBjb2xvcjogd2hpdGU7XG5cbiAgLmhlYWRlci1jb250ZW50IHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgZ2FwOiAxLjVyZW07XG5cbiAgICAuaGVhZGVyLWljb24ge1xuICAgICAgZm9udC1zaXplOiAzcmVtO1xuICAgICAgb3BhY2l0eTogMC45O1xuICAgIH1cblxuICAgIC5oZWFkZXItdGV4dCB7XG4gICAgICBoMSB7XG4gICAgICAgIGZvbnQtc2l6ZTogMi41cmVtO1xuICAgICAgICBmb250LXdlaWdodDogNzAwO1xuICAgICAgICBtYXJnaW46IDAgMCAwLjVyZW0gMDtcbiAgICAgIH1cblxuICAgICAgcCB7XG4gICAgICAgIGZvbnQtc2l6ZTogMS4xMjVyZW07XG4gICAgICAgIG1hcmdpbjogMDtcbiAgICAgICAgb3BhY2l0eTogMC45O1xuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG4vLyBDb250cm9scyBTZWN0aW9uXG4uaGVscC1jb250cm9scyB7XG4gIGJhY2tncm91bmQ6IHZhcigtLXN1cmZhY2UtY2FyZCk7XG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gIHBhZGRpbmc6IDEuNXJlbTtcbiAgbWFyZ2luLWJvdHRvbTogMnJlbTtcbiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcblxuICAuc2VhcmNoLXNlY3Rpb24ge1xuICAgIG1hcmdpbi1ib3R0b206IDEuNXJlbTtcblxuICAgIC5zZWFyY2gtd3JhcHBlciB7XG4gICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gICAgICBtYXgtd2lkdGg6IDUwMHB4O1xuXG4gICAgICAuc2VhcmNoLWljb24ge1xuICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgICAgIGxlZnQ6IDFyZW07XG4gICAgICAgIHRvcDogNTAlO1xuICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTUwJSk7XG4gICAgICAgIGNvbG9yOiB2YXIoLS10ZXh0LWNvbG9yLXNlY29uZGFyeSk7XG4gICAgICAgIHotaW5kZXg6IDE7XG4gICAgICB9XG5cbiAgICAgIC5zZWFyY2gtaW5wdXQge1xuICAgICAgICB3aWR0aDogMTAwJTtcbiAgICAgICAgcGFkZGluZzogMC43NXJlbSAxcmVtIDAuNzVyZW0gM3JlbTtcbiAgICAgICAgYm9yZGVyOiAycHggc29saWQgdmFyKC0tc3VyZmFjZS1ib3JkZXIpO1xuICAgICAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgICAgIGZvbnQtc2l6ZTogMXJlbTtcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcblxuICAgICAgICAmOmZvY3VzIHtcbiAgICAgICAgICBib3JkZXItY29sb3I6IHZhcigtLXByaW1hcnktY29sb3IpO1xuICAgICAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDNweCByZ2JhKHZhcigtLXByaW1hcnktY29sb3ItcmdiKSwgMC4xKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC5maWx0ZXItc2VjdGlvbiB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBmbGV4LXdyYXA6IHdyYXA7XG4gICAgZ2FwOiAxcmVtO1xuXG4gICAgLmNhdGVnb3J5LWZpbHRlcnMge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGZsZXgtd3JhcDogd3JhcDtcbiAgICAgIGdhcDogMC41cmVtO1xuXG4gICAgICAuY2F0ZWdvcnktYnRuIHtcbiAgICAgICAgYm9yZGVyLXJhZGl1czogMjBweDtcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcblxuICAgICAgICAmLmFjdGl2ZSB7XG4gICAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tcHJpbWFyeS1jb2xvcikgIWltcG9ydGFudDtcbiAgICAgICAgICBib3JkZXItY29sb3I6IHZhcigtLXByaW1hcnktY29sb3IpICFpbXBvcnRhbnQ7XG4gICAgICAgICAgY29sb3I6IHdoaXRlICFpbXBvcnRhbnQ7XG4gICAgICAgIH1cblxuICAgICAgICAmOm5vdCguYWN0aXZlKSB7XG4gICAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tc3VyZmFjZS0xMDApO1xuICAgICAgICAgIGJvcmRlci1jb2xvcjogdmFyKC0tc3VyZmFjZS0zMDApO1xuICAgICAgICAgIGNvbG9yOiB2YXIoLS10ZXh0LWNvbG9yKTtcblxuICAgICAgICAgICY6aG92ZXIge1xuICAgICAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tc3VyZmFjZS0yMDApO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC5hY3Rpb24tYnV0dG9ucyB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgZ2FwOiAwLjVyZW07XG4gICAgfVxuICB9XG59XG5cbi8vIEZBUSBDb250ZW50XG4uZmFxLWNvbnRlbnQge1xuICBtYXJnaW4tYm90dG9tOiAzcmVtO1xuXG4gIC5uby1yZXN1bHRzIHtcbiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gICAgcGFkZGluZzogM3JlbSAycmVtO1xuICAgIGNvbG9yOiB2YXIoLS10ZXh0LWNvbG9yLXNlY29uZGFyeSk7XG5cbiAgICAubm8tcmVzdWx0cy1pY29uIHtcbiAgICAgIGZvbnQtc2l6ZTogM3JlbTtcbiAgICAgIG1hcmdpbi1ib3R0b206IDFyZW07XG4gICAgICBvcGFjaXR5OiAwLjU7XG4gICAgfVxuXG4gICAgaDMge1xuICAgICAgZm9udC1zaXplOiAxLjVyZW07XG4gICAgICBtYXJnaW46IDAgMCAwLjVyZW0gMDtcbiAgICB9XG5cbiAgICBwIHtcbiAgICAgIGZvbnQtc2l6ZTogMXJlbTtcbiAgICAgIG1hcmdpbjogMDtcbiAgICB9XG4gIH1cblxuICAuZmFxLWxpc3Qge1xuICAgIC5mYXEtaXRlbSB7XG4gICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zdXJmYWNlLWNhcmQpO1xuICAgICAgYm9yZGVyLXJhZGl1czogMTJweDtcbiAgICAgIG1hcmdpbi1ib3R0b206IDFyZW07XG4gICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xuICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG5cbiAgICAgICY6aG92ZXIge1xuICAgICAgICBib3gtc2hhZG93OiAwIDRweCAxNnB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7XG4gICAgICB9XG5cbiAgICAgIC5mYXEtcXVlc3Rpb24ge1xuICAgICAgICBwYWRkaW5nOiAxLjVyZW07XG4gICAgICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICAgICAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcbiAgICAgICAgZ2FwOiAxcmVtO1xuICAgICAgICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kLWNvbG9yIDAuM3MgZWFzZTtcblxuICAgICAgICAmOmhvdmVyIHtcbiAgICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1zdXJmYWNlLTUwKTtcbiAgICAgICAgfVxuXG4gICAgICAgIC5xdWVzdGlvbi1jb250ZW50IHtcbiAgICAgICAgICBmbGV4OiAxO1xuXG4gICAgICAgICAgLmNhdGVnb3J5LWJhZGdlIHtcbiAgICAgICAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jaztcbiAgICAgICAgICAgIGJhY2tncm91bmQ6IHZhcigtLXByaW1hcnktMTAwKTtcbiAgICAgICAgICAgIGNvbG9yOiB2YXIoLS1wcmltYXJ5LTcwMCk7XG4gICAgICAgICAgICBwYWRkaW5nOiAwLjI1cmVtIDAuNzVyZW07XG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICAgICAgICAgICAgZm9udC1zaXplOiAwLjc1cmVtO1xuICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDAuNzVyZW07XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgaDMge1xuICAgICAgICAgICAgZm9udC1zaXplOiAxLjEyNXJlbTtcbiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgICAgICBtYXJnaW46IDA7XG4gICAgICAgICAgICBjb2xvcjogdmFyKC0tdGV4dC1jb2xvcik7XG4gICAgICAgICAgICBsaW5lLWhlaWdodDogMS40O1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIC5leHBhbmQtaWNvbiB7XG4gICAgICAgICAgY29sb3I6IHZhcigtLXByaW1hcnktY29sb3IpO1xuICAgICAgICAgIGZvbnQtc2l6ZTogMS4yNXJlbTtcbiAgICAgICAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgICYuZXhwYW5kZWQge1xuICAgICAgICAuZmFxLXF1ZXN0aW9uIC5leHBhbmQtaWNvbiB7XG4gICAgICAgICAgdHJhbnNmb3JtOiByb3RhdGUoMTgwZGVnKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAuZmFxLWFuc3dlciB7XG4gICAgICAgIG1heC1oZWlnaHQ6IDA7XG4gICAgICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgICAgIHRyYW5zaXRpb246IG1heC1oZWlnaHQgMC4zcyBlYXNlO1xuXG4gICAgICAgICYuc2hvdyB7XG4gICAgICAgICAgbWF4LWhlaWdodDogNTAwcHg7XG4gICAgICAgIH1cblxuICAgICAgICAuYW5zd2VyLWNvbnRlbnQge1xuICAgICAgICAgIHBhZGRpbmc6IDAgMS41cmVtIDEuNXJlbSAxLjVyZW07XG4gICAgICAgICAgYm9yZGVyLXRvcDogMXB4IHNvbGlkIHZhcigtLXN1cmZhY2UtYm9yZGVyKTtcblxuICAgICAgICAgIHAge1xuICAgICAgICAgICAgZm9udC1zaXplOiAxcmVtO1xuICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDEuNjtcbiAgICAgICAgICAgIGNvbG9yOiB2YXIoLS10ZXh0LWNvbG9yLXNlY29uZGFyeSk7XG4gICAgICAgICAgICBtYXJnaW46IDFyZW0gMCAwIDA7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLmFuc3dlci1pbWFnZSB7XG4gICAgICAgICAgICBtYXJnaW46IDEuNXJlbSAwIDAgMDtcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgICAgICAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgICAgICAgICAgIGJhY2tncm91bmQ6IHZhcigtLXN1cmZhY2UtNTApO1xuICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgdmFyKC0tc3VyZmFjZS1ib3JkZXIpO1xuXG4gICAgICAgICAgICBpbWcge1xuICAgICAgICAgICAgICB3aWR0aDogMTAwJTtcbiAgICAgICAgICAgICAgaGVpZ2h0OiBhdXRvO1xuICAgICAgICAgICAgICBkaXNwbGF5OiBibG9jaztcbiAgICAgICAgICAgICAgbWF4LXdpZHRoOiAxMDAlO1xuICAgICAgICAgICAgICBvYmplY3QtZml0OiBjb250YWluO1xuICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG4gICAgICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjtcblxuICAgICAgICAgICAgICAmOmhvdmVyIHtcbiAgICAgICAgICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMDIpO1xuICAgICAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgNnB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjE1KTtcbiAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgIC8vIEVuc3VyZSBpbWFnZXMgZG9uJ3QgZXhjZWVkIGNvbnRhaW5lciB3aWR0aFxuICAgICAgICAgICAgICBtYXgtaGVpZ2h0OiA0MDBweDtcbiAgICAgICAgICAgICAgb2JqZWN0LXBvc2l0aW9uOiBjZW50ZXI7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC8vIExvYWRpbmcgc3RhdGVcbiAgICAgICAgICAgICY6OmJlZm9yZSB7XG4gICAgICAgICAgICAgIGNvbnRlbnQ6ICcnO1xuICAgICAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgICAgICAgICAgIHRvcDogMDtcbiAgICAgICAgICAgICAgbGVmdDogMDtcbiAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7XG4gICAgICAgICAgICAgIGhlaWdodDogMTAwJTtcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tc3VyZmFjZS0xMDApO1xuICAgICAgICAgICAgICBkaXNwbGF5OiBub25lO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAvLyBFcnJvciBzdGF0ZSBzdHlsaW5nXG4gICAgICAgICAgICBpbWdbc3JjPVwiXCJdLCBpbWc6bm90KFtzcmNdKSB7XG4gICAgICAgICAgICAgIGRpc3BsYXk6IG5vbmU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbi8vIFF1aWNrIExpbmtzIFNlY3Rpb25cbi5xdWljay1saW5rcy1zZWN0aW9uIHtcbiAgbWFyZ2luLWJvdHRvbTogM3JlbTtcblxuICBoMiB7XG4gICAgZm9udC1zaXplOiAxLjc1cmVtO1xuICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgbWFyZ2luOiAwIDAgMS41cmVtIDA7XG4gICAgY29sb3I6IHZhcigtLXRleHQtY29sb3IpO1xuICB9XG5cbiAgLnF1aWNrLWxpbmtzLWdyaWQge1xuICAgIGRpc3BsYXk6IGdyaWQ7XG4gICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgyODBweCwgMWZyKSk7XG4gICAgZ2FwOiAxLjVyZW07XG5cbiAgICAucXVpY2stbGluay1jYXJkIHtcbiAgICAgIGJhY2tncm91bmQ6IHZhcigtLXN1cmZhY2UtY2FyZCk7XG4gICAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICAgICAgcGFkZGluZzogMS41cmVtO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBnYXA6IDFyZW07XG4gICAgICBjdXJzb3I6IHBvaW50ZXI7XG4gICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICAgICAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbiAgICAgIGNvbG9yOiBpbmhlcml0O1xuXG4gICAgICAmOmhvdmVyIHtcbiAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpO1xuICAgICAgICBib3gtc2hhZG93OiAwIDhweCAyNHB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7XG5cbiAgICAgICAgLmNhcmQtYXJyb3cge1xuICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWCg0cHgpO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIC5jYXJkLWljb24ge1xuICAgICAgICB3aWR0aDogM3JlbTtcbiAgICAgICAgaGVpZ2h0OiAzcmVtO1xuICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1wcmltYXJ5LTEwMCk7XG4gICAgICAgIGNvbG9yOiB2YXIoLS1wcmltYXJ5LTcwMCk7XG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgICBmb250LXNpemU6IDEuNXJlbTtcbiAgICAgICAgZmxleC1zaHJpbms6IDA7XG4gICAgICB9XG5cbiAgICAgIC5jYXJkLWNvbnRlbnQge1xuICAgICAgICBmbGV4OiAxO1xuXG4gICAgICAgIGgzIHtcbiAgICAgICAgICBmb250LXNpemU6IDEuMTI1cmVtO1xuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICAgICAgbWFyZ2luOiAwIDAgMC4yNXJlbSAwO1xuICAgICAgICAgIGNvbG9yOiB2YXIoLS10ZXh0LWNvbG9yKTtcbiAgICAgICAgfVxuXG4gICAgICAgIHAge1xuICAgICAgICAgIGZvbnQtc2l6ZTogMC44NzVyZW07XG4gICAgICAgICAgY29sb3I6IHZhcigtLXRleHQtY29sb3Itc2Vjb25kYXJ5KTtcbiAgICAgICAgICBtYXJnaW46IDA7XG4gICAgICAgICAgbGluZS1oZWlnaHQ6IDEuNDtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAuY2FyZC1hcnJvdyB7XG4gICAgICAgIGNvbG9yOiB2YXIoLS1wcmltYXJ5LWNvbG9yKTtcbiAgICAgICAgZm9udC1zaXplOiAxLjI1cmVtO1xuICAgICAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlO1xuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG4vLyBTdXBwb3J0IFNlY3Rpb25cbi5zdXBwb3J0LXNlY3Rpb24ge1xuICAuc3VwcG9ydC1jYXJkIHtcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCB2YXIoLS1zdXJmYWNlLTEwMCkgMCUsIHZhcigtLXN1cmZhY2UtMjAwKSAxMDAlKTtcbiAgICBib3JkZXItcmFkaXVzOiAxNnB4O1xuICAgIHBhZGRpbmc6IDJyZW07XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGdhcDogMS41cmVtO1xuXG4gICAgLnN1cHBvcnQtaWNvbiB7XG4gICAgICB3aWR0aDogNHJlbTtcbiAgICAgIGhlaWdodDogNHJlbTtcbiAgICAgIGJhY2tncm91bmQ6IHZhcigtLXByaW1hcnktY29sb3IpO1xuICAgICAgY29sb3I6IHdoaXRlO1xuICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgIGZvbnQtc2l6ZTogMnJlbTtcbiAgICAgIGZsZXgtc2hyaW5rOiAwO1xuICAgIH1cblxuICAgIC5zdXBwb3J0LWNvbnRlbnQge1xuICAgICAgZmxleDogMTtcblxuICAgICAgaDMge1xuICAgICAgICBmb250LXNpemU6IDEuNXJlbTtcbiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICAgICAgbWFyZ2luOiAwIDAgMC41cmVtIDA7XG4gICAgICAgIGNvbG9yOiB2YXIoLS10ZXh0LWNvbG9yKTtcbiAgICAgIH1cblxuICAgICAgcCB7XG4gICAgICAgIGZvbnQtc2l6ZTogMXJlbTtcbiAgICAgICAgY29sb3I6IHZhcigtLXRleHQtY29sb3Itc2Vjb25kYXJ5KTtcbiAgICAgICAgbWFyZ2luOiAwIDAgMS41cmVtIDA7XG4gICAgICAgIGxpbmUtaGVpZ2h0OiAxLjU7XG4gICAgICB9XG5cbiAgICAgIC5zdXBwb3J0LWJ1dHRvbiB7XG4gICAgICAgIGJhY2tncm91bmQ6IHZhcigtLXByaW1hcnktY29sb3IpO1xuICAgICAgICBib3JkZXItY29sb3I6IHZhcigtLXByaW1hcnktY29sb3IpO1xuICAgICAgfVxuICAgIH1cbiAgfVxufVxuXG4vLyBSZXNwb25zaXZlIERlc2lnblxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gIC5oZWxwLWNvbnRhaW5lciB7XG4gICAgcGFkZGluZzogMXJlbTtcbiAgfVxuXG4gIC5oZWxwLWhlYWRlciB7XG4gICAgcGFkZGluZzogMnJlbSAxLjVyZW07XG5cbiAgICAuaGVhZGVyLWNvbnRlbnQge1xuICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICAgIGdhcDogMXJlbTtcblxuICAgICAgLmhlYWRlci10ZXh0IGgxIHtcbiAgICAgICAgZm9udC1zaXplOiAycmVtO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC5oZWxwLWNvbnRyb2xzIHtcbiAgICBwYWRkaW5nOiAxcmVtO1xuXG4gICAgLmZpbHRlci1zZWN0aW9uIHtcbiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICBhbGlnbi1pdGVtczogc3RyZXRjaDtcblxuICAgICAgLmNhdGVnb3J5LWZpbHRlcnMge1xuICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgIH1cblxuICAgICAgLmFjdGlvbi1idXR0b25zIHtcbiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLnF1aWNrLWxpbmtzLWdyaWQge1xuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyO1xuICB9XG5cbiAgLnN1cHBvcnQtY2FyZCB7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gICAgcGFkZGluZzogMS41cmVtO1xuICB9XG5cbiAgLmZhcS1pdGVtIC5mYXEtcXVlc3Rpb24ge1xuICAgIHBhZGRpbmc6IDFyZW07XG5cbiAgICAucXVlc3Rpb24tY29udGVudCBoMyB7XG4gICAgICBmb250LXNpemU6IDFyZW07XG4gICAgfVxuICB9XG5cbiAgLmZhcS1pdGVtIC5hbnN3ZXItY29udGVudCB7XG4gICAgcGFkZGluZzogMCAxcmVtIDFyZW0gMXJlbTtcblxuICAgIC5hbnN3ZXItaW1hZ2Uge1xuICAgICAgbWFyZ2luOiAxcmVtIDAgMCAwO1xuICAgICAgYm9yZGVyLXJhZGl1czogNnB4O1xuXG4gICAgICBpbWcge1xuICAgICAgICBib3JkZXItcmFkaXVzOiA2cHg7XG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbi8vIERhcmsgdGhlbWUgc3VwcG9ydFxuOmhvc3QtY29udGV4dCguZGFyaykge1xuICAuaGVscC1oZWFkZXIge1xuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHZhcigtLXByaW1hcnktNjAwKSAwJSwgdmFyKC0tcHJpbWFyeS03MDApIDEwMCUpO1xuICB9XG5cbiAgLmNhdGVnb3J5LWJ0bjpub3QoLmFjdGl2ZSkge1xuICAgIGJhY2tncm91bmQ6IHZhcigtLXN1cmZhY2UtNzAwKTtcbiAgICBib3JkZXItY29sb3I6IHZhcigtLXN1cmZhY2UtNjAwKTtcbiAgICBjb2xvcjogdmFyKC0tdGV4dC1jb2xvcik7XG5cbiAgICAmOmhvdmVyIHtcbiAgICAgIGJhY2tncm91bmQ6IHZhcigtLXN1cmZhY2UtNjAwKTtcbiAgICB9XG4gIH1cblxuICAuc3VwcG9ydC1jYXJkIHtcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCB2YXIoLS1zdXJmYWNlLTcwMCkgMCUsIHZhcigtLXN1cmZhY2UtODAwKSAxMDAlKTtcbiAgfVxuXG4gIC5mYXEtaXRlbSAuYW5zd2VyLWNvbnRlbnQgLmFuc3dlci1pbWFnZSB7XG4gICAgYmFja2dyb3VuZDogdmFyKC0tc3VyZmFjZS04MDApO1xuICAgIGJvcmRlci1jb2xvcjogdmFyKC0tc3VyZmFjZS02MDApO1xuXG4gICAgaW1nIHtcbiAgICAgICY6aG92ZXIge1xuICAgICAgICBib3gtc2hhZG93OiAwIDZweCAyMHB4IHJnYmEoMCwgMCwgMCwgMC4zKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵlistener", "HelpComponent_button_19_Template_button_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r5", "category_r3", "$implicit", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "filterByCategory", "ɵɵelementEnd", "ɵɵclassMap", "ctx_r0", "selectedCate<PERSON><PERSON>", "ɵɵproperty", "ɵɵelement", "ɵɵtext", "ɵɵadvance", "faq_r7", "imageSrc", "ɵɵsanitizeUrl", "question", "HelpComponent_div_25_div_1_Template_div_click_1_listener", "_r12", "ctx_r11", "toggleFAQ", "ɵɵtemplate", "HelpComponent_div_25_div_1_div_12_Template", "ɵɵclassProp", "expanded", "ɵɵtextInterpolate", "category", "answer", "ɵɵsanitizeHtml", "HelpComponent_div_25_div_1_Template", "ctx_r2", "filteredFAQs", "HelpComponent", "constructor", "faqItems", "id", "categories", "searchQuery", "ngOnInit", "Array", "from", "Set", "map", "item", "applyFilters", "onSearch", "filtered", "filter", "trim", "query", "toLowerCase", "includes", "faq", "expandAll", "for<PERSON>ach", "collapseAll", "_", "_2", "selectors", "decls", "vars", "consts", "template", "HelpComponent_Template", "rf", "ctx", "HelpComponent_Template_input_ngModelChange_16_listener", "$event", "HelpComponent_Template_input_input_16_listener", "HelpComponent_button_19_Template", "HelpComponent_Template_button_click_21_listener", "HelpComponent_Template_button_click_22_listener", "HelpComponent_div_24_Template", "HelpComponent_div_25_Template", "breadcrumbItems", "length"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\help\\help.component.ts", "D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\help\\help.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\n\ninterface FAQItem {\n  id: string;\n  question: string;\n  answer: string;\n  category: string;\n  expanded?: boolean;\n  imageSrc?: string;\n}\n\n@Component({\n  selector: 'app-help',\n  templateUrl: './help.component.html',\n  styleUrls: ['./help.component.scss']\n})\nexport class HelpComponent implements OnInit {\n\n  faqItems: FAQItem[] = [\n    // Getting Started\n    {\n      id: 'getting-started-1',\n      question: 'How do I get started with SolarKapital?',\n      answer: 'Welcome to SolarKapital! To get started, you need to add your solar energy providers first. Navigate to the Providers section from the main menu and follow the setup wizard to connect your solar installations.',\n      category: 'Getting Started'\n    },\n    {\n      id: 'getting-started-2',\n      question: 'What information do I need to set up my account?',\n      answer: 'You will need your solar provider credentials (username and password), and depending on your provider, additional information like Portfolio ID or FTP URL. Make sure you have this information ready before starting the setup process.',\n      category: 'Getting Started'\n    },\n\n    // Providers Management\n    {\n      id: 'providers-1',\n      question: 'How do I add a new solar energy provider?',\n      answer: 'To add a new provider: 1) Go to the Providers page from the main menu, 2) Click the \"Add Provider\" button, 3) Select your provider from the dropdown list, 4) Enter your credentials (username and password), 5) For some providers like Aurora, you may need to enter a Portfolio ID,  6) For some providers like SMA, you may need to enter the Ftp Url, 7) Get the list of stations, 6) Select which stations you want to monitor, 7) Click \"Save Providers\" to complete the setup.',\n      category: 'Providers'\n    },\n    {\n      id: 'providers-2',\n      question: 'What providers are currently supported?',\n      answer: 'SolarKapital currently supports major solar energy providers including SolarEdge, Huawei, Fronius, SMA, and others. The list of supported providers is constantly expanding. If your provider is not listed, please contact our support team.',\n      category: 'Providers'\n    },\n    {\n      id: 'providers-3',\n      question: 'Why do I need to provide my provider credentials?',\n      answer: 'Your provider credentials are required to securely access your solar energy data from your provider\\'s platform. This allows SolarKapital to retrieve real-time and historical data about your solar installations. Your credentials are encrypted and stored securely.',\n      category: 'Providers'\n    },\n    {\n      id: 'providers-4',\n      question: 'What provider credentials are required for Huawei?',\n      answer: 'The username and password that should be used are not the ones that you use to enter Huawei portal. You should go to System->Company Management->Northbound Management and create a new user that will be able to retrieve portal data.',\n      category: 'Providers',\n      imageSrc: '/assets/layout/images/huawei_example.png'\n    },\n    {\n      id: 'providers-5',\n      question: 'What is a Portfolio ID and when do I need it?',\n      answer: 'A Portfolio ID is required for certain providers like Aurora. It\\'s a unique identifier that groups your solar installations under your account. You can find this ID in your provider\\'s web portal, usually in the account settings or dashboard overview.',\n      category: 'Providers',\n      imageSrc: '/assets/layout/images/aurora_example.png'\n    },\n    {\n      id: 'providers-6',\n      question: 'What is an FTP url and when do I need it?',\n      answer: 'An FTP url is required for certain providers like SMA. It\\'s the url of the FTP that is used to identify your solar installations under your account. The FTP url should be like <em>ftp://ftp.server.com/additionalfolder/</em>. Please use the full path where the stations folders are located.',\n      category: 'Providers'\n    },\n    {\n      id: 'providers-7',\n      question: 'How do I edit or remove a provider configuration?',\n      answer: 'To edit a provider: click the edit (pencil) icon on the provider card and update the information. To remove a provider: click the delete (trash) icon and confirm the removal. Note that removing a provider will also remove all associated station data.',\n      category: 'Providers'\n    },\n    {\n      id: 'providers-8',\n      question: 'Can I add multiple configurations for the same provider?',\n      answer: 'Yes, you can add multiple configurations for the same provider if you have multiple accounts or different access credentials. Each configuration will be treated separately and can monitor different sets of stations.',\n      category: 'Providers'\n    },\n\n    // Stations & Monitoring\n    {\n      id: 'stations-1',\n      question: 'How do I view my solar station data?',\n      answer: 'After setting up your providers, your stations will automatically appear in the Stations section. Click on any station to view detailed energy production data, charts, and performance metrics.',\n      category: 'Stations'\n    },\n    {\n      id: 'stations-2',\n      question: 'What data can I see for each station?',\n      answer: 'For each station, you can view: real-time energy production, historical energy data, performance charts, weather information, inverter details, and system health status. The data is updated regularly based on your provider\\'s refresh rate.',\n      category: 'Stations'\n    },\n    {\n      id: 'stations-3',\n      question: 'How often is the data updated?',\n      answer: 'Data update frequency depends on your solar provider. Most providers update data every 15-30 minutes. SolarKapital automatically syncs with your providers to ensure you have the latest information available.',\n      category: 'Stations'\n    },\n\n    // Troubleshooting\n    {\n      id: 'troubleshooting-1',\n      question: 'My provider configuration is not working. What should I do?',\n      answer: 'First, verify that your credentials are correct by logging into your provider\\'s website directly. If that works, check if your provider requires special permissions for API access. Some providers may need to enable third-party access in their settings.',\n      category: 'Troubleshooting'\n    },\n    {\n      id: 'troubleshooting-2',\n      question: 'I don\\'t see any data for my stations. Why?',\n      answer: 'This could be due to several reasons: 1) The provider configuration is incorrect, 2) Your stations are not properly selected in the provider setup, 3) There might be a temporary issue with the provider\\'s API, 4) Your stations might not have recent data available.',\n      category: 'Troubleshooting'\n    },\n    {\n      id: 'troubleshooting-3',\n      question: 'How do I reset my provider configuration?',\n      answer: 'To reset a provider configuration: go to the Providers page, click the delete icon for the problematic provider, confirm the removal, then add the provider again with the correct information. This will create a fresh configuration.',\n      category: 'Troubleshooting'\n    },\n\n    // Account & Security\n    {\n      id: 'security-1',\n      question: 'Is my data secure?',\n      answer: 'Yes, SolarKapital takes security seriously. All provider credentials are encrypted before storage, data transmission uses secure HTTPS connections, and we follow industry best practices for data protection. Your solar data is never shared with third parties.',\n      category: 'Security'\n    },\n    {\n      id: 'security-2',\n      question: 'Can I change my account password?',\n      answer: 'Yes, you can change your account password from the Profile Settings section. Click on your profile icon in the top-right corner and select \"Profile Settings\" to update your password and other account information.',\n      category: 'Security'\n    }\n  ];\n\n  categories: string[] = [];\n  selectedCategory: string = 'All';\n  filteredFAQs: FAQItem[] = [];\n  searchQuery: string = '';\n\n  ngOnInit() {\n    // Extract unique categories\n    this.categories = ['All', ...Array.from(new Set(this.faqItems.map(item => item.category)))];\n    this.filteredFAQs = this.faqItems;\n  }\n\n  filterByCategory(category: string) {\n    this.selectedCategory = category;\n    this.applyFilters();\n  }\n\n  onSearch() {\n    this.applyFilters();\n  }\n\n  private applyFilters() {\n    let filtered = this.faqItems;\n\n    // Filter by category\n    if (this.selectedCategory !== 'All') {\n      filtered = filtered.filter(item => item.category === this.selectedCategory);\n    }\n\n    // Filter by search query\n    if (this.searchQuery.trim()) {\n      const query = this.searchQuery.toLowerCase();\n      filtered = filtered.filter(item => \n        item.question.toLowerCase().includes(query) || \n        item.answer.toLowerCase().includes(query)\n      );\n    }\n\n    this.filteredFAQs = filtered;\n  }\n\n  toggleFAQ(faq: FAQItem) {\n    faq.expanded = !faq.expanded;\n  }\n\n  expandAll() {\n    this.filteredFAQs.forEach(faq => faq.expanded = true);\n  }\n\n  collapseAll() {\n    this.filteredFAQs.forEach(faq => faq.expanded = false);\n  }\n}\n", "<div class=\"help-container\">\n    <!-- Modern Breadcrumb -->\n    <div class=\"col-12 mb-4\">\n        <app-modern-breadcrumb [items]=\"breadcrumbItems\"></app-modern-breadcrumb>\n    </div>\n\n    <!-- Header Section -->\n    <div class=\"help-header\">\n        <div class=\"header-content\">\n            <div class=\"header-icon\">\n                <i class=\"pi pi-question-circle\"></i>\n            </div>\n            <div class=\"header-text\">\n                <h1>Help & FAQ</h1>\n                <p>Find answers to common questions and learn how to make the most of SolarKapital</p>\n            </div>\n        </div>\n    </div>\n\n    <!-- Search and Filters -->\n    <div class=\"help-controls\">\n        <div class=\"search-section\">\n            <div class=\"search-wrapper\">\n                <i class=\"pi pi-search search-icon\"></i>\n                <input type=\"text\" \n                       pInputText \n                       placeholder=\"Search for help topics...\" \n                       [(ngModel)]=\"searchQuery\"\n                       (input)=\"onSearch()\"\n                       class=\"search-input\">\n            </div>\n        </div>\n\n        <div class=\"filter-section\">\n            <div class=\"category-filters\">\n                <button *ngFor=\"let category of categories\"\n                        pButton\n                        [label]=\"category\"\n                        [class]=\"'category-btn ' + (selectedCategory === category ? 'active' : '')\"\n                        (click)=\"filterByCategory(category)\"\n                        size=\"small\">\n                </button>\n            </div>\n            \n            <div class=\"action-buttons\">\n                <button pButton \n                        label=\"Expand All\" \n                        icon=\"pi pi-plus\" \n                        (click)=\"expandAll()\"\n                        severity=\"secondary\"\n                        size=\"small\">\n                </button>\n                <button pButton \n                        label=\"Collapse All\" \n                        icon=\"pi pi-minus\" \n                        (click)=\"collapseAll()\"\n                        severity=\"secondary\"\n                        size=\"small\">\n                </button>\n            </div>\n        </div>\n    </div>\n\n    <!-- FAQ Content -->\n    <div class=\"faq-content\">\n        <div *ngIf=\"filteredFAQs.length === 0\" class=\"no-results\">\n            <div class=\"no-results-icon\">\n                <i class=\"pi pi-search\"></i>\n            </div>\n            <h3>No results found</h3>\n            <p>Try adjusting your search terms or browse different categories.</p>\n        </div>\n\n        <div *ngIf=\"filteredFAQs.length > 0\" class=\"faq-list\">\n            <div *ngFor=\"let faq of filteredFAQs; let i = index\" \n                 class=\"faq-item\"\n                 [class.expanded]=\"faq.expanded\">\n                \n                <div class=\"faq-question\" (click)=\"toggleFAQ(faq)\">\n                    <div class=\"question-content\">\n                        <span class=\"category-badge\">{{ faq.category }}</span>\n                        <h3>{{ faq.question }}</h3>\n                    </div>\n                    <div class=\"expand-icon\">\n                        <i class=\"pi\" [class.pi-chevron-down]=\"!faq.expanded\" [class.pi-chevron-up]=\"faq.expanded\"></i>\n                    </div>\n                </div>\n\n                <div class=\"faq-answer\" [class.show]=\"faq.expanded\">\n                    <div class=\"answer-content\">\n                        <p [innerHTML]=\"faq.answer\"></p>\n                        <div *ngIf=\"faq.imageSrc\" class=\"answer-image\">\n                            <img [src]=\"faq.imageSrc\" [alt]=\"faq.question\" />\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <!-- Quick Links Section -->\n    <div class=\"quick-links-section\">\n        <h2>Quick Links</h2>\n        <div class=\"quick-links-grid\">\n            <div class=\"quick-link-card\" routerLink=\"/app/providers\">\n                <div class=\"card-icon\">\n                    <i class=\"pi pi-server\"></i>\n                </div>\n                <div class=\"card-content\">\n                    <h3>Manage Providers</h3>\n                    <p>Add, edit, or remove your solar energy providers</p>\n                </div>\n                <div class=\"card-arrow\">\n                    <i class=\"pi pi-arrow-right\"></i>\n                </div>\n            </div>\n\n            <div class=\"quick-link-card\" routerLink=\"/app/stations\">\n                <div class=\"card-icon\">\n                    <i class=\"pi pi-chart-line\"></i>\n                </div>\n                <div class=\"card-content\">\n                    <h3>View Stations</h3>\n                    <p>Monitor your solar installations and energy data</p>\n                </div>\n                <div class=\"card-arrow\">\n                    <i class=\"pi pi-arrow-right\"></i>\n                </div>\n            </div>\n\n            <div class=\"quick-link-card\" routerLink=\"/app/index\">\n                <div class=\"card-icon\">\n                    <i class=\"pi pi-home\"></i>\n                </div>\n                <div class=\"card-content\">\n                    <h3>Dashboard</h3>\n                    <p>Overview of all your solar energy systems</p>\n                </div>\n                <div class=\"card-arrow\">\n                    <i class=\"pi pi-arrow-right\"></i>\n                </div>\n            </div>\n\n            <div class=\"quick-link-card\" routerLink=\"/app/communication\">\n                <div class=\"card-icon\">\n                    <i class=\"pi pi-comments\"></i>\n                </div>\n                <div class=\"card-content\">\n                    <h3>Communication</h3>\n                    <p>View system messages and notifications</p>\n                </div>\n                <div class=\"card-arrow\">\n                    <i class=\"pi pi-arrow-right\"></i>\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <!-- Contact Support Section -->\n    <div class=\"support-section\">\n        <div class=\"support-card\">\n            <div class=\"support-icon\">\n                <i class=\"pi pi-headphones\"></i>\n            </div>\n            <div class=\"support-content\">\n                <h3>Still need help?</h3>\n                <p>Can't find what you're looking for? Our support team is here to help you get the most out of SolarKapital.</p>\n                <button pButton \n                        label=\"Contact Support\" \n                        icon=\"pi pi-envelope\" \n                        class=\"support-button\">\n                </button>\n            </div>\n        </div>\n    </div>\n</div>\n"], "mappings": ";;;;;;;;;ICmCgBA,EAAA,CAAAC,cAAA,iBAKqB;IADbD,EAAA,CAAAE,UAAA,mBAAAC,yDAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,WAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,gBAAA,CAAAL,WAAA,CAA0B;IAAA,EAAC;IAE5CP,EAAA,CAAAa,YAAA,EAAS;;;;;IAHDb,EAAA,CAAAc,UAAA,oBAAAC,MAAA,CAAAC,gBAAA,KAAAT,WAAA,kBAA2E;IAD3EP,EAAA,CAAAiB,UAAA,UAAAV,WAAA,CAAkB;;;;;IA4BlCP,EAAA,CAAAC,cAAA,cAA0D;IAElDD,EAAA,CAAAkB,SAAA,YAA4B;IAChClB,EAAA,CAAAa,YAAA,EAAM;IACNb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAmB,MAAA,uBAAgB;IAAAnB,EAAA,CAAAa,YAAA,EAAK;IACzBb,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAmB,MAAA,sEAA+D;IAAAnB,EAAA,CAAAa,YAAA,EAAI;;;;;IAqB1Db,EAAA,CAAAC,cAAA,cAA+C;IAC3CD,EAAA,CAAAkB,SAAA,cAAiD;IACrDlB,EAAA,CAAAa,YAAA,EAAM;;;;IADGb,EAAA,CAAAoB,SAAA,GAAoB;IAApBpB,EAAA,CAAAiB,UAAA,QAAAI,MAAA,CAAAC,QAAA,EAAAtB,EAAA,CAAAuB,aAAA,CAAoB,QAAAF,MAAA,CAAAG,QAAA;;;;;;IAlBzCxB,EAAA,CAAAC,cAAA,cAEqC;IAEPD,EAAA,CAAAE,UAAA,mBAAAuB,yDAAA;MAAA,MAAArB,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAqB,IAAA;MAAA,MAAAL,MAAA,GAAAjB,WAAA,CAAAI,SAAA;MAAA,MAAAmB,OAAA,GAAA3B,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAgB,OAAA,CAAAC,SAAA,CAAAP,MAAA,CAAc;IAAA,EAAC;IAC9CrB,EAAA,CAAAC,cAAA,cAA8B;IACGD,EAAA,CAAAmB,MAAA,GAAkB;IAAAnB,EAAA,CAAAa,YAAA,EAAO;IACtDb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAmB,MAAA,GAAkB;IAAAnB,EAAA,CAAAa,YAAA,EAAK;IAE/Bb,EAAA,CAAAC,cAAA,cAAyB;IACrBD,EAAA,CAAAkB,SAAA,YAA+F;IACnGlB,EAAA,CAAAa,YAAA,EAAM;IAGVb,EAAA,CAAAC,cAAA,cAAoD;IAE5CD,EAAA,CAAAkB,SAAA,aAAgC;IAChClB,EAAA,CAAA6B,UAAA,KAAAC,0CAAA,kBAEM;IACV9B,EAAA,CAAAa,YAAA,EAAM;;;;IAlBTb,EAAA,CAAA+B,WAAA,aAAAV,MAAA,CAAAW,QAAA,CAA+B;IAIKhC,EAAA,CAAAoB,SAAA,GAAkB;IAAlBpB,EAAA,CAAAiC,iBAAA,CAAAZ,MAAA,CAAAa,QAAA,CAAkB;IAC3ClC,EAAA,CAAAoB,SAAA,GAAkB;IAAlBpB,EAAA,CAAAiC,iBAAA,CAAAZ,MAAA,CAAAG,QAAA,CAAkB;IAGRxB,EAAA,CAAAoB,SAAA,GAAuC;IAAvCpB,EAAA,CAAA+B,WAAA,qBAAAV,MAAA,CAAAW,QAAA,CAAuC,kBAAAX,MAAA,CAAAW,QAAA;IAIrChC,EAAA,CAAAoB,SAAA,GAA2B;IAA3BpB,EAAA,CAAA+B,WAAA,SAAAV,MAAA,CAAAW,QAAA,CAA2B;IAExChC,EAAA,CAAAoB,SAAA,GAAwB;IAAxBpB,EAAA,CAAAiB,UAAA,cAAAI,MAAA,CAAAc,MAAA,EAAAnC,EAAA,CAAAoC,cAAA,CAAwB;IACrBpC,EAAA,CAAAoB,SAAA,GAAkB;IAAlBpB,EAAA,CAAAiB,UAAA,SAAAI,MAAA,CAAAC,QAAA,CAAkB;;;;;IAlBxCtB,EAAA,CAAAC,cAAA,cAAsD;IAClDD,EAAA,CAAA6B,UAAA,IAAAQ,mCAAA,oBAsBM;IACVrC,EAAA,CAAAa,YAAA,EAAM;;;;IAvBmBb,EAAA,CAAAoB,SAAA,GAAiB;IAAjBpB,EAAA,CAAAiB,UAAA,YAAAqB,MAAA,CAAAC,YAAA,CAAiB;;;AD1DlD,OAAM,MAAOC,aAAa;EAL1BC,YAAA;IAOE,KAAAC,QAAQ,GAAc;IACpB;IACA;MACEC,EAAE,EAAE,mBAAmB;MACvBnB,QAAQ,EAAE,yCAAyC;MACnDW,MAAM,EAAE,mNAAmN;MAC3ND,QAAQ,EAAE;KACX,EACD;MACES,EAAE,EAAE,mBAAmB;MACvBnB,QAAQ,EAAE,kDAAkD;MAC5DW,MAAM,EAAE,0OAA0O;MAClPD,QAAQ,EAAE;KACX;IAED;IACA;MACES,EAAE,EAAE,aAAa;MACjBnB,QAAQ,EAAE,2CAA2C;MACrDW,MAAM,EAAE,wdAAwd;MACheD,QAAQ,EAAE;KACX,EACD;MACES,EAAE,EAAE,aAAa;MACjBnB,QAAQ,EAAE,yCAAyC;MACnDW,MAAM,EAAE,+OAA+O;MACvPD,QAAQ,EAAE;KACX,EACD;MACES,EAAE,EAAE,aAAa;MACjBnB,QAAQ,EAAE,mDAAmD;MAC7DW,MAAM,EAAE,yQAAyQ;MACjRD,QAAQ,EAAE;KACX,EACD;MACES,EAAE,EAAE,aAAa;MACjBnB,QAAQ,EAAE,oDAAoD;MAC9DW,MAAM,EAAE,yOAAyO;MACjPD,QAAQ,EAAE,WAAW;MACrBZ,QAAQ,EAAE;KACX,EACD;MACEqB,EAAE,EAAE,aAAa;MACjBnB,QAAQ,EAAE,+CAA+C;MACzDW,MAAM,EAAE,8PAA8P;MACtQD,QAAQ,EAAE,WAAW;MACrBZ,QAAQ,EAAE;KACX,EACD;MACEqB,EAAE,EAAE,aAAa;MACjBnB,QAAQ,EAAE,2CAA2C;MACrDW,MAAM,EAAE,oSAAoS;MAC5SD,QAAQ,EAAE;KACX,EACD;MACES,EAAE,EAAE,aAAa;MACjBnB,QAAQ,EAAE,mDAAmD;MAC7DW,MAAM,EAAE,4PAA4P;MACpQD,QAAQ,EAAE;KACX,EACD;MACES,EAAE,EAAE,aAAa;MACjBnB,QAAQ,EAAE,0DAA0D;MACpEW,MAAM,EAAE,yNAAyN;MACjOD,QAAQ,EAAE;KACX;IAED;IACA;MACES,EAAE,EAAE,YAAY;MAChBnB,QAAQ,EAAE,sCAAsC;MAChDW,MAAM,EAAE,kMAAkM;MAC1MD,QAAQ,EAAE;KACX,EACD;MACES,EAAE,EAAE,YAAY;MAChBnB,QAAQ,EAAE,uCAAuC;MACjDW,MAAM,EAAE,iPAAiP;MACzPD,QAAQ,EAAE;KACX,EACD;MACES,EAAE,EAAE,YAAY;MAChBnB,QAAQ,EAAE,gCAAgC;MAC1CW,MAAM,EAAE,iNAAiN;MACzND,QAAQ,EAAE;KACX;IAED;IACA;MACES,EAAE,EAAE,mBAAmB;MACvBnB,QAAQ,EAAE,6DAA6D;MACvEW,MAAM,EAAE,+PAA+P;MACvQD,QAAQ,EAAE;KACX,EACD;MACES,EAAE,EAAE,mBAAmB;MACvBnB,QAAQ,EAAE,6CAA6C;MACvDW,MAAM,EAAE,0QAA0Q;MAClRD,QAAQ,EAAE;KACX,EACD;MACES,EAAE,EAAE,mBAAmB;MACvBnB,QAAQ,EAAE,2CAA2C;MACrDW,MAAM,EAAE,yOAAyO;MACjPD,QAAQ,EAAE;KACX;IAED;IACA;MACES,EAAE,EAAE,YAAY;MAChBnB,QAAQ,EAAE,oBAAoB;MAC9BW,MAAM,EAAE,oQAAoQ;MAC5QD,QAAQ,EAAE;KACX,EACD;MACES,EAAE,EAAE,YAAY;MAChBnB,QAAQ,EAAE,mCAAmC;MAC7CW,MAAM,EAAE,sNAAsN;MAC9ND,QAAQ,EAAE;KACX,CACF;IAED,KAAAU,UAAU,GAAa,EAAE;IACzB,KAAA5B,gBAAgB,GAAW,KAAK;IAChC,KAAAuB,YAAY,GAAc,EAAE;IAC5B,KAAAM,WAAW,GAAW,EAAE;;EAExBC,QAAQA,CAAA;IACN;IACA,IAAI,CAACF,UAAU,GAAG,CAAC,KAAK,EAAE,GAAGG,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAAC,IAAI,CAACP,QAAQ,CAACQ,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACjB,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC3F,IAAI,CAACK,YAAY,GAAG,IAAI,CAACG,QAAQ;EACnC;EAEA9B,gBAAgBA,CAACsB,QAAgB;IAC/B,IAAI,CAAClB,gBAAgB,GAAGkB,QAAQ;IAChC,IAAI,CAACkB,YAAY,EAAE;EACrB;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACD,YAAY,EAAE;EACrB;EAEQA,YAAYA,CAAA;IAClB,IAAIE,QAAQ,GAAG,IAAI,CAACZ,QAAQ;IAE5B;IACA,IAAI,IAAI,CAAC1B,gBAAgB,KAAK,KAAK,EAAE;MACnCsC,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACJ,IAAI,IAAIA,IAAI,CAACjB,QAAQ,KAAK,IAAI,CAAClB,gBAAgB,CAAC;;IAG7E;IACA,IAAI,IAAI,CAAC6B,WAAW,CAACW,IAAI,EAAE,EAAE;MAC3B,MAAMC,KAAK,GAAG,IAAI,CAACZ,WAAW,CAACa,WAAW,EAAE;MAC5CJ,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACJ,IAAI,IAC7BA,IAAI,CAAC3B,QAAQ,CAACkC,WAAW,EAAE,CAACC,QAAQ,CAACF,KAAK,CAAC,IAC3CN,IAAI,CAAChB,MAAM,CAACuB,WAAW,EAAE,CAACC,QAAQ,CAACF,KAAK,CAAC,CAC1C;;IAGH,IAAI,CAAClB,YAAY,GAAGe,QAAQ;EAC9B;EAEA1B,SAASA,CAACgC,GAAY;IACpBA,GAAG,CAAC5B,QAAQ,GAAG,CAAC4B,GAAG,CAAC5B,QAAQ;EAC9B;EAEA6B,SAASA,CAAA;IACP,IAAI,CAACtB,YAAY,CAACuB,OAAO,CAACF,GAAG,IAAIA,GAAG,CAAC5B,QAAQ,GAAG,IAAI,CAAC;EACvD;EAEA+B,WAAWA,CAAA;IACT,IAAI,CAACxB,YAAY,CAACuB,OAAO,CAACF,GAAG,IAAIA,GAAG,CAAC5B,QAAQ,GAAG,KAAK,CAAC;EACxD;EAAC,QAAAgC,CAAA,G;qBA9KUxB,aAAa;EAAA;EAAA,QAAAyB,EAAA,G;UAAbzB,aAAa;IAAA0B,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QChB1BxE,EAAA,CAAAC,cAAA,aAA4B;QAGpBD,EAAA,CAAAkB,SAAA,+BAAyE;QAC7ElB,EAAA,CAAAa,YAAA,EAAM;QAGNb,EAAA,CAAAC,cAAA,aAAyB;QAGbD,EAAA,CAAAkB,SAAA,WAAqC;QACzClB,EAAA,CAAAa,YAAA,EAAM;QACNb,EAAA,CAAAC,cAAA,aAAyB;QACjBD,EAAA,CAAAmB,MAAA,iBAAU;QAAAnB,EAAA,CAAAa,YAAA,EAAK;QACnBb,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAmB,MAAA,uFAA+E;QAAAnB,EAAA,CAAAa,YAAA,EAAI;QAMlGb,EAAA,CAAAC,cAAA,cAA2B;QAGfD,EAAA,CAAAkB,SAAA,aAAwC;QACxClB,EAAA,CAAAC,cAAA,iBAK4B;QAFrBD,EAAA,CAAAE,UAAA,2BAAAwE,uDAAAC,MAAA;UAAA,OAAAF,GAAA,CAAA5B,WAAA,GAAA8B,MAAA;QAAA,EAAyB,mBAAAC,+CAAA;UAAA,OAChBH,GAAA,CAAApB,QAAA,EAAU;QAAA,EADM;QAHhCrD,EAAA,CAAAa,YAAA,EAK4B;QAIpCb,EAAA,CAAAC,cAAA,eAA4B;QAEpBD,EAAA,CAAA6B,UAAA,KAAAgD,gCAAA,qBAMS;QACb7E,EAAA,CAAAa,YAAA,EAAM;QAENb,EAAA,CAAAC,cAAA,eAA4B;QAIhBD,EAAA,CAAAE,UAAA,mBAAA4E,gDAAA;UAAA,OAASL,GAAA,CAAAZ,SAAA,EAAW;QAAA,EAAC;QAG7B7D,EAAA,CAAAa,YAAA,EAAS;QACTb,EAAA,CAAAC,cAAA,kBAKqB;QAFbD,EAAA,CAAAE,UAAA,mBAAA6E,gDAAA;UAAA,OAASN,GAAA,CAAAV,WAAA,EAAa;QAAA,EAAC;QAG/B/D,EAAA,CAAAa,YAAA,EAAS;QAMrBb,EAAA,CAAAC,cAAA,eAAyB;QACrBD,EAAA,CAAA6B,UAAA,KAAAmD,6BAAA,kBAMM,KAAAC,6BAAA;QA2BVjF,EAAA,CAAAa,YAAA,EAAM;QAGNb,EAAA,CAAAC,cAAA,eAAiC;QACzBD,EAAA,CAAAmB,MAAA,mBAAW;QAAAnB,EAAA,CAAAa,YAAA,EAAK;QACpBb,EAAA,CAAAC,cAAA,eAA8B;QAGlBD,EAAA,CAAAkB,SAAA,aAA4B;QAChClB,EAAA,CAAAa,YAAA,EAAM;QACNb,EAAA,CAAAC,cAAA,eAA0B;QAClBD,EAAA,CAAAmB,MAAA,wBAAgB;QAAAnB,EAAA,CAAAa,YAAA,EAAK;QACzBb,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAmB,MAAA,wDAAgD;QAAAnB,EAAA,CAAAa,YAAA,EAAI;QAE3Db,EAAA,CAAAC,cAAA,eAAwB;QACpBD,EAAA,CAAAkB,SAAA,aAAiC;QACrClB,EAAA,CAAAa,YAAA,EAAM;QAGVb,EAAA,CAAAC,cAAA,eAAwD;QAEhDD,EAAA,CAAAkB,SAAA,aAAgC;QACpClB,EAAA,CAAAa,YAAA,EAAM;QACNb,EAAA,CAAAC,cAAA,eAA0B;QAClBD,EAAA,CAAAmB,MAAA,qBAAa;QAAAnB,EAAA,CAAAa,YAAA,EAAK;QACtBb,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAmB,MAAA,wDAAgD;QAAAnB,EAAA,CAAAa,YAAA,EAAI;QAE3Db,EAAA,CAAAC,cAAA,eAAwB;QACpBD,EAAA,CAAAkB,SAAA,aAAiC;QACrClB,EAAA,CAAAa,YAAA,EAAM;QAGVb,EAAA,CAAAC,cAAA,eAAqD;QAE7CD,EAAA,CAAAkB,SAAA,aAA0B;QAC9BlB,EAAA,CAAAa,YAAA,EAAM;QACNb,EAAA,CAAAC,cAAA,eAA0B;QAClBD,EAAA,CAAAmB,MAAA,iBAAS;QAAAnB,EAAA,CAAAa,YAAA,EAAK;QAClBb,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAmB,MAAA,iDAAyC;QAAAnB,EAAA,CAAAa,YAAA,EAAI;QAEpDb,EAAA,CAAAC,cAAA,eAAwB;QACpBD,EAAA,CAAAkB,SAAA,aAAiC;QACrClB,EAAA,CAAAa,YAAA,EAAM;QAGVb,EAAA,CAAAC,cAAA,eAA6D;QAErDD,EAAA,CAAAkB,SAAA,aAA8B;QAClClB,EAAA,CAAAa,YAAA,EAAM;QACNb,EAAA,CAAAC,cAAA,eAA0B;QAClBD,EAAA,CAAAmB,MAAA,qBAAa;QAAAnB,EAAA,CAAAa,YAAA,EAAK;QACtBb,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAmB,MAAA,8CAAsC;QAAAnB,EAAA,CAAAa,YAAA,EAAI;QAEjDb,EAAA,CAAAC,cAAA,eAAwB;QACpBD,EAAA,CAAAkB,SAAA,aAAiC;QACrClB,EAAA,CAAAa,YAAA,EAAM;QAMlBb,EAAA,CAAAC,cAAA,eAA6B;QAGjBD,EAAA,CAAAkB,SAAA,aAAgC;QACpClB,EAAA,CAAAa,YAAA,EAAM;QACNb,EAAA,CAAAC,cAAA,eAA6B;QACrBD,EAAA,CAAAmB,MAAA,wBAAgB;QAAAnB,EAAA,CAAAa,YAAA,EAAK;QACzBb,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAmB,MAAA,kHAA0G;QAAAnB,EAAA,CAAAa,YAAA,EAAI;QACjHb,EAAA,CAAAkB,SAAA,kBAIS;QACblB,EAAA,CAAAa,YAAA,EAAM;;;QAzKab,EAAA,CAAAoB,SAAA,GAAyB;QAAzBpB,EAAA,CAAAiB,UAAA,UAAAwD,GAAA,CAAAS,eAAA,CAAyB;QAwBjClF,EAAA,CAAAoB,SAAA,IAAyB;QAAzBpB,EAAA,CAAAiB,UAAA,YAAAwD,GAAA,CAAA5B,WAAA,CAAyB;QAQH7C,EAAA,CAAAoB,SAAA,GAAa;QAAbpB,EAAA,CAAAiB,UAAA,YAAAwD,GAAA,CAAA7B,UAAA,CAAa;QA8B5C5C,EAAA,CAAAoB,SAAA,GAA+B;QAA/BpB,EAAA,CAAAiB,UAAA,SAAAwD,GAAA,CAAAlC,YAAA,CAAA4C,MAAA,OAA+B;QAQ/BnF,EAAA,CAAAoB,SAAA,GAA6B;QAA7BpB,EAAA,CAAAiB,UAAA,SAAAwD,GAAA,CAAAlC,YAAA,CAAA4C,MAAA,KAA6B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}