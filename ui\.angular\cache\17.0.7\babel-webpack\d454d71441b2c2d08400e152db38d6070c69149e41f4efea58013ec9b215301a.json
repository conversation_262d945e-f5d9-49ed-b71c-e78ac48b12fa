{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./service/app.layout.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/inputtext\";\nimport * as i6 from \"primeng/tooltip\";\nconst _c0 = [\"menubutton\"];\nconst _c1 = [\"topbarmenubutton\"];\nconst _c2 = [\"topbarmenu\"];\nexport class AppTopBarComponent {\n  constructor(layoutService, router) {\n    this.layoutService = layoutService;\n    this.router = router;\n    this.searchQuery = '';\n  }\n  onSearch() {\n    if (this.searchQuery.trim()) {\n      // Implement search functionality\n      console.log('Searching for:', this.searchQuery);\n      // You can navigate to a search results page or filter current data\n      // this.router.navigate(['/app/search'], { queryParams: { q: this.searchQuery } });\n    }\n  }\n\n  navigateToHelp() {\n    this.router.navigate(['/app/help']);\n  }\n  navigateToProfile() {\n    this.router.navigate(['/app/profile']);\n  }\n  toggleTheme() {\n    const currentTheme = this.layoutService.config().colorScheme;\n    const newTheme = currentTheme === 'light' ? 'dark' : 'light';\n    this.layoutService.config.update(config => ({\n      ...config,\n      colorScheme: newTheme,\n      theme: newTheme === 'light' ? 'lara-light-indigo' : 'lara-dark-indigo'\n    }));\n  }\n  static #_ = this.ɵfac = function AppTopBarComponent_Factory(t) {\n    return new (t || AppTopBarComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppTopBarComponent,\n    selectors: [[\"app-topbar\"]],\n    viewQuery: function AppTopBarComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(_c2, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menuButton = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.topbarMenuButton = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menu = _t.first);\n      }\n    },\n    decls: 28,\n    vars: 3,\n    consts: [[1, \"layout-topbar\"], [1, \"p-link\", \"layout-menu-button\", \"layout-topbar-button\", 3, \"click\"], [\"menubutton\", \"\"], [1, \"pi\", \"pi-bars\"], [1, \"layout-topbar-logo-section\"], [\"routerLink\", \"/app/index\", 1, \"layout-topbar-logo\"], [\"alt\", \"SolarKapital\", 3, \"src\"], [1, \"logo-text\"], [1, \"layout-topbar-search\"], [1, \"p-input-icon-left\"], [1, \"pi\", \"pi-search\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"Search stations, devices...\", 1, \"search-input\", 3, \"ngModel\", \"ngModelChange\", \"keyup.enter\"], [1, \"layout-topbar-actions\"], [\"pTooltip\", \"Notifications\", \"tooltipPosition\", \"bottom\", 1, \"p-link\", \"layout-topbar-button\", \"notification-button\"], [1, \"pi\", \"pi-bell\"], [1, \"notification-badge\"], [\"pTooltip\", \"Help & FAQ\", \"tooltipPosition\", \"bottom\", 1, \"p-link\", \"layout-topbar-button\", \"help-button\", 3, \"click\"], [1, \"pi\", \"pi-question-circle\"], [\"pTooltip\", \"Toggle Theme\", \"tooltipPosition\", \"bottom\", 1, \"p-link\", \"layout-topbar-button\", \"theme-toggle\", 3, \"click\"], [1, \"pi\", 3, \"ngClass\"], [\"pTooltip\", \"User Profile\", \"tooltipPosition\", \"bottom\", 1, \"p-link\", \"layout-topbar-button\", \"user-profile-btn\", 3, \"click\"], [1, \"pi\", \"pi-user\"], [\"pTooltip\", \"Profile Menu\", \"tooltipPosition\", \"bottom\", 1, \"p-link\", \"layout-topbar-menu-button\", \"layout-topbar-button\", \"profile-button\", 3, \"click\"], [\"topbarmenubutton\", \"\"], [1, \"profile-avatar\"]],\n    template: function AppTopBarComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"button\", 1, 2);\n        i0.ɵɵlistener(\"click\", function AppTopBarComponent_Template_button_click_1_listener() {\n          return ctx.layoutService.onMenuToggle();\n        });\n        i0.ɵɵelement(3, \"i\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 4)(5, \"a\", 5);\n        i0.ɵɵelement(6, \"img\", 6);\n        i0.ɵɵelementStart(7, \"span\", 7);\n        i0.ɵɵtext(8, \"SolarKapital\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(9, \"div\", 8)(10, \"div\", 9);\n        i0.ɵɵelement(11, \"i\", 10);\n        i0.ɵɵelementStart(12, \"input\", 11);\n        i0.ɵɵlistener(\"ngModelChange\", function AppTopBarComponent_Template_input_ngModelChange_12_listener($event) {\n          return ctx.searchQuery = $event;\n        })(\"keyup.enter\", function AppTopBarComponent_Template_input_keyup_enter_12_listener() {\n          return ctx.onSearch();\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(13, \"div\", 12)(14, \"button\", 13);\n        i0.ɵɵelement(15, \"i\", 14);\n        i0.ɵɵelementStart(16, \"span\", 15);\n        i0.ɵɵtext(17, \"3\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(18, \"button\", 16);\n        i0.ɵɵlistener(\"click\", function AppTopBarComponent_Template_button_click_18_listener() {\n          return ctx.navigateToHelp();\n        });\n        i0.ɵɵelement(19, \"i\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"button\", 18);\n        i0.ɵɵlistener(\"click\", function AppTopBarComponent_Template_button_click_20_listener() {\n          return ctx.toggleTheme();\n        });\n        i0.ɵɵelement(21, \"i\", 19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"button\", 20);\n        i0.ɵɵlistener(\"click\", function AppTopBarComponent_Template_button_click_22_listener() {\n          return ctx.navigateToProfile();\n        });\n        i0.ɵɵelement(23, \"i\", 21);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(24, \"button\", 22, 23);\n        i0.ɵɵlistener(\"click\", function AppTopBarComponent_Template_button_click_24_listener() {\n          return ctx.layoutService.showProfileSidebar();\n        });\n        i0.ɵɵelementStart(26, \"div\", 24);\n        i0.ɵɵelement(27, \"i\", 21);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(6);\n        i0.ɵɵpropertyInterpolate1(\"src\", \"assets/layout/images/\", ctx.layoutService.config().colorScheme === \"light\" ? \"logo-dark\" : \"logo-white\", \".png\", i0.ɵɵsanitizeUrl);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngClass\", ctx.layoutService.config().colorScheme === \"light\" ? \"pi-moon\" : \"pi-sun\");\n      }\n    },\n    dependencies: [i3.NgClass, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, i5.InputText, i6.Tooltip, i2.RouterLink],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["AppTopBarComponent", "constructor", "layoutService", "router", "searchQuery", "onSearch", "trim", "console", "log", "navigateToHelp", "navigate", "navigateToProfile", "toggleTheme", "currentTheme", "config", "colorScheme", "newTheme", "update", "theme", "_", "i0", "ɵɵdirectiveInject", "i1", "LayoutService", "i2", "Router", "_2", "selectors", "viewQuery", "AppTopBarComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "AppTopBarComponent_Template_button_click_1_listener", "onMenuToggle", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "AppTopBarComponent_Template_input_ngModelChange_12_listener", "$event", "AppTopBarComponent_Template_input_keyup_enter_12_listener", "AppTopBarComponent_Template_button_click_18_listener", "AppTopBarComponent_Template_button_click_20_listener", "AppTopBarComponent_Template_button_click_22_listener", "AppTopBarComponent_Template_button_click_24_listener", "showProfileSidebar", "ɵɵadvance", "ɵɵpropertyInterpolate1", "ɵɵsanitizeUrl", "ɵɵproperty"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\layout\\app.topbar.component.ts", "D:\\Greg\\solarkapital\\ui\\src\\app\\layout\\app.topbar.component.html"], "sourcesContent": ["import { Component, ElementRef, ViewChild } from '@angular/core';\nimport { MenuItem } from 'primeng/api';\nimport { LayoutService } from \"./service/app.layout.service\";\nimport { Router } from '@angular/router';\n\n@Component({\n    selector: 'app-topbar',\n    templateUrl: './app.topbar.component.html'\n})\nexport class AppTopBarComponent {\n\n    items!: MenuItem[];\n    searchQuery: string = '';\n\n    @ViewChild('menubutton') menuButton!: ElementRef;\n    @ViewChild('topbarmenubutton') topbarMenuButton!: ElementRef;\n    @ViewChild('topbarmenu') menu!: ElementRef;\n\n    constructor(\n        public layoutService: LayoutService,\n        private router: Router\n    ) { }\n\n    onSearch() {\n        if (this.searchQuery.trim()) {\n            // Implement search functionality\n            console.log('Searching for:', this.searchQuery);\n            // You can navigate to a search results page or filter current data\n            // this.router.navigate(['/app/search'], { queryParams: { q: this.searchQuery } });\n        }\n    }\n\n    navigateToHelp() {\n        this.router.navigate(['/app/help']);\n    }\n\n    navigateToProfile() {\n        this.router.navigate(['/app/profile']);\n    }\n\n    toggleTheme() {\n        const currentTheme = this.layoutService.config().colorScheme;\n        const newTheme = currentTheme === 'light' ? 'dark' : 'light';\n\n        this.layoutService.config.update(config => ({\n            ...config,\n            colorScheme: newTheme,\n            theme: newTheme === 'light' ? 'lara-light-indigo' : 'lara-dark-indigo'\n        }));\n    }\n\n    // toggleTheme() {\n    //     const currentTheme = this.layoutService.config();\n    //     const newColorScheme = currentTheme.colorScheme === 'light' ? 'dark' : 'light';\n    //     const newTheme = currentTheme.colorScheme === 'light' ? 'lara-dark-indigo' : 'lara-light-indigo';\n\n    //     this.layoutService.config.set({\n    //         ...currentTheme,\n    //         colorScheme: newColorScheme,\n    //         theme: newTheme\n    //     });\n    // }\n}\n", "<div class=\"layout-topbar\">\n    <!-- Mobile Menu Button -->\n    <button #menubutton class=\"p-link layout-menu-button layout-topbar-button\" (click)=\"layoutService.onMenuToggle()\">\n        <i class=\"pi pi-bars\"></i>\n    </button>\n\n    <!-- Logo Section -->\n    <div class=\"layout-topbar-logo-section\">\n        <a class=\"layout-topbar-logo\" routerLink=\"/app/index\">\n            <img src=\"assets/layout/images/{{layoutService.config().colorScheme === 'light' ? 'logo-dark' : 'logo-white'}}.png\" alt=\"SolarKapital\">\n            <span class=\"logo-text\">SolarKapital</span>\n        </a>\n    </div>\n\n    <!-- Search Bar (Desktop) -->\n    <div class=\"layout-topbar-search\">\n        <div class=\"p-input-icon-left\">\n            <i class=\"pi pi-search\"></i>\n            <input type=\"text\"\n                   pInputText\n                   placeholder=\"Search stations, devices...\"\n                   class=\"search-input\"\n                   [(ngModel)]=\"searchQuery\"\n                   (keyup.enter)=\"onSearch()\">\n        </div>\n    </div>\n\n    <!-- Quick Actions -->\n    <div class=\"layout-topbar-actions\">\n        <!-- Notifications -->\n        <button class=\"p-link layout-topbar-button notification-button\"\n                pTooltip=\"Notifications\"\n                tooltipPosition=\"bottom\">\n            <i class=\"pi pi-bell\"></i>\n            <span class=\"notification-badge\">3</span>\n        </button>\n\n        <!-- Help -->\n        <button class=\"p-link layout-topbar-button help-button\"\n                (click)=\"navigateToHelp()\"\n                pTooltip=\"Help & FAQ\"\n                tooltipPosition=\"bottom\">\n            <i class=\"pi pi-question-circle\"></i>\n        </button>\n\n        <!-- Theme Toggle -->\n        <button class=\"p-link layout-topbar-button theme-toggle\"\n                (click)=\"toggleTheme()\"\n                pTooltip=\"Toggle Theme\"\n                tooltipPosition=\"bottom\">\n            <i class=\"pi\" [ngClass]=\"layoutService.config().colorScheme === 'light' ? 'pi-moon' : 'pi-sun'\"></i>\n        </button>\n\n        <!-- User Profile Button -->\n        <button class=\"p-link layout-topbar-button user-profile-btn\"\n                (click)=\"navigateToProfile()\"\n                pTooltip=\"User Profile\"\n                tooltipPosition=\"bottom\">\n            <i class=\"pi pi-user\"></i>\n        </button>\n\n        <!-- Profile Menu Button -->\n        <button #topbarmenubutton\n                class=\"p-link layout-topbar-menu-button layout-topbar-button profile-button\"\n                (click)=\"layoutService.showProfileSidebar()\"\n                pTooltip=\"Profile Menu\"\n                tooltipPosition=\"bottom\">\n            <div class=\"profile-avatar\">\n                <i class=\"pi pi-user\"></i>\n            </div>\n        </button>\n    </div>\n\n    <!-- Profile Dropdown Menu -->\n    <!-- <div #topbarmenu class=\"layout-topbar-menu\" [ngClass]=\"{'layout-topbar-menu-mobile-active': layoutService.state.profileSidebarVisible}\">\n        <div class=\"topbar-menu-header\">\n            <div class=\"user-info\">\n                <div class=\"user-avatar\">\n                    <i class=\"pi pi-user\"></i>\n                </div>\n                <div class=\"user-details\">\n                    <span class=\"user-name\">John Doe</span>\n                    <span class=\"user-email\">john&#64;example.com</span>\n                </div>\n            </div>\n        </div>\n\n        <div class=\"topbar-menu-items\">\n            <button class=\"p-link layout-topbar-button menu-item\">\n                <i class=\"pi pi-calendar\"></i>\n                <span>Calendar</span>\n            </button>\n            <button class=\"p-link layout-topbar-button menu-item\" [routerLink]=\"'/app/profile'\">\n                <i class=\"pi pi-user\"></i>\n                <span>Profile</span>\n            </button>\n            <button class=\"p-link layout-topbar-button menu-item\" [routerLink]=\"'/app/providers'\">\n                <i class=\"pi pi-cog\"></i>\n                <span>Settings</span>\n            </button>\n            <div class=\"menu-divider\"></div>\n            <button class=\"p-link layout-topbar-button menu-item logout-item\">\n                <i class=\"pi pi-power-off\"></i>\n                <span>Logout</span>\n            </button>\n        </div>\n    </div> -->\n</div>"], "mappings": ";;;;;;;;;;AASA,OAAM,MAAOA,kBAAkB;EAS3BC,YACWC,aAA4B,EAC3BC,MAAc;IADf,KAAAD,aAAa,GAAbA,aAAa;IACZ,KAAAC,MAAM,GAANA,MAAM;IARlB,KAAAC,WAAW,GAAW,EAAE;EASpB;EAEJC,QAAQA,CAAA;IACJ,IAAI,IAAI,CAACD,WAAW,CAACE,IAAI,EAAE,EAAE;MACzB;MACAC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACJ,WAAW,CAAC;MAC/C;MACA;;EAER;;EAEAK,cAAcA,CAAA;IACV,IAAI,CAACN,MAAM,CAACO,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACvC;EAEAC,iBAAiBA,CAAA;IACb,IAAI,CAACR,MAAM,CAACO,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;EAC1C;EAEAE,WAAWA,CAAA;IACP,MAAMC,YAAY,GAAG,IAAI,CAACX,aAAa,CAACY,MAAM,EAAE,CAACC,WAAW;IAC5D,MAAMC,QAAQ,GAAGH,YAAY,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;IAE5D,IAAI,CAACX,aAAa,CAACY,MAAM,CAACG,MAAM,CAACH,MAAM,KAAK;MACxC,GAAGA,MAAM;MACTC,WAAW,EAAEC,QAAQ;MACrBE,KAAK,EAAEF,QAAQ,KAAK,OAAO,GAAG,mBAAmB,GAAG;KACvD,CAAC,CAAC;EACP;EAAC,QAAAG,CAAA,G;qBAxCQnB,kBAAkB,EAAAoB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlB1B,kBAAkB;IAAA2B,SAAA;IAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;QCT/BV,EAAA,CAAAY,cAAA,aAA2B;QAEoDZ,EAAA,CAAAa,UAAA,mBAAAC,oDAAA;UAAA,OAASH,GAAA,CAAA7B,aAAA,CAAAiC,YAAA,EAA4B;QAAA,EAAC;QAC7Gf,EAAA,CAAAgB,SAAA,WAA0B;QAC9BhB,EAAA,CAAAiB,YAAA,EAAS;QAGTjB,EAAA,CAAAY,cAAA,aAAwC;QAEhCZ,EAAA,CAAAgB,SAAA,aAAuI;QACvIhB,EAAA,CAAAY,cAAA,cAAwB;QAAAZ,EAAA,CAAAkB,MAAA,mBAAY;QAAAlB,EAAA,CAAAiB,YAAA,EAAO;QAKnDjB,EAAA,CAAAY,cAAA,aAAkC;QAE1BZ,EAAA,CAAAgB,SAAA,aAA4B;QAC5BhB,EAAA,CAAAY,cAAA,iBAKkC;QAD3BZ,EAAA,CAAAa,UAAA,2BAAAM,4DAAAC,MAAA;UAAA,OAAAT,GAAA,CAAA3B,WAAA,GAAAoC,MAAA;QAAA,EAAyB,yBAAAC,0DAAA;UAAA,OACVV,GAAA,CAAA1B,QAAA,EAAU;QAAA,EADA;QAJhCe,EAAA,CAAAiB,YAAA,EAKkC;QAK1CjB,EAAA,CAAAY,cAAA,eAAmC;QAK3BZ,EAAA,CAAAgB,SAAA,aAA0B;QAC1BhB,EAAA,CAAAY,cAAA,gBAAiC;QAAAZ,EAAA,CAAAkB,MAAA,SAAC;QAAAlB,EAAA,CAAAiB,YAAA,EAAO;QAI7CjB,EAAA,CAAAY,cAAA,kBAGiC;QAFzBZ,EAAA,CAAAa,UAAA,mBAAAS,qDAAA;UAAA,OAASX,GAAA,CAAAtB,cAAA,EAAgB;QAAA,EAAC;QAG9BW,EAAA,CAAAgB,SAAA,aAAqC;QACzChB,EAAA,CAAAiB,YAAA,EAAS;QAGTjB,EAAA,CAAAY,cAAA,kBAGiC;QAFzBZ,EAAA,CAAAa,UAAA,mBAAAU,qDAAA;UAAA,OAASZ,GAAA,CAAAnB,WAAA,EAAa;QAAA,EAAC;QAG3BQ,EAAA,CAAAgB,SAAA,aAAoG;QACxGhB,EAAA,CAAAiB,YAAA,EAAS;QAGTjB,EAAA,CAAAY,cAAA,kBAGiC;QAFzBZ,EAAA,CAAAa,UAAA,mBAAAW,qDAAA;UAAA,OAASb,GAAA,CAAApB,iBAAA,EAAmB;QAAA,EAAC;QAGjCS,EAAA,CAAAgB,SAAA,aAA0B;QAC9BhB,EAAA,CAAAiB,YAAA,EAAS;QAGTjB,EAAA,CAAAY,cAAA,sBAIiC;QAFzBZ,EAAA,CAAAa,UAAA,mBAAAY,qDAAA;UAAA,OAASd,GAAA,CAAA7B,aAAA,CAAA4C,kBAAA,EAAkC;QAAA,EAAC;QAGhD1B,EAAA,CAAAY,cAAA,eAA4B;QACxBZ,EAAA,CAAAgB,SAAA,aAA0B;QAC9BhB,EAAA,CAAAiB,YAAA,EAAM;;;QA5DDjB,EAAA,CAAA2B,SAAA,GAA8G;QAA9G3B,EAAA,CAAA4B,sBAAA,iCAAAjB,GAAA,CAAA7B,aAAA,CAAAY,MAAA,GAAAC,WAAA,mDAAAK,EAAA,CAAA6B,aAAA,CAA8G;QAa5G7B,EAAA,CAAA2B,SAAA,GAAyB;QAAzB3B,EAAA,CAAA8B,UAAA,YAAAnB,GAAA,CAAA3B,WAAA,CAAyB;QA4BlBgB,EAAA,CAAA2B,SAAA,GAAiF;QAAjF3B,EAAA,CAAA8B,UAAA,YAAAnB,GAAA,CAAA7B,aAAA,CAAAY,MAAA,GAAAC,WAAA,oCAAiF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}