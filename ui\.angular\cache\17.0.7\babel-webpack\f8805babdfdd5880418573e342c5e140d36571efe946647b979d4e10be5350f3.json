{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../service/stations.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../service/cache.service\";\nimport * as i4 from \"../../service/weather.service\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"primeng/chart\";\nimport * as i9 from \"primeng/tooltip\";\nimport * as i10 from \"primeng/table\";\nimport * as i11 from \"primeng/button\";\nimport * as i12 from \"primeng/inputtext\";\nimport * as i13 from \"primeng/dropdown\";\nimport * as i14 from \"primeng/calendar\";\nimport * as i15 from \"@fortawesome/angular-fontawesome\";\nimport * as i16 from \"primeng/card\";\nimport * as i17 from \"primeng/tag\";\nimport * as i18 from \"primeng/divider\";\nimport * as i19 from \"primeng/toast\";\nimport * as i20 from \"../../../shared/components/modern-breadcrumb/modern-breadcrumb.component\";\nfunction ViewStationComponent_div_0_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11);\n    i0.ɵɵelement(2, \"fa-icon\", 12);\n    i0.ɵɵelementStart(3, \"h3\", 13);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedStation.name);\n  }\n}\nfunction ViewStationComponent_div_0_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15);\n    i0.ɵɵelement(2, \"fa-icon\", 16);\n    i0.ɵɵelementStart(3, \"span\", 17);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(5, \"p-divider\");\n    i0.ɵɵelementStart(6, \"div\", 18)(7, \"div\", 19)(8, \"div\", 20)(9, \"div\", 21);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 22);\n    i0.ɵɵtext(12, \"Inverters\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 19)(14, \"div\", 20)(15, \"div\", 23);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 22);\n    i0.ɵɵtext(18, \"Total Devices\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelement(19, \"p-divider\");\n    i0.ɵɵelementStart(20, \"div\", 24)(21, \"h4\", 25);\n    i0.ɵɵtext(22, \"Monitoring Views\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"p-button\", 26);\n    i0.ɵɵlistener(\"click\", function ViewStationComponent_div_0_ng_template_6_Template_p_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      ctx_r7.showGeneral = true;\n      ctx_r7.showInvert = false;\n      return i0.ɵɵresetView(ctx_r7.showString = false);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"p-button\", 27);\n    i0.ɵɵlistener(\"click\", function ViewStationComponent_div_0_ng_template_6_Template_p_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.showInvertMonitoring());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"p-button\", 28);\n    i0.ɵɵlistener(\"click\", function ViewStationComponent_div_0_ng_template_6_Template_p_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.showStringMonitoring());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedStation.address);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.inverters.length);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.devices.length);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"severity\", ctx_r2.showGeneral ? \"primary\" : \"secondary\")(\"text\", !ctx_r2.showGeneral);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"severity\", ctx_r2.showInvert ? \"primary\" : \"secondary\")(\"text\", !ctx_r2.showInvert);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"severity\", ctx_r2.showString ? \"primary\" : \"secondary\")(\"text\", !ctx_r2.showString);\n  }\n}\nfunction ViewStationComponent_div_0_p_card_7_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 11);\n    i0.ɵɵelement(2, \"fa-icon\", 30);\n    i0.ɵɵelementStart(3, \"h4\", 31);\n    i0.ɵɵtext(4, \"Performance Summary\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ViewStationComponent_div_0_p_card_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 32)(2, \"div\", 33)(3, \"div\", 11);\n    i0.ɵɵelement(4, \"fa-icon\", 34);\n    i0.ɵɵelementStart(5, \"span\", 35);\n    i0.ɵɵtext(6, \"Day Power\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"span\", 36);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 32)(10, \"div\", 33)(11, \"div\", 11);\n    i0.ɵɵelement(12, \"fa-icon\", 37);\n    i0.ɵɵelementStart(13, \"span\", 35);\n    i0.ɵɵtext(14, \"Total Power\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"span\", 36);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 32)(18, \"div\", 33)(19, \"div\", 11);\n    i0.ɵɵelement(20, \"fa-icon\", 38);\n    i0.ɵɵelementStart(21, \"span\", 35);\n    i0.ɵɵtext(22, \"Day Income\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"span\", 36);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 32)(26, \"div\", 33)(27, \"div\", 11);\n    i0.ɵɵelement(28, \"fa-icon\", 39);\n    i0.ɵɵelementStart(29, \"span\", 35);\n    i0.ɵɵtext(30, \"Month Power\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"span\", 36);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 32)(34, \"div\", 33)(35, \"div\", 11);\n    i0.ɵɵelement(36, \"fa-icon\", 40);\n    i0.ɵɵelementStart(37, \"span\", 35);\n    i0.ɵɵtext(38, \"Day Grid Energy\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"span\", 36);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(41, \"div\", 32)(42, \"div\", 33)(43, \"div\", 11);\n    i0.ɵɵelement(44, \"fa-icon\", 41);\n    i0.ɵɵelementStart(45, \"span\", 35);\n    i0.ɵɵtext(46, \"Day Use Energy\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"span\", 36);\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(49, \"div\", 2)(50, \"div\", 33)(51, \"div\", 11);\n    i0.ɵɵelement(52, \"fa-icon\", 42);\n    i0.ɵɵelementStart(53, \"span\", 35);\n    i0.ɵɵtext(54, \"Total Income\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"span\", 43);\n    i0.ɵɵtext(56);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r12.sumData.dayPower, \" kW\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r12.sumData.totalPower, \" kW\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\\u20AC\", ctx_r12.sumData.dayIncome, \"\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r12.sumData.monthPower, \" kW\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r12.sumData.dayOnGridEnergy, \" kWh\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r12.sumData.dayUseEnergy, \" kWh\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\\u20AC\", ctx_r12.sumData.totalIncome, \"\");\n  }\n}\nfunction ViewStationComponent_div_0_p_card_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-card\");\n    i0.ɵɵtemplate(1, ViewStationComponent_div_0_p_card_7_ng_template_1_Template, 5, 0, \"ng-template\", 6)(2, ViewStationComponent_div_0_p_card_7_ng_template_2_Template, 57, 7, \"ng-template\", 7);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewStationComponent_div_0_div_8_p_card_1_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 11);\n    i0.ɵɵelement(2, \"fa-icon\", 47);\n    i0.ɵɵelementStart(3, \"h4\", 48);\n    i0.ɵɵtext(4, \"Weather Forecast\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ViewStationComponent_div_0_div_8_p_card_1_ng_template_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 51)(2, \"h5\", 25);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"img\", 52);\n    i0.ɵɵelementStart(5, \"p\", 53);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 54);\n    i0.ɵɵelement(8, \"fa-icon\", 55);\n    i0.ɵɵelementStart(9, \"span\", 56);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"fa-icon\", 57);\n    i0.ɵɵelementStart(12, \"span\", 56);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 58);\n    i0.ɵɵelement(15, \"fa-icon\", 59);\n    i0.ɵɵelementStart(16, \"span\", 22);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const day_r19 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(day_r19.day);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", day_r19.icon, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(day_r19.description);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", day_r19.minTemperatureCelsius, \"\\u00B0\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", day_r19.maxTemperatureCelsius, \"\\u00B0\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", day_r19.windKph, \"km/h\");\n  }\n}\nfunction ViewStationComponent_div_0_div_8_p_card_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, ViewStationComponent_div_0_div_8_p_card_1_ng_template_2_div_1_Template, 18, 6, \"div\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r17.weatherData.weatherInfo);\n  }\n}\nfunction ViewStationComponent_div_0_div_8_p_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-card\", 5);\n    i0.ɵɵtemplate(1, ViewStationComponent_div_0_div_8_p_card_1_ng_template_1_Template, 5, 0, \"ng-template\", 6)(2, ViewStationComponent_div_0_div_8_p_card_1_ng_template_2_Template, 2, 1, \"ng-template\", 7);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewStationComponent_div_0_div_8_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 33)(2, \"div\", 11);\n    i0.ɵɵelement(3, \"fa-icon\", 60);\n    i0.ɵɵelementStart(4, \"h4\", 31);\n    i0.ɵɵtext(5, \"Energy Performance\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"p-tag\", 61);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ViewStationComponent_div_0_div_8_ng_template_5_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"label\", 64);\n    i0.ɵɵtext(2, \"Select Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-calendar\", 70);\n    i0.ɵɵlistener(\"ngModelChange\", function ViewStationComponent_div_0_div_8_ng_template_5_div_5_Template_p_calendar_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r23.selectedDate = $event);\n    })(\"onSelect\", function ViewStationComponent_div_0_div_8_ng_template_5_div_5_Template_p_calendar_onSelect_3_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r25.onDateChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r20.selectedDate)(\"showIcon\", true);\n  }\n}\nfunction ViewStationComponent_div_0_div_8_ng_template_5_p_chart_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-chart\", 71);\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"data\", ctx_r21.lineData)(\"options\", ctx_r21.lineOptions)(\"height\", 400);\n  }\n}\nfunction ViewStationComponent_div_0_div_8_ng_template_5_p_chart_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-chart\", 72);\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"data\", ctx_r22.barData)(\"options\", ctx_r22.lineOptions)(\"height\", 400);\n  }\n}\nfunction ViewStationComponent_div_0_div_8_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"div\", 63)(2, \"label\", 64);\n    i0.ɵɵtext(3, \"Time Frame\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p-dropdown\", 65);\n    i0.ɵɵlistener(\"ngModelChange\", function ViewStationComponent_div_0_div_8_ng_template_5_Template_p_dropdown_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r26.selectedTimeFrame = $event);\n    })(\"onChange\", function ViewStationComponent_div_0_div_8_ng_template_5_Template_p_dropdown_onChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r28 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r28.onTimeFrameChange($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, ViewStationComponent_div_0_div_8_ng_template_5_div_5_Template, 4, 2, \"div\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 67);\n    i0.ɵɵtemplate(7, ViewStationComponent_div_0_div_8_ng_template_5_p_chart_7_Template, 1, 3, \"p-chart\", 68)(8, ViewStationComponent_div_0_div_8_ng_template_5_p_chart_8_Template, 1, 3, \"p-chart\", 69);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"options\", ctx_r15.timeFrames)(\"ngModel\", ctx_r15.selectedTimeFrame)(\"showClear\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.selectedTimeFrame === \"day\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.searchType == null);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.searchType != null);\n  }\n}\nfunction ViewStationComponent_div_0_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, ViewStationComponent_div_0_div_8_p_card_1_Template, 3, 0, \"p-card\", 45);\n    i0.ɵɵelement(2, \"br\");\n    i0.ɵɵelementStart(3, \"p-card\");\n    i0.ɵɵtemplate(4, ViewStationComponent_div_0_div_8_ng_template_4_Template, 7, 0, \"ng-template\", 6)(5, ViewStationComponent_div_0_div_8_ng_template_5_Template, 9, 6, \"ng-template\", 7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.weatherData);\n  }\n}\nfunction ViewStationComponent_div_0_div_9_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"div\", 33)(2, \"div\", 11);\n    i0.ɵɵelement(3, \"fa-icon\", 74);\n    i0.ɵɵelementStart(4, \"h4\", 75);\n    i0.ɵɵtext(5, \"Inverter Power Monitoring\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"p-tag\", 76);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ViewStationComponent_div_0_div_9_ng_template_3_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"label\", 64);\n    i0.ɵɵtext(2, \"Select Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-calendar\", 70);\n    i0.ɵɵlistener(\"ngModelChange\", function ViewStationComponent_div_0_div_9_ng_template_3_div_5_Template_p_calendar_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r38);\n      const ctx_r37 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r37.selectedDate = $event);\n    })(\"onSelect\", function ViewStationComponent_div_0_div_9_ng_template_3_div_5_Template_p_calendar_onSelect_3_listener($event) {\n      i0.ɵɵrestoreView(_r38);\n      const ctx_r39 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r39.onDateChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r31.selectedDate)(\"showIcon\", true);\n  }\n}\nfunction ViewStationComponent_div_0_div_9_ng_template_3_p_chart_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-chart\", 71);\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"data\", ctx_r32.lineData)(\"options\", ctx_r32.lineOptions)(\"height\", 300);\n  }\n}\nfunction ViewStationComponent_div_0_div_9_ng_template_3_p_chart_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-chart\", 72);\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"data\", ctx_r33.barData)(\"options\", ctx_r33.lineOptions)(\"height\", 300);\n  }\n}\nfunction ViewStationComponent_div_0_div_9_ng_template_3_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 91);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 92)(4, \"div\", 93);\n    i0.ɵɵelement(5, \"fa-icon\", 94);\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7, \"Inverter Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"p-sortIcon\", 95);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"th\", 96)(10, \"div\", 93);\n    i0.ɵɵelement(11, \"fa-icon\", 97);\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13, \"Nominal Output\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"p-sortIcon\", 98);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"th\", 99)(16, \"div\", 93);\n    i0.ɵɵelement(17, \"fa-icon\", 100);\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19, \"Capacity\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"p-sortIcon\", 101);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"th\", 102)(22, \"div\", 93);\n    i0.ɵɵelement(23, \"fa-icon\", 103);\n    i0.ɵɵelementStart(24, \"span\");\n    i0.ɵɵtext(25, \"AC Power\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"p-sortIcon\", 104);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"th\", 105)(28, \"div\", 93);\n    i0.ɵɵelement(29, \"fa-icon\", 106);\n    i0.ɵɵelementStart(30, \"span\");\n    i0.ɵɵtext(31, \"Total Energy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(32, \"p-sortIcon\", 107);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"th\", 108)(34, \"div\", 93);\n    i0.ɵɵelement(35, \"fa-icon\", 109);\n    i0.ɵɵelementStart(36, \"span\");\n    i0.ɵɵtext(37, \"Performance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(38, \"p-sortIcon\", 110);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ViewStationComponent_div_0_div_9_ng_template_3_ng_template_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 111);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\")(4, \"div\", 11);\n    i0.ɵɵelement(5, \"fa-icon\", 112);\n    i0.ɵɵelementStart(6, \"span\", 113);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵelement(9, \"p-tag\", 114);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\")(11, \"span\", 113);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"td\")(14, \"span\", 115);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"td\")(17, \"span\", 113);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"td\")(20, \"div\", 11);\n    i0.ɵɵelement(21, \"p-tag\", 116);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const invert_r40 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", invert_r40);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(invert_r40.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", invert_r40.nominaloutput + \" kW\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", invert_r40.capacity, \" kW\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", invert_r40.acpower, \" kW\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", invert_r40.totalenergy, \" kWh\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", invert_r40.performance + \"%\")(\"severity\", invert_r40.performance > 80 ? \"success\" : invert_r40.performance > 60 ? \"warning\" : \"danger\");\n  }\n}\nconst _c0 = () => [\"name\", \"nominaloutput\", \"capacity\", \"acpower\", \"totalenergy\", \"performance\"];\nconst _c1 = () => [10, 20, 30];\nfunction ViewStationComponent_div_0_div_9_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"div\", 63)(2, \"label\", 64);\n    i0.ɵɵtext(3, \"Time Frame\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p-dropdown\", 65);\n    i0.ɵɵlistener(\"ngModelChange\", function ViewStationComponent_div_0_div_9_ng_template_3_Template_p_dropdown_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r41 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r41.selectedTimeFrame = $event);\n    })(\"onChange\", function ViewStationComponent_div_0_div_9_ng_template_3_Template_p_dropdown_onChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r43 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r43.onTimeFrameChange($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, ViewStationComponent_div_0_div_9_ng_template_3_div_5_Template, 4, 2, \"div\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 77);\n    i0.ɵɵtemplate(7, ViewStationComponent_div_0_div_9_ng_template_3_p_chart_7_Template, 1, 3, \"p-chart\", 68)(8, ViewStationComponent_div_0_div_9_ng_template_3_p_chart_8_Template, 1, 3, \"p-chart\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 78);\n    i0.ɵɵelement(10, \"p-toast\");\n    i0.ɵɵelementStart(11, \"div\", 79)(12, \"div\", 80)(13, \"div\", 11);\n    i0.ɵɵelement(14, \"fa-icon\", 81);\n    i0.ɵɵelementStart(15, \"h5\", 82);\n    i0.ɵɵtext(16, \"Inverter Performance Data\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 83)(18, \"span\", 84);\n    i0.ɵɵelement(19, \"i\", 85);\n    i0.ɵɵelementStart(20, \"input\", 86);\n    i0.ɵɵlistener(\"input\", function ViewStationComponent_div_0_div_9_ng_template_3_Template_input_input_20_listener($event) {\n      i0.ɵɵrestoreView(_r42);\n      const _r34 = i0.ɵɵreference(23);\n      const ctx_r44 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r44.onGlobalFilter(_r34, $event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"p-button\", 87);\n    i0.ɵɵlistener(\"click\", function ViewStationComponent_div_0_div_9_ng_template_3_Template_p_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r42);\n      const _r34 = i0.ɵɵreference(23);\n      return i0.ɵɵresetView(_r34.exportCSV());\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(22, \"p-table\", 88, 89);\n    i0.ɵɵlistener(\"selectionChange\", function ViewStationComponent_div_0_div_9_ng_template_3_Template_p_table_selectionChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r46 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r46.selectedProducts = $event);\n    });\n    i0.ɵɵtemplate(24, ViewStationComponent_div_0_div_9_ng_template_3_ng_template_24_Template, 39, 0, \"ng-template\", 6)(25, ViewStationComponent_div_0_div_9_ng_template_3_ng_template_25_Template, 22, 8, \"ng-template\", 90);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"options\", ctx_r30.timeFrames)(\"ngModel\", ctx_r30.selectedTimeFrame)(\"showClear\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r30.selectedTimeFrame === \"day\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r30.searchType == null);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r30.searchType != null);\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"value\", ctx_r30.invertpower)(\"columns\", ctx_r30.cols)(\"rows\", 10)(\"globalFilterFields\", i0.ɵɵpureFunction0(15, _c0))(\"paginator\", true)(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(16, _c1))(\"showCurrentPageReport\", true)(\"selection\", ctx_r30.selectedProducts)(\"rowHover\", true);\n  }\n}\nfunction ViewStationComponent_div_0_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"p-card\");\n    i0.ɵɵtemplate(2, ViewStationComponent_div_0_div_9_ng_template_2_Template, 7, 0, \"ng-template\", 6)(3, ViewStationComponent_div_0_div_9_ng_template_3_Template, 26, 17, \"ng-template\", 7);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ViewStationComponent_div_0_div_10_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117)(1, \"div\", 33)(2, \"div\", 11);\n    i0.ɵɵelement(3, \"fa-icon\", 118);\n    i0.ɵɵelementStart(4, \"h4\", 119);\n    i0.ɵɵtext(5, \"String Current Monitoring\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"p-tag\", 120);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ViewStationComponent_div_0_div_10_ng_template_3_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 91);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 92)(4, \"div\", 93);\n    i0.ɵɵelement(5, \"fa-icon\", 124);\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7, \"String Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"p-sortIcon\", 95);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"th\", 96)(10, \"div\", 93);\n    i0.ɵɵelement(11, \"fa-icon\", 97);\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13, \"Current (A)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"p-sortIcon\", 98);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"th\", 99)(16, \"div\", 93);\n    i0.ɵɵelement(17, \"fa-icon\", 100);\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19, \"Voltage (V)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"p-sortIcon\", 101);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"th\", 102)(22, \"div\", 93);\n    i0.ɵɵelement(23, \"fa-icon\", 103);\n    i0.ɵɵelementStart(24, \"span\");\n    i0.ɵɵtext(25, \"Power (W)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"p-sortIcon\", 104);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"th\", 105)(28, \"div\", 93);\n    i0.ɵɵelement(29, \"fa-icon\", 106);\n    i0.ɵɵelementStart(30, \"span\");\n    i0.ɵɵtext(31, \"Total Energy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(32, \"p-sortIcon\", 107);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"th\", 108)(34, \"div\", 93);\n    i0.ɵɵelement(35, \"fa-icon\", 109);\n    i0.ɵɵelementStart(36, \"span\");\n    i0.ɵɵtext(37, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(38, \"p-sortIcon\", 110);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ViewStationComponent_div_0_div_10_ng_template_3_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 111);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\")(4, \"div\", 11);\n    i0.ɵɵelement(5, \"fa-icon\", 125);\n    i0.ɵɵelementStart(6, \"span\", 113);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"td\")(9, \"span\", 126);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"td\")(12, \"span\", 113);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"td\")(15, \"span\", 115);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"td\")(18, \"span\", 113);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"td\")(21, \"div\", 11);\n    i0.ɵɵelement(22, \"p-tag\", 127);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const invert_r52 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", invert_r52);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(invert_r52.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", invert_r52.nominaloutput, \" A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", invert_r52.capacity, \" V\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", invert_r52.acpower, \" W\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", invert_r52.totalenergy, \" kWh\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", invert_r52.performance > 80 ? \"Normal\" : invert_r52.performance > 60 ? \"Warning\" : \"Critical\")(\"severity\", invert_r52.performance > 80 ? \"success\" : invert_r52.performance > 60 ? \"warning\" : \"danger\");\n  }\n}\nfunction ViewStationComponent_div_0_div_10_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r54 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 77);\n    i0.ɵɵelement(1, \"p-chart\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 78);\n    i0.ɵɵelement(3, \"p-toast\");\n    i0.ɵɵelementStart(4, \"div\", 79)(5, \"div\", 80)(6, \"div\", 11);\n    i0.ɵɵelement(7, \"fa-icon\", 81);\n    i0.ɵɵelementStart(8, \"h5\", 82);\n    i0.ɵɵtext(9, \"String Performance Data\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 83)(11, \"span\", 84);\n    i0.ɵɵelement(12, \"i\", 85);\n    i0.ɵɵelementStart(13, \"input\", 121);\n    i0.ɵɵlistener(\"input\", function ViewStationComponent_div_0_div_10_ng_template_3_Template_input_input_13_listener($event) {\n      i0.ɵɵrestoreView(_r54);\n      const _r49 = i0.ɵɵreference(16);\n      const ctx_r53 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r53.onGlobalFilter(_r49, $event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"p-button\", 87);\n    i0.ɵɵlistener(\"click\", function ViewStationComponent_div_0_div_10_ng_template_3_Template_p_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r54);\n      const _r49 = i0.ɵɵreference(16);\n      return i0.ɵɵresetView(_r49.exportCSV());\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"p-table\", 122, 123);\n    i0.ɵɵlistener(\"selectionChange\", function ViewStationComponent_div_0_div_10_ng_template_3_Template_p_table_selectionChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r54);\n      const ctx_r56 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r56.selectedProducts = $event);\n    });\n    i0.ɵɵtemplate(17, ViewStationComponent_div_0_div_10_ng_template_3_ng_template_17_Template, 39, 0, \"ng-template\", 6)(18, ViewStationComponent_div_0_div_10_ng_template_3_ng_template_18_Template, 23, 8, \"ng-template\", 90);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r48 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"data\", ctx_r48.lineData)(\"options\", ctx_r48.lineOptions)(\"height\", 300);\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"value\", ctx_r48.invertpower)(\"columns\", ctx_r48.cols)(\"rows\", 10)(\"globalFilterFields\", i0.ɵɵpureFunction0(12, _c0))(\"paginator\", true)(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(13, _c1))(\"showCurrentPageReport\", true)(\"selection\", ctx_r48.selectedProducts)(\"rowHover\", true);\n  }\n}\nfunction ViewStationComponent_div_0_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"p-card\");\n    i0.ɵɵtemplate(2, ViewStationComponent_div_0_div_10_ng_template_2_Template, 7, 0, \"ng-template\", 6)(3, ViewStationComponent_div_0_div_10_ng_template_3_Template, 19, 14, \"ng-template\", 7);\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c2 = () => ({\n  label: \"Stations\",\n  routerLink: \"/app/index\",\n  icon: \"solar-panel\"\n});\nconst _c3 = a0 => ({\n  label: a0,\n  icon: \"building\"\n});\nconst _c4 = (a0, a1) => [a0, a1];\nfunction ViewStationComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n    i0.ɵɵelement(2, \"app-modern-breadcrumb\", 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 4)(4, \"p-card\", 5);\n    i0.ɵɵtemplate(5, ViewStationComponent_div_0_ng_template_5_Template, 5, 1, \"ng-template\", 6)(6, ViewStationComponent_div_0_ng_template_6_Template, 26, 9, \"ng-template\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, ViewStationComponent_div_0_p_card_7_Template, 3, 0, \"p-card\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, ViewStationComponent_div_0_div_8_Template, 6, 1, \"div\", 9)(9, ViewStationComponent_div_0_div_9_Template, 4, 0, \"div\", 9)(10, ViewStationComponent_div_0_div_10_Template, 4, 0, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"items\", i0.ɵɵpureFunction2(8, _c4, i0.ɵɵpureFunction0(5, _c2), i0.ɵɵpureFunction1(6, _c3, ctx_r0.selectedStation.name)));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sumData);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showGeneral);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showInvert);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showString);\n  }\n}\nexport class ViewStationComponent {\n  constructor(stationsService, route, cacheService, weatherService, messageService) {\n    this.stationsService = stationsService;\n    this.route = route;\n    this.cacheService = cacheService;\n    this.weatherService = weatherService;\n    this.messageService = messageService;\n    this.stations = [];\n    this.devices = [];\n    this.inverters = [];\n    this.searchType = null;\n    this.selectedDate = new Date(); // default σημερινή ημερομηνία\n    this.sortOptionsCountry = [];\n    this.sortOptionsStatus = [];\n    this.sortOrder = 0;\n    this.sortField = '';\n    this.sourceCities = [];\n    this.targetCities = [];\n    this.orderCities = [];\n    this.showGeneral = true;\n    this.showInvert = false;\n    this.showString = false;\n    this.invertpower = [];\n    this.invert = {};\n    this.rowsPerPageOptions = [5, 10, 20];\n    this.cols = [];\n  }\n  ngOnInit() {\n    let stations = this.cacheService.getStations();\n    console.log(stations);\n    //console.log(this.stations)\n    this.route.paramMap.subscribe(params => {\n      this.selectedStation = stations.find(s => s.id == params.get('id'));\n      //this.selectedStation = params.get('id');\n      console.log('Station ID:', this.selectedStation);\n    });\n    this.getStationDevices();\n    this.getStationSumData();\n    this.getWeather();\n    //this.initCharts();\n    ///duummyy\n    // this.initDays();\n    //dummy\n    this.stationsService.getInvertPower().then(data => this.invertpower = data);\n    this.cols = [];\n    this.timeFrames = [{\n      id: 'day',\n      label: 'Day'\n    }, {\n      id: 'month',\n      label: 'Month'\n    }, {\n      id: 'year',\n      label: 'Year'\n    }, {\n      id: 'last30days',\n      label: 'Last 30 days'\n    }, {\n      id: 'last365days',\n      label: 'Last 365 days'\n    }, {\n      id: 'thisYear',\n      label: 'This year (1/1 - today)'\n    }, {\n      id: 'fromBeginning',\n      label: 'From the beginning'\n    }];\n    this.selectedTimeFrame = \"day\";\n  }\n  // initDays(){\n  //     this.days = [\n  //         {\n  //             \"day\":\"Tuesday\",\n  //             \"temp\":23,\n  //             \"kati\":3.20,\n  //             \"image\":\"https://cdn-icons-png.flaticon.com/512/979/979585.png\",\n  //             \"alert\":\"No alerts\"\n  //         },\n  //         {\n  //             \"day\":\"Today\",\n  //             \"temp\":23,\n  //             \"kati\":3.20,\n  //             \"image\":\"https://cdn-icons-png.flaticon.com/512/5213/5213385.png\",\n  //             \"alert\":\"No alerts\"\n  //         },\n  //         {\n  //             \"day\":\"Thursday\",\n  //             \"temp\":22,\n  //             \"kati\":3.20,\n  //             \"image\":\"https://cdn-icons-png.flaticon.com/512/5213/5213385.png\",\n  //             \"alert\":\"No alerts\"\n  //         },\n  //         {\n  //             \"day\":\"Friday\",\n  //             \"temp\":23,\n  //             \"kati\":3.20,\n  //             \"image\":\"https://cdn-icons-png.flaticon.com/512/5213/5213385.png\",\n  //             \"alert\":\"No alerts\"\n  //         },\n  //         {\n  //             \"day\":\"Saturday\",\n  //             \"temp\":23,\n  //             \"kati\":3.20,\n  //             \"image\":\"https://cdn-icons-png.flaticon.com/512/1163/1163657.png\",\n  //             \"alert\":\"Light Hail Probability\"\n  //         },\n  //         {\n  //             \"day\":\"Sunday\",\n  //             \"temp\":21,\n  //             \"kati\":3.20,\n  //             \"image\":\"https://cdn-icons-png.flaticon.com/512/979/979585.png\",\n  //             \"alert\":\"No alerts\"\n  //         },\n  //         {\n  //             \"day\":\"Monday\",\n  //             \"temp\":23,\n  //             \"kati\":3.20,\n  //             \"image\":\"https://cdn-icons-png.flaticon.com/512/12607/12607703.png\",\n  //             \"alert\":\"No alerts\"\n  //         }\n  //     ]\n  // }\n  initCharts(separated, searchType) {\n    const documentStyle = getComputedStyle(document.documentElement);\n    const textColor = documentStyle.getPropertyValue('--text-color');\n    const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n    const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\n    if (separated) {\n      // Βρίσκουμε τις μοναδικές ημερομηνίες με σειρά\n      const uniqueDates = Array.from(new Set(this.energyData.data.map(d => d.dateTime)));\n      const uniqueDateDescriptions = Array.from(new Set(this.energyData.data.map(d => d.dateDescription)));\n      // Βρίσκουμε τους μοναδικούς inverters\n      const uniqueInverters = Array.from(new Set(this.energyData.data.map(d => d.name)));\n      // Δημιουργούμε datasets, ένα για κάθε inverter\n      const barDatasets = uniqueInverters.map(inv => {\n        const color = this.getRandomColor();\n        const data = uniqueDates.map(date => {\n          const entry = this.energyData.data.find(d => d.name === inv && d.dateTime === date);\n          return entry ? entry.activePower : 0;\n        });\n        return {\n          label: inv,\n          data: data,\n          backgroundColor: color\n        };\n      });\n      this.barData = {\n        labels: uniqueDateDescriptions.map(dt => dt),\n        datasets: barDatasets\n      };\n      // const uniqueLineDates = Array.from(new Set(this.energyData.data.map(d => d.dateTime)));\n      // const uniqueLineDateDescriptions = Array.from(new Set(this.energyData.data.map(d => d.dateDescription)));\n      // const uniqueLineInverters = Array.from(new Set(this.energyData.data.map(d => d.name)));\n      // const lineDatasets = uniqueLineInverters.map(inv => {\n      //     const color = this.getRandomColor();\n      //     const data = uniqueLineDates.map(date => {\n      //         const entry = this.energyData.data.find(d => d.name === inv && d.dateTime === date);\n      //         return entry ? entry.activePower : 0;\n      //     });\n      //     return {\n      //         label: inv,\n      //         data: data,\n      //         borderColor: color,\n      //         fill: false,\n      //         tension: 0.3, // προαιρετικά για πιο ομαλές καμπύλες\n      //     };\n      // });\n      // this.lineData = {\n      //     labels: uniqueLineDateDescriptions, // ετικέτες στον X-άξονα\n      //     datasets: lineDatasets\n      // };\n      var datasets = [];\n      // const uniqueInverters = Array.from(new Set(this.energyData.data.map(d => d.name)));\n      const activePowerByName = {};\n      this.energyData.data.forEach(d => {\n        if (!activePowerByName[d.name]) {\n          activePowerByName[d.name] = [];\n        }\n        activePowerByName[d.name].push(d.activePower);\n      });\n      uniqueInverters.forEach(inv => {\n        var color = this.getRandomColor();\n        datasets.push({\n          label: inv,\n          data: activePowerByName[inv],\n          fill: false,\n          backgroundColor: color,\n          borderColor: color,\n          tension: .4\n        });\n      });\n      const firstName = this.energyData.data[0].name; // Παίρνουμε το πρώτο name\n      const dateTimesForFirstName = this.energyData.data.filter(d => d.name === firstName) // Φιλτράρουμε μόνο τα αντικείμενα με το ίδιο name\n      .map(d => d.dateTime); // Παίρνουμε μόνο τα dateTime\n      this.lineData = {\n        labels: dateTimesForFirstName.map((dt, index) => index % 2 === 0 ? new Date(dt).toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        }) : ''),\n        datasets: datasets\n      };\n      // this.barData = {\n      //     labels: this.energyData.data.map(item => item.name), // Το property \"name\" για τον X-άξονα\n      //     datasets: datasets\n      // };\n    } else {\n      this.lineData = {\n        labels: this.energyData.data.map((e, index) => index % 2 === 0 ? new Date(e.dateTime).toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        }) : ''),\n        datasets: [{\n          label: 'Active Power',\n          data: this.energyData.data.map(e => e.activePower),\n          fill: false,\n          backgroundColor: documentStyle.getPropertyValue('--primary-500'),\n          borderColor: documentStyle.getPropertyValue('--primary-500'),\n          tension: .4\n        }, {\n          label: 'Total Input Power',\n          data: this.energyData.data.map(e => e.totalInputPower),\n          fill: false,\n          backgroundColor: documentStyle.getPropertyValue('--primary-200'),\n          borderColor: documentStyle.getPropertyValue('--primary-200'),\n          tension: .4\n        }]\n      };\n      this.barData = {\n        labels: this.energyData.data.map(item => item.name),\n        datasets: [{\n          label: 'Active Power',\n          data: this.energyData.data.map(e => e.activePower),\n          fill: false,\n          backgroundColor: documentStyle.getPropertyValue('--primary-500'),\n          borderColor: documentStyle.getPropertyValue('--primary-500'),\n          tension: .4\n        }, {\n          label: 'Total Input Power',\n          data: this.energyData.data.map(e => e.totalInputPower),\n          fill: false,\n          backgroundColor: documentStyle.getPropertyValue('--primary-200'),\n          borderColor: documentStyle.getPropertyValue('--primary-200'),\n          tension: .4\n        }]\n      };\n    }\n    this.lineOptions = {\n      plugins: {\n        legend: {\n          labels: {\n            fontColor: textColor\n          }\n        }\n      },\n      scales: {\n        x: {\n          ticks: {\n            color: textColorSecondary,\n            maxRotation: 0,\n            minRotation: 0\n          },\n          grid: {\n            color: surfaceBorder,\n            drawBorder: false\n          }\n        },\n        y: {\n          ticks: {\n            color: textColorSecondary\n          },\n          grid: {\n            color: surfaceBorder,\n            drawBorder: false\n          }\n        }\n      }\n    };\n  }\n  getStationDevices() {\n    let request = {\n      stationId: this.selectedStation.id\n    };\n    this.stationsService.getStationDevices(request).then(data => {\n      console.log(data);\n      this.devices = data;\n      this.inverters = this.devices.filter(device => device.type === \"StringInverter\");\n      console.log(\"DEBV\" + this.devices);\n      this.inverterIds = this.devices.filter(device => device.type === \"StringInverter\") // Φιλτράρει μόνο τα StringInverter\n      .map(device => device.id) // Παίρνει μόνο τα id\n      .join(\",\"); // Τα ενώνει με κόμματα\n      this.getHistoryEnergy(this.inverterIds);\n    });\n  }\n  getStationSumData() {\n    let request = {\n      stationId: this.selectedStation.id\n    };\n    this.stationsService.getStationSumData(request).then(data => this.sumData = data);\n  }\n  getWeather() {\n    let request = {\n      coordinates: this.selectedStation.longitude + \",\" + this.selectedStation.latitude\n    };\n    this.weatherService.getWeather(request).then(data => this.weatherData = data);\n  }\n  getHistoryEnergy(inverterIds, separated = false, dateTimeFrom = null, dateTimeTo = null, searchType = null) {\n    var now = new Date();\n    if (dateTimeFrom != null) var formattedStartDate = dateTimeFrom.toISOString();else var formattedStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0).toISOString();\n    if (dateTimeTo != null) var formattedEndDate = dateTimeTo.toISOString();else var formattedEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0).toISOString();\n    // var todayMidnightUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 0, 0, 0));\n    // console.log(dateTimeFrom)\n    // if (dateTimeFrom != null) {\n    //     now = new Date(dateTimeFrom);\n    //     todayMidnightUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 0, 0, 0));\n    // }\n    // var formattedStartDate = dateTimeFrom.toISOString();\n    // console.log(formattedStartDate);\n    // var tomorrowMidnightUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate() + 1, 0, 0, 0));\n    // if (dateTimeTo != null) {\n    //     now = new Date(dateTimeTo);\n    //     tomorrowMidnightUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()+1, 0, 0, 0));\n    // }\n    let request = {\n      devIds: inverterIds,\n      devTypeId: 1,\n      startDateTime: formattedStartDate,\n      endDateTime: formattedEndDate,\n      separated: separated,\n      searchType: searchType,\n      stationId: this.selectedStation.id\n    };\n    this.stationsService.getStationHistoricData(request).then(data => {\n      this.energyData = data;\n      console.log(this.energyData);\n      if (this.energyData.data.length > 0) {\n        this.initCharts(separated, searchType);\n      } else {\n        this.messageService.add({\n          severity: 'warn',\n          summary: 'No Data',\n          detail: 'No Data to display!'\n        });\n      }\n    });\n  }\n  showInvertMonitoring() {\n    var reload = true;\n    if (this.showInvert) reload = false;\n    this.showGeneral = false;\n    this.showInvert = true;\n    this.showString = false;\n    if (reload) this.getHistoryEnergy(this.inverterIds, true);\n  }\n  showStringMonitoring() {\n    this.showGeneral = false;\n    this.showInvert = false;\n    this.showString = true;\n  }\n  getRandomColor() {\n    const rootStyles = getComputedStyle(document.documentElement);\n    const cssVariables = Object.keys(rootStyles).map(key => rootStyles[key]).filter(value => value.startsWith('--')) // Φιλτράρουμε μόνο τις CSS variables\n    .map(varName => rootStyles.getPropertyValue(varName).trim()) // Παίρνουμε τις τιμές τους\n    .filter(value => {\n      // Ελέγχουμε αν το όνομα της μεταβλητής περιέχει έναν αριθμό μεγαλύτερο του 200\n      const match = value.match(/(\\d+)/); // Αντιστοιχεί στον αριθμό που υπάρχει στο όνομα\n      return match && parseInt(match[0], 10) > 200; // Φιλτράρει μόνο χρώματα με αριθμό > 200\n    });\n    // Συνάρτηση που επιστρέφει τυχαίο χρώμα από τη λίστα\n    return cssVariables[Math.floor(Math.random() * cssVariables.length)];\n  }\n  onDateChange(event) {\n    console.log('Ημερομηνία επιλέχθηκε:', event);\n    var datefrom = new Date(event.setHours(0, 0, 0, 0));\n    const pastDate = new Date(datefrom);\n    pastDate.setDate(pastDate.getDate() + 1);\n    this.getHistoryEnergy(this.inverterIds, !this.showGeneral, datefrom, pastDate, this.searchType);\n  }\n  onTimeFrameChange(event) {\n    console.log(event);\n    let currentDate = new Date();\n    console.log(currentDate);\n    let datefrom;\n    let dateto = new Date(currentDate);\n    switch (event.value) {\n      case 'day':\n        // Για την περίπτωση του \"Day\", το datefrom είναι σήμερα\n        datefrom = new Date(currentDate.setHours(0, 0, 0, 0)); // Ορίζουμε το time στα 00:00:00\n        this.searchType = null;\n        if (event.value === 'day') {\n          this.selectedDate = new Date(); // reset σε σημερινή ημερομηνία\n        }\n\n        break;\n      case 'month':\n        // Για την περίπτωση του \"Month\", το datefrom είναι η 1η μέρα του τρέχοντος μήνα\n        datefrom = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);\n        datefrom.setHours(0, 0, 0, 0);\n        this.searchType = \"monthly\";\n        break;\n      case 'year':\n        // Για την περίπτωση του \"Year\", το datefrom είναι η 1η Ιανουαρίου του τρέχοντος έτους\n        datefrom = new Date(currentDate.getFullYear(), 0, 1);\n        datefrom.setHours(0, 0, 0, 0);\n        this.searchType = \"yearly\";\n        break;\n      case 'last30days':\n        // Για την περίπτωση του \"Last 30 days\", το datefrom είναι 30 μέρες πριν\n        const pastDate = new Date(currentDate);\n        pastDate.setDate(pastDate.getDate() - 30);\n        pastDate.setHours(0, 0, 0, 0);\n        datefrom = pastDate;\n        this.searchType = \"monthly\";\n        break;\n      case 'last365days':\n        // Για την περίπτωση του \"Last 365 days\", το datefrom είναι 365 μέρες πριν\n        const pastDate2 = new Date(currentDate);\n        pastDate2.setDate(pastDate2.getDate() - 365);\n        pastDate2.setHours(0, 0, 0, 0);\n        datefrom = pastDate2;\n        this.searchType = \"yearly\";\n        break;\n      case 'thisYear':\n        // Για την περίπτωση του \"This year (1/1 - today)\", το datefrom είναι η 1η Ιανουαρίου του τρέχοντος έτους\n        datefrom = new Date(currentDate.getFullYear(), 0, 1);\n        datefrom.setHours(0, 0, 0, 0);\n        this.searchType = \"yearly\";\n        break;\n      case 'fromBeginning':\n        // Για την περίπτωση του \"From the beginning\", το datefrom μπορεί να είναι η αρχή της εφαρμογής ή άλλη προεπιλεγμένη ημερομηνία\n        // Εδώ χρησιμοποιούμε την αρχική ημερομηνία της εφαρμογής ή άλλη ημερομηνία\n        datefrom = new Date(2000, 0, 1); // Ή οποιαδήποτε άλλη ημερομηνία αρχής\n        this.searchType = \"lifetime\";\n        break;\n      default:\n        // Αν δεν είναι καμία από τις παραπάνω επιλογές, ορίζουμε μια προεπιλεγμένη τιμή\n        datefrom = new Date(currentDate.setHours(0, 0, 0, 0));\n        break;\n    }\n    // Καλούμε τη συνάρτηση getHistoryEnergy με τα ορίσματα\n    this.getHistoryEnergy(this.inverterIds, !this.showGeneral, datefrom, dateto, this.searchType);\n  }\n  onGlobalFilter(table, event) {\n    table.filterGlobal(event.target.value, 'contains');\n  }\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n  static #_ = this.ɵfac = function ViewStationComponent_Factory(t) {\n    return new (t || ViewStationComponent)(i0.ɵɵdirectiveInject(i1.StationsService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.CacheService), i0.ɵɵdirectiveInject(i4.WeatherService), i0.ɵɵdirectiveInject(i5.MessageService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ViewStationComponent,\n    selectors: [[\"ng-component\"]],\n    decls: 1,\n    vars: 1,\n    consts: [[\"class\", \"grid p-fluid\", 4, \"ngIf\"], [1, \"grid\", \"p-fluid\"], [1, \"col-12\"], [3, \"items\"], [1, \"col-12\", \"lg:col-3\"], [1, \"mb-3\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"content\"], [4, \"ngIf\"], [\"class\", \"col-12 lg:col-9\", 4, \"ngIf\"], [1, \"bg-primary-50\", \"p-3\", \"border-round-top\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"icon\", \"solar-panel\", 1, \"text-primary\", \"text-xl\"], [1, \"m-0\", \"text-primary\"], [1, \"space-y-3\"], [1, \"flex\", \"align-items-start\", \"gap-2\"], [\"icon\", \"map-marker-alt\", 1, \"text-600\", \"mt-1\"], [1, \"text-600\", \"text-sm\", 2, \"word-break\", \"break-word\"], [1, \"grid\"], [1, \"col-6\"], [1, \"text-center\"], [1, \"text-2xl\", \"font-bold\", \"text-primary\"], [1, \"text-xs\", \"text-600\"], [1, \"text-2xl\", \"font-bold\", \"text-orange-500\"], [1, \"space-y-2\"], [1, \"text-900\", \"font-semibold\", \"mb-2\"], [\"label\", \"General Overview\", \"icon\", \"pi pi-chart-line\", \"size\", \"small\", 1, \"w-full\", \"justify-content-start\", 3, \"severity\", \"text\", \"click\"], [\"label\", \"Inverter Monitoring\", \"icon\", \"pi pi-cog\", \"size\", \"small\", 1, \"w-full\", \"justify-content-start\", 3, \"severity\", \"text\", \"click\"], [\"label\", \"String Monitoring\", \"icon\", \"pi pi-sitemap\", \"size\", \"small\", 1, \"w-full\", \"justify-content-start\", 3, \"severity\", \"text\", \"click\"], [1, \"bg-green-50\", \"p-3\", \"border-round-top\"], [\"icon\", \"chart-bar\", 1, \"text-green-600\", \"text-lg\"], [1, \"m-0\", \"text-green-800\"], [1, \"col-12\", \"mb-2\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\"], [\"icon\", \"bolt\", 1, \"text-orange-500\"], [1, \"text-sm\", \"text-600\"], [1, \"font-bold\"], [\"icon\", \"chart-line\", 1, \"text-blue-500\"], [\"icon\", \"euro-sign\", 1, \"text-green-500\"], [\"icon\", \"calendar-alt\", 1, \"text-purple-500\"], [\"icon\", \"plug\", 1, \"text-cyan-500\"], [\"icon\", \"battery-half\", 1, \"text-yellow-500\"], [\"icon\", \"coins\", 1, \"text-green-600\"], [1, \"font-bold\", \"text-green-600\"], [1, \"col-12\", \"lg:col-9\"], [\"class\", \"mb-3\", 4, \"ngIf\"], [1, \"bg-blue-50\", \"p-3\", \"border-round-top\"], [\"icon\", \"cloud-sun\", 1, \"text-blue-600\", \"text-lg\"], [1, \"m-0\", \"text-blue-800\"], [\"class\", \"col-12 md:col-6 lg:col-3 xl:col\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"md:col-6\", \"lg:col-3\", \"xl:col\"], [1, \"text-center\", \"p-3\", \"border-round\", \"hover:bg-blue-50\", \"transition-colors\", \"transition-duration-150\"], [\"height\", \"50\", 1, \"mb-2\", 3, \"src\"], [1, \"text-600\", \"text-sm\", \"mb-2\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-2\", \"mb-1\"], [\"icon\", \"arrow-down\", 1, \"text-blue-500\", \"text-xs\"], [1, \"text-sm\"], [\"icon\", \"arrow-up\", 1, \"text-red-500\", \"text-xs\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-1\"], [\"icon\", \"wind\", 1, \"text-cyan-500\", \"text-xs\"], [\"icon\", \"chart-line\", 1, \"text-green-600\", \"text-lg\"], [\"value\", \"Live Data\", \"severity\", \"success\", \"icon\", \"pi pi-circle-fill\"], [1, \"flex\", \"flex-column\", \"md:flex-row\", \"gap-3\", \"mb-4\"], [1, \"flex-1\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-600\", \"mb-2\"], [\"placeholder\", \"Select TimeFrame\", \"optionLabel\", \"label\", \"optionValue\", \"id\", 1, \"w-full\", 3, \"options\", \"ngModel\", \"showClear\", \"ngModelChange\", \"onChange\"], [\"class\", \"flex-1\", 4, \"ngIf\"], [1, \"border-round\", \"border-1\", \"surface-border\", \"p-3\"], [\"type\", \"line\", 3, \"data\", \"options\", \"height\", 4, \"ngIf\"], [\"type\", \"bar\", 3, \"data\", \"options\", \"height\", 4, \"ngIf\"], [\"dateFormat\", \"dd/mm/yy\", 1, \"w-full\", 3, \"ngModel\", \"showIcon\", \"ngModelChange\", \"onSelect\"], [\"type\", \"line\", 3, \"data\", \"options\", \"height\"], [\"type\", \"bar\", 3, \"data\", \"options\", \"height\"], [1, \"bg-orange-50\", \"p-3\", \"border-round-top\"], [\"icon\", \"cog\", 1, \"text-orange-600\", \"text-lg\"], [1, \"m-0\", \"text-orange-800\"], [\"value\", \"Real-time\", \"severity\", \"warning\", \"icon\", \"pi pi-circle-fill\"], [1, \"border-round\", \"border-1\", \"surface-border\", \"p-3\", \"mb-4\"], [1, \"border-round\", \"border-1\", \"surface-border\"], [1, \"bg-gray-50\", \"p-3\", \"border-round-top\", \"border-bottom-1\", \"surface-border\"], [1, \"flex\", \"flex-column\", \"md:flex-row\", \"md:justify-content-between\", \"md:align-items-center\", \"gap-3\"], [\"icon\", \"table\", 1, \"text-600\"], [1, \"m-0\", \"text-900\"], [1, \"flex\", \"gap-2\"], [1, \"p-input-icon-left\"], [1, \"pi\", \"pi-search\"], [\"pInputText\", \"\", \"type\", \"text\", \"placeholder\", \"Search inverters...\", 1, \"w-full\", \"sm:w-auto\", 3, \"input\"], [\"icon\", \"pi pi-download\", \"severity\", \"help\", \"size\", \"small\", \"pTooltip\", \"Export to CSV\", 3, \"click\"], [\"responsiveLayout\", \"scroll\", \"currentPageReportTemplate\", \"Showing {first} to {last} of {totalRecords} inverters\", \"selectionMode\", \"multiple\", \"dataKey\", \"id\", \"styleClass\", \"p-datatable-sm\", 3, \"value\", \"columns\", \"rows\", \"globalFilterFields\", \"paginator\", \"rowsPerPageOptions\", \"showCurrentPageReport\", \"selection\", \"rowHover\", \"selectionChange\"], [\"dt\", \"\"], [\"pTemplate\", \"body\"], [2, \"width\", \"3rem\"], [\"pSortableColumn\", \"name\"], [1, \"flex\", \"align-items-center\", \"gap-1\"], [\"icon\", \"microchip\", 1, \"text-xs\"], [\"field\", \"name\"], [\"pSortableColumn\", \"nominaloutput\"], [\"icon\", \"bolt\", 1, \"text-xs\"], [\"field\", \"nominaloutput\"], [\"pSortableColumn\", \"capacity\"], [\"icon\", \"battery-full\", 1, \"text-xs\"], [\"field\", \"capacity\"], [\"pSortableColumn\", \"acpower\"], [\"icon\", \"plug\", 1, \"text-xs\"], [\"field\", \"acpower\"], [\"pSortableColumn\", \"totalenergy\"], [\"icon\", \"chart-area\", 1, \"text-xs\"], [\"field\", \"totalenergy\"], [\"pSortableColumn\", \"performance\"], [\"icon\", \"tachometer-alt\", 1, \"text-xs\"], [\"field\", \"performance\"], [3, \"value\"], [\"icon\", \"microchip\", 1, \"text-primary\"], [1, \"font-medium\"], [\"severity\", \"info\", 3, \"value\"], [1, \"font-medium\", \"text-orange-600\"], [3, \"value\", \"severity\"], [1, \"bg-purple-50\", \"p-3\", \"border-round-top\"], [\"icon\", \"sitemap\", 1, \"text-purple-600\", \"text-lg\"], [1, \"m-0\", \"text-purple-800\"], [\"value\", \"Live Data\", \"severity\", \"info\", \"icon\", \"pi pi-circle-fill\"], [\"pInputText\", \"\", \"type\", \"text\", \"placeholder\", \"Search strings...\", 1, \"w-full\", \"sm:w-auto\", 3, \"input\"], [\"responsiveLayout\", \"scroll\", \"currentPageReportTemplate\", \"Showing {first} to {last} of {totalRecords} strings\", \"selectionMode\", \"multiple\", \"dataKey\", \"id\", \"styleClass\", \"p-datatable-sm\", 3, \"value\", \"columns\", \"rows\", \"globalFilterFields\", \"paginator\", \"rowsPerPageOptions\", \"showCurrentPageReport\", \"selection\", \"rowHover\", \"selectionChange\"], [\"dt2\", \"\"], [\"icon\", \"link\", 1, \"text-xs\"], [\"icon\", \"link\", 1, \"text-purple-600\"], [1, \"font-medium\", \"text-blue-600\"], [\"icon\", \"pi pi-circle-fill\", 3, \"value\", \"severity\"]],\n    template: function ViewStationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, ViewStationComponent_div_0_Template, 11, 11, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedStation);\n      }\n    },\n    dependencies: [i6.NgForOf, i6.NgIf, i7.NgControlStatus, i7.NgModel, i8.UIChart, i9.Tooltip, i10.Table, i5.PrimeTemplate, i10.SortableColumn, i10.SortIcon, i10.TableCheckbox, i10.TableHeaderCheckbox, i11.Button, i12.InputText, i13.Dropdown, i14.Calendar, i15.FaIconComponent, i16.Card, i17.Tag, i18.Divider, i19.Toast, i20.ModernBreadcrumbComponent],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "selectedStation", "name", "ɵɵlistener", "ViewStationComponent_div_0_ng_template_6_Template_p_button_click_23_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "showGeneral", "showInvert", "ɵɵresetView", "showString", "ViewStationComponent_div_0_ng_template_6_Template_p_button_click_24_listener", "ctx_r9", "showInvertMonitoring", "ViewStationComponent_div_0_ng_template_6_Template_p_button_click_25_listener", "ctx_r10", "showStringMonitoring", "ctx_r2", "address", "inverters", "length", "devices", "ɵɵproperty", "ɵɵtextInterpolate1", "ctx_r12", "sumData", "day<PERSON>ower", "totalPower", "<PERSON><PERSON><PERSON><PERSON>", "month<PERSON>ower", "dayOnGridEnergy", "dayUseEnergy", "totalIncome", "ɵɵtemplate", "ViewStationComponent_div_0_p_card_7_ng_template_1_Template", "ViewStationComponent_div_0_p_card_7_ng_template_2_Template", "day_r19", "day", "icon", "ɵɵsanitizeUrl", "description", "minTemperatureCelsius", "maxTemperatureCelsius", "windKph", "ViewStationComponent_div_0_div_8_p_card_1_ng_template_2_div_1_Template", "ctx_r17", "weatherData", "weatherInfo", "ViewStationComponent_div_0_div_8_p_card_1_ng_template_1_Template", "ViewStationComponent_div_0_div_8_p_card_1_ng_template_2_Template", "ViewStationComponent_div_0_div_8_ng_template_5_div_5_Template_p_calendar_ngModelChange_3_listener", "$event", "_r24", "ctx_r23", "selectedDate", "ViewStationComponent_div_0_div_8_ng_template_5_div_5_Template_p_calendar_onSelect_3_listener", "ctx_r25", "onDateChange", "ctx_r20", "ctx_r21", "lineData", "lineOptions", "ctx_r22", "barData", "ViewStationComponent_div_0_div_8_ng_template_5_Template_p_dropdown_ngModelChange_4_listener", "_r27", "ctx_r26", "selectedTimeFrame", "ViewStationComponent_div_0_div_8_ng_template_5_Template_p_dropdown_onChange_4_listener", "ctx_r28", "onTimeFrameChange", "ViewStationComponent_div_0_div_8_ng_template_5_div_5_Template", "ViewStationComponent_div_0_div_8_ng_template_5_p_chart_7_Template", "ViewStationComponent_div_0_div_8_ng_template_5_p_chart_8_Template", "ctx_r15", "timeFrames", "searchType", "ViewStationComponent_div_0_div_8_p_card_1_Template", "ViewStationComponent_div_0_div_8_ng_template_4_Template", "ViewStationComponent_div_0_div_8_ng_template_5_Template", "ctx_r4", "ViewStationComponent_div_0_div_9_ng_template_3_div_5_Template_p_calendar_ngModelChange_3_listener", "_r38", "ctx_r37", "ViewStationComponent_div_0_div_9_ng_template_3_div_5_Template_p_calendar_onSelect_3_listener", "ctx_r39", "ctx_r31", "ctx_r32", "ctx_r33", "invert_r40", "nominaloutput", "capacity", "acpower", "totalenergy", "performance", "ViewStationComponent_div_0_div_9_ng_template_3_Template_p_dropdown_ngModelChange_4_listener", "_r42", "ctx_r41", "ViewStationComponent_div_0_div_9_ng_template_3_Template_p_dropdown_onChange_4_listener", "ctx_r43", "ViewStationComponent_div_0_div_9_ng_template_3_div_5_Template", "ViewStationComponent_div_0_div_9_ng_template_3_p_chart_7_Template", "ViewStationComponent_div_0_div_9_ng_template_3_p_chart_8_Template", "ViewStationComponent_div_0_div_9_ng_template_3_Template_input_input_20_listener", "_r34", "ɵɵreference", "ctx_r44", "onGlobalFilter", "ViewStationComponent_div_0_div_9_ng_template_3_Template_p_button_click_21_listener", "exportCSV", "ViewStationComponent_div_0_div_9_ng_template_3_Template_p_table_selectionChange_22_listener", "ctx_r46", "selectedProducts", "ViewStationComponent_div_0_div_9_ng_template_3_ng_template_24_Template", "ViewStationComponent_div_0_div_9_ng_template_3_ng_template_25_Template", "ctx_r30", "invertpower", "cols", "ɵɵpureFunction0", "_c0", "_c1", "ViewStationComponent_div_0_div_9_ng_template_2_Template", "ViewStationComponent_div_0_div_9_ng_template_3_Template", "invert_r52", "ViewStationComponent_div_0_div_10_ng_template_3_Template_input_input_13_listener", "_r54", "_r49", "ctx_r53", "ViewStationComponent_div_0_div_10_ng_template_3_Template_p_button_click_14_listener", "ViewStationComponent_div_0_div_10_ng_template_3_Template_p_table_selectionChange_15_listener", "ctx_r56", "ViewStationComponent_div_0_div_10_ng_template_3_ng_template_17_Template", "ViewStationComponent_div_0_div_10_ng_template_3_ng_template_18_Template", "ctx_r48", "ViewStationComponent_div_0_div_10_ng_template_2_Template", "ViewStationComponent_div_0_div_10_ng_template_3_Template", "ViewStationComponent_div_0_ng_template_5_Template", "ViewStationComponent_div_0_ng_template_6_Template", "ViewStationComponent_div_0_p_card_7_Template", "ViewStationComponent_div_0_div_8_Template", "ViewStationComponent_div_0_div_9_Template", "ViewStationComponent_div_0_div_10_Template", "ɵɵpureFunction2", "_c4", "_c2", "ɵɵpureFunction1", "_c3", "ctx_r0", "ViewStationComponent", "constructor", "stationsService", "route", "cacheService", "weatherService", "messageService", "stations", "Date", "sortOptionsCountry", "sortOptionsStatus", "sortOrder", "sortField", "sourceCities", "targetCities", "orderCities", "invert", "rowsPerPageOptions", "ngOnInit", "getStations", "console", "log", "paramMap", "subscribe", "params", "find", "s", "id", "get", "getStationDevices", "getStationSumData", "<PERSON><PERSON><PERSON><PERSON>", "getInvertPower", "then", "data", "label", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "separated", "documentStyle", "getComputedStyle", "document", "documentElement", "textColor", "getPropertyValue", "textColorSecondary", "surfaceBorder", "uniqueDates", "Array", "from", "Set", "energyData", "map", "d", "dateTime", "uniqueDateDescriptions", "dateDescription", "uniqueInverters", "barDatasets", "inv", "color", "getRandomColor", "date", "entry", "activePower", "backgroundColor", "labels", "dt", "datasets", "activePowerByName", "for<PERSON>ach", "push", "fill", "borderColor", "tension", "firstName", "dateTimesForFirstName", "filter", "index", "toLocaleTimeString", "hour", "minute", "e", "totalInputPower", "item", "plugins", "legend", "fontColor", "scales", "x", "ticks", "maxRotation", "minRotation", "grid", "drawBorder", "y", "request", "stationId", "device", "type", "inverterIds", "join", "getHistoryEnergy", "coordinates", "longitude", "latitude", "dateTimeFrom", "dateTimeTo", "now", "formattedStartDate", "toISOString", "getFullYear", "getMonth", "getDate", "formattedEndDate", "devIds", "devTypeId", "startDateTime", "endDateTime", "getStationHistoricData", "add", "severity", "summary", "detail", "reload", "rootStyles", "cssVariables", "Object", "keys", "key", "value", "startsWith", "varName", "trim", "match", "parseInt", "Math", "floor", "random", "event", "datefrom", "setHours", "pastDate", "setDate", "currentDate", "dateto", "pastDate2", "table", "filterGlobal", "target", "ngOnDestroy", "subscription", "unsubscribe", "_", "ɵɵdirectiveInject", "i1", "StationsService", "i2", "ActivatedRoute", "i3", "CacheService", "i4", "WeatherService", "i5", "MessageService", "_2", "selectors", "decls", "vars", "consts", "template", "ViewStationComponent_Template", "rf", "ctx", "ViewStationComponent_div_0_Template"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\stations\\view.component.ts", "D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\stations\\view.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { MenuItem, Message, MessageService } from 'primeng/api';\r\nimport { Product } from '../../api/product';\r\nimport { ProductService } from '../../service/product.service';\r\nimport { Subscription, debounceTime, throwIfEmpty } from 'rxjs';\r\n//import { LayoutService } from 'src/app/layout/service/app.layout.service';\r\nimport { SelectItem } from 'primeng/api';\r\nimport { DataView } from 'primeng/dataview';\r\nimport { StationsService } from '../../service/stations.service';\r\nimport { Station } from '../../api/station';\r\nimport { InvertPower } from '../../api/invertpower';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { Device } from '../../api/device';\r\nimport { GetHistoricDataResponse, GetStationSumDataResponse, GetWeatherResponse } from '../../api/responses';\r\nimport { GetHistoricDataRequest, GetStationDevicesRequest, GetWeatherRequest } from '../../api/requests';\r\nimport { CacheService } from '../../service/cache.service';\r\nimport { WeatherService } from '../../service/weather.service';\r\n\r\n@Component({\r\n    templateUrl: './view.component.html',\r\n})\r\nexport class ViewStationComponent implements OnInit, OnDestroy {\r\n\r\n    items!: MenuItem[];\r\n    stations: Station[] = [];\r\n    devices: Device[] =[];\r\n    inverters: Device[]= [];\r\n    selectedStation: Station;\r\n    sumData: GetStationSumDataResponse;\r\n    energyData: GetHistoricDataResponse;\r\n    weatherData: GetWeatherResponse;\r\n    inverterIds: string;\r\n    timeFrames: any[];\r\n    selectedTimeFrame: string;\r\n    searchType:string = null;\r\n    selectedDate: Date = new Date(); // default σημερινή ημερομηνία\r\n\r\n    chartData: any;\r\n\r\n    chartOptions: any;\r\n\r\n    subscription!: Subscription;\r\n\r\n    sortOptionsCountry: SelectItem[] = [];\r\n\r\n    sortOptionsStatus: SelectItem[] = [];\r\n\r\n    sortOrder: number = 0;\r\n\r\n    sortField: string = '';\r\n\r\n    sourceCities: any[] = [];\r\n\r\n    targetCities: any[] = [];\r\n\r\n    orderCities: any[] = [];\r\n\r\n    mapSrc:string;\r\n\r\n    lineData: any;\r\n\r\n    barData: any;\r\n\r\n    pieData: any;\r\n\r\n    polarData: any;\r\n\r\n    radarData: any;\r\n\r\n    lineOptions: any;\r\n\r\n    barOptions: any;\r\n\r\n    pieOptions: any;\r\n\r\n    polarOptions: any;\r\n\r\n    radarOptions: any;\r\n\r\n    days:any[];\r\n    showGeneral: boolean = true;\r\n    showInvert: boolean = false;\r\n    showString: boolean = false;\r\n    invertpower: InvertPower[] =[];\r\n    invert: InvertPower = {};\r\n    rowsPerPageOptions = [5, 10, 20];\r\n    cols: any[] = [];\r\n\r\n\r\n    constructor(private stationsService: StationsService, \r\n        private route: ActivatedRoute, \r\n        private cacheService: CacheService,\r\n        private weatherService: WeatherService,\r\n        private messageService: MessageService\r\n        ) {\r\n        \r\n    }\r\n \r\n    ngOnInit() {\r\n        let stations = this.cacheService.getStations();\r\n        console.log(stations)\r\n        \r\n        //console.log(this.stations)\r\n        this.route.paramMap.subscribe(params => {\r\n            this.selectedStation = stations.find(s => s.id == params.get('id'));\r\n            //this.selectedStation = params.get('id');\r\n            console.log('Station ID:', this.selectedStation);\r\n        });\r\n\r\n        this.getStationDevices();\r\n        this.getStationSumData();\r\n        this.getWeather();\r\n\r\n        //this.initCharts();\r\n        \r\n        ///duummyy\r\n        // this.initDays();\r\n\r\n        //dummy\r\n        this.stationsService.getInvertPower().then(data => this.invertpower = data);\r\n\r\n        this.cols = [\r\n           \r\n        ];\r\n\r\n        this.timeFrames = [\r\n            { id: 'day', label: 'Day' },\r\n            { id: 'month', label: 'Month' },\r\n            { id: 'year', label: 'Year' },\r\n            { id: 'last30days', label: 'Last 30 days' },\r\n            { id: 'last365days', label: 'Last 365 days' },\r\n            { id: 'thisYear', label: 'This year (1/1 - today)' },\r\n            { id: 'fromBeginning', label: 'From the beginning' }\r\n        ];\r\n          \r\n        this.selectedTimeFrame = \"day\";\r\n\r\n    }\r\n\r\n    // initDays(){\r\n    //     this.days = [\r\n    //         {\r\n    //             \"day\":\"Tuesday\",\r\n    //             \"temp\":23,\r\n    //             \"kati\":3.20,\r\n    //             \"image\":\"https://cdn-icons-png.flaticon.com/512/979/979585.png\",\r\n    //             \"alert\":\"No alerts\"\r\n    //         },\r\n    //         {\r\n    //             \"day\":\"Today\",\r\n    //             \"temp\":23,\r\n    //             \"kati\":3.20,\r\n    //             \"image\":\"https://cdn-icons-png.flaticon.com/512/5213/5213385.png\",\r\n    //             \"alert\":\"No alerts\"\r\n    //         },\r\n    //         {\r\n    //             \"day\":\"Thursday\",\r\n    //             \"temp\":22,\r\n    //             \"kati\":3.20,\r\n    //             \"image\":\"https://cdn-icons-png.flaticon.com/512/5213/5213385.png\",\r\n    //             \"alert\":\"No alerts\"\r\n    //         },\r\n    //         {\r\n    //             \"day\":\"Friday\",\r\n    //             \"temp\":23,\r\n    //             \"kati\":3.20,\r\n    //             \"image\":\"https://cdn-icons-png.flaticon.com/512/5213/5213385.png\",\r\n    //             \"alert\":\"No alerts\"\r\n    //         },\r\n    //         {\r\n    //             \"day\":\"Saturday\",\r\n    //             \"temp\":23,\r\n    //             \"kati\":3.20,\r\n    //             \"image\":\"https://cdn-icons-png.flaticon.com/512/1163/1163657.png\",\r\n    //             \"alert\":\"Light Hail Probability\"\r\n    //         },\r\n    //         {\r\n    //             \"day\":\"Sunday\",\r\n    //             \"temp\":21,\r\n    //             \"kati\":3.20,\r\n    //             \"image\":\"https://cdn-icons-png.flaticon.com/512/979/979585.png\",\r\n    //             \"alert\":\"No alerts\"\r\n    //         },\r\n    //         {\r\n    //             \"day\":\"Monday\",\r\n    //             \"temp\":23,\r\n    //             \"kati\":3.20,\r\n    //             \"image\":\"https://cdn-icons-png.flaticon.com/512/12607/12607703.png\",\r\n    //             \"alert\":\"No alerts\"\r\n    //         }\r\n    //     ]\r\n    // }\r\n\r\n    initCharts(separated: boolean, searchType: string) {\r\n        const documentStyle = getComputedStyle(document.documentElement);\r\n        const textColor = documentStyle.getPropertyValue('--text-color');\r\n        const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\r\n        const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\r\n        \r\n        if (separated){\r\n            \r\n            // Βρίσκουμε τις μοναδικές ημερομηνίες με σειρά\r\n            const uniqueDates = Array.from(new Set(this.energyData.data.map(d => d.dateTime)));\r\n            const uniqueDateDescriptions = Array.from(new Set(this.energyData.data.map(d => d.dateDescription)));\r\n            // Βρίσκουμε τους μοναδικούς inverters\r\n            const uniqueInverters = Array.from(new Set(this.energyData.data.map(d => d.name)));\r\n\r\n            // Δημιουργούμε datasets, ένα για κάθε inverter\r\n            const barDatasets = uniqueInverters.map(inv => {\r\n                const color = this.getRandomColor();\r\n                const data = uniqueDates.map(date => {\r\n                    const entry = this.energyData.data.find(d => d.name === inv && d.dateTime === date);\r\n                    return entry ? entry.activePower : 0;\r\n                });\r\n\r\n                return {\r\n                    label: inv,\r\n                    data: data,\r\n                    backgroundColor: color,\r\n                };\r\n            });\r\n\r\n            this.barData = {\r\n                labels: uniqueDateDescriptions.map(dt => dt), // ημερομηνίες στον Χ-άξονα\r\n                datasets: barDatasets\r\n            };\r\n\r\n            // const uniqueLineDates = Array.from(new Set(this.energyData.data.map(d => d.dateTime)));\r\n            // const uniqueLineDateDescriptions = Array.from(new Set(this.energyData.data.map(d => d.dateDescription)));\r\n            // const uniqueLineInverters = Array.from(new Set(this.energyData.data.map(d => d.name)));\r\n\r\n            // const lineDatasets = uniqueLineInverters.map(inv => {\r\n            //     const color = this.getRandomColor();\r\n            //     const data = uniqueLineDates.map(date => {\r\n            //         const entry = this.energyData.data.find(d => d.name === inv && d.dateTime === date);\r\n            //         return entry ? entry.activePower : 0;\r\n            //     });\r\n\r\n            //     return {\r\n            //         label: inv,\r\n            //         data: data,\r\n            //         borderColor: color,\r\n            //         fill: false,\r\n            //         tension: 0.3, // προαιρετικά για πιο ομαλές καμπύλες\r\n            //     };\r\n            // });\r\n\r\n            // this.lineData = {\r\n            //     labels: uniqueLineDateDescriptions, // ετικέτες στον X-άξονα\r\n            //     datasets: lineDatasets\r\n            // };\r\n            \r\n            var datasets:any[] = [];\r\n            // const uniqueInverters = Array.from(new Set(this.energyData.data.map(d => d.name)));\r\n             const activePowerByName: Record<string, number[]> = {};\r\n\r\n            this.energyData.data.forEach(d => {\r\n                if (!activePowerByName[d.name]) {\r\n                    activePowerByName[d.name] = [];\r\n                }\r\n                activePowerByName[d.name].push(d.activePower);\r\n            });\r\n            \r\n            uniqueInverters.forEach(inv => {\r\n                var color = this.getRandomColor();\r\n                datasets.push({\r\n                    label: inv,\r\n                    data: activePowerByName[inv],\r\n                    fill: false,\r\n                    backgroundColor: color,\r\n                    borderColor: color,\r\n                    tension: .4\r\n                });\r\n\r\n            });\r\n            \r\n            const firstName = this.energyData.data[0].name; // Παίρνουμε το πρώτο name\r\n            const dateTimesForFirstName = this.energyData.data\r\n            .filter(d => d.name === firstName) // Φιλτράρουμε μόνο τα αντικείμενα με το ίδιο name\r\n            .map(d => d.dateTime); // Παίρνουμε μόνο τα dateTime\r\n\r\n            this.lineData = {\r\n                labels: dateTimesForFirstName.map((dt, index) => \r\n                    index % 2 === 0 ? new Date(dt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : ''\r\n                ),\r\n                datasets: datasets\r\n            };\r\n\r\n            // this.barData = {\r\n            //     labels: this.energyData.data.map(item => item.name), // Το property \"name\" για τον X-άξονα\r\n            //     datasets: datasets\r\n            // };\r\n\r\n        }else{\r\n            this.lineData = {\r\n                labels: this.energyData.data.map((e, index) => \r\n                    index % 2 === 0 ? new Date(e.dateTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : ''\r\n                ),\r\n                datasets: [\r\n                    {\r\n                        label: 'Active Power',\r\n                        data: this.energyData.data.map(e => e.activePower),\r\n                        fill: false,\r\n                        backgroundColor: documentStyle.getPropertyValue('--primary-500'),\r\n                        borderColor: documentStyle.getPropertyValue('--primary-500'),\r\n                        tension: .4\r\n                    },\r\n                    {\r\n                        label: 'Total Input Power',\r\n                        data: this.energyData.data.map(e => e.totalInputPower),\r\n                        fill: false,\r\n                        backgroundColor: documentStyle.getPropertyValue('--primary-200'),\r\n                        borderColor: documentStyle.getPropertyValue('--primary-200'),\r\n                        tension: .4\r\n                    }\r\n                ]\r\n            };\r\n\r\n            this.barData = {\r\n                labels: this.energyData.data.map(item => item.name), // Το property \"name\" για τον X-άξονα\r\n                datasets: [\r\n                    {\r\n                        label: 'Active Power',\r\n                        data: this.energyData.data.map(e => e.activePower),\r\n                        fill: false,\r\n                        backgroundColor: documentStyle.getPropertyValue('--primary-500'),\r\n                        borderColor: documentStyle.getPropertyValue('--primary-500'),\r\n                        tension: .4\r\n                    },\r\n                    {\r\n                        label: 'Total Input Power',\r\n                        data: this.energyData.data.map(e => e.totalInputPower),\r\n                        fill: false,\r\n                        backgroundColor: documentStyle.getPropertyValue('--primary-200'),\r\n                        borderColor: documentStyle.getPropertyValue('--primary-200'),\r\n                        tension: .4\r\n                    }\r\n                ]\r\n            };\r\n        }\r\n\r\n\r\n\r\n        \r\n\r\n        this.lineOptions = {\r\n            plugins: {\r\n                legend: {\r\n                    labels: {\r\n                        fontColor: textColor\r\n                    }\r\n                }\r\n            },\r\n            scales: {\r\n                x: {\r\n                    ticks: {\r\n                        color: textColorSecondary,\r\n                        maxRotation: 0,\r\n                        minRotation: 0\r\n                    },\r\n                    grid: {\r\n                        color: surfaceBorder,\r\n                        drawBorder: false\r\n                    }\r\n                },\r\n                y: {\r\n                    ticks: {\r\n                        color: textColorSecondary\r\n                    },\r\n                    grid: {\r\n                        color: surfaceBorder,\r\n                        drawBorder: false\r\n                    }\r\n                },\r\n            }\r\n        };\r\n    }\r\n\r\n    getStationDevices(){\r\n        let request: GetStationDevicesRequest = {\r\n            stationId : this.selectedStation.id\r\n        }\r\n        this.stationsService.getStationDevices(request).then(data => {\r\n            console.log(data)\r\n            this.devices = data;\r\n            this.inverters = this.devices\r\n                .filter(device => device.type === \"StringInverter\")\r\n            console.log(\"DEBV\" + this.devices);\r\n            this.inverterIds = this.devices\r\n                .filter(device => device.type === \"StringInverter\") // Φιλτράρει μόνο τα StringInverter\r\n                .map(device => device.id) // Παίρνει μόνο τα id\r\n                .join(\",\"); // Τα ενώνει με κόμματα\r\n\r\n        this.getHistoryEnergy(this.inverterIds);\r\n        });\r\n        \r\n        \r\n    }\r\n\r\n    getStationSumData(){\r\n        let request: GetStationDevicesRequest = {\r\n            stationId : this.selectedStation.id\r\n        }\r\n        this.stationsService.getStationSumData(request).then(data => this.sumData = data);\r\n    }\r\n\r\n\r\n    getWeather(){\r\n        let request: GetWeatherRequest = {\r\n            coordinates : this.selectedStation.longitude + \",\" + this.selectedStation.latitude\r\n        }\r\n        this.weatherService.getWeather(request).then(data => this.weatherData = data);\r\n    }\r\n\r\n    getHistoryEnergy(inverterIds: string, separated: boolean = false, dateTimeFrom: Date = null, dateTimeTo: Date = null, searchType: string = null){\r\n        var now = new Date();\r\n\r\n        if (dateTimeFrom != null)\r\n            var formattedStartDate = dateTimeFrom.toISOString();\r\n        else\r\n            var formattedStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0).toISOString();\r\n\r\n        if (dateTimeTo != null)\r\n            var formattedEndDate = dateTimeTo.toISOString();\r\n        else\r\n            var formattedEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0).toISOString();\r\n\r\n\r\n        // var todayMidnightUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 0, 0, 0));\r\n        // console.log(dateTimeFrom)\r\n        // if (dateTimeFrom != null) {\r\n        //     now = new Date(dateTimeFrom);\r\n        //     todayMidnightUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 0, 0, 0));\r\n        // }\r\n\r\n        // var formattedStartDate = dateTimeFrom.toISOString();\r\n        // console.log(formattedStartDate);\r\n\r\n        // var tomorrowMidnightUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate() + 1, 0, 0, 0));\r\n        // if (dateTimeTo != null) {\r\n        //     now = new Date(dateTimeTo);\r\n        //     tomorrowMidnightUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()+1, 0, 0, 0));\r\n        // }\r\n        \r\n        \r\n\r\n\r\n        let request: GetHistoricDataRequest = {\r\n            devIds : inverterIds,\r\n            devTypeId: 1,\r\n            startDateTime: formattedStartDate,\r\n            endDateTime: formattedEndDate,\r\n            separated: separated,\r\n            searchType:searchType,\r\n            stationId: this.selectedStation.id\r\n        }\r\n        this.stationsService.getStationHistoricData(request).then(data => {\r\n            this.energyData = data\r\n            console.log(this.energyData)\r\n            if (this.energyData.data.length > 0){\r\n                this.initCharts(separated, searchType);\r\n            }else{\r\n                this.messageService.add({ severity: 'warn', summary: 'No Data', detail: 'No Data to display!' });\r\n            }\r\n        });\r\n    }\r\n\r\n    showInvertMonitoring(){\r\n        var reload = true;\r\n        if (this.showInvert)\r\n            reload = false;\r\n        \r\n        this.showGeneral = false;\r\n        this.showInvert = true;\r\n        this.showString = false;\r\n\r\n        if (reload)\r\n            this.getHistoryEnergy(this.inverterIds, true)\r\n    }\r\n\r\n    showStringMonitoring(){\r\n        this.showGeneral = false;\r\n        this.showInvert = false;\r\n        this.showString = true;\r\n    }\r\n\r\n\r\n    getRandomColor(){\r\n        const rootStyles = getComputedStyle(document.documentElement);\r\n        const cssVariables = Object.keys(rootStyles)\r\n        .map(key => rootStyles[key])\r\n        .filter(value => value.startsWith('--')) // Φιλτράρουμε μόνο τις CSS variables\r\n        .map(varName => rootStyles.getPropertyValue(varName).trim()) // Παίρνουμε τις τιμές τους\r\n        .filter(value => {\r\n            // Ελέγχουμε αν το όνομα της μεταβλητής περιέχει έναν αριθμό μεγαλύτερο του 200\r\n            const match = value.match(/(\\d+)/); // Αντιστοιχεί στον αριθμό που υπάρχει στο όνομα\r\n            return match && parseInt(match[0], 10) > 200; // Φιλτράρει μόνο χρώματα με αριθμό > 200\r\n        });\r\n        \r\n        // Συνάρτηση που επιστρέφει τυχαίο χρώμα από τη λίστα\r\n        return cssVariables[Math.floor(Math.random() * cssVariables.length)];\r\n\r\n    }\r\n\r\n    onDateChange(event: Date) {\r\n        console.log('Ημερομηνία επιλέχθηκε:', event);\r\n        var datefrom = new Date(event.setHours(0,0,0,0));\r\n        const pastDate = new Date(datefrom);\r\n        pastDate.setDate(pastDate.getDate() +1);\r\n        this.getHistoryEnergy(this.inverterIds, !this.showGeneral, datefrom, pastDate, this.searchType);\r\n\r\n    }\r\n\r\n\r\n    onTimeFrameChange(event: any): void {\r\n        console.log(event)\r\n        let currentDate = new Date();\r\n        console.log(currentDate)\r\n        let datefrom: Date;\r\n        let dateto: Date = new Date(currentDate); \r\n        \r\n        switch (event.value) {\r\n           \r\n          case 'day':\r\n            // Για την περίπτωση του \"Day\", το datefrom είναι σήμερα\r\n            datefrom = new Date(currentDate.setHours(0, 0, 0, 0)); // Ορίζουμε το time στα 00:00:00\r\n            this.searchType = null;\r\n            if (event.value === 'day') {\r\n                this.selectedDate = new Date(); // reset σε σημερινή ημερομηνία\r\n            }\r\n            break;\r\n      \r\n          case 'month':\r\n            // Για την περίπτωση του \"Month\", το datefrom είναι η 1η μέρα του τρέχοντος μήνα\r\n            datefrom = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);\r\n            datefrom.setHours(0,0,0,0);\r\n            this.searchType = \"monthly\";\r\n            break;\r\n      \r\n          case 'year':\r\n            // Για την περίπτωση του \"Year\", το datefrom είναι η 1η Ιανουαρίου του τρέχοντος έτους\r\n            datefrom = new Date(currentDate.getFullYear(), 0, 1);\r\n            datefrom.setHours(0,0,0,0);\r\n            this.searchType = \"yearly\";\r\n            break;\r\n      \r\n          case 'last30days':\r\n            // Για την περίπτωση του \"Last 30 days\", το datefrom είναι 30 μέρες πριν\r\n            const pastDate = new Date(currentDate);\r\n            pastDate.setDate(pastDate.getDate() - 30);\r\n            pastDate.setHours(0, 0, 0, 0);\r\n            datefrom = pastDate;\r\n            this.searchType = \"monthly\";\r\n            break;\r\n      \r\n          case 'last365days':\r\n            // Για την περίπτωση του \"Last 365 days\", το datefrom είναι 365 μέρες πριν\r\n            const pastDate2 = new Date(currentDate);\r\n            pastDate2.setDate(pastDate2.getDate() - 365);\r\n            pastDate2.setHours(0, 0, 0, 0);\r\n            datefrom = pastDate2;\r\n            this.searchType = \"yearly\";\r\n            break;\r\n      \r\n          case 'thisYear':\r\n            // Για την περίπτωση του \"This year (1/1 - today)\", το datefrom είναι η 1η Ιανουαρίου του τρέχοντος έτους\r\n            datefrom = new Date(currentDate.getFullYear(), 0, 1);\r\n            datefrom.setHours(0,0,0,0);\r\n            this.searchType = \"yearly\";\r\n            break;\r\n      \r\n          case 'fromBeginning':\r\n            // Για την περίπτωση του \"From the beginning\", το datefrom μπορεί να είναι η αρχή της εφαρμογής ή άλλη προεπιλεγμένη ημερομηνία\r\n            // Εδώ χρησιμοποιούμε την αρχική ημερομηνία της εφαρμογής ή άλλη ημερομηνία\r\n            datefrom = new Date(2000, 0, 1); // Ή οποιαδήποτε άλλη ημερομηνία αρχής\r\n            this.searchType = \"lifetime\";\r\n            break;\r\n      \r\n          default:\r\n              \r\n            // Αν δεν είναι καμία από τις παραπάνω επιλογές, ορίζουμε μια προεπιλεγμένη τιμή\r\n            datefrom = new Date(currentDate.setHours(0, 0, 0, 0));\r\n            break;\r\n        }\r\n      \r\n        // Καλούμε τη συνάρτηση getHistoryEnergy με τα ορίσματα\r\n        this.getHistoryEnergy(this.inverterIds, !this.showGeneral, datefrom, dateto, this.searchType);\r\n      }\r\n\r\n\r\n\r\n    onGlobalFilter(table: any, event: Event) {\r\n        table.filterGlobal((event.target as HTMLInputElement).value, 'contains');\r\n    }\r\n\r\n    ngOnDestroy() {\r\n        if (this.subscription) {\r\n            this.subscription.unsubscribe();\r\n        }\r\n    }\r\n}\r\n", "<div class=\"grid p-fluid\" *ngIf=\"selectedStation\">\r\n    <!-- Modern Breadcrumb -->\r\n    <div class=\"col-12\">\r\n        <app-modern-breadcrumb\r\n            [items]=\"[\r\n                { label: 'Stations', routerLink: '/app/index', icon: 'solar-panel' },\r\n                { label: selectedStation.name, icon: 'building' }\r\n            ]\">\r\n        </app-modern-breadcrumb>\r\n    </div>\r\n\r\n    <!-- Sidebar -->\r\n    <div class=\"col-12 lg:col-3\">\r\n        <!-- Station Info Card -->\r\n        <p-card class=\"mb-3\">\r\n            <ng-template pTemplate=\"header\">\r\n                <div class=\"bg-primary-50 p-3 border-round-top\">\r\n                    <div class=\"flex align-items-center gap-2\">\r\n                        <fa-icon icon=\"solar-panel\" class=\"text-primary text-xl\"></fa-icon>\r\n                        <h3 class=\"m-0 text-primary\">{{selectedStation.name}}</h3>\r\n                    </div>\r\n                </div>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"content\">\r\n                <div class=\"space-y-3\">\r\n                    <!-- Location -->\r\n                    <div class=\"flex align-items-start gap-2\">\r\n                        <fa-icon icon=\"map-marker-alt\" class=\"text-600 mt-1\"></fa-icon>\r\n                        <span class=\"text-600 text-sm\" style=\"word-break: break-word;\">{{selectedStation.address}}</span>\r\n                    </div>\r\n\r\n                    <p-divider></p-divider>\r\n\r\n                    <!-- Equipment Stats -->\r\n                    <div class=\"grid\">\r\n                        <div class=\"col-6\">\r\n                            <div class=\"text-center\">\r\n                                <div class=\"text-2xl font-bold text-primary\">{{inverters.length}}</div>\r\n                                <div class=\"text-xs text-600\">Inverters</div>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"col-6\">\r\n                            <div class=\"text-center\">\r\n                                <div class=\"text-2xl font-bold text-orange-500\">{{devices.length}}</div>\r\n                                <div class=\"text-xs text-600\">Total Devices</div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <p-divider></p-divider>\r\n\r\n                    <!-- Monitoring Navigation -->\r\n                    <div class=\"space-y-2\">\r\n                        <h4 class=\"text-900 font-semibold mb-2\">Monitoring Views</h4>\r\n                        <p-button label=\"General Overview\"\r\n                                  icon=\"pi pi-chart-line\"\r\n                                  [severity]=\"showGeneral ? 'primary' : 'secondary'\"\r\n                                  [text]=\"!showGeneral\"\r\n                                  size=\"small\"\r\n                                  class=\"w-full justify-content-start\"\r\n                                  (click)=\"showGeneral = true; showInvert = false; showString = false\">\r\n                        </p-button>\r\n                        <p-button label=\"Inverter Monitoring\"\r\n                                  icon=\"pi pi-cog\"\r\n                                  [severity]=\"showInvert ? 'primary' : 'secondary'\"\r\n                                  [text]=\"!showInvert\"\r\n                                  size=\"small\"\r\n                                  class=\"w-full justify-content-start\"\r\n                                  (click)=\"showInvertMonitoring()\">\r\n                        </p-button>\r\n                        <p-button label=\"String Monitoring\"\r\n                                  icon=\"pi pi-sitemap\"\r\n                                  [severity]=\"showString ? 'primary' : 'secondary'\"\r\n                                  [text]=\"!showString\"\r\n                                  size=\"small\"\r\n                                  class=\"w-full justify-content-start\"\r\n                                  (click)=\"showStringMonitoring()\">\r\n                        </p-button>\r\n                    </div>\r\n                </div>\r\n            </ng-template>\r\n        </p-card>\r\n\r\n        <!-- Performance Summary Card -->\r\n        <p-card *ngIf=\"sumData\">\r\n            <ng-template pTemplate=\"header\">\r\n                <div class=\"bg-green-50 p-3 border-round-top\">\r\n                    <div class=\"flex align-items-center gap-2\">\r\n                        <fa-icon icon=\"chart-bar\" class=\"text-green-600 text-lg\"></fa-icon>\r\n                        <h4 class=\"m-0 text-green-800\">Performance Summary</h4>\r\n                    </div>\r\n                </div>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"content\">\r\n                <div class=\"grid\">\r\n                    <!-- Day Power -->\r\n                    <div class=\"col-12 mb-2\">\r\n                        <div class=\"flex align-items-center justify-content-between\">\r\n                            <div class=\"flex align-items-center gap-2\">\r\n                                <fa-icon icon=\"bolt\" class=\"text-orange-500\"></fa-icon>\r\n                                <span class=\"text-sm text-600\">Day Power</span>\r\n                            </div>\r\n                            <span class=\"font-bold\">{{ sumData.dayPower }} kW</span>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- Total Power -->\r\n                    <div class=\"col-12 mb-2\">\r\n                        <div class=\"flex align-items-center justify-content-between\">\r\n                            <div class=\"flex align-items-center gap-2\">\r\n                                <fa-icon icon=\"chart-line\" class=\"text-blue-500\"></fa-icon>\r\n                                <span class=\"text-sm text-600\">Total Power</span>\r\n                            </div>\r\n                            <span class=\"font-bold\">{{ sumData.totalPower }} kW</span>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- Day Income -->\r\n                    <div class=\"col-12 mb-2\">\r\n                        <div class=\"flex align-items-center justify-content-between\">\r\n                            <div class=\"flex align-items-center gap-2\">\r\n                                <fa-icon icon=\"euro-sign\" class=\"text-green-500\"></fa-icon>\r\n                                <span class=\"text-sm text-600\">Day Income</span>\r\n                            </div>\r\n                            <span class=\"font-bold\">€{{ sumData.dayIncome }}</span>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- Month Power -->\r\n                    <div class=\"col-12 mb-2\">\r\n                        <div class=\"flex align-items-center justify-content-between\">\r\n                            <div class=\"flex align-items-center gap-2\">\r\n                                <fa-icon icon=\"calendar-alt\" class=\"text-purple-500\"></fa-icon>\r\n                                <span class=\"text-sm text-600\">Month Power</span>\r\n                            </div>\r\n                            <span class=\"font-bold\">{{ sumData.monthPower }} kW</span>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- Day On Grid Energy -->\r\n                    <div class=\"col-12 mb-2\">\r\n                        <div class=\"flex align-items-center justify-content-between\">\r\n                            <div class=\"flex align-items-center gap-2\">\r\n                                <fa-icon icon=\"plug\" class=\"text-cyan-500\"></fa-icon>\r\n                                <span class=\"text-sm text-600\">Day Grid Energy</span>\r\n                            </div>\r\n                            <span class=\"font-bold\">{{ sumData.dayOnGridEnergy }} kWh</span>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- Day Use Energy -->\r\n                    <div class=\"col-12 mb-2\">\r\n                        <div class=\"flex align-items-center justify-content-between\">\r\n                            <div class=\"flex align-items-center gap-2\">\r\n                                <fa-icon icon=\"battery-half\" class=\"text-yellow-500\"></fa-icon>\r\n                                <span class=\"text-sm text-600\">Day Use Energy</span>\r\n                            </div>\r\n                            <span class=\"font-bold\">{{ sumData.dayUseEnergy }} kWh</span>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- Total Income -->\r\n                    <div class=\"col-12\">\r\n                        <div class=\"flex align-items-center justify-content-between\">\r\n                            <div class=\"flex align-items-center gap-2\">\r\n                                <fa-icon icon=\"coins\" class=\"text-green-600\"></fa-icon>\r\n                                <span class=\"text-sm text-600\">Total Income</span>\r\n                            </div>\r\n                            <span class=\"font-bold text-green-600\">€{{ sumData.totalIncome }}</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </ng-template>\r\n        </p-card>\r\n    </div>\r\n\r\n    <!-- Main Content Area -->\r\n    <div class=\"col-12 lg:col-9\" *ngIf=\"showGeneral\">\r\n        <!-- Weather Forecast Card -->\r\n        <p-card *ngIf=\"weatherData\" class=\"mb-3\">\r\n            <ng-template pTemplate=\"header\">\r\n                <div class=\"bg-blue-50 p-3 border-round-top\">\r\n                    <div class=\"flex align-items-center gap-2\">\r\n                        <fa-icon icon=\"cloud-sun\" class=\"text-blue-600 text-lg\"></fa-icon>\r\n                        <h4 class=\"m-0 text-blue-800\">Weather Forecast</h4>\r\n                    </div>\r\n                </div>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"content\">\r\n                <div class=\"grid\">\r\n                    <div *ngFor=\"let day of weatherData.weatherInfo\" class=\"col-12 md:col-6 lg:col-3 xl:col\">\r\n                        <div class=\"text-center p-3 border-round hover:bg-blue-50 transition-colors transition-duration-150\">\r\n                            <h5 class=\"text-900 font-semibold mb-2\">{{day.day}}</h5>\r\n                            <img [src]=\"day.icon\" height=\"50\" class=\"mb-2\"/>\r\n                            <p class=\"text-600 text-sm mb-2\">{{day.description}}</p>\r\n                            <div class=\"flex align-items-center justify-content-center gap-2 mb-1\">\r\n                                <fa-icon icon=\"arrow-down\" class=\"text-blue-500 text-xs\"></fa-icon>\r\n                                <span class=\"text-sm\">{{day.minTemperatureCelsius}}°</span>\r\n                                <fa-icon icon=\"arrow-up\" class=\"text-red-500 text-xs\"></fa-icon>\r\n                                <span class=\"text-sm\">{{day.maxTemperatureCelsius}}°</span>\r\n                            </div>\r\n                            <div class=\"flex align-items-center justify-content-center gap-1\">\r\n                                <fa-icon icon=\"wind\" class=\"text-cyan-500 text-xs\"></fa-icon>\r\n                                <span class=\"text-xs text-600\">{{day.windKph}}km/h</span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </ng-template>\r\n        </p-card>\r\n    <br>\r\n        <!-- Energy Performance Chart Card -->\r\n        <p-card>\r\n            <ng-template pTemplate=\"header\">\r\n                <div class=\"bg-green-50 p-3 border-round-top\">\r\n                    <div class=\"flex align-items-center justify-content-between\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            <fa-icon icon=\"chart-line\" class=\"text-green-600 text-lg\"></fa-icon>\r\n                            <h4 class=\"m-0 text-green-800\">Energy Performance</h4>\r\n                        </div>\r\n                        <p-tag value=\"Live Data\" severity=\"success\" icon=\"pi pi-circle-fill\"></p-tag>\r\n                    </div>\r\n                </div>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"content\">\r\n                <!-- Controls -->\r\n                <div class=\"flex flex-column md:flex-row gap-3 mb-4\">\r\n                    <div class=\"flex-1\">\r\n                        <label class=\"block text-sm font-medium text-600 mb-2\">Time Frame</label>\r\n                        <p-dropdown [options]=\"timeFrames\"\r\n                                   [(ngModel)]=\"selectedTimeFrame\"\r\n                                   placeholder=\"Select TimeFrame\"\r\n                                   [showClear]=\"true\"\r\n                                   optionLabel=\"label\"\r\n                                   optionValue=\"id\"\r\n                                   (onChange)=\"onTimeFrameChange($event)\"\r\n                                   class=\"w-full\">\r\n                        </p-dropdown>\r\n                    </div>\r\n\r\n                    <!-- Date picker for day selection -->\r\n                    <div *ngIf=\"selectedTimeFrame === 'day'\" class=\"flex-1\">\r\n                        <label class=\"block text-sm font-medium text-600 mb-2\">Select Date</label>\r\n                        <p-calendar [(ngModel)]=\"selectedDate\"\r\n                                   [showIcon]=\"true\"\r\n                                   (onSelect)=\"onDateChange($event)\"\r\n                                   dateFormat=\"dd/mm/yy\"\r\n                                   class=\"w-full\">\r\n                        </p-calendar>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- Chart -->\r\n                <div class=\"border-round border-1 surface-border p-3\">\r\n                    <p-chart type=\"line\"\r\n                             *ngIf=\"searchType == null\"\r\n                             [data]=\"lineData\"\r\n                             [options]=\"lineOptions\"\r\n                             [height]=\"400\">\r\n                    </p-chart>\r\n                    <p-chart type=\"bar\"\r\n                             *ngIf=\"searchType != null\"\r\n                             [data]=\"barData\"\r\n                             [options]=\"lineOptions\"\r\n                             [height]=\"400\">\r\n                    </p-chart>\r\n                </div>\r\n            </ng-template>\r\n        </p-card>\r\n    </div>\r\n    <!-- Inverter Monitoring -->\r\n    <div class=\"col-12 lg:col-9\" *ngIf=\"showInvert\">\r\n        <p-card>\r\n            <ng-template pTemplate=\"header\">\r\n                <div class=\"bg-orange-50 p-3 border-round-top\">\r\n                    <div class=\"flex align-items-center justify-content-between\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            <fa-icon icon=\"cog\" class=\"text-orange-600 text-lg\"></fa-icon>\r\n                            <h4 class=\"m-0 text-orange-800\">Inverter Power Monitoring</h4>\r\n                        </div>\r\n                        <p-tag value=\"Real-time\" severity=\"warning\" icon=\"pi pi-circle-fill\"></p-tag>\r\n                    </div>\r\n                </div>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"content\">\r\n                <!-- Controls -->\r\n                <div class=\"flex flex-column md:flex-row gap-3 mb-4\">\r\n                    <div class=\"flex-1\">\r\n                        <label class=\"block text-sm font-medium text-600 mb-2\">Time Frame</label>\r\n                        <p-dropdown [options]=\"timeFrames\"\r\n                                   [(ngModel)]=\"selectedTimeFrame\"\r\n                                   placeholder=\"Select TimeFrame\"\r\n                                   [showClear]=\"true\"\r\n                                   optionLabel=\"label\"\r\n                                   optionValue=\"id\"\r\n                                   (onChange)=\"onTimeFrameChange($event)\"\r\n                                   class=\"w-full\">\r\n                        </p-dropdown>\r\n                    </div>\r\n\r\n                    <div *ngIf=\"selectedTimeFrame === 'day'\" class=\"flex-1\">\r\n                        <label class=\"block text-sm font-medium text-600 mb-2\">Select Date</label>\r\n                        <p-calendar [(ngModel)]=\"selectedDate\"\r\n                                   [showIcon]=\"true\"\r\n                                   (onSelect)=\"onDateChange($event)\"\r\n                                   dateFormat=\"dd/mm/yy\"\r\n                                   class=\"w-full\">\r\n                        </p-calendar>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- Chart -->\r\n                <div class=\"border-round border-1 surface-border p-3 mb-4\">\r\n                    <p-chart type=\"line\"\r\n                             *ngIf=\"searchType == null\"\r\n                             [data]=\"lineData\"\r\n                             [options]=\"lineOptions\"\r\n                             [height]=\"300\">\r\n                    </p-chart>\r\n                    <p-chart type=\"bar\"\r\n                             *ngIf=\"searchType != null\"\r\n                             [data]=\"barData\"\r\n                             [options]=\"lineOptions\"\r\n                             [height]=\"300\">\r\n                    </p-chart>\r\n                </div>\r\n\r\n                <!-- Inverter Data Table -->\r\n                <div class=\"border-round border-1 surface-border\">\r\n                    <p-toast></p-toast>\r\n\r\n                    <!-- Table Toolbar -->\r\n                    <div class=\"bg-gray-50 p-3 border-round-top border-bottom-1 surface-border\">\r\n                        <div class=\"flex flex-column md:flex-row md:justify-content-between md:align-items-center gap-3\">\r\n                            <div class=\"flex align-items-center gap-2\">\r\n                                <fa-icon icon=\"table\" class=\"text-600\"></fa-icon>\r\n                                <h5 class=\"m-0 text-900\">Inverter Performance Data</h5>\r\n                            </div>\r\n\r\n                            <div class=\"flex gap-2\">\r\n                                <span class=\"p-input-icon-left\">\r\n                                    <i class=\"pi pi-search\"></i>\r\n                                    <input pInputText\r\n                                           type=\"text\"\r\n                                           (input)=\"onGlobalFilter(dt, $event)\"\r\n                                           placeholder=\"Search inverters...\"\r\n                                           class=\"w-full sm:w-auto\"/>\r\n                                </span>\r\n                                <p-button icon=\"pi pi-download\"\r\n                                          severity=\"help\"\r\n                                          size=\"small\"\r\n                                          pTooltip=\"Export to CSV\"\r\n                                          (click)=\"dt.exportCSV()\">\r\n                                </p-button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- Data Table -->\r\n                    <p-table #dt\r\n                             [value]=\"invertpower\"\r\n                             [columns]=\"cols\"\r\n                             responsiveLayout=\"scroll\"\r\n                             [rows]=\"10\"\r\n                             [globalFilterFields]=\"['name','nominaloutput','capacity','acpower','totalenergy','performance']\"\r\n                             [paginator]=\"true\"\r\n                             [rowsPerPageOptions]=\"[10,20,30]\"\r\n                             [showCurrentPageReport]=\"true\"\r\n                             currentPageReportTemplate=\"Showing {first} to {last} of {totalRecords} inverters\"\r\n                             [(selection)]=\"selectedProducts\"\r\n                             selectionMode=\"multiple\"\r\n                             [rowHover]=\"true\"\r\n                             dataKey=\"id\"\r\n                             styleClass=\"p-datatable-sm\">\r\n\r\n                        <ng-template pTemplate=\"header\">\r\n                            <tr>\r\n                                <th style=\"width: 3rem\">\r\n                                    <p-tableHeaderCheckbox></p-tableHeaderCheckbox>\r\n                                </th>\r\n                                <th pSortableColumn=\"name\">\r\n                                    <div class=\"flex align-items-center gap-1\">\r\n                                        <fa-icon icon=\"microchip\" class=\"text-xs\"></fa-icon>\r\n                                        <span>Inverter Name</span>\r\n                                        <p-sortIcon field=\"name\"></p-sortIcon>\r\n                                    </div>\r\n                                </th>\r\n                                <th pSortableColumn=\"nominaloutput\">\r\n                                    <div class=\"flex align-items-center gap-1\">\r\n                                        <fa-icon icon=\"bolt\" class=\"text-xs\"></fa-icon>\r\n                                        <span>Nominal Output</span>\r\n                                        <p-sortIcon field=\"nominaloutput\"></p-sortIcon>\r\n                                    </div>\r\n                                </th>\r\n                                <th pSortableColumn=\"capacity\">\r\n                                    <div class=\"flex align-items-center gap-1\">\r\n                                        <fa-icon icon=\"battery-full\" class=\"text-xs\"></fa-icon>\r\n                                        <span>Capacity</span>\r\n                                        <p-sortIcon field=\"capacity\"></p-sortIcon>\r\n                                    </div>\r\n                                </th>\r\n                                <th pSortableColumn=\"acpower\">\r\n                                    <div class=\"flex align-items-center gap-1\">\r\n                                        <fa-icon icon=\"plug\" class=\"text-xs\"></fa-icon>\r\n                                        <span>AC Power</span>\r\n                                        <p-sortIcon field=\"acpower\"></p-sortIcon>\r\n                                    </div>\r\n                                </th>\r\n                                <th pSortableColumn=\"totalenergy\">\r\n                                    <div class=\"flex align-items-center gap-1\">\r\n                                        <fa-icon icon=\"chart-area\" class=\"text-xs\"></fa-icon>\r\n                                        <span>Total Energy</span>\r\n                                        <p-sortIcon field=\"totalenergy\"></p-sortIcon>\r\n                                    </div>\r\n                                </th>\r\n                                <th pSortableColumn=\"performance\">\r\n                                    <div class=\"flex align-items-center gap-1\">\r\n                                        <fa-icon icon=\"tachometer-alt\" class=\"text-xs\"></fa-icon>\r\n                                        <span>Performance</span>\r\n                                        <p-sortIcon field=\"performance\"></p-sortIcon>\r\n                                    </div>\r\n                                </th>\r\n                            </tr>\r\n                        </ng-template>\r\n\r\n                        <ng-template pTemplate=\"body\" let-invert>\r\n                            <tr>\r\n                                <td>\r\n                                    <p-tableCheckbox [value]=\"invert\"></p-tableCheckbox>\r\n                                </td>\r\n                                <td>\r\n                                    <div class=\"flex align-items-center gap-2\">\r\n                                        <fa-icon icon=\"microchip\" class=\"text-primary\"></fa-icon>\r\n                                        <span class=\"font-medium\">{{invert.name}}</span>\r\n                                    </div>\r\n                                </td>\r\n                                <td>\r\n                                    <p-tag [value]=\"invert.nominaloutput + ' kW'\" severity=\"info\"></p-tag>\r\n                                </td>\r\n                                <td>\r\n                                    <span class=\"font-medium\">{{invert.capacity}} kW</span>\r\n                                </td>\r\n                                <td>\r\n                                    <span class=\"font-medium text-orange-600\">{{invert.acpower}} kW</span>\r\n                                </td>\r\n                                <td>\r\n                                    <span class=\"font-medium\">{{invert.totalenergy}} kWh</span>\r\n                                </td>\r\n                                <td>\r\n                                    <div class=\"flex align-items-center gap-2\">\r\n                                        <p-tag [value]=\"invert.performance + '%'\"\r\n                                               [severity]=\"invert.performance > 80 ? 'success' : invert.performance > 60 ? 'warning' : 'danger'\">\r\n                                        </p-tag>\r\n                                    </div>\r\n                                </td>\r\n                            </tr>\r\n                        </ng-template>\r\n                    </p-table>\r\n                </div>\r\n            </ng-template>\r\n        </p-card>\r\n    </div>\r\n\r\n    <!-- String Monitoring -->\r\n    <div class=\"col-12 lg:col-9\" *ngIf=\"showString\">\r\n        <p-card>\r\n            <ng-template pTemplate=\"header\">\r\n                <div class=\"bg-purple-50 p-3 border-round-top\">\r\n                    <div class=\"flex align-items-center justify-content-between\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            <fa-icon icon=\"sitemap\" class=\"text-purple-600 text-lg\"></fa-icon>\r\n                            <h4 class=\"m-0 text-purple-800\">String Current Monitoring</h4>\r\n                        </div>\r\n                        <p-tag value=\"Live Data\" severity=\"info\" icon=\"pi pi-circle-fill\"></p-tag>\r\n                    </div>\r\n                </div>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"content\">\r\n                <!-- Chart -->\r\n                <div class=\"border-round border-1 surface-border p-3 mb-4\">\r\n                    <p-chart type=\"line\"\r\n                             [data]=\"lineData\"\r\n                             [options]=\"lineOptions\"\r\n                             [height]=\"300\">\r\n                    </p-chart>\r\n                </div>\r\n\r\n                <!-- String Data Table -->\r\n                <div class=\"border-round border-1 surface-border\">\r\n                    <p-toast></p-toast>\r\n\r\n                    <!-- Table Toolbar -->\r\n                    <div class=\"bg-gray-50 p-3 border-round-top border-bottom-1 surface-border\">\r\n                        <div class=\"flex flex-column md:flex-row md:justify-content-between md:align-items-center gap-3\">\r\n                            <div class=\"flex align-items-center gap-2\">\r\n                                <fa-icon icon=\"table\" class=\"text-600\"></fa-icon>\r\n                                <h5 class=\"m-0 text-900\">String Performance Data</h5>\r\n                            </div>\r\n\r\n                            <div class=\"flex gap-2\">\r\n                                <span class=\"p-input-icon-left\">\r\n                                    <i class=\"pi pi-search\"></i>\r\n                                    <input pInputText\r\n                                           type=\"text\"\r\n                                           (input)=\"onGlobalFilter(dt2, $event)\"\r\n                                           placeholder=\"Search strings...\"\r\n                                           class=\"w-full sm:w-auto\"/>\r\n                                </span>\r\n                                <p-button icon=\"pi pi-download\"\r\n                                          severity=\"help\"\r\n                                          size=\"small\"\r\n                                          pTooltip=\"Export to CSV\"\r\n                                          (click)=\"dt2.exportCSV()\">\r\n                                </p-button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- Data Table -->\r\n                    <p-table #dt2\r\n                             [value]=\"invertpower\"\r\n                             [columns]=\"cols\"\r\n                             responsiveLayout=\"scroll\"\r\n                             [rows]=\"10\"\r\n                             [globalFilterFields]=\"['name','nominaloutput','capacity','acpower','totalenergy','performance']\"\r\n                             [paginator]=\"true\"\r\n                             [rowsPerPageOptions]=\"[10,20,30]\"\r\n                             [showCurrentPageReport]=\"true\"\r\n                             currentPageReportTemplate=\"Showing {first} to {last} of {totalRecords} strings\"\r\n                             [(selection)]=\"selectedProducts\"\r\n                             selectionMode=\"multiple\"\r\n                             [rowHover]=\"true\"\r\n                             dataKey=\"id\"\r\n                             styleClass=\"p-datatable-sm\">\r\n\r\n                        <ng-template pTemplate=\"header\">\r\n                            <tr>\r\n                                <th style=\"width: 3rem\">\r\n                                    <p-tableHeaderCheckbox></p-tableHeaderCheckbox>\r\n                                </th>\r\n                                <th pSortableColumn=\"name\">\r\n                                    <div class=\"flex align-items-center gap-1\">\r\n                                        <fa-icon icon=\"link\" class=\"text-xs\"></fa-icon>\r\n                                        <span>String Name</span>\r\n                                        <p-sortIcon field=\"name\"></p-sortIcon>\r\n                                    </div>\r\n                                </th>\r\n                                <th pSortableColumn=\"nominaloutput\">\r\n                                    <div class=\"flex align-items-center gap-1\">\r\n                                        <fa-icon icon=\"bolt\" class=\"text-xs\"></fa-icon>\r\n                                        <span>Current (A)</span>\r\n                                        <p-sortIcon field=\"nominaloutput\"></p-sortIcon>\r\n                                    </div>\r\n                                </th>\r\n                                <th pSortableColumn=\"capacity\">\r\n                                    <div class=\"flex align-items-center gap-1\">\r\n                                        <fa-icon icon=\"battery-full\" class=\"text-xs\"></fa-icon>\r\n                                        <span>Voltage (V)</span>\r\n                                        <p-sortIcon field=\"capacity\"></p-sortIcon>\r\n                                    </div>\r\n                                </th>\r\n                                <th pSortableColumn=\"acpower\">\r\n                                    <div class=\"flex align-items-center gap-1\">\r\n                                        <fa-icon icon=\"plug\" class=\"text-xs\"></fa-icon>\r\n                                        <span>Power (W)</span>\r\n                                        <p-sortIcon field=\"acpower\"></p-sortIcon>\r\n                                    </div>\r\n                                </th>\r\n                                <th pSortableColumn=\"totalenergy\">\r\n                                    <div class=\"flex align-items-center gap-1\">\r\n                                        <fa-icon icon=\"chart-area\" class=\"text-xs\"></fa-icon>\r\n                                        <span>Total Energy</span>\r\n                                        <p-sortIcon field=\"totalenergy\"></p-sortIcon>\r\n                                    </div>\r\n                                </th>\r\n                                <th pSortableColumn=\"performance\">\r\n                                    <div class=\"flex align-items-center gap-1\">\r\n                                        <fa-icon icon=\"tachometer-alt\" class=\"text-xs\"></fa-icon>\r\n                                        <span>Status</span>\r\n                                        <p-sortIcon field=\"performance\"></p-sortIcon>\r\n                                    </div>\r\n                                </th>\r\n                            </tr>\r\n                        </ng-template>\r\n\r\n                        <ng-template pTemplate=\"body\" let-invert>\r\n                            <tr>\r\n                                <td>\r\n                                    <p-tableCheckbox [value]=\"invert\"></p-tableCheckbox>\r\n                                </td>\r\n                                <td>\r\n                                    <div class=\"flex align-items-center gap-2\">\r\n                                        <fa-icon icon=\"link\" class=\"text-purple-600\"></fa-icon>\r\n                                        <span class=\"font-medium\">{{invert.name}}</span>\r\n                                    </div>\r\n                                </td>\r\n                                <td>\r\n                                    <span class=\"font-medium text-blue-600\">{{invert.nominaloutput}} A</span>\r\n                                </td>\r\n                                <td>\r\n                                    <span class=\"font-medium\">{{invert.capacity}} V</span>\r\n                                </td>\r\n                                <td>\r\n                                    <span class=\"font-medium text-orange-600\">{{invert.acpower}} W</span>\r\n                                </td>\r\n                                <td>\r\n                                    <span class=\"font-medium\">{{invert.totalenergy}} kWh</span>\r\n                                </td>\r\n                                <td>\r\n                                    <div class=\"flex align-items-center gap-2\">\r\n                                        <p-tag [value]=\"invert.performance > 80 ? 'Normal' : invert.performance > 60 ? 'Warning' : 'Critical'\"\r\n                                               [severity]=\"invert.performance > 80 ? 'success' : invert.performance > 60 ? 'warning' : 'danger'\"\r\n                                               icon=\"pi pi-circle-fill\">\r\n                                        </p-tag>\r\n                                    </div>\r\n                                </td>\r\n                            </tr>\r\n                        </ng-template>\r\n                    </p-table>\r\n                </div>\r\n            </ng-template>\r\n        </p-card>\r\n    </div>\r\n</div>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;ICgBgBA,EAAA,CAAAC,cAAA,cAAgD;IAExCD,EAAA,CAAAE,SAAA,kBAAmE;IACnEF,EAAA,CAAAC,cAAA,aAA6B;IAAAD,EAAA,CAAAG,MAAA,GAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;IAA7BJ,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,eAAA,CAAAC,IAAA,CAAwB;;;;;;IAM7DT,EAAA,CAAAC,cAAA,cAAuB;IAGfD,EAAA,CAAAE,SAAA,kBAA+D;IAC/DF,EAAA,CAAAC,cAAA,eAA+D;IAAAD,EAAA,CAAAG,MAAA,GAA2B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGrGJ,EAAA,CAAAE,SAAA,gBAAuB;IAGvBF,EAAA,CAAAC,cAAA,cAAkB;IAGuCD,EAAA,CAAAG,MAAA,IAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACvEJ,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAGrDJ,EAAA,CAAAC,cAAA,eAAmB;IAEqCD,EAAA,CAAAG,MAAA,IAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACxEJ,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAK7DJ,EAAA,CAAAE,SAAA,iBAAuB;IAGvBF,EAAA,CAAAC,cAAA,eAAuB;IACqBD,EAAA,CAAAG,MAAA,wBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC7DJ,EAAA,CAAAC,cAAA,oBAM+E;IAArED,EAAA,CAAAU,UAAA,mBAAAC,6EAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAAD,MAAA,CAAAE,WAAA,GAAuB,IAAI;MAAAF,MAAA,CAAAG,UAAA,GAAe,KAAK;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAAJ,MAAA,CAAAK,UAAA,GAAe,KAAK;IAAA,EAAC;IAC9EnB,EAAA,CAAAI,YAAA,EAAW;IACXJ,EAAA,CAAAC,cAAA,oBAM2C;IAAjCD,EAAA,CAAAU,UAAA,mBAAAU,6EAAA;MAAApB,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAQ,MAAA,GAAArB,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAkB,WAAA,CAAAG,MAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAC1CtB,EAAA,CAAAI,YAAA,EAAW;IACXJ,EAAA,CAAAC,cAAA,oBAM2C;IAAjCD,EAAA,CAAAU,UAAA,mBAAAa,6EAAA;MAAAvB,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAW,OAAA,GAAAxB,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAkB,WAAA,CAAAM,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAC1CzB,EAAA,CAAAI,YAAA,EAAW;;;;IAjDoDJ,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAM,iBAAA,CAAAoB,MAAA,CAAAlB,eAAA,CAAAmB,OAAA,CAA2B;IASrC3B,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAM,iBAAA,CAAAoB,MAAA,CAAAE,SAAA,CAAAC,MAAA,CAAoB;IAMjB7B,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,iBAAA,CAAAoB,MAAA,CAAAI,OAAA,CAAAD,MAAA,CAAkB;IAahE7B,EAAA,CAAAK,SAAA,GAAkD;IAAlDL,EAAA,CAAA+B,UAAA,aAAAL,MAAA,CAAAV,WAAA,2BAAkD,UAAAU,MAAA,CAAAV,WAAA;IAQlDhB,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAA+B,UAAA,aAAAL,MAAA,CAAAT,UAAA,2BAAiD,UAAAS,MAAA,CAAAT,UAAA;IAQjDjB,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAA+B,UAAA,aAAAL,MAAA,CAAAP,UAAA,2BAAiD,UAAAO,MAAA,CAAAP,UAAA;;;;;IAcnEnB,EAAA,CAAAC,cAAA,cAA8C;IAEtCD,EAAA,CAAAE,SAAA,kBAAmE;IACnEF,EAAA,CAAAC,cAAA,aAA+B;IAAAD,EAAA,CAAAG,MAAA,0BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IAM/DJ,EAAA,CAAAC,cAAA,cAAkB;IAKFD,EAAA,CAAAE,SAAA,kBAAuD;IACvDF,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAG,MAAA,gBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEnDJ,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAG,MAAA,GAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAKhEJ,EAAA,CAAAC,cAAA,cAAyB;IAGbD,EAAA,CAAAE,SAAA,mBAA2D;IAC3DF,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAErDJ,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAG,MAAA,IAA2B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAKlEJ,EAAA,CAAAC,cAAA,eAAyB;IAGbD,EAAA,CAAAE,SAAA,mBAA2D;IAC3DF,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEpDJ,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAG,MAAA,IAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAK/DJ,EAAA,CAAAC,cAAA,eAAyB;IAGbD,EAAA,CAAAE,SAAA,mBAA+D;IAC/DF,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAErDJ,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAG,MAAA,IAA2B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAKlEJ,EAAA,CAAAC,cAAA,eAAyB;IAGbD,EAAA,CAAAE,SAAA,mBAAqD;IACrDF,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAG,MAAA,uBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEzDJ,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAG,MAAA,IAAiC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAKxEJ,EAAA,CAAAC,cAAA,eAAyB;IAGbD,EAAA,CAAAE,SAAA,mBAA+D;IAC/DF,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAG,MAAA,sBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAExDJ,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAG,MAAA,IAA8B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAKrEJ,EAAA,CAAAC,cAAA,cAAoB;IAGRD,EAAA,CAAAE,SAAA,mBAAuD;IACvDF,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEtDJ,EAAA,CAAAC,cAAA,gBAAuC;IAAAD,EAAA,CAAAG,MAAA,IAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAlEhDJ,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAgC,kBAAA,KAAAC,OAAA,CAAAC,OAAA,CAAAC,QAAA,QAAyB;IAWzBnC,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAgC,kBAAA,KAAAC,OAAA,CAAAC,OAAA,CAAAE,UAAA,QAA2B;IAW3BpC,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAgC,kBAAA,WAAAC,OAAA,CAAAC,OAAA,CAAAG,SAAA,KAAwB;IAWxBrC,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAgC,kBAAA,KAAAC,OAAA,CAAAC,OAAA,CAAAI,UAAA,QAA2B;IAW3BtC,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAgC,kBAAA,KAAAC,OAAA,CAAAC,OAAA,CAAAK,eAAA,SAAiC;IAWjCvC,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAgC,kBAAA,KAAAC,OAAA,CAAAC,OAAA,CAAAM,YAAA,SAA8B;IAWfxC,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAgC,kBAAA,WAAAC,OAAA,CAAAC,OAAA,CAAAO,WAAA,KAA0B;;;;;IArFrFzC,EAAA,CAAAC,cAAA,aAAwB;IACpBD,EAAA,CAAA0C,UAAA,IAAAC,0DAAA,yBAOc,IAAAC,0DAAA;IAkFlB5C,EAAA,CAAAI,YAAA,EAAS;;;;;IAQDJ,EAAA,CAAAC,cAAA,cAA6C;IAErCD,EAAA,CAAAE,SAAA,kBAAkE;IAClEF,EAAA,CAAAC,cAAA,aAA8B;IAAAD,EAAA,CAAAG,MAAA,uBAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;IAOvDJ,EAAA,CAAAC,cAAA,cAAyF;IAEzCD,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACxDJ,EAAA,CAAAE,SAAA,cAAgD;IAChDF,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACxDJ,EAAA,CAAAC,cAAA,cAAuE;IACnED,EAAA,CAAAE,SAAA,kBAAmE;IACnEF,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAG,MAAA,IAA8B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC3DJ,EAAA,CAAAE,SAAA,mBAAgE;IAChEF,EAAA,CAAAC,cAAA,gBAAsB;IAAAD,EAAA,CAAAG,MAAA,IAA8B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE/DJ,EAAA,CAAAC,cAAA,eAAkE;IAC9DD,EAAA,CAAAE,SAAA,mBAA6D;IAC7DF,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAG,MAAA,IAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAXrBJ,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAAM,iBAAA,CAAAuC,OAAA,CAAAC,GAAA,CAAW;IAC9C9C,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAA+B,UAAA,QAAAc,OAAA,CAAAE,IAAA,EAAA/C,EAAA,CAAAgD,aAAA,CAAgB;IACYhD,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,iBAAA,CAAAuC,OAAA,CAAAI,WAAA,CAAmB;IAG1BjD,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAgC,kBAAA,KAAAa,OAAA,CAAAK,qBAAA,WAA8B;IAE9BlD,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAgC,kBAAA,KAAAa,OAAA,CAAAM,qBAAA,WAA8B;IAIrBnD,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAgC,kBAAA,KAAAa,OAAA,CAAAO,OAAA,SAAmB;;;;;IAdlEpD,EAAA,CAAAC,cAAA,cAAkB;IACdD,EAAA,CAAA0C,UAAA,IAAAW,sEAAA,mBAgBM;IACVrD,EAAA,CAAAI,YAAA,EAAM;;;;IAjBmBJ,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAA+B,UAAA,YAAAuB,OAAA,CAAAC,WAAA,CAAAC,WAAA,CAA0B;;;;;IAZ3DxD,EAAA,CAAAC,cAAA,gBAAyC;IACrCD,EAAA,CAAA0C,UAAA,IAAAe,gEAAA,yBAOc,IAAAC,gEAAA;IAuBlB1D,EAAA,CAAAI,YAAA,EAAS;;;;;IAKDJ,EAAA,CAAAC,cAAA,cAA8C;IAGlCD,EAAA,CAAAE,SAAA,kBAAoE;IACpEF,EAAA,CAAAC,cAAA,aAA+B;IAAAD,EAAA,CAAAG,MAAA,yBAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAE1DJ,EAAA,CAAAE,SAAA,gBAA6E;IACjFF,EAAA,CAAAI,YAAA,EAAM;;;;;;IAqBNJ,EAAA,CAAAC,cAAA,cAAwD;IACGD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1EJ,EAAA,CAAAC,cAAA,qBAI0B;IAJdD,EAAA,CAAAU,UAAA,2BAAAiD,kGAAAC,MAAA;MAAA5D,EAAA,CAAAY,aAAA,CAAAiD,IAAA;MAAA,MAAAC,OAAA,GAAA9D,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAAA4C,OAAA,CAAAC,YAAA,GAAAH,MAAA;IAAA,EAA0B,sBAAAI,6FAAAJ,MAAA;MAAA5D,EAAA,CAAAY,aAAA,CAAAiD,IAAA;MAAA,MAAAI,OAAA,GAAAjE,EAAA,CAAAe,aAAA;MAAA,OAEff,EAAA,CAAAkB,WAAA,CAAA+C,OAAA,CAAAC,YAAA,CAAAN,MAAA,CAAoB;IAAA,EAFL;IAKtC5D,EAAA,CAAAI,YAAA,EAAa;;;;IALDJ,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAA+B,UAAA,YAAAoC,OAAA,CAAAJ,YAAA,CAA0B;;;;;IAW1C/D,EAAA,CAAAE,SAAA,kBAKU;;;;IAHDF,EAAA,CAAA+B,UAAA,SAAAqC,OAAA,CAAAC,QAAA,CAAiB,YAAAD,OAAA,CAAAE,WAAA;;;;;IAI1BtE,EAAA,CAAAE,SAAA,kBAKU;;;;IAHDF,EAAA,CAAA+B,UAAA,SAAAwC,OAAA,CAAAC,OAAA,CAAgB,YAAAD,OAAA,CAAAD,WAAA;;;;;;IApC7BtE,EAAA,CAAAC,cAAA,cAAqD;IAEUD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACzEJ,EAAA,CAAAC,cAAA,qBAO0B;IANfD,EAAA,CAAAU,UAAA,2BAAA+D,4FAAAb,MAAA;MAAA5D,EAAA,CAAAY,aAAA,CAAA8D,IAAA;MAAA,MAAAC,OAAA,GAAA3E,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAAAyD,OAAA,CAAAC,iBAAA,GAAAhB,MAAA;IAAA,EAA+B,sBAAAiB,uFAAAjB,MAAA;MAAA5D,EAAA,CAAAY,aAAA,CAAA8D,IAAA;MAAA,MAAAI,OAAA,GAAA9E,EAAA,CAAAe,aAAA;MAAA,OAKnBf,EAAA,CAAAkB,WAAA,CAAA4D,OAAA,CAAAC,iBAAA,CAAAnB,MAAA,CAAyB;IAAA,EALN;IAO1C5D,EAAA,CAAAI,YAAA,EAAa;IAIjBJ,EAAA,CAAA0C,UAAA,IAAAsC,6DAAA,kBAQM;IACVhF,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAC,cAAA,cAAsD;IAClDD,EAAA,CAAA0C,UAAA,IAAAuC,iEAAA,sBAKU,IAAAC,iEAAA;IAOdlF,EAAA,CAAAI,YAAA,EAAM;;;;IArCcJ,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA+B,UAAA,YAAAoD,OAAA,CAAAC,UAAA,CAAsB,YAAAD,OAAA,CAAAP,iBAAA;IAYhC5E,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAA+B,UAAA,SAAAoD,OAAA,CAAAP,iBAAA,WAAiC;IAc7B5E,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAA+B,UAAA,SAAAoD,OAAA,CAAAE,UAAA,SAAwB;IAMxBrF,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAA+B,UAAA,SAAAoD,OAAA,CAAAE,UAAA,SAAwB;;;;;IAtFlDrF,EAAA,CAAAC,cAAA,cAAiD;IAE7CD,EAAA,CAAA0C,UAAA,IAAA4C,kDAAA,qBA+BS;IACbtF,EAAA,CAAAE,SAAA,SAAI;IAEAF,EAAA,CAAAC,cAAA,aAAQ;IACJD,EAAA,CAAA0C,UAAA,IAAA6C,uDAAA,yBAUc,IAAAC,uDAAA;IA8ClBxF,EAAA,CAAAI,YAAA,EAAS;;;;IA3FAJ,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAA+B,UAAA,SAAA0D,MAAA,CAAAlC,WAAA,CAAiB;;;;;IAiGlBvD,EAAA,CAAAC,cAAA,cAA+C;IAGnCD,EAAA,CAAAE,SAAA,kBAA8D;IAC9DF,EAAA,CAAAC,cAAA,aAAgC;IAAAD,EAAA,CAAAG,MAAA,gCAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAElEJ,EAAA,CAAAE,SAAA,gBAA6E;IACjFF,EAAA,CAAAI,YAAA,EAAM;;;;;;IAoBNJ,EAAA,CAAAC,cAAA,cAAwD;IACGD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC1EJ,EAAA,CAAAC,cAAA,qBAI0B;IAJdD,EAAA,CAAAU,UAAA,2BAAAgF,kGAAA9B,MAAA;MAAA5D,EAAA,CAAAY,aAAA,CAAA+E,IAAA;MAAA,MAAAC,OAAA,GAAA5F,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAAA0E,OAAA,CAAA7B,YAAA,GAAAH,MAAA;IAAA,EAA0B,sBAAAiC,6FAAAjC,MAAA;MAAA5D,EAAA,CAAAY,aAAA,CAAA+E,IAAA;MAAA,MAAAG,OAAA,GAAA9F,EAAA,CAAAe,aAAA;MAAA,OAEff,EAAA,CAAAkB,WAAA,CAAA4E,OAAA,CAAA5B,YAAA,CAAAN,MAAA,CAAoB;IAAA,EAFL;IAKtC5D,EAAA,CAAAI,YAAA,EAAa;;;;IALDJ,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAA+B,UAAA,YAAAgE,OAAA,CAAAhC,YAAA,CAA0B;;;;;IAW1C/D,EAAA,CAAAE,SAAA,kBAKU;;;;IAHDF,EAAA,CAAA+B,UAAA,SAAAiE,OAAA,CAAA3B,QAAA,CAAiB,YAAA2B,OAAA,CAAA1B,WAAA;;;;;IAI1BtE,EAAA,CAAAE,SAAA,kBAKU;;;;IAHDF,EAAA,CAAA+B,UAAA,SAAAkE,OAAA,CAAAzB,OAAA,CAAgB,YAAAyB,OAAA,CAAA3B,WAAA;;;;;IAuDjBtE,EAAA,CAAAC,cAAA,SAAI;IAEID,EAAA,CAAAE,SAAA,4BAA+C;IACnDF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,aAA2B;IAEnBD,EAAA,CAAAE,SAAA,kBAAoD;IACpDF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC1BJ,EAAA,CAAAE,SAAA,qBAAsC;IAC1CF,EAAA,CAAAI,YAAA,EAAM;IAEVJ,EAAA,CAAAC,cAAA,aAAoC;IAE5BD,EAAA,CAAAE,SAAA,mBAA+C;IAC/CF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,sBAAc;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAC3BJ,EAAA,CAAAE,SAAA,sBAA+C;IACnDF,EAAA,CAAAI,YAAA,EAAM;IAEVJ,EAAA,CAAAC,cAAA,cAA+B;IAEvBD,EAAA,CAAAE,SAAA,oBAAuD;IACvDF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACrBJ,EAAA,CAAAE,SAAA,uBAA0C;IAC9CF,EAAA,CAAAI,YAAA,EAAM;IAEVJ,EAAA,CAAAC,cAAA,eAA8B;IAEtBD,EAAA,CAAAE,SAAA,oBAA+C;IAC/CF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACrBJ,EAAA,CAAAE,SAAA,uBAAyC;IAC7CF,EAAA,CAAAI,YAAA,EAAM;IAEVJ,EAAA,CAAAC,cAAA,eAAkC;IAE1BD,EAAA,CAAAE,SAAA,oBAAqD;IACrDF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACzBJ,EAAA,CAAAE,SAAA,uBAA6C;IACjDF,EAAA,CAAAI,YAAA,EAAM;IAEVJ,EAAA,CAAAC,cAAA,eAAkC;IAE1BD,EAAA,CAAAE,SAAA,oBAAyD;IACzDF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACxBJ,EAAA,CAAAE,SAAA,uBAA6C;IACjDF,EAAA,CAAAI,YAAA,EAAM;;;;;IAMdJ,EAAA,CAAAC,cAAA,SAAI;IAEID,EAAA,CAAAE,SAAA,2BAAoD;IACxDF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IAEID,EAAA,CAAAE,SAAA,mBAAyD;IACzDF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAG,MAAA,GAAe;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGxDJ,EAAA,CAAAC,cAAA,SAAI;IACAD,EAAA,CAAAE,SAAA,iBAAsE;IAC1EF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,UAAI;IAC0BD,EAAA,CAAAG,MAAA,IAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE3DJ,EAAA,CAAAC,cAAA,UAAI;IAC0CD,EAAA,CAAAG,MAAA,IAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE1EJ,EAAA,CAAAC,cAAA,UAAI;IAC0BD,EAAA,CAAAG,MAAA,IAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE/DJ,EAAA,CAAAC,cAAA,UAAI;IAEID,EAAA,CAAAE,SAAA,kBAEQ;IACZF,EAAA,CAAAI,YAAA,EAAM;;;;IAzBWJ,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAA+B,UAAA,UAAAmE,UAAA,CAAgB;IAKHlG,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,iBAAA,CAAA4F,UAAA,CAAAzF,IAAA,CAAe;IAItCT,EAAA,CAAAK,SAAA,GAAsC;IAAtCL,EAAA,CAAA+B,UAAA,UAAAmE,UAAA,CAAAC,aAAA,SAAsC;IAGnBnG,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAgC,kBAAA,KAAAkE,UAAA,CAAAE,QAAA,QAAsB;IAGNpG,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAgC,kBAAA,KAAAkE,UAAA,CAAAG,OAAA,QAAqB;IAGrCrG,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAgC,kBAAA,KAAAkE,UAAA,CAAAI,WAAA,SAA0B;IAIzCtG,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAA+B,UAAA,UAAAmE,UAAA,CAAAK,WAAA,OAAkC,aAAAL,UAAA,CAAAK,WAAA,oBAAAL,UAAA,CAAAK,WAAA;;;;;;;;IApKjEvG,EAAA,CAAAC,cAAA,cAAqD;IAEUD,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACzEJ,EAAA,CAAAC,cAAA,qBAO0B;IANfD,EAAA,CAAAU,UAAA,2BAAA8F,4FAAA5C,MAAA;MAAA5D,EAAA,CAAAY,aAAA,CAAA6F,IAAA;MAAA,MAAAC,OAAA,GAAA1G,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAAAwF,OAAA,CAAA9B,iBAAA,GAAAhB,MAAA;IAAA,EAA+B,sBAAA+C,uFAAA/C,MAAA;MAAA5D,EAAA,CAAAY,aAAA,CAAA6F,IAAA;MAAA,MAAAG,OAAA,GAAA5G,EAAA,CAAAe,aAAA;MAAA,OAKnBf,EAAA,CAAAkB,WAAA,CAAA0F,OAAA,CAAA7B,iBAAA,CAAAnB,MAAA,CAAyB;IAAA,EALN;IAO1C5D,EAAA,CAAAI,YAAA,EAAa;IAGjBJ,EAAA,CAAA0C,UAAA,IAAAmE,6DAAA,kBAQM;IACV7G,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAC,cAAA,cAA2D;IACvDD,EAAA,CAAA0C,UAAA,IAAAoE,iEAAA,sBAKU,IAAAC,iEAAA;IAOd/G,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAC,cAAA,cAAkD;IAC9CD,EAAA,CAAAE,SAAA,eAAmB;IAGnBF,EAAA,CAAAC,cAAA,eAA4E;IAGhED,EAAA,CAAAE,SAAA,mBAAiD;IACjDF,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAG,MAAA,iCAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAG3DJ,EAAA,CAAAC,cAAA,eAAwB;IAEhBD,EAAA,CAAAE,SAAA,aAA4B;IAC5BF,EAAA,CAAAC,cAAA,iBAIiC;IAF1BD,EAAA,CAAAU,UAAA,mBAAAsG,gFAAApD,MAAA;MAAA5D,EAAA,CAAAY,aAAA,CAAA6F,IAAA;MAAA,MAAAQ,IAAA,GAAAjH,EAAA,CAAAkH,WAAA;MAAA,MAAAC,OAAA,GAAAnH,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAkB,WAAA,CAAAiG,OAAA,CAAAC,cAAA,CAAAH,IAAA,EAAArD,MAAA,CAA0B;IAAA,EAAC;IAF3C5D,EAAA,CAAAI,YAAA,EAIiC;IAErCJ,EAAA,CAAAC,cAAA,oBAImC;IAAzBD,EAAA,CAAAU,UAAA,mBAAA2G,mFAAA;MAAArH,EAAA,CAAAY,aAAA,CAAA6F,IAAA;MAAA,MAAAQ,IAAA,GAAAjH,EAAA,CAAAkH,WAAA;MAAA,OAASlH,EAAA,CAAAkB,WAAA,CAAA+F,IAAA,CAAAK,SAAA,EAAc;IAAA,EAAC;IAClCtH,EAAA,CAAAI,YAAA,EAAW;IAMvBJ,EAAA,CAAAC,cAAA,uBAcqC;IAJ5BD,EAAA,CAAAU,UAAA,6BAAA6G,4FAAA3D,MAAA;MAAA5D,EAAA,CAAAY,aAAA,CAAA6F,IAAA;MAAA,MAAAe,OAAA,GAAAxH,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAAAsG,OAAA,CAAAC,gBAAA,GAAA7D,MAAA;IAAA,EAAgC;IAMrC5D,EAAA,CAAA0C,UAAA,KAAAgF,sEAAA,0BAgDc,KAAAC,sEAAA;IAkClB3H,EAAA,CAAAI,YAAA,EAAU;;;;IAxKMJ,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA+B,UAAA,YAAA6F,OAAA,CAAAxC,UAAA,CAAsB,YAAAwC,OAAA,CAAAhD,iBAAA;IAWhC5E,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAA+B,UAAA,SAAA6F,OAAA,CAAAhD,iBAAA,WAAiC;IAc7B5E,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAA+B,UAAA,SAAA6F,OAAA,CAAAvC,UAAA,SAAwB;IAMxBrF,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAA+B,UAAA,SAAA6F,OAAA,CAAAvC,UAAA,SAAwB;IAwCzBrF,EAAA,CAAAK,SAAA,IAAqB;IAArBL,EAAA,CAAA+B,UAAA,UAAA6F,OAAA,CAAAC,WAAA,CAAqB,YAAAD,OAAA,CAAAE,IAAA,oCAAA9H,EAAA,CAAA+H,eAAA,KAAAC,GAAA,4CAAAhI,EAAA,CAAA+H,eAAA,KAAAE,GAAA,+CAAAL,OAAA,CAAAH,gBAAA;;;;;IA1F9CzH,EAAA,CAAAC,cAAA,cAAgD;IAExCD,EAAA,CAAA0C,UAAA,IAAAwF,uDAAA,yBAUc,IAAAC,uDAAA;IAkLlBnI,EAAA,CAAAI,YAAA,EAAS;;;;;IAODJ,EAAA,CAAAC,cAAA,eAA+C;IAGnCD,EAAA,CAAAE,SAAA,mBAAkE;IAClEF,EAAA,CAAAC,cAAA,cAAgC;IAAAD,EAAA,CAAAG,MAAA,gCAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAElEJ,EAAA,CAAAE,SAAA,iBAA0E;IAC9EF,EAAA,CAAAI,YAAA,EAAM;;;;;IA+DEJ,EAAA,CAAAC,cAAA,SAAI;IAEID,EAAA,CAAAE,SAAA,4BAA+C;IACnDF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,aAA2B;IAEnBD,EAAA,CAAAE,SAAA,mBAA+C;IAC/CF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACxBJ,EAAA,CAAAE,SAAA,qBAAsC;IAC1CF,EAAA,CAAAI,YAAA,EAAM;IAEVJ,EAAA,CAAAC,cAAA,aAAoC;IAE5BD,EAAA,CAAAE,SAAA,mBAA+C;IAC/CF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACxBJ,EAAA,CAAAE,SAAA,sBAA+C;IACnDF,EAAA,CAAAI,YAAA,EAAM;IAEVJ,EAAA,CAAAC,cAAA,cAA+B;IAEvBD,EAAA,CAAAE,SAAA,oBAAuD;IACvDF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACxBJ,EAAA,CAAAE,SAAA,uBAA0C;IAC9CF,EAAA,CAAAI,YAAA,EAAM;IAEVJ,EAAA,CAAAC,cAAA,eAA8B;IAEtBD,EAAA,CAAAE,SAAA,oBAA+C;IAC/CF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACtBJ,EAAA,CAAAE,SAAA,uBAAyC;IAC7CF,EAAA,CAAAI,YAAA,EAAM;IAEVJ,EAAA,CAAAC,cAAA,eAAkC;IAE1BD,EAAA,CAAAE,SAAA,oBAAqD;IACrDF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,oBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACzBJ,EAAA,CAAAE,SAAA,uBAA6C;IACjDF,EAAA,CAAAI,YAAA,EAAM;IAEVJ,EAAA,CAAAC,cAAA,eAAkC;IAE1BD,EAAA,CAAAE,SAAA,oBAAyD;IACzDF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACnBJ,EAAA,CAAAE,SAAA,uBAA6C;IACjDF,EAAA,CAAAI,YAAA,EAAM;;;;;IAMdJ,EAAA,CAAAC,cAAA,SAAI;IAEID,EAAA,CAAAE,SAAA,2BAAoD;IACxDF,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,SAAI;IAEID,EAAA,CAAAE,SAAA,mBAAuD;IACvDF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAG,MAAA,GAAe;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGxDJ,EAAA,CAAAC,cAAA,SAAI;IACwCD,EAAA,CAAAG,MAAA,IAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE7EJ,EAAA,CAAAC,cAAA,UAAI;IAC0BD,EAAA,CAAAG,MAAA,IAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE1DJ,EAAA,CAAAC,cAAA,UAAI;IAC0CD,EAAA,CAAAG,MAAA,IAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEzEJ,EAAA,CAAAC,cAAA,UAAI;IAC0BD,EAAA,CAAAG,MAAA,IAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE/DJ,EAAA,CAAAC,cAAA,UAAI;IAEID,EAAA,CAAAE,SAAA,kBAGQ;IACZF,EAAA,CAAAI,YAAA,EAAM;;;;IA1BWJ,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAA+B,UAAA,UAAAqG,UAAA,CAAgB;IAKHpI,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,iBAAA,CAAA8H,UAAA,CAAA3H,IAAA,CAAe;IAILT,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAgC,kBAAA,KAAAoG,UAAA,CAAAjC,aAAA,OAA0B;IAGxCnG,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAgC,kBAAA,KAAAoG,UAAA,CAAAhC,QAAA,OAAqB;IAGLpG,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAgC,kBAAA,KAAAoG,UAAA,CAAA/B,OAAA,OAAoB;IAGpCrG,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAgC,kBAAA,KAAAoG,UAAA,CAAA9B,WAAA,SAA0B;IAIzCtG,EAAA,CAAAK,SAAA,GAA+F;IAA/FL,EAAA,CAAA+B,UAAA,UAAAqG,UAAA,CAAA7B,WAAA,mBAAA6B,UAAA,CAAA7B,WAAA,+BAA+F,aAAA6B,UAAA,CAAA7B,WAAA,oBAAA6B,UAAA,CAAA7B,WAAA;;;;;;IAnI9HvG,EAAA,CAAAC,cAAA,cAA2D;IACvDD,EAAA,CAAAE,SAAA,kBAIU;IACdF,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAC,cAAA,cAAkD;IAC9CD,EAAA,CAAAE,SAAA,cAAmB;IAGnBF,EAAA,CAAAC,cAAA,cAA4E;IAGhED,EAAA,CAAAE,SAAA,kBAAiD;IACjDF,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAG,MAAA,8BAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAGzDJ,EAAA,CAAAC,cAAA,eAAwB;IAEhBD,EAAA,CAAAE,SAAA,aAA4B;IAC5BF,EAAA,CAAAC,cAAA,kBAIiC;IAF1BD,EAAA,CAAAU,UAAA,mBAAA2H,iFAAAzE,MAAA;MAAA5D,EAAA,CAAAY,aAAA,CAAA0H,IAAA;MAAA,MAAAC,IAAA,GAAAvI,EAAA,CAAAkH,WAAA;MAAA,MAAAsB,OAAA,GAAAxI,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAkB,WAAA,CAAAsH,OAAA,CAAApB,cAAA,CAAAmB,IAAA,EAAA3E,MAAA,CAA2B;IAAA,EAAC;IAF5C5D,EAAA,CAAAI,YAAA,EAIiC;IAErCJ,EAAA,CAAAC,cAAA,oBAIoC;IAA1BD,EAAA,CAAAU,UAAA,mBAAA+H,oFAAA;MAAAzI,EAAA,CAAAY,aAAA,CAAA0H,IAAA;MAAA,MAAAC,IAAA,GAAAvI,EAAA,CAAAkH,WAAA;MAAA,OAASlH,EAAA,CAAAkB,WAAA,CAAAqH,IAAA,CAAAjB,SAAA,EAAe;IAAA,EAAC;IACnCtH,EAAA,CAAAI,YAAA,EAAW;IAMvBJ,EAAA,CAAAC,cAAA,yBAcqC;IAJ5BD,EAAA,CAAAU,UAAA,6BAAAgI,6FAAA9E,MAAA;MAAA5D,EAAA,CAAAY,aAAA,CAAA0H,IAAA;MAAA,MAAAK,OAAA,GAAA3I,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAkB,WAAA,CAAAyH,OAAA,CAAAlB,gBAAA,GAAA7D,MAAA;IAAA,EAAgC;IAMrC5D,EAAA,CAAA0C,UAAA,KAAAkG,uEAAA,0BAgDc,KAAAC,uEAAA;IAmClB7I,EAAA,CAAAI,YAAA,EAAU;;;;IAzIDJ,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAA+B,UAAA,SAAA+G,OAAA,CAAAzE,QAAA,CAAiB,YAAAyE,OAAA,CAAAxE,WAAA;IAuCjBtE,EAAA,CAAAK,SAAA,IAAqB;IAArBL,EAAA,CAAA+B,UAAA,UAAA+G,OAAA,CAAAjB,WAAA,CAAqB,YAAAiB,OAAA,CAAAhB,IAAA,oCAAA9H,EAAA,CAAA+H,eAAA,KAAAC,GAAA,4CAAAhI,EAAA,CAAA+H,eAAA,KAAAE,GAAA,+CAAAa,OAAA,CAAArB,gBAAA;;;;;IAzD9CzH,EAAA,CAAAC,cAAA,cAAgD;IAExCD,EAAA,CAAA0C,UAAA,IAAAqG,wDAAA,yBAUc,IAAAC,wDAAA;IAkJlBhJ,EAAA,CAAAI,YAAA,EAAS;;;;;;;;;;;;;;;IAnnBjBJ,EAAA,CAAAC,cAAA,aAAkD;IAG1CD,EAAA,CAAAE,SAAA,+BAKwB;IAC5BF,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAC,cAAA,aAA6B;IAGrBD,EAAA,CAAA0C,UAAA,IAAAuG,iDAAA,yBAOc,IAAAC,iDAAA;IA4DlBlJ,EAAA,CAAAI,YAAA,EAAS;IAGTJ,EAAA,CAAA0C,UAAA,IAAAyG,4CAAA,oBA0FS;IACbnJ,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAA0C,UAAA,IAAA0G,yCAAA,iBA8FM,IAAAC,yCAAA,sBAAAC,0CAAA;IAoWVtJ,EAAA,CAAAI,YAAA,EAAM;;;;IAjnBMJ,EAAA,CAAAK,SAAA,GAGE;IAHFL,EAAA,CAAA+B,UAAA,UAAA/B,EAAA,CAAAuJ,eAAA,IAAAC,GAAA,EAAAxJ,EAAA,CAAA+H,eAAA,IAAA0B,GAAA,GAAAzJ,EAAA,CAAA0J,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAApJ,eAAA,CAAAC,IAAA,GAGE;IA8EGT,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAA+B,UAAA,SAAA6H,MAAA,CAAA1H,OAAA,CAAa;IA8FIlC,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAA+B,UAAA,SAAA6H,MAAA,CAAA5I,WAAA,CAAiB;IAgGjBhB,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAA+B,UAAA,SAAA6H,MAAA,CAAA3I,UAAA,CAAgB;IAkMhBjB,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAA+B,UAAA,SAAA6H,MAAA,CAAAzI,UAAA,CAAgB;;;ADhclD,OAAM,MAAO0I,oBAAoB;EAoE7BC,YAAoBC,eAAgC,EACxCC,KAAqB,EACrBC,YAA0B,EAC1BC,cAA8B,EAC9BC,cAA8B;IAJtB,KAAAJ,eAAe,GAAfA,eAAe;IACvB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IArE1B,KAAAC,QAAQ,GAAc,EAAE;IACxB,KAAAtI,OAAO,GAAY,EAAE;IACrB,KAAAF,SAAS,GAAY,EAAE;IAQvB,KAAAyD,UAAU,GAAU,IAAI;IACxB,KAAAtB,YAAY,GAAS,IAAIsG,IAAI,EAAE,CAAC,CAAC;IAQjC,KAAAC,kBAAkB,GAAiB,EAAE;IAErC,KAAAC,iBAAiB,GAAiB,EAAE;IAEpC,KAAAC,SAAS,GAAW,CAAC;IAErB,KAAAC,SAAS,GAAW,EAAE;IAEtB,KAAAC,YAAY,GAAU,EAAE;IAExB,KAAAC,YAAY,GAAU,EAAE;IAExB,KAAAC,WAAW,GAAU,EAAE;IAyBvB,KAAA5J,WAAW,GAAY,IAAI;IAC3B,KAAAC,UAAU,GAAY,KAAK;IAC3B,KAAAE,UAAU,GAAY,KAAK;IAC3B,KAAA0G,WAAW,GAAiB,EAAE;IAC9B,KAAAgD,MAAM,GAAgB,EAAE;IACxB,KAAAC,kBAAkB,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;IAChC,KAAAhD,IAAI,GAAU,EAAE;EAUhB;EAEAiD,QAAQA,CAAA;IACJ,IAAIX,QAAQ,GAAG,IAAI,CAACH,YAAY,CAACe,WAAW,EAAE;IAC9CC,OAAO,CAACC,GAAG,CAACd,QAAQ,CAAC;IAErB;IACA,IAAI,CAACJ,KAAK,CAACmB,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;MACnC,IAAI,CAAC7K,eAAe,GAAG4J,QAAQ,CAACkB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,IAAIH,MAAM,CAACI,GAAG,CAAC,IAAI,CAAC,CAAC;MACnE;MACAR,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC1K,eAAe,CAAC;IACpD,CAAC,CAAC;IAEF,IAAI,CAACkL,iBAAiB,EAAE;IACxB,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,UAAU,EAAE;IAEjB;IAEA;IACA;IAEA;IACA,IAAI,CAAC7B,eAAe,CAAC8B,cAAc,EAAE,CAACC,IAAI,CAACC,IAAI,IAAI,IAAI,CAAClE,WAAW,GAAGkE,IAAI,CAAC;IAE3E,IAAI,CAACjE,IAAI,GAAG,EAEX;IAED,IAAI,CAAC1C,UAAU,GAAG,CACd;MAAEoG,EAAE,EAAE,KAAK;MAAEQ,KAAK,EAAE;IAAK,CAAE,EAC3B;MAAER,EAAE,EAAE,OAAO;MAAEQ,KAAK,EAAE;IAAO,CAAE,EAC/B;MAAER,EAAE,EAAE,MAAM;MAAEQ,KAAK,EAAE;IAAM,CAAE,EAC7B;MAAER,EAAE,EAAE,YAAY;MAAEQ,KAAK,EAAE;IAAc,CAAE,EAC3C;MAAER,EAAE,EAAE,aAAa;MAAEQ,KAAK,EAAE;IAAe,CAAE,EAC7C;MAAER,EAAE,EAAE,UAAU;MAAEQ,KAAK,EAAE;IAAyB,CAAE,EACpD;MAAER,EAAE,EAAE,eAAe;MAAEQ,KAAK,EAAE;IAAoB,CAAE,CACvD;IAED,IAAI,CAACpH,iBAAiB,GAAG,KAAK;EAElC;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEAqH,UAAUA,CAACC,SAAkB,EAAE7G,UAAkB;IAC7C,MAAM8G,aAAa,GAAGC,gBAAgB,CAACC,QAAQ,CAACC,eAAe,CAAC;IAChE,MAAMC,SAAS,GAAGJ,aAAa,CAACK,gBAAgB,CAAC,cAAc,CAAC;IAChE,MAAMC,kBAAkB,GAAGN,aAAa,CAACK,gBAAgB,CAAC,wBAAwB,CAAC;IACnF,MAAME,aAAa,GAAGP,aAAa,CAACK,gBAAgB,CAAC,kBAAkB,CAAC;IAExE,IAAIN,SAAS,EAAC;MAEV;MACA,MAAMS,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAAC,IAAI,CAACC,UAAU,CAAChB,IAAI,CAACiB,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC;MAClF,MAAMC,sBAAsB,GAAGP,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAAC,IAAI,CAACC,UAAU,CAAChB,IAAI,CAACiB,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACG,eAAe,CAAC,CAAC,CAAC;MACpG;MACA,MAAMC,eAAe,GAAGT,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAAC,IAAI,CAACC,UAAU,CAAChB,IAAI,CAACiB,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACxM,IAAI,CAAC,CAAC,CAAC;MAElF;MACA,MAAM6M,WAAW,GAAGD,eAAe,CAACL,GAAG,CAACO,GAAG,IAAG;QAC1C,MAAMC,KAAK,GAAG,IAAI,CAACC,cAAc,EAAE;QACnC,MAAM1B,IAAI,GAAGY,WAAW,CAACK,GAAG,CAACU,IAAI,IAAG;UAChC,MAAMC,KAAK,GAAG,IAAI,CAACZ,UAAU,CAAChB,IAAI,CAACT,IAAI,CAAC2B,CAAC,IAAIA,CAAC,CAACxM,IAAI,KAAK8M,GAAG,IAAIN,CAAC,CAACC,QAAQ,KAAKQ,IAAI,CAAC;UACnF,OAAOC,KAAK,GAAGA,KAAK,CAACC,WAAW,GAAG,CAAC;QACxC,CAAC,CAAC;QAEF,OAAO;UACH5B,KAAK,EAAEuB,GAAG;UACVxB,IAAI,EAAEA,IAAI;UACV8B,eAAe,EAAEL;SACpB;MACL,CAAC,CAAC;MAEF,IAAI,CAAChJ,OAAO,GAAG;QACXsJ,MAAM,EAAEX,sBAAsB,CAACH,GAAG,CAACe,EAAE,IAAIA,EAAE,CAAC;QAC5CC,QAAQ,EAAEV;OACb;MAED;MACA;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;MACA;MAEA,IAAIU,QAAQ,GAAS,EAAE;MACvB;MACC,MAAMC,iBAAiB,GAA6B,EAAE;MAEvD,IAAI,CAAClB,UAAU,CAAChB,IAAI,CAACmC,OAAO,CAACjB,CAAC,IAAG;QAC7B,IAAI,CAACgB,iBAAiB,CAAChB,CAAC,CAACxM,IAAI,CAAC,EAAE;UAC5BwN,iBAAiB,CAAChB,CAAC,CAACxM,IAAI,CAAC,GAAG,EAAE;;QAElCwN,iBAAiB,CAAChB,CAAC,CAACxM,IAAI,CAAC,CAAC0N,IAAI,CAAClB,CAAC,CAACW,WAAW,CAAC;MACjD,CAAC,CAAC;MAEFP,eAAe,CAACa,OAAO,CAACX,GAAG,IAAG;QAC1B,IAAIC,KAAK,GAAG,IAAI,CAACC,cAAc,EAAE;QACjCO,QAAQ,CAACG,IAAI,CAAC;UACVnC,KAAK,EAAEuB,GAAG;UACVxB,IAAI,EAAEkC,iBAAiB,CAACV,GAAG,CAAC;UAC5Ba,IAAI,EAAE,KAAK;UACXP,eAAe,EAAEL,KAAK;UACtBa,WAAW,EAAEb,KAAK;UAClBc,OAAO,EAAE;SACZ,CAAC;MAEN,CAAC,CAAC;MAEF,MAAMC,SAAS,GAAG,IAAI,CAACxB,UAAU,CAAChB,IAAI,CAAC,CAAC,CAAC,CAACtL,IAAI,CAAC,CAAC;MAChD,MAAM+N,qBAAqB,GAAG,IAAI,CAACzB,UAAU,CAAChB,IAAI,CACjD0C,MAAM,CAACxB,CAAC,IAAIA,CAAC,CAACxM,IAAI,KAAK8N,SAAS,CAAC,CAAC;MAAA,CAClCvB,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC;MAEvB,IAAI,CAAC7I,QAAQ,GAAG;QACZyJ,MAAM,EAAEU,qBAAqB,CAACxB,GAAG,CAAC,CAACe,EAAE,EAAEW,KAAK,KACxCA,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,IAAIrE,IAAI,CAAC0D,EAAE,CAAC,CAACY,kBAAkB,CAAC,EAAE,EAAE;UAAEC,IAAI,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAS,CAAE,CAAC,GAAG,EAAE,CACrG;QACDb,QAAQ,EAAEA;OACb;MAED;MACA;MACA;MACA;KAEH,MAAI;MACD,IAAI,CAAC3J,QAAQ,GAAG;QACZyJ,MAAM,EAAE,IAAI,CAACf,UAAU,CAAChB,IAAI,CAACiB,GAAG,CAAC,CAAC8B,CAAC,EAAEJ,KAAK,KACtCA,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,IAAIrE,IAAI,CAACyE,CAAC,CAAC5B,QAAQ,CAAC,CAACyB,kBAAkB,CAAC,EAAE,EAAE;UAAEC,IAAI,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAS,CAAE,CAAC,GAAG,EAAE,CAC7G;QACDb,QAAQ,EAAE,CACN;UACIhC,KAAK,EAAE,cAAc;UACrBD,IAAI,EAAE,IAAI,CAACgB,UAAU,CAAChB,IAAI,CAACiB,GAAG,CAAC8B,CAAC,IAAIA,CAAC,CAAClB,WAAW,CAAC;UAClDQ,IAAI,EAAE,KAAK;UACXP,eAAe,EAAE1B,aAAa,CAACK,gBAAgB,CAAC,eAAe,CAAC;UAChE6B,WAAW,EAAElC,aAAa,CAACK,gBAAgB,CAAC,eAAe,CAAC;UAC5D8B,OAAO,EAAE;SACZ,EACD;UACItC,KAAK,EAAE,mBAAmB;UAC1BD,IAAI,EAAE,IAAI,CAACgB,UAAU,CAAChB,IAAI,CAACiB,GAAG,CAAC8B,CAAC,IAAIA,CAAC,CAACC,eAAe,CAAC;UACtDX,IAAI,EAAE,KAAK;UACXP,eAAe,EAAE1B,aAAa,CAACK,gBAAgB,CAAC,eAAe,CAAC;UAChE6B,WAAW,EAAElC,aAAa,CAACK,gBAAgB,CAAC,eAAe,CAAC;UAC5D8B,OAAO,EAAE;SACZ;OAER;MAED,IAAI,CAAC9J,OAAO,GAAG;QACXsJ,MAAM,EAAE,IAAI,CAACf,UAAU,CAAChB,IAAI,CAACiB,GAAG,CAACgC,IAAI,IAAIA,IAAI,CAACvO,IAAI,CAAC;QACnDuN,QAAQ,EAAE,CACN;UACIhC,KAAK,EAAE,cAAc;UACrBD,IAAI,EAAE,IAAI,CAACgB,UAAU,CAAChB,IAAI,CAACiB,GAAG,CAAC8B,CAAC,IAAIA,CAAC,CAAClB,WAAW,CAAC;UAClDQ,IAAI,EAAE,KAAK;UACXP,eAAe,EAAE1B,aAAa,CAACK,gBAAgB,CAAC,eAAe,CAAC;UAChE6B,WAAW,EAAElC,aAAa,CAACK,gBAAgB,CAAC,eAAe,CAAC;UAC5D8B,OAAO,EAAE;SACZ,EACD;UACItC,KAAK,EAAE,mBAAmB;UAC1BD,IAAI,EAAE,IAAI,CAACgB,UAAU,CAAChB,IAAI,CAACiB,GAAG,CAAC8B,CAAC,IAAIA,CAAC,CAACC,eAAe,CAAC;UACtDX,IAAI,EAAE,KAAK;UACXP,eAAe,EAAE1B,aAAa,CAACK,gBAAgB,CAAC,eAAe,CAAC;UAChE6B,WAAW,EAAElC,aAAa,CAACK,gBAAgB,CAAC,eAAe,CAAC;UAC5D8B,OAAO,EAAE;SACZ;OAER;;IAOL,IAAI,CAAChK,WAAW,GAAG;MACf2K,OAAO,EAAE;QACLC,MAAM,EAAE;UACJpB,MAAM,EAAE;YACJqB,SAAS,EAAE5C;;;OAGtB;MACD6C,MAAM,EAAE;QACJC,CAAC,EAAE;UACCC,KAAK,EAAE;YACH9B,KAAK,EAAEf,kBAAkB;YACzB8C,WAAW,EAAE,CAAC;YACdC,WAAW,EAAE;WAChB;UACDC,IAAI,EAAE;YACFjC,KAAK,EAAEd,aAAa;YACpBgD,UAAU,EAAE;;SAEnB;QACDC,CAAC,EAAE;UACCL,KAAK,EAAE;YACH9B,KAAK,EAAEf;WACV;UACDgD,IAAI,EAAE;YACFjC,KAAK,EAAEd,aAAa;YACpBgD,UAAU,EAAE;;;;KAI3B;EACL;EAEAhE,iBAAiBA,CAAA;IACb,IAAIkE,OAAO,GAA6B;MACpCC,SAAS,EAAG,IAAI,CAACrP,eAAe,CAACgL;KACpC;IACD,IAAI,CAACzB,eAAe,CAAC2B,iBAAiB,CAACkE,OAAO,CAAC,CAAC9D,IAAI,CAACC,IAAI,IAAG;MACxDd,OAAO,CAACC,GAAG,CAACa,IAAI,CAAC;MACjB,IAAI,CAACjK,OAAO,GAAGiK,IAAI;MACnB,IAAI,CAACnK,SAAS,GAAG,IAAI,CAACE,OAAO,CACxB2M,MAAM,CAACqB,MAAM,IAAIA,MAAM,CAACC,IAAI,KAAK,gBAAgB,CAAC;MACvD9E,OAAO,CAACC,GAAG,CAAC,MAAM,GAAG,IAAI,CAACpJ,OAAO,CAAC;MAClC,IAAI,CAACkO,WAAW,GAAG,IAAI,CAAClO,OAAO,CAC1B2M,MAAM,CAACqB,MAAM,IAAIA,MAAM,CAACC,IAAI,KAAK,gBAAgB,CAAC,CAAC;MAAA,CACnD/C,GAAG,CAAC8C,MAAM,IAAIA,MAAM,CAACtE,EAAE,CAAC,CAAC;MAAA,CACzByE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;MAEpB,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACF,WAAW,CAAC;IACvC,CAAC,CAAC;EAGN;EAEArE,iBAAiBA,CAAA;IACb,IAAIiE,OAAO,GAA6B;MACpCC,SAAS,EAAG,IAAI,CAACrP,eAAe,CAACgL;KACpC;IACD,IAAI,CAACzB,eAAe,CAAC4B,iBAAiB,CAACiE,OAAO,CAAC,CAAC9D,IAAI,CAACC,IAAI,IAAI,IAAI,CAAC7J,OAAO,GAAG6J,IAAI,CAAC;EACrF;EAGAH,UAAUA,CAAA;IACN,IAAIgE,OAAO,GAAsB;MAC7BO,WAAW,EAAG,IAAI,CAAC3P,eAAe,CAAC4P,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC5P,eAAe,CAAC6P;KAC7E;IACD,IAAI,CAACnG,cAAc,CAAC0B,UAAU,CAACgE,OAAO,CAAC,CAAC9D,IAAI,CAACC,IAAI,IAAI,IAAI,CAACxI,WAAW,GAAGwI,IAAI,CAAC;EACjF;EAEAmE,gBAAgBA,CAACF,WAAmB,EAAE9D,SAAA,GAAqB,KAAK,EAAEoE,YAAA,GAAqB,IAAI,EAAEC,UAAA,GAAmB,IAAI,EAAElL,UAAA,GAAqB,IAAI;IAC3I,IAAImL,GAAG,GAAG,IAAInG,IAAI,EAAE;IAEpB,IAAIiG,YAAY,IAAI,IAAI,EACpB,IAAIG,kBAAkB,GAAGH,YAAY,CAACI,WAAW,EAAE,CAAC,KAEpD,IAAID,kBAAkB,GAAG,IAAIpG,IAAI,CAACmG,GAAG,CAACG,WAAW,EAAE,EAAEH,GAAG,CAACI,QAAQ,EAAE,EAAEJ,GAAG,CAACK,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACH,WAAW,EAAE;IAE9G,IAAIH,UAAU,IAAI,IAAI,EAClB,IAAIO,gBAAgB,GAAGP,UAAU,CAACG,WAAW,EAAE,CAAC,KAEhD,IAAII,gBAAgB,GAAG,IAAIzG,IAAI,CAACmG,GAAG,CAACG,WAAW,EAAE,EAAEH,GAAG,CAACI,QAAQ,EAAE,EAAEJ,GAAG,CAACK,OAAO,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACH,WAAW,EAAE;IAGhH;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IAEA;IACA;IACA;IACA;IACA;IAKA,IAAId,OAAO,GAA2B;MAClCmB,MAAM,EAAGf,WAAW;MACpBgB,SAAS,EAAE,CAAC;MACZC,aAAa,EAAER,kBAAkB;MACjCS,WAAW,EAAEJ,gBAAgB;MAC7B5E,SAAS,EAAEA,SAAS;MACpB7G,UAAU,EAACA,UAAU;MACrBwK,SAAS,EAAE,IAAI,CAACrP,eAAe,CAACgL;KACnC;IACD,IAAI,CAACzB,eAAe,CAACoH,sBAAsB,CAACvB,OAAO,CAAC,CAAC9D,IAAI,CAACC,IAAI,IAAG;MAC7D,IAAI,CAACgB,UAAU,GAAGhB,IAAI;MACtBd,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC6B,UAAU,CAAC;MAC5B,IAAI,IAAI,CAACA,UAAU,CAAChB,IAAI,CAAClK,MAAM,GAAG,CAAC,EAAC;QAChC,IAAI,CAACoK,UAAU,CAACC,SAAS,EAAE7G,UAAU,CAAC;OACzC,MAAI;QACD,IAAI,CAAC8E,cAAc,CAACiH,GAAG,CAAC;UAAEC,QAAQ,EAAE,MAAM;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAqB,CAAE,CAAC;;IAExG,CAAC,CAAC;EACN;EAEAjQ,oBAAoBA,CAAA;IAChB,IAAIkQ,MAAM,GAAG,IAAI;IACjB,IAAI,IAAI,CAACvQ,UAAU,EACfuQ,MAAM,GAAG,KAAK;IAElB,IAAI,CAACxQ,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACE,UAAU,GAAG,KAAK;IAEvB,IAAIqQ,MAAM,EACN,IAAI,CAACtB,gBAAgB,CAAC,IAAI,CAACF,WAAW,EAAE,IAAI,CAAC;EACrD;EAEAvO,oBAAoBA,CAAA;IAChB,IAAI,CAACT,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACE,UAAU,GAAG,IAAI;EAC1B;EAGAsM,cAAcA,CAAA;IACV,MAAMgE,UAAU,GAAGrF,gBAAgB,CAACC,QAAQ,CAACC,eAAe,CAAC;IAC7D,MAAMoF,YAAY,GAAGC,MAAM,CAACC,IAAI,CAACH,UAAU,CAAC,CAC3CzE,GAAG,CAAC6E,GAAG,IAAIJ,UAAU,CAACI,GAAG,CAAC,CAAC,CAC3BpD,MAAM,CAACqD,KAAK,IAAIA,KAAK,CAACC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAAA,CACxC/E,GAAG,CAACgF,OAAO,IAAIP,UAAU,CAACjF,gBAAgB,CAACwF,OAAO,CAAC,CAACC,IAAI,EAAE,CAAC,CAAC;IAAA,CAC5DxD,MAAM,CAACqD,KAAK,IAAG;MACZ;MACA,MAAMI,KAAK,GAAGJ,KAAK,CAACI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;MACpC,OAAOA,KAAK,IAAIC,QAAQ,CAACD,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC;IAClD,CAAC,CAAC;IAEF;IACA,OAAOR,YAAY,CAACU,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAGZ,YAAY,CAAC7P,MAAM,CAAC,CAAC;EAExE;EAEAqC,YAAYA,CAACqO,KAAW;IACpBtH,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEqH,KAAK,CAAC;IAC5C,IAAIC,QAAQ,GAAG,IAAInI,IAAI,CAACkI,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;IAChD,MAAMC,QAAQ,GAAG,IAAIrI,IAAI,CAACmI,QAAQ,CAAC;IACnCE,QAAQ,CAACC,OAAO,CAACD,QAAQ,CAAC7B,OAAO,EAAE,GAAE,CAAC,CAAC;IACvC,IAAI,CAACX,gBAAgB,CAAC,IAAI,CAACF,WAAW,EAAE,CAAC,IAAI,CAAChP,WAAW,EAAEwR,QAAQ,EAAEE,QAAQ,EAAE,IAAI,CAACrN,UAAU,CAAC;EAEnG;EAGAN,iBAAiBA,CAACwN,KAAU;IACxBtH,OAAO,CAACC,GAAG,CAACqH,KAAK,CAAC;IAClB,IAAIK,WAAW,GAAG,IAAIvI,IAAI,EAAE;IAC5BY,OAAO,CAACC,GAAG,CAAC0H,WAAW,CAAC;IACxB,IAAIJ,QAAc;IAClB,IAAIK,MAAM,GAAS,IAAIxI,IAAI,CAACuI,WAAW,CAAC;IAExC,QAAQL,KAAK,CAACT,KAAK;MAEjB,KAAK,KAAK;QACR;QACAU,QAAQ,GAAG,IAAInI,IAAI,CAACuI,WAAW,CAACH,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,IAAI,CAACpN,UAAU,GAAG,IAAI;QACtB,IAAIkN,KAAK,CAACT,KAAK,KAAK,KAAK,EAAE;UACvB,IAAI,CAAC/N,YAAY,GAAG,IAAIsG,IAAI,EAAE,CAAC,CAAC;;;QAEpC;MAEF,KAAK,OAAO;QACV;QACAmI,QAAQ,GAAG,IAAInI,IAAI,CAACuI,WAAW,CAACjC,WAAW,EAAE,EAAEiC,WAAW,CAAChC,QAAQ,EAAE,EAAE,CAAC,CAAC;QACzE4B,QAAQ,CAACC,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC;QAC1B,IAAI,CAACpN,UAAU,GAAG,SAAS;QAC3B;MAEF,KAAK,MAAM;QACT;QACAmN,QAAQ,GAAG,IAAInI,IAAI,CAACuI,WAAW,CAACjC,WAAW,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;QACpD6B,QAAQ,CAACC,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC;QAC1B,IAAI,CAACpN,UAAU,GAAG,QAAQ;QAC1B;MAEF,KAAK,YAAY;QACf;QACA,MAAMqN,QAAQ,GAAG,IAAIrI,IAAI,CAACuI,WAAW,CAAC;QACtCF,QAAQ,CAACC,OAAO,CAACD,QAAQ,CAAC7B,OAAO,EAAE,GAAG,EAAE,CAAC;QACzC6B,QAAQ,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC7BD,QAAQ,GAAGE,QAAQ;QACnB,IAAI,CAACrN,UAAU,GAAG,SAAS;QAC3B;MAEF,KAAK,aAAa;QAChB;QACA,MAAMyN,SAAS,GAAG,IAAIzI,IAAI,CAACuI,WAAW,CAAC;QACvCE,SAAS,CAACH,OAAO,CAACG,SAAS,CAACjC,OAAO,EAAE,GAAG,GAAG,CAAC;QAC5CiC,SAAS,CAACL,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC9BD,QAAQ,GAAGM,SAAS;QACpB,IAAI,CAACzN,UAAU,GAAG,QAAQ;QAC1B;MAEF,KAAK,UAAU;QACb;QACAmN,QAAQ,GAAG,IAAInI,IAAI,CAACuI,WAAW,CAACjC,WAAW,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;QACpD6B,QAAQ,CAACC,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC;QAC1B,IAAI,CAACpN,UAAU,GAAG,QAAQ;QAC1B;MAEF,KAAK,eAAe;QAClB;QACA;QACAmN,QAAQ,GAAG,IAAInI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,CAAChF,UAAU,GAAG,UAAU;QAC5B;MAEF;QAEE;QACAmN,QAAQ,GAAG,IAAInI,IAAI,CAACuI,WAAW,CAACH,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrD;;IAGJ;IACA,IAAI,CAACvC,gBAAgB,CAAC,IAAI,CAACF,WAAW,EAAE,CAAC,IAAI,CAAChP,WAAW,EAAEwR,QAAQ,EAAEK,MAAM,EAAE,IAAI,CAACxN,UAAU,CAAC;EAC/F;EAIF+B,cAAcA,CAAC2L,KAAU,EAAER,KAAY;IACnCQ,KAAK,CAACC,YAAY,CAAET,KAAK,CAACU,MAA2B,CAACnB,KAAK,EAAE,UAAU,CAAC;EAC5E;EAEAoB,WAAWA,CAAA;IACP,IAAI,IAAI,CAACC,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAACC,WAAW,EAAE;;EAEvC;EAAC,QAAAC,CAAA,G;qBAlkBQxJ,oBAAoB,EAAA7J,EAAA,CAAAsT,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAxT,EAAA,CAAAsT,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA1T,EAAA,CAAAsT,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAA5T,EAAA,CAAAsT,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA9T,EAAA,CAAAsT,iBAAA,CAAAS,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA,G;UAApBpK,oBAAoB;IAAAqK,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCrBjCxU,EAAA,CAAA0C,UAAA,IAAAgS,mCAAA,mBAqnBM;;;QArnBqB1U,EAAA,CAAA+B,UAAA,SAAA0S,GAAA,CAAAjU,eAAA,CAAqB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}