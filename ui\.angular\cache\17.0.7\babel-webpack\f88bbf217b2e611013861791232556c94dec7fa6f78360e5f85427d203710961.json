{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { ListDemoRoutingModule } from './listdemo-routing.module';\nimport { DataViewModule } from 'primeng/dataview';\nimport { PickListModule } from 'primeng/picklist';\nimport { OrderListModule } from 'primeng/orderlist';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { RatingModule } from 'primeng/rating';\nimport { ButtonModule } from 'primeng/button';\nimport * as i0 from \"@angular/core\";\nexport let ListDemoModule = /*#__PURE__*/(() => {\n  class ListDemoModule {\n    static #_ = this.ɵfac = function ListDemoModule_Factory(t) {\n      return new (t || ListDemoModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ListDemoModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, ListDemoRoutingModule, DataViewModule, PickListModule, OrderListModule, InputTextModule, DropdownModule, RatingModule, ButtonModule]\n    });\n  }\n  return ListDemoModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}