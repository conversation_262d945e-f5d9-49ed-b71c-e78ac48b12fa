{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { FormLayoutDemoComponent } from './formlayoutdemo.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class FormLayoutDemoRoutingModule {\n  static #_ = this.ɵfac = function FormLayoutDemoRoutingModule_Factory(t) {\n    return new (t || FormLayoutDemoRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: FormLayoutDemoRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild([{\n      path: '',\n      component: FormLayoutDemoComponent\n    }]), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(FormLayoutDemoRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "FormLayoutDemoComponent", "FormLayoutDemoRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "imports", "i1", "exports"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\uikit\\formlayout\\formlayoutdemo-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule } from '@angular/router';\nimport { FormLayoutDemoComponent } from './formlayoutdemo.component';\n\n@NgModule({\n\timports: [RouterModule.forChild([\n\t\t{ path: '', component: FormLayoutDemoComponent }\n\t])],\n\texports: [RouterModule]\n})\nexport class FormLayoutDemoRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,uBAAuB,QAAQ,4BAA4B;;;AAQpE,OAAM,MAAOC,2BAA2B;EAAA,QAAAC,CAAA,G;qBAA3BD,2BAA2B;EAAA;EAAA,QAAAE,EAAA,G;UAA3BF;EAA2B;EAAA,QAAAG,EAAA,G;cAL7BL,YAAY,CAACM,QAAQ,CAAC,CAC/B;MAAEC,IAAI,EAAE,EAAE;MAAEC,SAAS,EAAEP;IAAuB,CAAE,CAChD,CAAC,EACQD,YAAY;EAAA;;;2EAEVE,2BAA2B;IAAAO,OAAA,GAAAC,EAAA,CAAAV,YAAA;IAAAW,OAAA,GAF7BX,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}