{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../layout/service/app.layout.service\";\nimport * as i2 from \"../../service/stations.service\";\nimport * as i3 from \"../../service/providers.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"../../service/cache.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"primeng/breadcrumb\";\nfunction ProvidersComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtext(1, \" You are not registered to any provider. You can start adding providers now! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProvidersComponent_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18)(2, \"div\")(3, \"label\", 19);\n    i0.ɵɵtext(4, \"Provider\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 20);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\")(8, \"label\", 19);\n    i0.ɵɵtext(9, \"Username\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\")(13, \"label\", 19);\n    i0.ɵɵtext(14, \"Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\");\n    i0.ɵɵtext(16, \" ********* \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\")(18, \"label\", 19);\n    i0.ɵɵtext(19, \"Station\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 21)(23, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ProvidersComponent_div_9_div_1_Template_button_click_23_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r8);\n      const i_r6 = restoredCtx.index;\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.editProvider(i_r6));\n    });\n    i0.ɵɵtext(24, \" Edit \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function ProvidersComponent_div_9_div_1_Template_button_click_25_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r8);\n      const i_r6 = restoredCtx.index;\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.removeProvider(i_r6));\n    });\n    i0.ɵɵtext(26, \" Remove \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const provider_r5 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r4.getSelectedProviderName(provider_r5.providerId));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(provider_r5.configuration.Username);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\" \", provider_r5.configuration.Stations[0].StationName, \" \");\n  }\n}\nfunction ProvidersComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, ProvidersComponent_div_9_div_1_Template, 27, 3, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.userProviders);\n  }\n}\nfunction ProvidersComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"p\", 25);\n    i0.ɵɵtext(2, \" You are not registered to any provider. You can start adding providers now! \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProvidersComponent_div_16_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const p_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", p_r15.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(p_r15.name);\n  }\n}\nfunction ProvidersComponent_div_16_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"label\", 19);\n    i0.ɵɵtext(2, \"Portfolio Id\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 34);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProvidersComponent_div_16_div_16_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const station_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", station_r17.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(station_r17.name);\n  }\n}\nfunction ProvidersComponent_div_16_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"label\", 35);\n    i0.ɵɵtext(2, \"Select Station\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"select\", 36);\n    i0.ɵɵtemplate(4, ProvidersComponent_div_16_div_16_option_4_Template, 2, 2, \"option\", 28);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const provider_r10 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", (tmp_0_0 = provider_r10.get(\"stations\")) == null ? null : tmp_0_0.value);\n  }\n}\nfunction ProvidersComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 18)(2, \"div\")(3, \"label\", 19);\n    i0.ɵɵtext(4, \"Provider\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"select\", 27);\n    i0.ɵɵtemplate(6, ProvidersComponent_div_16_option_6_Template, 2, 2, \"option\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\")(8, \"label\", 19);\n    i0.ɵɵtext(9, \"Username\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\")(12, \"label\", 19);\n    i0.ɵɵtext(13, \"Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, ProvidersComponent_div_16_div_15_Template, 4, 0, \"div\", 8)(16, ProvidersComponent_div_16_div_16_Template, 5, 1, \"div\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function ProvidersComponent_div_16_Template_button_click_17_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r20);\n      const i_r11 = restoredCtx.index;\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.removeProvider(i_r11));\n    });\n    i0.ɵɵtext(18, \" Remove \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function ProvidersComponent_div_16_Template_button_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r20);\n      const i_r11 = restoredCtx.index;\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.getStations(i_r11));\n    });\n    i0.ɵɵtext(20, \" Get Stations \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const provider_r10 = ctx.$implicit;\n    const i_r11 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext();\n    let tmp_3_0;\n    let tmp_4_0;\n    i0.ɵɵproperty(\"formGroupName\", i_r11);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.availableProviders);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.needsPortfolio(ctx_r3.index));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = provider_r10.get(\"stations\")) == null ? null : tmp_3_0.value.length) > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !((tmp_4_0 = provider_r10.get(\"providerId\")) == null ? null : tmp_4_0.value));\n  }\n}\nconst _c0 = () => ({\n  label: \"Providers\"\n});\nconst _c1 = a0 => [a0];\nconst _c2 = () => ({\n  icon: \"pi pi-home\"\n});\nexport class ProvidersComponent {\n  constructor(layoutService, stationsService, providersService, messageService, fb, cacheService) {\n    this.layoutService = layoutService;\n    this.stationsService = stationsService;\n    this.providersService = providersService;\n    this.messageService = messageService;\n    this.fb = fb;\n    this.cacheService = cacheService;\n    this.availableProviders = [];\n    this.userProviders = [];\n    this.stations = [];\n    this.userProvidersForm = this.fb.group({\n      providers: this.fb.array([])\n    });\n  }\n  ngOnInit() {\n    this.providersService.getProviders().then(data => {\n      this.availableProviders = data;\n    });\n    this.getUserProviders();\n    this.addProvider(); // Ξεκινάμε με μία προεπιλεγμένη σειρά\n  }\n\n  addProvider() {\n    const providerGroup = this.fb.group({\n      providerId: ['', Validators.required],\n      username: ['', Validators.required],\n      password: ['', Validators.required],\n      portfolioId: [''],\n      stations: [[]],\n      selectedStation: ['']\n    });\n    this.providers.push(providerGroup);\n  }\n  get providers() {\n    return this.userProvidersForm.get('providers');\n  }\n  getSelectedProviderName(id) {\n    return this.availableProviders.find(p => p.id == id).name;\n  }\n  removeProvider(index) {\n    this.providers.removeAt(index);\n  }\n  getStations(index) {\n    const providerId = this.providers.at(index).get('providerId')?.value;\n    if (!providerId) return;\n    let request = {\n      providerId: this.providers.at(index).get('providerId')?.value,\n      username: this.providers.at(index).get('username')?.value,\n      password: this.providers.at(index).get('password')?.value,\n      portfolioId: this.providers.at(index).get('portfolioId')?.value\n    };\n    console.log('Form Data:', request);\n    this.stationsService.getStations(request).then(data => {\n      this.providers.at(index).patchValue({\n        stations: data\n      });\n    });\n  }\n  onSubmit() {\n    if (this.userProvidersForm.valid) {\n      console.log('Form Data:', this.userProvidersForm.value.providers);\n      // Εδώ μπορείς να κάνεις POST τα δεδομένα στο API\n      let request = {\n        providers: this.userProvidersForm.value.providers\n      };\n      this.providersService.saveUserProviders(request).then(data => {\n        console.log(data);\n        this.getUserProviders();\n      });\n    }\n  }\n  needsPortfolio(index) {\n    return this.providers && this.providers.length > index && this.providers.at(index).get('providerId')?.value === 4;\n  }\n  getUserProviders() {\n    this.providersService.getUserProviders().then(data => {\n      this.userProviders = data;\n      console.log(this.userProviders);\n      this.userProviders.forEach(up => {\n        up.configuration = JSON.parse(up.configuration);\n      });\n      if (data.length > 0) {\n        this.stationsService.getUserStations().then(data => {\n          this.stations = data;\n          this.cacheService.setStations(this.stations);\n        });\n      }\n    });\n  }\n  static #_ = this.ɵfac = function ProvidersComponent_Factory(t) {\n    return new (t || ProvidersComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.StationsService), i0.ɵɵdirectiveInject(i3.ProvidersService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.FormBuilder), i0.ɵɵdirectiveInject(i6.CacheService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProvidersComponent,\n    selectors: [[\"app-providers\"]],\n    decls: 21,\n    vars: 11,\n    consts: [[1, \"grid\"], [1, \"col-12\"], [3, \"model\", \"home\"], [1, \"col-12\", \"lg:col-12\", \"xl:col-12\"], [1, \"card\", \"p-4\"], [1, \"container\"], [1, \"text-xl\", \"font-semibold\", \"mb-4\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"text-center text-lg\", 4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\"], [\"formArrayName\", \"providers\"], [\"class\", \"mb-4 border p-3 rounded shadow\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 1, \"mt-4\", \"bg-blue-500\", \"text-white\", \"p-2\", \"rounded\", 3, \"click\"], [\"type\", \"submit\", 1, \"mt-4\", \"bg-green-500\", \"text-white\", \"p-2\", \"rounded\"], [1, \"alert\", \"alert-info\"], [\"class\", \"mb-4 border p-4 rounded shadow\", 4, \"ngFor\", \"ngForOf\"], [1, \"mb-4\", \"border\", \"p-4\", \"rounded\", \"shadow\"], [1, \"grid\", \"grid-cols-3\", \"gap-4\"], [1, \"block\", \"font-medium\", \"mb-1\"], [1, \"font-medium\"], [1, \"mt-3\"], [\"type\", \"button\", 1, \"bg-yellow-500\", \"text-white\", \"py-2\", \"px-4\", \"rounded\", \"mr-2\", 3, \"click\"], [\"type\", \"button\", 1, \"bg-red-500\", \"text-white\", \"py-2\", \"px-4\", \"rounded\", 3, \"click\"], [1, \"text-center\", \"text-lg\"], [1, \"text-red-500\", \"font-semibold\"], [1, \"mb-4\", \"border\", \"p-3\", \"rounded\", \"shadow\", 3, \"formGroupName\"], [\"formControlName\", \"providerId\", 1, \"w-full\", \"p-2\", \"border\", \"rounded\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"formControlName\", \"username\", \"placeholder\", \"Enter username\", 1, \"w-full\", \"p-2\", \"border\", \"rounded\"], [\"type\", \"text\", \"formControlName\", \"password\", \"placeholder\", \"Enter Password\", 1, \"w-full\", \"p-2\", \"border\", \"rounded\"], [\"type\", \"button\", 1, \"mt-2\", \"text-red-500\", 3, \"click\"], [\"type\", \"button\", 1, \"mt-2\", \"text-blue-500\", 3, \"disabled\", \"click\"], [3, \"value\"], [\"type\", \"text\", \"formControlName\", \"portfolioId\", \"placeholder\", \"Enter Your Portfolio Id\", 1, \"w-full\", \"p-2\", \"border\", \"rounded\"], [1, \"block\", \"font-medium\", \"mt-2\"], [\"formControlName\", \"selectedStation\", 1, \"w-full\", \"p-2\", \"border\", \"rounded\"]],\n    template: function ProvidersComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"p-breadcrumb\", 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"h2\", 6);\n        i0.ɵɵtext(7, \"Your Providers\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(8, ProvidersComponent_div_8_Template, 2, 0, \"div\", 7)(9, ProvidersComponent_div_9_Template, 2, 1, \"div\", 8);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 4);\n        i0.ɵɵtemplate(11, ProvidersComponent_div_11_Template, 3, 0, \"div\", 9);\n        i0.ɵɵelementStart(12, \"h2\", 6);\n        i0.ɵɵtext(13, \"Add Providers\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"form\", 10);\n        i0.ɵɵlistener(\"ngSubmit\", function ProvidersComponent_Template_form_ngSubmit_14_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(15, \"div\", 11);\n        i0.ɵɵtemplate(16, ProvidersComponent_div_16_Template, 21, 5, \"div\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"button\", 13);\n        i0.ɵɵlistener(\"click\", function ProvidersComponent_Template_button_click_17_listener() {\n          return ctx.addProvider();\n        });\n        i0.ɵɵtext(18, \" + Add Provider \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"button\", 14);\n        i0.ɵɵtext(20, \" Save Providers \");\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"model\", i0.ɵɵpureFunction1(8, _c1, i0.ɵɵpureFunction0(7, _c0)))(\"home\", i0.ɵɵpureFunction0(10, _c2));\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngIf\", ctx.userProviders.length === 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.userProviders.length > 0);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.userProviders.length == 0);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"formGroup\", ctx.userProvidersForm);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.providers.controls);\n      }\n    },\n    dependencies: [i7.NgForOf, i7.NgIf, i5.ɵNgNoValidate, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.FormGroupDirective, i5.FormControlName, i5.FormGroupName, i5.FormArrayName, i8.Breadcrumb],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ProvidersComponent_div_9_div_1_Template_button_click_23_listener", "restoredCtx", "ɵɵrestoreView", "_r8", "i_r6", "index", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "edit<PERSON><PERSON><PERSON>", "ProvidersComponent_div_9_div_1_Template_button_click_25_listener", "ctx_r9", "removeProvider", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r4", "getSelectedProviderName", "provider_r5", "providerId", "configuration", "Username", "ɵɵtextInterpolate1", "Stations", "StationName", "ɵɵtemplate", "ProvidersComponent_div_9_div_1_Template", "ɵɵproperty", "ctx_r1", "userProviders", "p_r15", "id", "name", "ɵɵelement", "station_r17", "ProvidersComponent_div_16_div_16_option_4_Template", "tmp_0_0", "provider_r10", "get", "value", "ProvidersComponent_div_16_option_6_Template", "ProvidersComponent_div_16_div_15_Template", "ProvidersComponent_div_16_div_16_Template", "ProvidersComponent_div_16_Template_button_click_17_listener", "_r20", "i_r11", "ctx_r19", "ProvidersComponent_div_16_Template_button_click_19_listener", "ctx_r21", "getStations", "ctx_r3", "availableProviders", "needsPortfolio", "tmp_3_0", "length", "tmp_4_0", "ProvidersComponent", "constructor", "layoutService", "stationsService", "providersService", "messageService", "fb", "cacheService", "stations", "userProvidersForm", "group", "providers", "array", "ngOnInit", "getProviders", "then", "data", "getUserProviders", "addProvider", "providerGroup", "required", "username", "password", "portfolioId", "selectedStation", "push", "find", "p", "removeAt", "at", "request", "console", "log", "patchValue", "onSubmit", "valid", "saveUserProviders", "for<PERSON>ach", "up", "JSON", "parse", "getUserStations", "setStations", "_", "ɵɵdirectiveInject", "i1", "LayoutService", "i2", "StationsService", "i3", "ProvidersService", "i4", "MessageService", "i5", "FormBuilder", "i6", "CacheService", "_2", "selectors", "decls", "vars", "consts", "template", "ProvidersComponent_Template", "rf", "ctx", "ProvidersComponent_div_8_Template", "ProvidersComponent_div_9_Template", "ProvidersComponent_div_11_Template", "ProvidersComponent_Template_form_ngSubmit_14_listener", "ProvidersComponent_div_16_Template", "ProvidersComponent_Template_button_click_17_listener", "ɵɵpureFunction1", "_c1", "ɵɵpureFunction0", "_c0", "_c2", "controls"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\providers\\providers.component.ts", "D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\providers\\providers.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { MessageService } from 'primeng/api';\r\nimport { LayoutService } from '../../../layout/service/app.layout.service';\r\n\r\nimport { GetStationsRequest, SaveUserProvidersRequest } from '../../api/requests';\r\nimport { IProvider, IUserProvider, IUserProviderConfiguration } from '../../api/responses';\r\nimport { Station } from '../../api/station';\r\nimport { CacheService } from '../../service/cache.service';\r\nimport { ProvidersService } from '../../service/providers.service';\r\nimport { StationsService } from '../../service/stations.service';\r\n\r\n@Component({\r\n    selector: 'app-providers',\r\n    templateUrl: './providers.component.html'\r\n})\r\nexport class ProvidersComponent {\r\n\r\n    availableProviders:IProvider[] = [];\r\n    userProviders:IUserProviderConfiguration[] = [];\r\n    userProvidersForm: FormGroup;\r\n    stations: Station[] =[];\r\n\r\n    constructor(public layoutService: LayoutService, \r\n        private stationsService: StationsService,\r\n        private providersService: ProvidersService,\r\n        private messageService: MessageService,\r\n        private fb: FormBuilder,\r\n        private cacheService: CacheService) {\r\n        \r\n        this.userProvidersForm = this.fb.group({\r\n          providers: this.fb.array([])\r\n        });\r\n    }\r\n\r\n    ngOnInit(){\r\n        this.providersService.getProviders().then(data => {\r\n            this.availableProviders = data;\r\n          });\r\n\r\n          this.getUserProviders();\r\n\r\n          this.addProvider(); // Ξεκινάμε με μία προεπιλεγμένη σειρά\r\n    }\r\n\r\n\r\n    addProvider() {\r\n        const providerGroup = this.fb.group({\r\n          providerId: ['', Validators.required],\r\n          username: ['', Validators.required],\r\n          password: ['', Validators.required],\r\n          portfolioId: [''],\r\n          stations: [[]], // Αρχικά empty array για το multiSelect,\r\n          selectedStation:['']\r\n        });\r\n        this.providers.push(providerGroup);\r\n      }\r\n  \r\n      get providers(): FormArray {\r\n        return this.userProvidersForm.get('providers') as FormArray;\r\n      }\r\n\r\n      getSelectedProviderName(id:number): string | undefined {\r\n        return this.availableProviders.find(p => p.id == id).name;\r\n      }\r\n    \r\n  \r\n  \r\n      removeProvider(index: number) {\r\n        this.providers.removeAt(index);\r\n      }\r\n  \r\n      getStations(index: number) {\r\n        const providerId = this.providers.at(index).get('providerId')?.value;\r\n    \r\n        if (!providerId) return;\r\n\r\n        let request: GetStationsRequest = {\r\n          providerId : this.providers.at(index).get('providerId')?.value,\r\n          username: this.providers.at(index).get('username')?.value, \r\n          password: this.providers.at(index).get('password')?.value,\r\n          portfolioId: this.providers.at(index).get('portfolioId')?.value,\r\n        }\r\n        console.log('Form Data:', request);\r\n  \r\n        this.stationsService.getStations(request).then(data => {\r\n          this.providers.at(index).patchValue({ stations: data });\r\n        });\r\n      }\r\n    \r\n      onSubmit() {\r\n        if (this.userProvidersForm.valid) {\r\n          console.log('Form Data:', this.userProvidersForm.value.providers);\r\n          // Εδώ μπορείς να κάνεις POST τα δεδομένα στο API\r\n          let request:SaveUserProvidersRequest = {\r\n            providers : this.userProvidersForm.value.providers\r\n          };\r\n  \r\n          this.providersService.saveUserProviders(request).then(data => {\r\n            console.log(data);  \r\n            this.getUserProviders();\r\n          });\r\n  \r\n        }\r\n      }\r\n\r\n      needsPortfolio(index:number): boolean{\r\n        return this.providers && this.providers.length > index && this.providers.at(index).get('providerId')?.value === 4;\r\n      }\r\n  \r\n      getUserProviders(){\r\n        this.providersService.getUserProviders().then(data => {\r\n          this.userProviders = data;\r\n          console.log(this.userProviders)\r\n          this.userProviders.forEach(up => {\r\n            up.configuration = JSON.parse(up.configuration);\r\n\r\n          })\r\n          if (data.length > 0){\r\n            this.stationsService.getUserStations().then(data => {\r\n              this.stations = data;\r\n              this.cacheService.setStations(this.stations);\r\n            });\r\n          }\r\n        });\r\n      }\r\n    \r\n}\r\n", "<div class=\"grid\">\r\n    <div class=\"col-12\" >\r\n        <p-breadcrumb [model]=\"[{ label: 'Providers' }]\" [home]=\"{icon: 'pi pi-home'}\"></p-breadcrumb>\r\n    </div>\r\n    <div class=\"col-12 lg:col-12 xl:col-12\">\r\n\t\t<div class=\"card p-4\">\r\n            <div class=\"container\">\r\n                <h2 class=\"text-xl font-semibold mb-4\">Your Providers</h2>\r\n                \r\n                <div *ngIf=\"userProviders.length === 0\" class=\"alert alert-info\">\r\n                  You are not registered to any provider. You can start adding providers now!\r\n                </div>\r\n              \r\n                <div *ngIf=\"userProviders.length > 0\">\r\n                  <div *ngFor=\"let provider of userProviders; let i = index\" class=\"mb-4 border p-4 rounded shadow\">\r\n                    <div class=\"grid grid-cols-3 gap-4\">\r\n                      <!-- Provider Info -->\r\n                      <div>\r\n                        <label class=\"block font-medium mb-1\">Provider</label>\r\n                        <div class=\"font-medium\">{{ getSelectedProviderName(provider.providerId) }}</div>\r\n                      </div>\r\n              \r\n                      <!-- Username Info -->\r\n                      <div>\r\n                        <label class=\"block font-medium mb-1\">Username</label>\r\n                        <div>{{ provider.configuration.Username }}</div>\r\n                      </div>\r\n              \r\n                      <!-- Password Info -->\r\n                      <div>\r\n                        <label class=\"block font-medium mb-1\">Password</label>\r\n                        <div> ********* </div>\r\n                      </div>\r\n\r\n\t\t\t\t\t  <div>\r\n                        <label class=\"block font-medium mb-1\">Station</label>\r\n                        <div> {{provider.configuration.Stations[0].StationName}} </div>\r\n                      </div>\r\n                    </div>\r\n              \r\n                    <div class=\"mt-3\">\r\n                      <button type=\"button\" class=\"bg-yellow-500 text-white py-2 px-4 rounded mr-2\"\r\n                              (click)=\"editProvider(i)\">\r\n                        Edit\r\n                      </button>\r\n                      <button type=\"button\" class=\"bg-red-500 text-white py-2 px-4 rounded\"\r\n                              (click)=\"removeProvider(i)\">\r\n                        Remove\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              \r\n        </div>\r\n\r\n        <div class=\"card p-4\">\r\n\t\t  <div *ngIf=\"userProviders.length == 0\" class=\"text-center text-lg\">\r\n\t\t\t<p class=\"text-red-500 font-semibold\">\r\n\t\t\t  You are not registered to any provider. You can start adding providers now!\r\n\t\t\t</p>\r\n\t\t  </div>\r\n          <h2 class=\"text-xl font-semibold mb-4\">Add Providers</h2>\r\n          <!-- Dynamic Form -->\r\n\t\t  <form [formGroup]=\"userProvidersForm\" (ngSubmit)=\"onSubmit()\">\r\n\t\t\t<div formArrayName=\"providers\">\r\n\t\t\t  <div *ngFor=\"let provider of providers.controls; let i = index\" [formGroupName]=\"i\" class=\"mb-4 border p-3 rounded shadow\">\r\n\t\t\t\t<div class=\"grid grid-cols-3 gap-4\">\r\n\t\t\t\t  <!-- Dropdown για επιλογή provider -->\r\n\t\t\t\t  <div>\r\n\t\t\t\t\t<label class=\"block font-medium mb-1\">Provider</label>\r\n\t\t\t\t\t<select formControlName=\"providerId\" class=\"w-full p-2 border rounded\">\r\n\t\t\t\t\t  <option *ngFor=\"let p of availableProviders\" [value]=\"p.id\">{{ p.name }}</option>\r\n\t\t\t\t\t</select>\r\n\t\t\t\t  </div>\r\n\t  \r\n\t\t\t\t  <!-- Input για username -->\r\n\t\t\t\t  <div>\r\n\t\t\t\t\t<label class=\"block font-medium mb-1\">Username</label>\r\n\t\t\t\t\t<input type=\"text\" formControlName=\"username\" class=\"w-full p-2 border rounded\" placeholder=\"Enter username\">\r\n\t\t\t\t  </div>\r\n\t  \r\n\t\t\t\t  <!-- Input για API Key -->\r\n\t\t\t\t  <div>\r\n\t\t\t\t\t<label class=\"block font-medium mb-1\">Password</label>\r\n\t\t\t\t\t<input type=\"text\" formControlName=\"password\" class=\"w-full p-2 border rounded\" placeholder=\"Enter Password\">\r\n\t\t\t\t  </div>\r\n\t\t\t\t  <div *ngIf=\"needsPortfolio(index)\">\r\n\t\t\t\t\t<label class=\"block font-medium mb-1\">Portfolio Id</label>\r\n\t\t\t\t\t<input type=\"text\" formControlName=\"portfolioId\" class=\"w-full p-2 border rounded\" placeholder=\"Enter Your Portfolio Id\">\r\n\t\t\t\t  </div>\r\n\t\t\t\t  <!-- Dropdown για επιλογή σταθμού (εμφανίζεται μόνο αν υπάρχουν stations) -->\r\n\t\t\t\t\t<div *ngIf=\"provider.get('stations')?.value.length > 0\">\r\n\t\t\t\t\t\t<label class=\"block font-medium mt-2\">Select Station</label>\r\n\t\t\t\t\t\t<select formControlName=\"selectedStation\" class=\"w-full p-2 border rounded\">\r\n\t\t\t\t\t\t<option *ngFor=\"let station of provider.get('stations')?.value\" [value]=\"station.id\">{{ station.name }}</option>\r\n\t\t\t\t\t\t</select>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t  \r\n\t\t\t\t</div>\r\n\t  \r\n\t\t\t\t<!-- Remove Button -->\r\n\t\t\t\t<button type=\"button\" class=\"mt-2 text-red-500\" (click)=\"removeProvider(i)\">\r\n\t\t\t\t  Remove\r\n\t\t\t\t</button>\r\n\t\t\t\t<button type=\"button\" class=\"mt-2 text-blue-500\" \r\n\t\t\t\t\t\t[disabled]=\"!provider.get('providerId')?.value\" \r\n\t\t\t\t\t\t(click)=\"getStations(i)\">\r\n\t\t\t\t\t\tGet Stations\r\n\t\t\t\t</button>\r\n\r\n\t\t\t\t\r\n\t\t\t  </div>\r\n\t\t\t</div>\r\n\t  \r\n\t\t\t<!-- Add Provider Button -->\r\n\t\t\t<button type=\"button\" class=\"mt-4 bg-blue-500 text-white p-2 rounded\" (click)=\"addProvider()\">\r\n\t\t\t  + Add Provider\r\n\t\t\t</button>\r\n\t  \r\n\t\t\t<!-- Submit Button -->\r\n\t\t\t<button type=\"submit\" class=\"mt-4 bg-green-500 text-white p-2 rounded\">\r\n\t\t\t  Save Providers\r\n\t\t\t</button>\r\n\t\t  </form>\r\n\t\t</div>\r\n\t  </div>\r\n</div>"], "mappings": "AACA,SAA4CA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;ICQ9DC,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAE,MAAA,oFACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAGJH,EAAA,CAAAC,cAAA,cAAkG;IAItDD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtDH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAkD;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAInFH,EAAA,CAAAC,cAAA,UAAK;IACmCD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtDH,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAE,MAAA,IAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAIlDH,EAAA,CAAAC,cAAA,WAAK;IACmCD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtDH,EAAA,CAAAC,cAAA,WAAK;IAACD,EAAA,CAAAE,MAAA,mBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGvCH,EAAA,CAAAC,cAAA,WAAK;IACkDD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrDH,EAAA,CAAAC,cAAA,WAAK;IAACD,EAAA,CAAAE,MAAA,IAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAInEH,EAAA,CAAAC,cAAA,eAAkB;IAERD,EAAA,CAAAI,UAAA,mBAAAC,iEAAA;MAAA,MAAAC,WAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,IAAA,GAAAH,WAAA,CAAAI,KAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAF,MAAA,CAAAG,YAAA,CAAAL,IAAA,CAAe;IAAA,EAAC;IAC/BT,EAAA,CAAAE,MAAA,cACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACoC;IAA5BD,EAAA,CAAAI,UAAA,mBAAAW,iEAAA;MAAA,MAAAT,WAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,IAAA,GAAAH,WAAA,CAAAI,KAAA;MAAA,MAAAM,MAAA,GAAAhB,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAG,MAAA,CAAAC,cAAA,CAAAR,IAAA,CAAiB;IAAA,EAAC;IACjCT,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IA7BkBH,EAAA,CAAAkB,SAAA,GAAkD;IAAlDlB,EAAA,CAAAmB,iBAAA,CAAAC,MAAA,CAAAC,uBAAA,CAAAC,WAAA,CAAAC,UAAA,EAAkD;IAMtEvB,EAAA,CAAAkB,SAAA,GAAqC;IAArClB,EAAA,CAAAmB,iBAAA,CAAAG,WAAA,CAAAE,aAAA,CAAAC,QAAA,CAAqC;IAWpCzB,EAAA,CAAAkB,SAAA,IAAmD;IAAnDlB,EAAA,CAAA0B,kBAAA,MAAAJ,WAAA,CAAAE,aAAA,CAAAG,QAAA,IAAAC,WAAA,MAAmD;;;;;IAvBjE5B,EAAA,CAAAC,cAAA,UAAsC;IACpCD,EAAA,CAAA6B,UAAA,IAAAC,uCAAA,mBAoCM;IACR9B,EAAA,CAAAG,YAAA,EAAM;;;;IArCsBH,EAAA,CAAAkB,SAAA,GAAkB;IAAlBlB,EAAA,CAAA+B,UAAA,YAAAC,MAAA,CAAAC,aAAA,CAAkB;;;;;IA2C1DjC,EAAA,CAAAC,cAAA,cAAmE;IAElED,EAAA,CAAAE,MAAA,oFACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAYAH,EAAA,CAAAC,cAAA,iBAA4D;IAAAD,EAAA,CAAAE,MAAA,GAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAApCH,EAAA,CAAA+B,UAAA,UAAAG,KAAA,CAAAC,EAAA,CAAc;IAACnC,EAAA,CAAAkB,SAAA,GAAY;IAAZlB,EAAA,CAAAmB,iBAAA,CAAAe,KAAA,CAAAE,IAAA,CAAY;;;;;IAezEpC,EAAA,CAAAC,cAAA,UAAmC;IACED,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1DH,EAAA,CAAAqC,SAAA,gBAAyH;IACxHrC,EAAA,CAAAG,YAAA,EAAM;;;;;IAKNH,EAAA,CAAAC,cAAA,iBAAqF;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAhDH,EAAA,CAAA+B,UAAA,UAAAO,WAAA,CAAAH,EAAA,CAAoB;IAACnC,EAAA,CAAAkB,SAAA,GAAkB;IAAlBlB,EAAA,CAAAmB,iBAAA,CAAAmB,WAAA,CAAAF,IAAA,CAAkB;;;;;IAHxGpC,EAAA,CAAAC,cAAA,UAAwD;IACjBD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5DH,EAAA,CAAAC,cAAA,iBAA4E;IAC5ED,EAAA,CAAA6B,UAAA,IAAAU,kDAAA,qBAAgH;IAChHvC,EAAA,CAAAG,YAAA,EAAS;;;;;IADmBH,EAAA,CAAAkB,SAAA,GAAkC;IAAlClB,EAAA,CAAA+B,UAAA,aAAAS,OAAA,GAAAC,YAAA,CAAAC,GAAA,+BAAAF,OAAA,CAAAG,KAAA,CAAkC;;;;;;IA7B/D3C,EAAA,CAAAC,cAAA,cAA2H;IAIrFD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtDH,EAAA,CAAAC,cAAA,iBAAuE;IACrED,EAAA,CAAA6B,UAAA,IAAAe,2CAAA,qBAAiF;IACnF5C,EAAA,CAAAG,YAAA,EAAS;IAIRH,EAAA,CAAAC,cAAA,UAAK;IACgCD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtDH,EAAA,CAAAqC,SAAA,iBAA6G;IAC5GrC,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IACgCD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtDH,EAAA,CAAAqC,SAAA,iBAA6G;IAC5GrC,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAA6B,UAAA,KAAAgB,yCAAA,iBAGM,KAAAC,yCAAA;IASR9C,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,kBAA4E;IAA5BD,EAAA,CAAAI,UAAA,mBAAA2C,4DAAA;MAAA,MAAAzC,WAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAyC,IAAA;MAAA,MAAAC,KAAA,GAAA3C,WAAA,CAAAI,KAAA;MAAA,MAAAwC,OAAA,GAAAlD,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAqC,OAAA,CAAAjC,cAAA,CAAAgC,KAAA,CAAiB;IAAA,EAAC;IACzEjD,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAE2B;IAAzBD,EAAA,CAAAI,UAAA,mBAAA+C,4DAAA;MAAA,MAAA7C,WAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAyC,IAAA;MAAA,MAAAC,KAAA,GAAA3C,WAAA,CAAAI,KAAA;MAAA,MAAA0C,OAAA,GAAApD,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAuC,OAAA,CAAAC,WAAA,CAAAJ,KAAA,CAAc;IAAA,EAAC;IACxBjD,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;IA3CwDH,EAAA,CAAA+B,UAAA,kBAAAkB,KAAA,CAAmB;IAM3DjD,EAAA,CAAAkB,SAAA,GAAqB;IAArBlB,EAAA,CAAA+B,UAAA,YAAAuB,MAAA,CAAAC,kBAAA,CAAqB;IAetCvD,EAAA,CAAAkB,SAAA,GAA2B;IAA3BlB,EAAA,CAAA+B,UAAA,SAAAuB,MAAA,CAAAE,cAAA,CAAAF,MAAA,CAAA5C,KAAA,EAA2B;IAK5BV,EAAA,CAAAkB,SAAA,GAAgD;IAAhDlB,EAAA,CAAA+B,UAAA,WAAA0B,OAAA,GAAAhB,YAAA,CAAAC,GAAA,+BAAAe,OAAA,CAAAd,KAAA,CAAAe,MAAA,MAAgD;IAcrD1D,EAAA,CAAAkB,SAAA,GAA+C;IAA/ClB,EAAA,CAAA+B,UAAA,gBAAA4B,OAAA,GAAAlB,YAAA,CAAAC,GAAA,iCAAAiB,OAAA,CAAAhB,KAAA,EAA+C;;;;;;;;;;ADzFrD,OAAM,MAAOiB,kBAAkB;EAO3BC,YAAmBC,aAA4B,EACnCC,eAAgC,EAChCC,gBAAkC,EAClCC,cAA8B,EAC9BC,EAAe,EACfC,YAA0B;IALnB,KAAAL,aAAa,GAAbA,aAAa;IACpB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,YAAY,GAAZA,YAAY;IAVxB,KAAAZ,kBAAkB,GAAe,EAAE;IACnC,KAAAtB,aAAa,GAAgC,EAAE;IAE/C,KAAAmC,QAAQ,GAAa,EAAE;IASnB,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACH,EAAE,CAACI,KAAK,CAAC;MACrCC,SAAS,EAAE,IAAI,CAACL,EAAE,CAACM,KAAK,CAAC,EAAE;KAC5B,CAAC;EACN;EAEAC,QAAQA,CAAA;IACJ,IAAI,CAACT,gBAAgB,CAACU,YAAY,EAAE,CAACC,IAAI,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACrB,kBAAkB,GAAGqB,IAAI;IAChC,CAAC,CAAC;IAEF,IAAI,CAACC,gBAAgB,EAAE;IAEvB,IAAI,CAACC,WAAW,EAAE,CAAC,CAAC;EAC1B;;EAGAA,WAAWA,CAAA;IACP,MAAMC,aAAa,GAAG,IAAI,CAACb,EAAE,CAACI,KAAK,CAAC;MAClC/C,UAAU,EAAE,CAAC,EAAE,EAAExB,UAAU,CAACiF,QAAQ,CAAC;MACrCC,QAAQ,EAAE,CAAC,EAAE,EAAElF,UAAU,CAACiF,QAAQ,CAAC;MACnCE,QAAQ,EAAE,CAAC,EAAE,EAAEnF,UAAU,CAACiF,QAAQ,CAAC;MACnCG,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBf,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdgB,eAAe,EAAC,CAAC,EAAE;KACpB,CAAC;IACF,IAAI,CAACb,SAAS,CAACc,IAAI,CAACN,aAAa,CAAC;EACpC;EAEA,IAAIR,SAASA,CAAA;IACX,OAAO,IAAI,CAACF,iBAAiB,CAAC3B,GAAG,CAAC,WAAW,CAAc;EAC7D;EAEArB,uBAAuBA,CAACc,EAAS;IAC/B,OAAO,IAAI,CAACoB,kBAAkB,CAAC+B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpD,EAAE,IAAIA,EAAE,CAAC,CAACC,IAAI;EAC3D;EAIAnB,cAAcA,CAACP,KAAa;IAC1B,IAAI,CAAC6D,SAAS,CAACiB,QAAQ,CAAC9E,KAAK,CAAC;EAChC;EAEA2C,WAAWA,CAAC3C,KAAa;IACvB,MAAMa,UAAU,GAAG,IAAI,CAACgD,SAAS,CAACkB,EAAE,CAAC/E,KAAK,CAAC,CAACgC,GAAG,CAAC,YAAY,CAAC,EAAEC,KAAK;IAEpE,IAAI,CAACpB,UAAU,EAAE;IAEjB,IAAImE,OAAO,GAAuB;MAChCnE,UAAU,EAAG,IAAI,CAACgD,SAAS,CAACkB,EAAE,CAAC/E,KAAK,CAAC,CAACgC,GAAG,CAAC,YAAY,CAAC,EAAEC,KAAK;MAC9DsC,QAAQ,EAAE,IAAI,CAACV,SAAS,CAACkB,EAAE,CAAC/E,KAAK,CAAC,CAACgC,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK;MACzDuC,QAAQ,EAAE,IAAI,CAACX,SAAS,CAACkB,EAAE,CAAC/E,KAAK,CAAC,CAACgC,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK;MACzDwC,WAAW,EAAE,IAAI,CAACZ,SAAS,CAACkB,EAAE,CAAC/E,KAAK,CAAC,CAACgC,GAAG,CAAC,aAAa,CAAC,EAAEC;KAC3D;IACDgD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEF,OAAO,CAAC;IAElC,IAAI,CAAC3B,eAAe,CAACV,WAAW,CAACqC,OAAO,CAAC,CAACf,IAAI,CAACC,IAAI,IAAG;MACpD,IAAI,CAACL,SAAS,CAACkB,EAAE,CAAC/E,KAAK,CAAC,CAACmF,UAAU,CAAC;QAAEzB,QAAQ,EAAEQ;MAAI,CAAE,CAAC;IACzD,CAAC,CAAC;EACJ;EAEAkB,QAAQA,CAAA;IACN,IAAI,IAAI,CAACzB,iBAAiB,CAAC0B,KAAK,EAAE;MAChCJ,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACvB,iBAAiB,CAAC1B,KAAK,CAAC4B,SAAS,CAAC;MACjE;MACA,IAAImB,OAAO,GAA4B;QACrCnB,SAAS,EAAG,IAAI,CAACF,iBAAiB,CAAC1B,KAAK,CAAC4B;OAC1C;MAED,IAAI,CAACP,gBAAgB,CAACgC,iBAAiB,CAACN,OAAO,CAAC,CAACf,IAAI,CAACC,IAAI,IAAG;QAC3De,OAAO,CAACC,GAAG,CAAChB,IAAI,CAAC;QACjB,IAAI,CAACC,gBAAgB,EAAE;MACzB,CAAC,CAAC;;EAGN;EAEArB,cAAcA,CAAC9C,KAAY;IACzB,OAAO,IAAI,CAAC6D,SAAS,IAAI,IAAI,CAACA,SAAS,CAACb,MAAM,GAAGhD,KAAK,IAAI,IAAI,CAAC6D,SAAS,CAACkB,EAAE,CAAC/E,KAAK,CAAC,CAACgC,GAAG,CAAC,YAAY,CAAC,EAAEC,KAAK,KAAK,CAAC;EACnH;EAEAkC,gBAAgBA,CAAA;IACd,IAAI,CAACb,gBAAgB,CAACa,gBAAgB,EAAE,CAACF,IAAI,CAACC,IAAI,IAAG;MACnD,IAAI,CAAC3C,aAAa,GAAG2C,IAAI;MACzBe,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC3D,aAAa,CAAC;MAC/B,IAAI,CAACA,aAAa,CAACgE,OAAO,CAACC,EAAE,IAAG;QAC9BA,EAAE,CAAC1E,aAAa,GAAG2E,IAAI,CAACC,KAAK,CAACF,EAAE,CAAC1E,aAAa,CAAC;MAEjD,CAAC,CAAC;MACF,IAAIoD,IAAI,CAAClB,MAAM,GAAG,CAAC,EAAC;QAClB,IAAI,CAACK,eAAe,CAACsC,eAAe,EAAE,CAAC1B,IAAI,CAACC,IAAI,IAAG;UACjD,IAAI,CAACR,QAAQ,GAAGQ,IAAI;UACpB,IAAI,CAACT,YAAY,CAACmC,WAAW,CAAC,IAAI,CAAClC,QAAQ,CAAC;QAC9C,CAAC,CAAC;;IAEN,CAAC,CAAC;EACJ;EAAC,QAAAmC,CAAA,G;qBA7GM3C,kBAAkB,EAAA5D,EAAA,CAAAwG,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA1G,EAAA,CAAAwG,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA5G,EAAA,CAAAwG,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAA9G,EAAA,CAAAwG,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAhH,EAAA,CAAAwG,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAAlH,EAAA,CAAAwG,iBAAA,CAAAW,EAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlBzD,kBAAkB;IAAA0D,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCjB/B5H,EAAA,CAAAC,cAAA,aAAkB;QAEVD,EAAA,CAAAqC,SAAA,sBAA8F;QAClGrC,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,aAAwC;QAGWD,EAAA,CAAAE,MAAA,qBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAE1DH,EAAA,CAAA6B,UAAA,IAAAiG,iCAAA,iBAEM,IAAAC,iCAAA;QAyCR/H,EAAA,CAAAG,YAAA,EAAM;QAIZH,EAAA,CAAAC,cAAA,cAAsB;QAC1BD,EAAA,CAAA6B,UAAA,KAAAmG,kCAAA,iBAIM;QACAhI,EAAA,CAAAC,cAAA,aAAuC;QAAAD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAE/DH,EAAA,CAAAC,cAAA,gBAA8D;QAAxBD,EAAA,CAAAI,UAAA,sBAAA6H,sDAAA;UAAA,OAAYJ,GAAA,CAAA/B,QAAA,EAAU;QAAA,EAAC;QAC9D9F,EAAA,CAAAC,cAAA,eAA+B;QAC7BD,EAAA,CAAA6B,UAAA,KAAAqG,kCAAA,mBA8CM;QACRlI,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAC,cAAA,kBAA8F;QAAxBD,EAAA,CAAAI,UAAA,mBAAA+H,qDAAA;UAAA,OAASN,GAAA,CAAA/C,WAAA,EAAa;QAAA,EAAC;QAC3F9E,EAAA,CAAAE,MAAA,wBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAGTH,EAAA,CAAAC,cAAA,kBAAuE;QACrED,EAAA,CAAAE,MAAA,wBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;QAzHUH,EAAA,CAAAkB,SAAA,GAAkC;QAAlClB,EAAA,CAAA+B,UAAA,UAAA/B,EAAA,CAAAoI,eAAA,IAAAC,GAAA,EAAArI,EAAA,CAAAsI,eAAA,IAAAC,GAAA,GAAkC,SAAAvI,EAAA,CAAAsI,eAAA,KAAAE,GAAA;QAOlCxI,EAAA,CAAAkB,SAAA,GAAgC;QAAhClB,EAAA,CAAA+B,UAAA,SAAA8F,GAAA,CAAA5F,aAAA,CAAAyB,MAAA,OAAgC;QAIhC1D,EAAA,CAAAkB,SAAA,GAA8B;QAA9BlB,EAAA,CAAA+B,UAAA,SAAA8F,GAAA,CAAA5F,aAAA,CAAAyB,MAAA,KAA8B;QA4C1C1D,EAAA,CAAAkB,SAAA,GAA+B;QAA/BlB,EAAA,CAAA+B,UAAA,SAAA8F,GAAA,CAAA5F,aAAA,CAAAyB,MAAA,MAA+B;QAO/B1D,EAAA,CAAAkB,SAAA,GAA+B;QAA/BlB,EAAA,CAAA+B,UAAA,cAAA8F,GAAA,CAAAxD,iBAAA,CAA+B;QAEVrE,EAAA,CAAAkB,SAAA,GAAuB;QAAvBlB,EAAA,CAAA+B,UAAA,YAAA8F,GAAA,CAAAtD,SAAA,CAAAkE,QAAA,CAAuB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}