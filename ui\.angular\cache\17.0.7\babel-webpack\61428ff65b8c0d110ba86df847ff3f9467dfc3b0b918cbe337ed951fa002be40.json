{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let UIRoutingModule = /*#__PURE__*/(() => {\n  class UIRoutingModule {\n    static #_ = this.ɵfac = function UIRoutingModule_Factory(t) {\n      return new (t || UIRoutingModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: UIRoutingModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild([{\n        path: 'index',\n        data: {\n          breadcrumb: 'Index'\n        },\n        loadChildren: () => import('.././index/index.module').then(m => m.IndexModule)\n      }, {\n        path: 'station/:id',\n        data: {\n          breadcrumb: 'Stations'\n        },\n        loadChildren: () => import('.././stations/view.module').then(m => m.ViewStationModule)\n      }, {\n        path: 'add',\n        data: {\n          breadcrumb: 'Add Station'\n        },\n        loadChildren: () => import('.././stations/add.module').then(m => m.AddStationModule)\n      }, {\n        path: 'filters',\n        data: {\n          breadcrumb: 'Filters'\n        },\n        loadChildren: () => import('.././filters/filters.module').then(m => m.FiltersModule)\n      }, {\n        path: 'communication',\n        data: {\n          breadcrumb: 'Communication'\n        },\n        loadChildren: () => import('.././communication/communication.module').then(m => m.CommunicationModule)\n      }, {\n        path: 'login',\n        data: {\n          breadcrumb: 'Login'\n        },\n        loadChildren: () => import('.././auth/login/login.module').then(m => m.LoginModule)\n      }, {\n        path: 'providers',\n        data: {\n          breadcrumb: 'Providers'\n        },\n        loadChildren: () => import('.././providers/providers.module').then(m => m.ProvidersModule)\n      }, {\n        path: 'help',\n        data: {\n          breadcrumb: 'Help'\n        },\n        loadChildren: () => import('.././help/help.module').then(m => m.HelpModule)\n      }, {\n        path: 'profile',\n        data: {\n          breadcrumb: 'Profile'\n        },\n        loadChildren: () => import('.././settings/profile.module').then(m => m.ProfileModule)\n      }, {\n        path: 'button',\n        data: {\n          breadcrumb: 'Button'\n        },\n        loadChildren: () => import('./button/buttondemo.module').then(m => m.ButtonDemoModule)\n      }, {\n        path: 'dashboard',\n        data: {\n          breadcrumb: 'Dashboard'\n        },\n        loadChildren: () => import('.././dashboard/dashboard.module').then(m => m.DashboardModule)\n      }, {\n        path: 'charts',\n        data: {\n          breadcrumb: 'Charts'\n        },\n        loadChildren: () => import('./charts/chartsdemo.module').then(m => m.ChartsDemoModule)\n      }, {\n        path: 'file',\n        data: {\n          breadcrumb: 'File'\n        },\n        loadChildren: () => import('./file/filedemo.module').then(m => m.FileDemoModule)\n      }, {\n        path: 'floatlabel',\n        data: {\n          breadcrumb: 'Float Label'\n        },\n        loadChildren: () => import('./floatlabel/floatlabeldemo.module').then(m => m.FloatlabelDemoModule)\n      }, {\n        path: 'formlayout',\n        data: {\n          breadcrumb: 'Form Layout'\n        },\n        loadChildren: () => import('./formlayout/formlayoutdemo.module').then(m => m.FormLayoutDemoModule)\n      }, {\n        path: 'input',\n        data: {\n          breadcrumb: 'Input'\n        },\n        loadChildren: () => import('./input/inputdemo.module').then(m => m.InputDemoModule)\n      }, {\n        path: 'invalidstate',\n        data: {\n          breadcrumb: 'Invalid State'\n        },\n        loadChildren: () => import('./invalid/invalidstatedemo.module').then(m => m.InvalidStateDemoModule)\n      }, {\n        path: 'list',\n        data: {\n          breadcrumb: 'List'\n        },\n        loadChildren: () => import('./list/listdemo.module').then(m => m.ListDemoModule)\n      }, {\n        path: 'media',\n        data: {\n          breadcrumb: 'Media'\n        },\n        loadChildren: () => import('./media/mediademo.module').then(m => m.MediaDemoModule)\n      }, {\n        path: 'message',\n        data: {\n          breadcrumb: 'Message'\n        },\n        loadChildren: () => import('./messages/messagesdemo.module').then(m => m.MessagesDemoModule)\n      }, {\n        path: 'misc',\n        data: {\n          breadcrumb: 'Misc'\n        },\n        loadChildren: () => import('./misc/miscdemo.module').then(m => m.MiscDemoModule)\n      }, {\n        path: 'overlay',\n        data: {\n          breadcrumb: 'Overlay'\n        },\n        loadChildren: () => import('./overlays/overlaysdemo.module').then(m => m.OverlaysDemoModule)\n      }, {\n        path: 'panel',\n        data: {\n          breadcrumb: 'Panel'\n        },\n        loadChildren: () => import('./panels/panelsdemo.module').then(m => m.PanelsDemoModule)\n      }, {\n        path: 'table',\n        data: {\n          breadcrumb: 'Table'\n        },\n        loadChildren: () => import('./table/tabledemo.module').then(m => m.TableDemoModule)\n      }, {\n        path: 'tree',\n        data: {\n          breadcrumb: 'Tree'\n        },\n        loadChildren: () => import('./tree/treedemo.module').then(m => m.TreeDemoModule)\n      }, {\n        path: 'menu',\n        data: {\n          breadcrumb: 'Menu'\n        },\n        loadChildren: () => import('./menus/menus.module').then(m => m.MenusModule)\n      }, {\n        path: '**',\n        redirectTo: '/notfound'\n      }]), RouterModule]\n    });\n  }\n  return UIRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}