{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { LoginRoutingModule } from './login-routing.module';\nimport { ButtonModule } from 'primeng/button';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { PasswordModule } from 'primeng/password';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { FontAwesomeModule } from '@fortawesome/angular-fontawesome';\nimport { fas } from '@fortawesome/free-solid-svg-icons';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@fortawesome/angular-fontawesome\";\nexport let LoginModule = /*#__PURE__*/(() => {\n  class LoginModule {\n    constructor(library) {\n      library.addIconPacks(fas);\n    }\n    static #_ = this.ɵfac = function LoginModule_Factory(t) {\n      return new (t || LoginModule)(i0.ɵɵinject(i1.FaIconLibrary));\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: LoginModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, LoginRoutingModule, ButtonModule, CheckboxModule, InputTextModule, FormsModule, ReactiveFormsModule, PasswordModule, FontAwesomeModule]\n    });\n  }\n  return LoginModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}