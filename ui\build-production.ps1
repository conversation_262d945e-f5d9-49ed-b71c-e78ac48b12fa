# PowerShell script to build Angular app for production
# Run this script from the ui directory

Write-Host "Building Angular application for production..." -ForegroundColor Green

# Clean previous build
if (Test-Path "dist") {
    Write-Host "Cleaning previous build..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force "dist"
}

# Build the application
Write-Host "Running ng build..." -ForegroundColor Blue
ng build --configuration production --base-href /solarkapital.ui/

if ($LASTEXITCODE -eq 0) {
    Write-Host "Build completed successfully!" -ForegroundColor Green
    
    # Check if web.config exists in dist
    $webConfigPath = "dist/solarkapital-ui/web.config"
    if (Test-Path $webConfigPath) {
        Write-Host "web.config found in dist folder" -ForegroundColor Green
    } else {
        Write-Host "Warning: web.config not found in dist folder" -ForegroundColor Yellow
    }
    
    # Check if .htaccess exists in dist
    $htaccessPath = "dist/solarkapital-ui/.htaccess"
    if (Test-Path $htaccessPath) {
        Write-Host ".htaccess found in dist folder" -ForegroundColor Green
    } else {
        Write-Host "Warning: .htaccess not found in dist folder" -ForegroundColor Yellow
    }
    
    Write-Host "Production build is ready in dist/solarkapital-ui/" -ForegroundColor Green
    Write-Host "Deploy the contents of this folder to your web server" -ForegroundColor Cyan
} else {
    Write-Host "Build failed!" -ForegroundColor Red
    exit 1
}
