/* Chart container styles to fix tooltip positioning */
.chart-container {
  position: relative;
  overflow: visible;
}

/* Ensure chart canvas has proper positioning context */
c-chart {
  position: relative;
  display: block;
  
  canvas {
    position: relative !important;
  }
}

/* Fix for tooltip positioning issues */
:host ::ng-deep {
  .chartjs-tooltip {
    position: absolute !important;
    pointer-events: none;
    z-index: 1000;
  }
  
  /* Ensure chart containers don't clip tooltips */
  .p-card-content {
    overflow: visible;
  }
  
  /* Chart specific fixes */
  canvas {
    position: relative !important;
  }
}

/* Station card improvements */
.station-card {
  .chart-section {
    position: relative;
    overflow: visible;
    min-height: 80px;
    
    c-chart {
      position: relative;
      z-index: 1;
    }
  }
}

/* Responsive chart adjustments */
@media (max-width: 768px) {
  c-chart {
    height: 40px !important;
    width: 100px !important;
  }
}
