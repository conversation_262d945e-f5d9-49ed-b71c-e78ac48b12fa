import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private apiLoginUrl = environment.solarApi + 'api/auth/authenticate'; // Endpoint για login
  private apiRegisterUrl = environment.solarApi + 'api/user/register'; // Endpoint για register
  private tokenKey = 'auth_token'; // Κλειδί για αποθήκευση του token

  constructor(private http: HttpClient) {}

  login(username: string, password: string): Observable<{ token: string }> {
    console.log("API URL" + this.apiLoginUrl)
    return this.http.post<{ token: string }>(this.apiLoginUrl, { username, password }).pipe(
      tap(response => {
          console.log(response)
        if (response.token) {
          localStorage.setItem(this.tokenKey, response.token);
          console.log('Token αποθηκεύτηκε:', response.token); // ✅ Έλεγχος αν αποθηκεύεται
        }
      })
    );
  }


  register(username: string, password: string, email: string, firstName: string, lastName: string): Observable<{ token: string }> {
    console.log("API URL" + this.apiRegisterUrl)
    return this.http.post<{ token: string }>(this.apiRegisterUrl, { username, password, email, firstName, lastName }).pipe(
      tap(response => {
        console.log(response)
        if (response.token) {

        }
      })
    );
  }


  logout(): void {
    localStorage.removeItem(this.tokenKey);
  }

  getToken(): string | null {
    return localStorage.getItem(this.tokenKey);
  }

  isLoggedIn(): boolean {
    return !!this.getToken();
  }
}
