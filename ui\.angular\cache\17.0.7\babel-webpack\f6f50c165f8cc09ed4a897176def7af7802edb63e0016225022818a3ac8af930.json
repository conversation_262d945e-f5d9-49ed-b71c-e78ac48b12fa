{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { ChartModule } from 'primeng/chart';\nimport { MenuModule } from 'primeng/menu';\nimport { TableModule } from 'primeng/table';\nimport { ButtonModule } from 'primeng/button';\nimport { StyleClassModule } from 'primeng/styleclass';\nimport { PanelMenuModule } from 'primeng/panelmenu';\nimport { ViewStationRoutingModule } from './view-routing.module';\nimport { DataViewModule } from 'primeng/dataview';\nimport { KnobModule } from 'primeng/knob';\nimport { PickListModule } from 'primeng/picklist';\nimport { OrderListModule } from 'primeng/orderlist';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { RatingModule } from 'primeng/rating';\nlet ViewStationModule = class ViewStationModule {};\nViewStationModule = __decorate([NgModule({\n  imports: [CommonModule, FormsModule, ChartModule, MenuModule, TableModule, StyleClassModule, PanelMenuModule, ButtonModule, ViewStationRoutingModule, DataViewModule, KnobModule, CommonModule, FormsModule, DataViewModule, PickListModule, OrderListModule, InputTextModule, DropdownModule, RatingModule, ButtonModule],\n  declarations: [ViewStationModule]\n})], ViewStationModule);\nexport { ViewStationModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "FormsModule", "ChartModule", "MenuModule", "TableModule", "ButtonModule", "StyleClassModule", "PanelMenuModule", "ViewStationRoutingModule", "DataViewModule", "KnobModule", "PickListModule", "OrderListModule", "InputTextModule", "DropdownModule", "RatingModule", "ViewStationModule", "__decorate", "imports", "declarations"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\stations\\view.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { ViewStationCompoenent } from './view.component';\r\nimport { ChartModule } from 'primeng/chart';\r\nimport { MenuModule } from 'primeng/menu';\r\nimport { TableModule } from 'primeng/table';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { StyleClassModule } from 'primeng/styleclass';\r\nimport { PanelMenuModule } from 'primeng/panelmenu';\r\nimport { ViewStationRoutingModule } from './view-routing.module';\r\nimport { DataViewModule } from 'primeng/dataview';\r\nimport { KnobModule } from 'primeng/knob';\r\nimport { PickListModule } from 'primeng/picklist';\r\nimport { OrderListModule } from 'primeng/orderlist';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { RatingModule } from 'primeng/rating';\r\n\r\n@NgModule({\r\n    imports: [\r\n        CommonModule,\r\n        FormsModule,\r\n        ChartModule,\r\n        MenuModule,\r\n        TableModule,\r\n        StyleClassModule,\r\n        PanelMenuModule,\r\n        ButtonModule,\r\n        ViewStationRoutingModule,\r\n        DataViewModule,\r\n        KnobModule,\r\n        CommonModule,\r\n\t\tFormsModule,\r\n\t\tDataViewModule,\r\n\t\tPickListModule,\r\n\t\tOrderListModule,\r\n\t\tInputTextModule,\r\n\t\tDropdownModule,\r\n\t\tRatingModule,\r\n\t\tButtonModule\r\n    ],\r\n    declarations: [ViewStationModule]\r\n})\r\nexport class ViewStationModule { }\r\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,wBAAwB,QAAQ,uBAAuB;AAChE,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,YAAY,QAAQ,gBAAgB;AA2BtC,IAAMC,iBAAiB,GAAvB,MAAMA,iBAAiB,GAAI;AAArBA,iBAAiB,GAAAC,UAAA,EAzB7BlB,QAAQ,CAAC;EACNmB,OAAO,EAAE,CACLlB,YAAY,EACZC,WAAW,EACXC,WAAW,EACXC,UAAU,EACVC,WAAW,EACXE,gBAAgB,EAChBC,eAAe,EACfF,YAAY,EACZG,wBAAwB,EACxBC,cAAc,EACdC,UAAU,EACVV,YAAY,EAClBC,WAAW,EACXQ,cAAc,EACdE,cAAc,EACdC,eAAe,EACfC,eAAe,EACfC,cAAc,EACdC,YAAY,EACZV,YAAY,CACT;EACDc,YAAY,EAAE,CAACH,iBAAiB;CACnC,CAAC,C,EACWA,iBAAiB,CAAI"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}