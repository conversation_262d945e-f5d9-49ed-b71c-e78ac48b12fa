{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class UserService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = environment.solarApi + 'api/user';\n  }\n  getAuthHeaders() {\n    const token = localStorage.getItem('auth_token');\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    });\n  }\n  getUser() {\n    return this.http.get(this.apiUrl, {\n      headers: this.getAuthHeaders()\n    });\n  }\n  updateUser(updateRequest) {\n    return this.http.put(this.apiUrl, updateRequest, {\n      headers: this.getAuthHeaders()\n    });\n  }\n  static #_ = this.ɵfac = function UserService_Factory(t) {\n    return new (t || UserService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: UserService,\n    factory: UserService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "environment", "UserService", "constructor", "http", "apiUrl", "solarApi", "getAuthHeaders", "token", "localStorage", "getItem", "getUser", "get", "headers", "updateUser", "updateRequest", "put", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\service\\user.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { environment } from '../../../environments/environment';\nimport { User, UpdateUserRequest, UpdateUserResponse } from '../api/user';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class UserService {\n  private apiUrl = environment.solarApi + 'api/user';\n\n  constructor(private http: HttpClient) {}\n\n  private getAuthHeaders(): HttpHeaders {\n    const token = localStorage.getItem('auth_token');\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    });\n  }\n\n  getUser(): Observable<User> {\n    return this.http.get<User>(this.apiUrl, { \n      headers: this.getAuthHeaders() \n    });\n  }\n\n  updateUser(updateRequest: UpdateUserRequest): Observable<UpdateUserResponse> {\n    return this.http.put<UpdateUserResponse>(this.apiUrl, updateRequest, {\n      headers: this.getAuthHeaders()\n    });\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAE9D,SAASC,WAAW,QAAQ,mCAAmC;;;AAM/D,OAAM,MAAOC,WAAW;EAGtBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFhB,KAAAC,MAAM,GAAGJ,WAAW,CAACK,QAAQ,GAAG,UAAU;EAEX;EAE/BC,cAAcA,CAAA;IACpB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,OAAO,IAAIV,WAAW,CAAC;MACrB,eAAe,EAAE,UAAUQ,KAAK,EAAE;MAClC,cAAc,EAAE;KACjB,CAAC;EACJ;EAEAG,OAAOA,CAAA;IACL,OAAO,IAAI,CAACP,IAAI,CAACQ,GAAG,CAAO,IAAI,CAACP,MAAM,EAAE;MACtCQ,OAAO,EAAE,IAAI,CAACN,cAAc;KAC7B,CAAC;EACJ;EAEAO,UAAUA,CAACC,aAAgC;IACzC,OAAO,IAAI,CAACX,IAAI,CAACY,GAAG,CAAqB,IAAI,CAACX,MAAM,EAAEU,aAAa,EAAE;MACnEF,OAAO,EAAE,IAAI,CAACN,cAAc;KAC7B,CAAC;EACJ;EAAC,QAAAU,CAAA,G;qBAvBUf,WAAW,EAAAgB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAXpB,WAAW;IAAAqB,OAAA,EAAXrB,WAAW,CAAAsB,IAAA;IAAAC,UAAA,EAFV;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}