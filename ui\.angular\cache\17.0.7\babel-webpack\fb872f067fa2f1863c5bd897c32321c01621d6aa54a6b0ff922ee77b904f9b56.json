{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AuthRoutingModule {\n  static #_ = this.ɵfac = function AuthRoutingModule_Factory(t) {\n    return new (t || AuthRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AuthRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild([{\n      path: 'error',\n      loadChildren: () => import('./error/error.module').then(m => m.ErrorModule)\n    }, {\n      path: 'access',\n      loadChildren: () => import('./access/access.module').then(m => m.AccessModule)\n    }, {\n      path: 'login',\n      loadChildren: () => import('./login/login.module').then(m => m.LoginModule)\n    }, {\n      path: 'register',\n      loadChildren: () => import('./register/register.module').then(m => m.RegisterModule)\n    }, {\n      path: '**',\n      redirectTo: '/notfound'\n    }]), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AuthRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "AuthRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "path", "loadChildren", "then", "m", "ErrorModule", "AccessModule", "LoginModule", "RegisterModule", "redirectTo", "imports", "i1", "exports"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\auth\\auth-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule } from '@angular/router';\n\n@NgModule({\n    imports: [RouterModule.forChild([\n        { path: 'error', loadChildren: () => import('./error/error.module').then(m => m.ErrorModule) },\n        { path: 'access', loadChildren: () => import('./access/access.module').then(m => m.AccessModule) },\n        { path: 'login', loadChildren: () => import('./login/login.module').then(m => m.LoginModule) },\n        { path: 'register', loadChildren: () => import('./register/register.module').then(m => m.RegisterModule) },\n        { path: '**', redirectTo: '/notfound' }\n    ])],\n    exports: [RouterModule]\n})\nexport class AuthRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;;AAY9C,OAAM,MAAOC,iBAAiB;EAAA,QAAAC,CAAA,G;qBAAjBD,iBAAiB;EAAA;EAAA,QAAAE,EAAA,G;UAAjBF;EAAiB;EAAA,QAAAG,EAAA,G;cAThBJ,YAAY,CAACK,QAAQ,CAAC,CAC5B;MAAEC,IAAI,EAAE,OAAO;MAAEC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW;IAAC,CAAE,EAC9F;MAAEJ,IAAI,EAAE,QAAQ;MAAEC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,YAAY;IAAC,CAAE,EAClG;MAAEL,IAAI,EAAE,OAAO;MAAEC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,WAAW;IAAC,CAAE,EAC9F;MAAEN,IAAI,EAAE,UAAU;MAAEC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,cAAc;IAAC,CAAE,EAC1G;MAAEP,IAAI,EAAE,IAAI;MAAEQ,UAAU,EAAE;IAAW,CAAE,CAC1C,CAAC,EACQd,YAAY;EAAA;;;2EAEbC,iBAAiB;IAAAc,OAAA,GAAAC,EAAA,CAAAhB,YAAA;IAAAiB,OAAA,GAFhBjB,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}