{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { ViewStationComponent } from './view.component';\nimport { ChartModule } from 'primeng/chart';\nimport { MenuModule } from 'primeng/menu';\nimport { TableModule } from 'primeng/table';\nimport { ButtonModule } from 'primeng/button';\nimport { StyleClassModule } from 'primeng/styleclass';\nimport { PanelMenuModule } from 'primeng/panelmenu';\nimport { ViewStationRoutingModule } from './view-routing.module';\nimport { DataViewModule } from 'primeng/dataview';\nimport { KnobModule } from 'primeng/knob';\nimport { PickListModule } from 'primeng/picklist';\nimport { OrderListModule } from 'primeng/orderlist';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { RatingModule } from 'primeng/rating';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { ToolbarModule } from 'primeng/toolbar';\nimport * as i0 from \"@angular/core\";\nexport class ViewStationModule {\n  static #_ = this.ɵfac = function ViewStationModule_Factory(t) {\n    return new (t || ViewStationModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ViewStationModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, FormsModule, ChartModule, MenuModule, TableModule, StyleClassModule, PanelMenuModule, ButtonModule, ViewStationRoutingModule, DataViewModule, KnobModule, CommonModule, FormsModule, DataViewModule, PickListModule, OrderListModule, InputTextModule, DropdownModule, RatingModule, ButtonModule, BreadcrumbModule, ToolbarModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ViewStationModule, {\n    declarations: [ViewStationComponent],\n    imports: [CommonModule, FormsModule, ChartModule, MenuModule, TableModule, StyleClassModule, PanelMenuModule, ButtonModule, ViewStationRoutingModule, DataViewModule, KnobModule, CommonModule, FormsModule, DataViewModule, PickListModule, OrderListModule, InputTextModule, DropdownModule, RatingModule, ButtonModule, BreadcrumbModule, ToolbarModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ViewStationComponent", "ChartModule", "MenuModule", "TableModule", "ButtonModule", "StyleClassModule", "PanelMenuModule", "ViewStationRoutingModule", "DataViewModule", "KnobModule", "PickListModule", "OrderListModule", "InputTextModule", "DropdownModule", "RatingModule", "BreadcrumbModule", "ToolbarModule", "ViewStationModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\stations\\view.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { ViewStationComponent } from './view.component';\r\nimport { ChartModule } from 'primeng/chart';\r\nimport { MenuModule } from 'primeng/menu';\r\nimport { TableModule } from 'primeng/table';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { StyleClassModule } from 'primeng/styleclass';\r\nimport { PanelMenuModule } from 'primeng/panelmenu';\r\nimport { ViewStationRoutingModule } from './view-routing.module';\r\nimport { DataViewModule } from 'primeng/dataview';\r\nimport { KnobModule } from 'primeng/knob';\r\nimport { PickListModule } from 'primeng/picklist';\r\nimport { OrderListModule } from 'primeng/orderlist';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { RatingModule } from 'primeng/rating';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { ToolbarModule } from 'primeng/toolbar';\r\n\r\n@NgModule({\r\n    imports: [\r\n        CommonModule,\r\n        FormsModule,\r\n        ChartModule,\r\n        MenuModule,\r\n        TableModule,\r\n        StyleClassModule,\r\n        PanelMenuModule,\r\n        ButtonModule,\r\n        ViewStationRoutingModule,\r\n        DataViewModule,\r\n        KnobModule,\r\n        CommonModule,\r\n\t\tFormsModule,\r\n\t\tDataViewModule,\r\n\t\tPickListModule,\r\n\t\tOrderListModule,\r\n\t\tInputTextModule,\r\n\t\tDropdownModule,\r\n\t\tRatingModule,\r\n\t\tButtonModule,\r\n        BreadcrumbModule,\r\n        ToolbarModule\r\n    ],\r\n    declarations: [ViewStationComponent]\r\n})\r\nexport class ViewStationModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,oBAAoB,QAAQ,kBAAkB;AACvD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,wBAAwB,QAAQ,uBAAuB;AAChE,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,aAAa,QAAQ,iBAAiB;;AA6B/C,OAAM,MAAOC,iBAAiB;EAAA,QAAAC,CAAA,G;qBAAjBD,iBAAiB;EAAA;EAAA,QAAAE,EAAA,G;UAAjBF;EAAiB;EAAA,QAAAG,EAAA,G;cAzBtBtB,YAAY,EACZC,WAAW,EACXE,WAAW,EACXC,UAAU,EACVC,WAAW,EACXE,gBAAgB,EAChBC,eAAe,EACfF,YAAY,EACZG,wBAAwB,EACxBC,cAAc,EACdC,UAAU,EACVX,YAAY,EAClBC,WAAW,EACXS,cAAc,EACdE,cAAc,EACdC,eAAe,EACfC,eAAe,EACfC,cAAc,EACdC,YAAY,EACZV,YAAY,EACNW,gBAAgB,EAChBC,aAAa;EAAA;;;2EAIRC,iBAAiB;IAAAI,YAAA,GAFXrB,oBAAoB;IAAAsB,OAAA,GAvB/BxB,YAAY,EACZC,WAAW,EACXE,WAAW,EACXC,UAAU,EACVC,WAAW,EACXE,gBAAgB,EAChBC,eAAe,EACfF,YAAY,EACZG,wBAAwB,EACxBC,cAAc,EACdC,UAAU,EACVX,YAAY,EAClBC,WAAW,EACXS,cAAc,EACdE,cAAc,EACdC,eAAe,EACfC,eAAe,EACfC,cAAc,EACdC,YAAY,EACZV,YAAY,EACNW,gBAAgB,EAChBC,aAAa;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}