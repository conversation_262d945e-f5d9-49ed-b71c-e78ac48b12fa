{"ast": null, "code": "import { tap } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AuthService {\n  constructor(http) {\n    this.http = http;\n    this.apiLoginUrl = environment.solarApi + 'api/auth/authenticate'; // Endpoint για login\n    this.apiRegisterUrl = environment.solarApi + 'api/user/register'; // Endpoint για register\n    this.tokenKey = 'auth_token'; // Κλειδί για αποθήκευση του token\n  }\n\n  login(username, password) {\n    console.log(\"API URL\" + this.apiLoginUrl);\n    return this.http.post(this.apiLoginUrl, {\n      username,\n      password\n    }).pipe(tap(response => {\n      console.log(response);\n      if (response.token) {\n        localStorage.setItem(this.tokenKey, response.token);\n        console.log('Token αποθηκεύτηκε:', response.token); // ✅ Έλεγχος αν αποθηκεύεται\n      }\n    }));\n  }\n\n  register(username, password, email, firstName, lastName) {\n    console.log(\"API URL\" + this.apiRegisterUrl);\n    return this.http.post(this.apiRegisterUrl, {\n      username,\n      password,\n      email,\n      firstName,\n      lastName\n    }).pipe(tap(response => {\n      console.log(response);\n      if (response.token) {}\n    }));\n  }\n  logout() {\n    localStorage.removeItem(this.tokenKey);\n  }\n  getToken() {\n    return localStorage.getItem(this.tokenKey);\n  }\n  isLoggedIn() {\n    return !!this.getToken();\n  }\n  static #_ = this.ɵfac = function AuthService_Factory(t) {\n    return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthService,\n    factory: AuthService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["tap", "environment", "AuthService", "constructor", "http", "apiLoginUrl", "solarApi", "apiRegisterUrl", "<PERSON><PERSON><PERSON>", "login", "username", "password", "console", "log", "post", "pipe", "response", "token", "localStorage", "setItem", "register", "email", "firstName", "lastName", "logout", "removeItem", "getToken", "getItem", "isLoggedIn", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\service\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport { tap } from 'rxjs/operators';\r\nimport { environment } from '../../../environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AuthService {\r\n  private apiLoginUrl = environment.solarApi + 'api/auth/authenticate'; // Endpoint για login\r\n  private apiRegisterUrl = environment.solarApi + 'api/user/register'; // Endpoint για register\r\n  private tokenKey = 'auth_token'; // Κλειδί για αποθήκευση του token\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  login(username: string, password: string): Observable<{ token: string }> {\r\n    console.log(\"API URL\" + this.apiLoginUrl)\r\n    return this.http.post<{ token: string }>(this.apiLoginUrl, { username, password }).pipe(\r\n      tap(response => {\r\n          console.log(response)\r\n        if (response.token) {\r\n          localStorage.setItem(this.tokenKey, response.token);\r\n          console.log('Token αποθηκεύτηκε:', response.token); // ✅ Έλεγχος αν αποθηκεύεται\r\n        }\r\n      })\r\n    );\r\n  }\r\n\r\n\r\n  register(username: string, password: string, email: string, firstName: string, lastName: string): Observable<{ token: string }> {\r\n    console.log(\"API URL\" + this.apiRegisterUrl)\r\n    return this.http.post<{ token: string }>(this.apiRegisterUrl, { username, password, email, firstName, lastName }).pipe(\r\n      tap(response => {\r\n        console.log(response)\r\n        if (response.token) {\r\n\r\n        }\r\n      })\r\n    );\r\n  }\r\n\r\n\r\n  logout(): void {\r\n    localStorage.removeItem(this.tokenKey);\r\n  }\r\n\r\n  getToken(): string | null {\r\n    return localStorage.getItem(this.tokenKey);\r\n  }\r\n\r\n  isLoggedIn(): boolean {\r\n    return !!this.getToken();\r\n  }\r\n}\r\n"], "mappings": "AAGA,SAASA,GAAG,QAAQ,gBAAgB;AACpC,SAASC,WAAW,QAAQ,mCAAmC;;;AAK/D,OAAM,MAAOC,WAAW;EAKtBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAJhB,KAAAC,WAAW,GAAGJ,WAAW,CAACK,QAAQ,GAAG,uBAAuB,CAAC,CAAC;IAC9D,KAAAC,cAAc,GAAGN,WAAW,CAACK,QAAQ,GAAG,mBAAmB,CAAC,CAAC;IAC7D,KAAAE,QAAQ,GAAG,YAAY,CAAC,CAAC;EAEM;;EAEvCC,KAAKA,CAACC,QAAgB,EAAEC,QAAgB;IACtCC,OAAO,CAACC,GAAG,CAAC,SAAS,GAAG,IAAI,CAACR,WAAW,CAAC;IACzC,OAAO,IAAI,CAACD,IAAI,CAACU,IAAI,CAAoB,IAAI,CAACT,WAAW,EAAE;MAAEK,QAAQ;MAAEC;IAAQ,CAAE,CAAC,CAACI,IAAI,CACrFf,GAAG,CAACgB,QAAQ,IAAG;MACXJ,OAAO,CAACC,GAAG,CAACG,QAAQ,CAAC;MACvB,IAAIA,QAAQ,CAACC,KAAK,EAAE;QAClBC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACX,QAAQ,EAAEQ,QAAQ,CAACC,KAAK,CAAC;QACnDL,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEG,QAAQ,CAACC,KAAK,CAAC,CAAC,CAAC;;IAExD,CAAC,CAAC,CACH;EACH;;EAGAG,QAAQA,CAACV,QAAgB,EAAEC,QAAgB,EAAEU,KAAa,EAAEC,SAAiB,EAAEC,QAAgB;IAC7FX,OAAO,CAACC,GAAG,CAAC,SAAS,GAAG,IAAI,CAACN,cAAc,CAAC;IAC5C,OAAO,IAAI,CAACH,IAAI,CAACU,IAAI,CAAoB,IAAI,CAACP,cAAc,EAAE;MAAEG,QAAQ;MAAEC,QAAQ;MAAEU,KAAK;MAAEC,SAAS;MAAEC;IAAQ,CAAE,CAAC,CAACR,IAAI,CACpHf,GAAG,CAACgB,QAAQ,IAAG;MACbJ,OAAO,CAACC,GAAG,CAACG,QAAQ,CAAC;MACrB,IAAIA,QAAQ,CAACC,KAAK,EAAE,C;IAGtB,CAAC,CAAC,CACH;EACH;EAGAO,MAAMA,CAAA;IACJN,YAAY,CAACO,UAAU,CAAC,IAAI,CAACjB,QAAQ,CAAC;EACxC;EAEAkB,QAAQA,CAAA;IACN,OAAOR,YAAY,CAACS,OAAO,CAAC,IAAI,CAACnB,QAAQ,CAAC;EAC5C;EAEAoB,UAAUA,CAAA;IACR,OAAO,CAAC,CAAC,IAAI,CAACF,QAAQ,EAAE;EAC1B;EAAC,QAAAG,CAAA,G;qBA5CU3B,WAAW,EAAA4B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAXhC,WAAW;IAAAiC,OAAA,EAAXjC,WAAW,CAAAkC,IAAA;IAAAC,UAAA,EAFV;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}