﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Auth.API.AuthenticationManager.CustomToken;
using Auth.API.AuthenticationManager.JWT;
using Auth.API.DBContext;
using Auth.API.Helpers;
using Auth.API.Implementation;
using Auth.API.Interfaces;
using Auth.API.Jobs;
using Auth.API.Repositories.Interfaces;
using Auth.API.Repository;
using Auth.Demo.Repositories;
using Auth.Demo.Repositories.Interfaces;
using Hangfire;
using Hangfire.Dashboard.BasicAuthorization;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;

namespace Auth.Demo
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            #region JWT Authentication
            /*
            //Repository Pattern
            services.AddScoped<IUserRepository, MockUserRepository>();

            //Get tokenKey from the configuration
            var tokenKey = Configuration.GetValue<string>("TokenKey");
            var key = Encoding.ASCII.GetBytes(tokenKey);

            services.AddAuthentication(x =>
            {
                x.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                x.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(x =>
            {
                x.RequireHttpsMetadata = false;
                x.SaveToken = true;
                x.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = false,
                    ValidateAudience = false
                };
            });

            services.AddSingleton<IJWTAuthenticationManager>(new JWTAuthenticationManager(Configuration, new MockUserRepository(), tokenKey));
            */
            #endregion

            services.AddAuthentication("Basic").AddScheme<BasicAuthenticationOptions, CustomAuthenticationHandler>("Basic", null);
            services.AddScoped<ICustomAuthenticationManager, CustomAuthenticationManager>();

            services.AddDbContext<AppDbContext>(options => options.UseSqlServer(Configuration.GetConnectionString("DefaultConnection")));

            
            //Repository Pattern
            //services.AddSingleton<IUserRepository, MockUserRepository>();
            services.AddScoped<IProviderRepositoryService, ProviderRepositoryService>();
            services.AddScoped<IUserRepositoryService, UserRepositoryService>();
            services.AddScoped<IUserProvidersRepositoryService, UserProvidersRepositoryService>();
            services.AddScoped<IDataRepositoryService, DataRepositoryService>();

            services.AddScoped<HuaweiTokenService>();
            //services.AddHttpClient<HuaweiTokenService>();
            services.AddScoped<AuroraTokenService>();
            services.AddScoped<RefulogTokenService>();
            services.AddHttpClient<HuaweiApiService>();
            services.AddHttpClient<AuroraApiService>();
            services.AddHttpClient<RefulogApiService>();
            services.AddHttpClient<WeatherService>();

            services.AddMemoryCache();


            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IApiService, ApiService>();

            var tokenKey = Configuration.GetValue<string>("TokenKey");
            var key = Encoding.ASCII.GetBytes(tokenKey);

            
            


            services.AddAutoMapper(typeof(MappingProfile).Assembly);
            

            services.AddCors(options =>
            {
                options.AddPolicy("AllowAngularClient",
                    builder => builder.WithOrigins("http://localhost:4200")
                    .AllowAnyMethod().AllowAnyHeader());
                //Production
                //options.AddPolicy("AllowAngularClient",
                //    builder => builder.WithOrigins("http://localhost/solarkapital.ui")
                //    .AllowAnyMethod().AllowAnyHeader());
                //options.AddPolicy("AllowAngularClient",
                //    builder => builder.WithOrigins("http://***************/solarkapital.ui")
                //    .AllowAnyMethod().AllowAnyHeader());
            });

            services.AddAuthorization(options =>
            {
                options.AddPolicy("AdminPolicy", policy => policy.RequireRole("Admin"));
            });

            // Προσθήκη Hangfire με SQL Server
            services.AddHangfire(config =>
                config.UseSqlServerStorage(Configuration.GetConnectionString("DefaultConnection")));
            services.AddHangfireServer();

            services.AddHealthChecks()
                .AddSqlServer(Configuration.GetConnectionString("DefaultConnection"), name: "SQL DB");
                //.AddUrlGroup((new Uri("")), name: "External Api");

            services.AddControllers();

        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILogger<Startup> logger)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            app.UseCors("AllowAngularClient");
            app.UseMiddleware<ExceptionHandlingMiddleware>();
            app.UseRouting();

            app.UseAuthentication();
            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapHealthChecks("/health", new HealthCheckOptions
                {
                    ResponseWriter = async (context, report) =>
                    {
                        context.Response.ContentType = "application/json";

                        var result = new
                        {
                            status = report.Status.ToString(),
                            checks = report.Entries.Select(entry => new
                            {
                                name = entry.Key,
                                status = entry.Value.Status.ToString(),
                                description = entry.Value.Description,
                                data = entry.Value.Data
                            })
                        };

                        await context.Response.WriteAsync(JsonSerializer.Serialize(result, new JsonSerializerOptions
                        {
                            WriteIndented = true
                        }));
                    }
                });
                endpoints.MapControllers();
                // Προσθήκη Hangfire Dashboard (προαιρετικά)
                app.UseHangfireDashboard("/hangfire", new DashboardOptions
                {
                    Authorization = new[]
                        {
                    new BasicAuthAuthorizationFilter(new BasicAuthAuthorizationFilterOptions
                    {
                        SslRedirect = false,
                        RequireSsl = false,
                        LoginCaseSensitive = true,
                        Users = new[]
                        {
                            new BasicAuthAuthorizationUser
                            {
                                Login = "admin",
                                PasswordClear = "mypassword"
                            }
                        }
                    })
                }
                });

                //// ✅ Προσθήκη του background job
                RecurringJob.AddOrUpdate<HuaweiJob>(
                    "huawei-job",
                    job => job.FetchAndStoreData(), // 🔹 Περιμένει το async task να ολοκληρωθεί
                    "*/10 * * * *");
                RecurringJob.AddOrUpdate<SMAJob>(
                    "sma-job",
                    job => job.FetchAndStoreData(), // 🔹 Περιμένει το async task να ολοκληρωθεί
                    "*/10 * * * *"
                    );

                GlobalJobFilters.Filters.Add(new AutomaticRetryAttribute
                {
                    Attempts = 1, // π.χ. retry έως 3 φορές
                    DelaysInSeconds = new[] { 120 }, // καθυστέρηση: 1', 2', 5'
                    OnAttemptsExceeded = AttemptsExceededAction.Fail // ή Delete
                });

            });

            app.UseDefaultFiles();
            app.UseStaticFiles();

            



            // Καταγραφή μηνύματος κατά την εκκίνηση
            logger.LogInformation("Api started successfully!");
        }
    }
}
