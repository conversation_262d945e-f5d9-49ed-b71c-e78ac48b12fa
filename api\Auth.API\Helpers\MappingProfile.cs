﻿using Auth.API.AuroraModels;
using Auth.API.HuaweiModels;
using Auth.API.Models;
using Auth.API.RefulogModels;
using Auth.API.Repository.Models;
using Auth.API.Types;
using Auth.Demo.Models;
using AutoMapper;
using System;

namespace Auth.API.Helpers
{
    public class MappingProfile : Profile
    {
        public MappingProfile() {

            CreateMap<User, RepoUser>();
            CreateMap<RepoUser, User>();

            CreateMap<UserProvider, RepoUserProvider>();
            CreateMap<RepoUserProvider, UserProvider>();

            CreateMap<HuaweiStation, Station>()
                .ForMember(s => s.Id, opt => opt.MapFrom(src => src.PlantCode))
                .ForMember(s => s.Name, opt => opt.MapFrom(src => src.PlantName))
                .ForMember(s => s.Status, opt => opt.MapFrom(src => "Active"))
                .ForMember(s => s.Address, opt => opt.MapFrom(src => src.PlantAddress));

            CreateMap<HuaweiDevice, Device>()
                .ForMember(s => s.Name, opt => opt.MapFrom(src => src.DevName))
                .ForMember(s => s.Type, opt => opt.MapFrom(src => src.DevTypeId));
            
            
            /*Aurora API */

            CreateMap<AuroraStation, Station>()
                .ForMember(s => s.Id, opt => opt.MapFrom(src => src.plantEntityID.ToString()))
                .ForMember(s => s.Name, opt => opt.MapFrom(src => src.plantName))
                .ForMember(s => s.Status, opt => opt.MapFrom(src => src.plantStatus))
                .ForMember(s => s.Address, opt => opt.MapFrom(src => src.plantDescription));

            CreateMap<AuroraDevice, Device>()
                .ForMember(s => s.Id, opt => opt.MapFrom(src => src.deviceEntityID.ToString()))
                .ForMember(s => s.Name, opt => opt.MapFrom(src => src.deviceSerialNumber))
                .ForMember(s => s.Type, opt => opt.MapFrom(src => 1));

            CreateMap<AuroraHistoriTimeDataResult, RealTimeData>()
                .ForMember(s => s.dateTime, opt => opt.MapFrom(src => DateTimeOffset.FromUnixTimeSeconds(src.start).ToLocalTime().ToString("o")))
                .ForMember(s => s.timestamp, opt => opt.MapFrom(src => src.start))
                .ForMember(s => s.activePower, opt => opt.MapFrom(src => src.value));

            /* Refulog Api */
            CreateMap<RefulogStation, Station>()
                .ForMember(s => s.Id, opt => opt.MapFrom(src => src.ID))
                .ForMember(s => s.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(s => s.Status, opt => opt.MapFrom(src => src.Status))
                .ForMember(s => s.Address, opt => opt.MapFrom(src => src.Street + "," + src.Town + "," + src.PostCode))
                .ForMember(s => s.Longitude, opt => opt.MapFrom(src => src.Longitude))
                .ForMember(s => s.Latitude, opt => opt.MapFrom(src => src.Latitude));

            CreateMap<RefulogDevice, Device>()
                .ForMember(s => s.Name, opt => opt.MapFrom(src => src.Serial))
                .ForMember(s => s.Type, opt => opt.MapFrom(src => 1));

            CreateMap<RefulogChartData, RealTimeData>()
                .ForMember(dest => dest.dateTime, opt => opt.MapFrom(src => ConvertUnixToAthensIso(src.DateUnixFormat)))
                .ForMember(s => s.activePower, opt => opt.MapFrom(src => float.IsNaN(src.Value) ? 0m : (decimal)src.Value));


            CreateMap<DataItemMap, SumData>();



        }

        public static string ConvertUnixToAthensIso(long unix)
        {
            // Convert Unix timestamp to UTC DateTime
            var utcDateTime = DateTime.UnixEpoch.AddSeconds(unix);

            // Convert to Athens timezone
            TimeZoneInfo athensTimeZone = TimeZoneInfo.FindSystemTimeZoneById("GTB Standard Time");
            var athensDateTime = TimeZoneInfo.ConvertTimeFromUtc(utcDateTime, athensTimeZone);

            // Return as ISO string with timezone info
            return athensDateTime.ToString("yyyy-MM-ddTHH:mm:ss.fffK");
        }

    }
}
