{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let DateUtilsService = /*#__PURE__*/(() => {\n  class DateUtilsService {\n    constructor() {\n      this.ATHENS_TIMEZONE = 'Europe/Athens';\n    }\n    /**\n     * Converts any date to Athens timezone and formats it for display\n     */\n    toAthensTime(date) {\n      const inputDate = typeof date === 'string' ? new Date(date) : date;\n      // Create a new date in Athens timezone\n      const athensDate = new Date(inputDate.toLocaleString('en-US', {\n        timeZone: this.ATHENS_TIMEZONE\n      }));\n      return athensDate;\n    }\n    /**\n     * Formats date for chart labels (time only)\n     */\n    formatTimeForChart(date) {\n      const athensDate = this.toAthensTime(date);\n      return athensDate.toLocaleTimeString('el-GR', {\n        hour: '2-digit',\n        minute: '2-digit',\n        timeZone: this.ATHENS_TIMEZONE\n      });\n    }\n    /**\n     * Formats date for chart tooltips (full date and time)\n     */\n    formatDateTimeForTooltip(date) {\n      const athensDate = this.toAthensTime(date);\n      return athensDate.toLocaleString('el-GR', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit',\n        timeZone: this.ATHENS_TIMEZONE\n      });\n    }\n    /**\n     * Formats date for table display\n     */\n    formatDateForTable(date) {\n      const athensDate = this.toAthensTime(date);\n      return athensDate.toLocaleDateString('el-GR', {\n        timeZone: this.ATHENS_TIMEZONE\n      });\n    }\n    /**\n     * Formats time for table display\n     */\n    formatTimeForTable(date) {\n      const athensDate = this.toAthensTime(date);\n      return athensDate.toLocaleTimeString('el-GR', {\n        hour: '2-digit',\n        minute: '2-digit',\n        timeZone: this.ATHENS_TIMEZONE\n      });\n    }\n    /**\n     * Converts local date to UTC for API calls\n     */\n    toUtcForApi(date) {\n      return date.toISOString();\n    }\n    /**\n     * Creates a date in Athens timezone for date pickers\n     */\n    createAthensDate(year, month, day, hour = 0, minute = 0) {\n      // Create date string in Athens timezone format\n      const dateString = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}T${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:00`;\n      // Parse as local time and then convert to Athens\n      const localDate = new Date(dateString);\n      return this.toAthensTime(localDate);\n    }\n    static #_ = this.ɵfac = function DateUtilsService_Factory(t) {\n      return new (t || DateUtilsService)();\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: DateUtilsService,\n      factory: DateUtilsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return DateUtilsService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}