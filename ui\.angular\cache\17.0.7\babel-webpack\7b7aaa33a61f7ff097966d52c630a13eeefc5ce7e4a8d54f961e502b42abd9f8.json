{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { IndexComponent } from './index.component';\nimport { ChartModule } from 'primeng/chart';\nimport { MenuModule } from 'primeng/menu';\nimport { TableModule } from 'primeng/table';\nimport { ButtonModule } from 'primeng/button';\nimport { StyleClassModule } from 'primeng/styleclass';\nimport { PanelMenuModule } from 'primeng/panelmenu';\nimport { IndexRoutingModule } from './index-routing.module';\nimport { DataViewModule } from 'primeng/dataview';\nimport { KnobModule } from 'primeng/knob';\nimport { PickListModule } from 'primeng/picklist';\nimport { OrderListModule } from 'primeng/orderlist';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { RatingModule } from 'primeng/rating';\nimport { FontAwesomeModule } from '@fortawesome/angular-fontawesome';\nimport { fas } from '@fortawesome/free-solid-svg-icons';\nimport { ChartjsComponent } from '@coreui/angular-chartjs';\nimport { ColComponent, RowComponent, TextColorDirective, WidgetStatEComponent } from '@coreui/angular';\nimport { BadgeModule } from 'primeng/badge';\nimport { CardModule } from 'primeng/card';\nimport { TagModule } from 'primeng/tag';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\nimport { SkeletonModule } from 'primeng/skeleton';\nimport { RippleModule } from 'primeng/ripple';\nimport { PaginatorModule } from 'primeng/paginator';\nlet IndexModule = class IndexModule {\n  constructor(library) {\n    //library.addIcons(faSmile);\n    library.addIconPacks(fas);\n  }\n};\nIndexModule = __decorate([NgModule({\n  imports: [CommonModule, FormsModule, ChartModule, MenuModule, TableModule, StyleClassModule, PanelMenuModule, ButtonModule, IndexRoutingModule, DataViewModule, KnobModule, CommonModule, FormsModule, DataViewModule, PickListModule, OrderListModule, InputTextModule, DropdownModule, RatingModule, ButtonModule, ChartjsComponent, WidgetStatEComponent, ColComponent, RowComponent, TextColorDirective, BadgeModule, FontAwesomeModule, ReactiveFormsModule, CardModule, TagModule, TooltipModule, ProgressSpinnerModule, SkeletonModule, RippleModule, PaginatorModule],\n  declarations: [IndexComponent]\n})], IndexModule);\nexport { IndexModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "FormsModule", "ReactiveFormsModule", "IndexComponent", "ChartModule", "MenuModule", "TableModule", "ButtonModule", "StyleClassModule", "PanelMenuModule", "IndexRoutingModule", "DataViewModule", "KnobModule", "PickListModule", "OrderListModule", "InputTextModule", "DropdownModule", "RatingModule", "FontAwesomeModule", "fas", "ChartjsComponent", "ColComponent", "RowComponent", "TextColorDirective", "WidgetStatEComponent", "BadgeModule", "CardModule", "TagModule", "TooltipModule", "ProgressSpinnerModule", "SkeletonModule", "RippleModule", "PaginatorModule", "IndexModule", "constructor", "library", "addIconPacks", "__decorate", "imports", "declarations"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\index\\index.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { IndexComponent } from './index.component';\r\nimport { ChartModule } from 'primeng/chart';\r\nimport { MenuModule } from 'primeng/menu';\r\nimport { TableModule } from 'primeng/table';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { StyleClassModule } from 'primeng/styleclass';\r\nimport { PanelMenuModule } from 'primeng/panelmenu';\r\nimport { IndexRoutingModule } from './index-routing.module';\r\nimport { DataViewModule } from 'primeng/dataview';\r\nimport { KnobModule } from 'primeng/knob';\r\nimport { PickListModule } from 'primeng/picklist';\r\nimport { OrderListModule } from 'primeng/orderlist';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { RatingModule } from 'primeng/rating';\r\nimport { FaIconLibrary, FontAwesomeModule } from '@fortawesome/angular-fontawesome';\r\nimport { fas } from '@fortawesome/free-solid-svg-icons';\r\nimport { ChartjsComponent } from '@coreui/angular-chartjs';\r\nimport { ColComponent, RowComponent, TextColorDirective, WidgetStatEComponent } from '@coreui/angular';\r\nimport { ChartData, ChartOptions } from 'chart.js';\r\nimport { BadgeModule } from 'primeng/badge';\r\nimport { CardModule } from 'primeng/card';\r\nimport { TagModule } from 'primeng/tag';\r\nimport { TooltipModule } from 'primeng/tooltip';\r\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\r\nimport { SkeletonModule } from 'primeng/skeleton';\r\nimport { RippleModule } from 'primeng/ripple';\r\nimport { PaginatorModule } from 'primeng/paginator';\r\n\r\n@NgModule({\r\n    imports: [\r\n        CommonModule,\r\n        FormsModule,\r\n        ChartModule,\r\n        MenuModule,\r\n        TableModule,\r\n        StyleClassModule,\r\n        PanelMenuModule,\r\n        ButtonModule,\r\n        IndexRoutingModule,\r\n        DataViewModule,\r\n        KnobModule,\r\n        CommonModule,\r\n\t\tFormsModule,\r\n\t\tDataViewModule,\r\n\t\tPickListModule,\r\n\t\tOrderListModule,\r\n\t\tInputTextModule,\r\n\t\tDropdownModule,\r\n\t\tRatingModule,\r\n\t\tButtonModule,\r\n        ChartjsComponent,\r\n        WidgetStatEComponent,\r\n        ColComponent,\r\n        RowComponent,\r\n        TextColorDirective,\r\n        BadgeModule,\r\n        FontAwesomeModule,\r\n        ReactiveFormsModule,\r\n        CardModule,\r\n        TagModule,\r\n        TooltipModule,\r\n        ProgressSpinnerModule,\r\n        SkeletonModule,\r\n        RippleModule,\r\n        PaginatorModule\r\n    ],\r\n    declarations: [IndexComponent]\r\n})\r\nexport class IndexModule {\r\n    constructor(library: FaIconLibrary){\r\n        //library.addIcons(faSmile);\r\n        library.addIconPacks(fas);\r\n    }\r\n }"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAAwBC,iBAAiB,QAAQ,kCAAkC;AACnF,SAASC,GAAG,QAAQ,mCAAmC;AACvD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,YAAY,EAAEC,YAAY,EAAEC,kBAAkB,EAAEC,oBAAoB,QAAQ,iBAAiB;AAEtG,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,eAAe,QAAQ,mBAAmB;AA0C5C,IAAMC,WAAW,GAAjB,MAAMA,WAAW;EACpBC,YAAYC,OAAsB;IAC9B;IACAA,OAAO,CAACC,YAAY,CAACjB,GAAG,CAAC;EAC7B;CACF;AALWc,WAAW,GAAAI,UAAA,EAxCvBtC,QAAQ,CAAC;EACNuC,OAAO,EAAE,CACLtC,YAAY,EACZC,WAAW,EACXG,WAAW,EACXC,UAAU,EACVC,WAAW,EACXE,gBAAgB,EAChBC,eAAe,EACfF,YAAY,EACZG,kBAAkB,EAClBC,cAAc,EACdC,UAAU,EACVZ,YAAY,EAClBC,WAAW,EACXU,cAAc,EACdE,cAAc,EACdC,eAAe,EACfC,eAAe,EACfC,cAAc,EACdC,YAAY,EACZV,YAAY,EACNa,gBAAgB,EAChBI,oBAAoB,EACpBH,YAAY,EACZC,YAAY,EACZC,kBAAkB,EAClBE,WAAW,EACXP,iBAAiB,EACjBhB,mBAAmB,EACnBwB,UAAU,EACVC,SAAS,EACTC,aAAa,EACbC,qBAAqB,EACrBC,cAAc,EACdC,YAAY,EACZC,eAAe,CAClB;EACDO,YAAY,EAAE,CAACpC,cAAc;CAChC,CAAC,C,EACW8B,WAAW,CAKtB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}