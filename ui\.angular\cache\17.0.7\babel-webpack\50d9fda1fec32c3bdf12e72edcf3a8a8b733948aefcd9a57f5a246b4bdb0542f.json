{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { HelpComponent } from './help.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: HelpComponent\n}];\nexport let HelpRoutingModule = /*#__PURE__*/(() => {\n  class HelpRoutingModule {\n    static #_ = this.ɵfac = function HelpRoutingModule_Factory(t) {\n      return new (t || HelpRoutingModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: HelpRoutingModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n  return HelpRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}