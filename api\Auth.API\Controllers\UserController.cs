﻿using Auth.API.DBContext;
using Auth.API.HuaweiModels;
using Auth.API.Implementation;
using Auth.API.Interfaces;
using Auth.API.Models;
using Auth.API.Repositories.Interfaces;
using Auth.API.Repository.Models;
using Auth.API.Types;
using Auth.Demo.Models;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using OpenQA.Selenium.DevTools.V132.Debugger;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.Threading.Tasks;
using System.Threading.Tasks.Sources;

namespace Auth.API.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class UserController : ControllerBase
    {
        private readonly AppDbContext _context;
        private readonly IMapper _mapper;
        private readonly IUserService _userService;
        private readonly IApiService _apiService;
        private readonly IProviderRepositoryService _providerRepositoryService;

        public UserController(AppDbContext context, IMapper mapper, IUserService userService, IApiService apiService, IProviderRepositoryService providerRepositoryService)
        {
            _context = context;
            _mapper = mapper;
            _userService = userService;
            _apiService = apiService;
            _providerRepositoryService = providerRepositoryService;
        }

        [Route("register", Name = "RegisterUser")]
        [AllowAnonymous]
        [HttpPost("register")]
        public async Task<ActionResult<Demo.Models.User>> Register([FromBody] Demo.Models.User user)
        {
            // Ελέγξτε αν υπάρχει ήδη χρήστης με το ίδιο username ή email
            if (await _context.Users.AnyAsync(u => u.Username == user.Username || u.Email == user.Email))
                return BadRequest("User already exists.");

            var repoUser = _mapper.Map<Demo.Models.User, Repository.Models.RepoUser>(user);

            // Δημιουργία του PasswordHasher και hashing του κωδικού
            var passwordHasher = new PasswordHasher<Repository.Models.RepoUser>();
            repoUser.PasswordHash = passwordHasher.HashPassword(repoUser, user.Password);
            repoUser.Role = "User";

            // Προσθήκη του χρήστη στη βάση δεδομένων
            _context.Users.Add(repoUser);
            await _context.SaveChangesAsync();



            return Ok(user);
        }

        // Endpoint για την ανάκτηση ενός χρήστη με βάση το userId
        [HttpGet]
        public async Task<IActionResult> GetUser()
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            var user = await _userService.GetUserAsync(Guid.Parse(userId));
            if (user == null)
            {
                return NotFound("User not found.");
            }

            // Remove password from response for security
            user.Password = null;

            return Ok(user);
        }

        // Endpoint για την ανάκτηση ενός χρήστη με βάση το userId
        [Route("providers", Name = "GetUserProviders")]
        [HttpGet]
        public async Task<ActionResult<GetUserProvidersResponse>> GetUserProviders()
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            var userProviders = await _userService.GetUserProvidersAsync(Guid.Parse(userId));

            //TODO add more data for each of them
            foreach (var provider in userProviders)
            {
                var conf = JsonSerializer.Deserialize<UserProviderConfiguration>(provider.Configuration);
                conf.Password = "*******";
                provider.Configuration = JsonSerializer.Serialize(conf);

            }

            var response = new GetUserProvidersResponse()
            {
                Providers = userProviders
            };
            
            return Ok(response);
        }

        // Endpoint για την ανάκτηση ενός χρήστη με βάση το userId
        [Route("providers", Name = "SaveUserProviders")]
        [HttpPost]
        public async Task<ActionResult<SaveUserProvidersResponse>> SaveUserProviders([FromBody] SaveUserProvidersRequest request)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            foreach (NewProvider provider in request.Providers)
            {
                if (provider.ProviderId == "1" || provider.ProviderId == "4" || provider.ProviderId == "5")
                {

                   // foreach (Station station in provider.Stations) {
                        
                        var devices = await _apiService.GetStationDevicesWithCredsAsync(provider.ProviderId, userId, provider.SelectedStation, provider.Username, provider.Password);
                        provider.Stations.First(s => s.Id == provider.SelectedStation).DeviceIds = string.Join(",", devices.Select(u => u.Id));
                        
                            
                    //}
                }
            }

            var success = await _userService.SaveUserProviderAsync(Guid.Parse(userId), request.Providers);

            var response = new SaveUserProvidersResponse()
            {
                Success = success
            };
            
            return Ok(response);
        }


        [HttpGet]
        [Route("stations", Name = "GetUserStations")]
        public async Task<ActionResult<GetStationsResponse>> Get()
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            var userStations = await _userService.GetUserStationsAsync(Guid.Parse(userId));

            var userProviders = await _userService.GetUserProvidersAsync(Guid.Parse(userId));
            var providers = await _providerRepositoryService.GetAllProvidersAsync();

            foreach (var userStation in userStations) {
                var userProvider = userProviders.FirstOrDefault(up => JsonSerializer.Deserialize<UserProviderConfiguration>(up.Configuration).Stations.Any(s => s.StationId == userStation.Id));
                userStation.Provider = providers.FirstOrDefault(p => p.Id == userProvider.ProviderId).Name;
            }
            

            var response = new GetStationsResponse()
            {
                Stations = userStations
            };

            return Ok(response);
        }

    }
}
