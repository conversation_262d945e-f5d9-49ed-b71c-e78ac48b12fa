{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, Component, ViewEncapsulation, ChangeDetectionStrategy, Inject, Input, Output, ContentChildren, ViewChild, NgModule } from '@angular/core';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\n\n/**\n * Splitter is utilized to separate and resize panels.\n * @group Components\n */\nconst _c0 = [\"container\"];\nfunction Splitter_ng_template_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Splitter_ng_template_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵlistener(\"mousedown\", function Splitter_ng_template_2_div_2_Template_div_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const i_r3 = i0.ɵɵnextContext().index;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onGutterMouseDown($event, i_r3));\n    })(\"touchstart\", function Splitter_ng_template_2_div_2_Template_div_touchstart_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const i_r3 = i0.ɵɵnextContext().index;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onGutterTouchStart($event, i_r3));\n    })(\"touchmove\", function Splitter_ng_template_2_div_2_Template_div_touchmove_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.onGutterTouchMove($event));\n    })(\"touchend\", function Splitter_ng_template_2_div_2_Template_div_touchend_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const i_r3 = i0.ɵɵnextContext().index;\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.onGutterTouchEnd($event, i_r3));\n    });\n    i0.ɵɵelementStart(1, \"div\", 7);\n    i0.ɵɵlistener(\"keyup\", function Splitter_ng_template_2_div_2_Template_div_keyup_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.onGutterKeyUp($event));\n    })(\"keydown\", function Splitter_ng_template_2_div_2_Template_div_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const i_r3 = i0.ɵɵnextContext().index;\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.onGutterKeyDown($event, i_r3));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-p-gutter-resizing\", false)(\"data-pc-section\", \"gutter\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r5.gutterStyle());\n    i0.ɵɵattribute(\"aria-orientation\", ctx_r5.layout)(\"aria-valuenow\", ctx_r5.prevSize)(\"data-pc-section\", \"gutterhandle\");\n  }\n}\nfunction Splitter_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵtemplate(1, Splitter_ng_template_2_ng_container_1_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, Splitter_ng_template_2_div_2_Template, 2, 6, \"div\", 5);\n  }\n  if (rf & 2) {\n    const panel_r2 = ctx.$implicit;\n    const i_r3 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.panelStyleClass);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.panelContainerClass())(\"ngStyle\", ctx_r1.panelStyle);\n    i0.ɵɵattribute(\"data-pc-name\", \"splitter\")(\"data-pc-section\", \"root\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", panel_r2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i_r3 !== ctx_r1.panels.length - 1);\n  }\n}\nclass Splitter {\n  document;\n  platformId;\n  renderer;\n  cd;\n  el;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Style class of the panel.\n   * @group Props\n   */\n  panelStyleClass;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Inline style of the panel.\n   * @group Props\n   */\n  panelStyle;\n  /**\n   * Defines where a stateful splitter keeps its state, valid values are 'session' for sessionStorage and 'local' for localStorage.\n   * @group Props\n   */\n  stateStorage = 'session';\n  /**\n   * Storage identifier of a stateful Splitter.\n   * @group Props\n   */\n  stateKey = null;\n  /**\n   * Orientation of the panels. Valid values are 'horizontal' and 'vertical'.\n   * @group Props\n   */\n  layout = 'horizontal';\n  /**\n   * Size of the divider in pixels.\n   * @group Props\n   */\n  gutterSize = 4;\n  /**\n   * Step factor to increment/decrement the size of the panels while pressing the arrow keys.\n   * @group Props\n   */\n  step = 5;\n  /**\n   * Minimum size of the elements relative to 100%.\n   * @group Props\n   */\n  minSizes = [];\n  /**\n   * Size of the elements relative to 100%.\n   * @group Props\n   */\n  get panelSizes() {\n    return this._panelSizes;\n  }\n  set panelSizes(val) {\n    this._panelSizes = val;\n    if (this.el && this.el.nativeElement && this.panels.length > 0) {\n      let children = [...this.el.nativeElement.children[0].children].filter(child => DomHandler.hasClass(child, 'p-splitter-panel'));\n      let _panelSizes = [];\n      this.panels.map((panel, i) => {\n        let panelInitialSize = this.panelSizes.length - 1 >= i ? this.panelSizes[i] : null;\n        let panelSize = panelInitialSize || 100 / this.panels.length;\n        _panelSizes[i] = panelSize;\n        children[i].style.flexBasis = 'calc(' + panelSize + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n      });\n    }\n  }\n  /**\n   * Callback to invoke when resize ends.\n   * @param {SplitterResizeEndEvent} event - Custom panel resize end event\n   * @group Emits\n   */\n  onResizeEnd = new EventEmitter();\n  /**\n   * Callback to invoke when resize starts.\n   * @param {SplitterResizeStartEvent} event - Custom panel resize start event\n   * @group Emits\n   */\n  onResizeStart = new EventEmitter();\n  templates;\n  containerViewChild;\n  nested = false;\n  panels = [];\n  dragging = false;\n  mouseMoveListener;\n  mouseUpListener;\n  touchMoveListener;\n  touchEndListener;\n  size;\n  gutterElement;\n  startPos;\n  prevPanelElement;\n  nextPanelElement;\n  nextPanelSize;\n  prevPanelSize;\n  _panelSizes = [];\n  prevPanelIndex;\n  timer;\n  prevSize;\n  window;\n  constructor(document, platformId, renderer, cd, el) {\n    this.document = document;\n    this.platformId = platformId;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.el = el;\n    this.window = this.document.defaultView;\n  }\n  ngOnInit() {\n    this.nested = this.isNested();\n  }\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'panel':\n          this.panels.push(item.template);\n          break;\n        default:\n          this.panels.push(item.template);\n          break;\n      }\n    });\n  }\n  ngAfterViewInit() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (this.panels && this.panels.length) {\n        let initialized = false;\n        if (this.isStateful()) {\n          initialized = this.restoreState();\n        }\n        if (!initialized) {\n          let children = [...this.el.nativeElement.children[0].children].filter(child => DomHandler.hasClass(child, 'p-splitter-panel'));\n          let _panelSizes = [];\n          this.panels.map((panel, i) => {\n            let panelInitialSize = this.panelSizes.length - 1 >= i ? this.panelSizes[i] : null;\n            let panelSize = panelInitialSize || 100 / this.panels.length;\n            _panelSizes[i] = panelSize;\n            children[i].style.flexBasis = 'calc(' + panelSize + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n          });\n          this._panelSizes = _panelSizes;\n          this.prevSize = parseFloat(_panelSizes[0]).toFixed(4);\n        }\n      }\n    }\n  }\n  resizeStart(event, index, isKeyDown) {\n    this.gutterElement = event.currentTarget || event.target.parentElement;\n    this.size = this.horizontal() ? DomHandler.getWidth(this.containerViewChild.nativeElement) : DomHandler.getHeight(this.containerViewChild.nativeElement);\n    if (!isKeyDown) {\n      this.dragging = true;\n      this.startPos = this.horizontal() ? event instanceof MouseEvent ? event.pageX : event.changedTouches[0].pageX : event instanceof MouseEvent ? event.pageY : event.changedTouches[0].pageY;\n    }\n    this.prevPanelElement = this.gutterElement.previousElementSibling;\n    this.nextPanelElement = this.gutterElement.nextElementSibling;\n    if (isKeyDown) {\n      this.prevPanelSize = this.horizontal() ? DomHandler.getOuterWidth(this.prevPanelElement, true) : DomHandler.getOuterHeight(this.prevPanelElement, true);\n      this.nextPanelSize = this.horizontal() ? DomHandler.getOuterWidth(this.nextPanelElement, true) : DomHandler.getOuterHeight(this.nextPanelElement, true);\n    } else {\n      this.prevPanelSize = 100 * (this.horizontal() ? DomHandler.getOuterWidth(this.prevPanelElement, true) : DomHandler.getOuterHeight(this.prevPanelElement, true)) / this.size;\n      this.nextPanelSize = 100 * (this.horizontal() ? DomHandler.getOuterWidth(this.nextPanelElement, true) : DomHandler.getOuterHeight(this.nextPanelElement, true)) / this.size;\n    }\n    this.prevPanelIndex = index;\n    DomHandler.addClass(this.gutterElement, 'p-splitter-gutter-resizing');\n    this.gutterElement.setAttribute('data-p-gutter-resizing', 'true');\n    DomHandler.addClass(this.containerViewChild.nativeElement, 'p-splitter-resizing');\n    this.containerViewChild.nativeElement.setAttribute('data-p-resizing', 'true');\n    this.onResizeStart.emit({\n      originalEvent: event,\n      sizes: this._panelSizes\n    });\n  }\n  onResize(event, step, isKeyDown) {\n    let newPos, newPrevPanelSize, newNextPanelSize;\n    if (isKeyDown) {\n      if (this.horizontal()) {\n        newPrevPanelSize = 100 * (this.prevPanelSize + step) / this.size;\n        newNextPanelSize = 100 * (this.nextPanelSize - step) / this.size;\n      } else {\n        newPrevPanelSize = 100 * (this.prevPanelSize - step) / this.size;\n        newNextPanelSize = 100 * (this.nextPanelSize + step) / this.size;\n      }\n    } else {\n      if (this.horizontal()) newPos = event.pageX * 100 / this.size - this.startPos * 100 / this.size;else newPos = event.pageY * 100 / this.size - this.startPos * 100 / this.size;\n      newPrevPanelSize = this.prevPanelSize + newPos;\n      newNextPanelSize = this.nextPanelSize - newPos;\n    }\n    this.prevSize = parseFloat(newPrevPanelSize).toFixed(4);\n    if (this.validateResize(newPrevPanelSize, newNextPanelSize)) {\n      this.prevPanelElement.style.flexBasis = 'calc(' + newPrevPanelSize + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n      this.nextPanelElement.style.flexBasis = 'calc(' + newNextPanelSize + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n      this._panelSizes[this.prevPanelIndex] = newPrevPanelSize;\n      this._panelSizes[this.prevPanelIndex + 1] = newNextPanelSize;\n    }\n  }\n  resizeEnd(event) {\n    if (this.isStateful()) {\n      this.saveState();\n    }\n    this.onResizeEnd.emit({\n      originalEvent: event,\n      sizes: this._panelSizes\n    });\n    DomHandler.removeClass(this.gutterElement, 'p-splitter-gutter-resizing');\n    DomHandler.removeClass(this.containerViewChild.nativeElement, 'p-splitter-resizing');\n    this.clear();\n  }\n  onGutterMouseDown(event, index) {\n    this.resizeStart(event, index);\n    this.bindMouseListeners();\n  }\n  onGutterTouchStart(event, index) {\n    if (event.cancelable) {\n      this.resizeStart(event, index);\n      this.bindTouchListeners();\n      event.preventDefault();\n    }\n  }\n  onGutterTouchMove(event) {\n    this.onResize(event);\n    event.preventDefault();\n  }\n  onGutterTouchEnd(event) {\n    this.resizeEnd(event);\n    this.unbindTouchListeners();\n    if (event.cancelable) event.preventDefault();\n  }\n  repeat(event, index, step) {\n    this.resizeStart(event, index, true);\n    this.onResize(event, step, true);\n  }\n  setTimer(event, index, step) {\n    this.clearTimer();\n    this.timer = setTimeout(() => {\n      this.repeat(event, index, step);\n    }, 40);\n  }\n  clearTimer() {\n    if (this.timer) {\n      clearTimeout(this.timer);\n    }\n  }\n  onGutterKeyUp(event) {\n    this.clearTimer();\n    this.resizeEnd(event);\n  }\n  onGutterKeyDown(event, index) {\n    switch (event.code) {\n      case 'ArrowLeft':\n        {\n          if (this.layout === 'horizontal') {\n            this.setTimer(event, index, this.step * -1);\n          }\n          event.preventDefault();\n          break;\n        }\n      case 'ArrowRight':\n        {\n          if (this.layout === 'horizontal') {\n            this.setTimer(event, index, this.step);\n          }\n          event.preventDefault();\n          break;\n        }\n      case 'ArrowDown':\n        {\n          if (this.layout === 'vertical') {\n            this.setTimer(event, index, this.step * -1);\n          }\n          event.preventDefault();\n          break;\n        }\n      case 'ArrowUp':\n        {\n          if (this.layout === 'vertical') {\n            this.setTimer(event, index, this.step);\n          }\n          event.preventDefault();\n          break;\n        }\n      default:\n        //no op\n        break;\n    }\n  }\n  validateResize(newPrevPanelSize, newNextPanelSize) {\n    if (this.minSizes.length >= 1 && this.minSizes[0] && this.minSizes[0] > newPrevPanelSize) {\n      return false;\n    }\n    if (this.minSizes.length > 1 && this.minSizes[1] && this.minSizes[1] > newNextPanelSize) {\n      return false;\n    }\n    return true;\n  }\n  bindMouseListeners() {\n    if (!this.mouseMoveListener) {\n      this.mouseMoveListener = this.renderer.listen(this.document, 'mousemove', event => {\n        this.onResize(event);\n      });\n    }\n    if (!this.mouseUpListener) {\n      this.mouseUpListener = this.renderer.listen(this.document, 'mouseup', event => {\n        this.resizeEnd(event);\n        this.unbindMouseListeners();\n      });\n    }\n  }\n  bindTouchListeners() {\n    if (!this.touchMoveListener) {\n      this.touchMoveListener = this.renderer.listen(this.document, 'touchmove', event => {\n        this.onResize(event.changedTouches[0]);\n      });\n    }\n    if (!this.touchEndListener) {\n      this.touchEndListener = this.renderer.listen(this.document, 'touchend', event => {\n        this.resizeEnd(event);\n        this.unbindTouchListeners();\n      });\n    }\n  }\n  unbindMouseListeners() {\n    if (this.mouseMoveListener) {\n      this.mouseMoveListener();\n      this.mouseMoveListener = null;\n    }\n    if (this.mouseUpListener) {\n      this.mouseUpListener();\n      this.mouseUpListener = null;\n    }\n  }\n  unbindTouchListeners() {\n    if (this.touchMoveListener) {\n      this.touchMoveListener();\n      this.touchMoveListener = null;\n    }\n    if (this.touchEndListener) {\n      this.touchEndListener();\n      this.touchEndListener = null;\n    }\n  }\n  clear() {\n    this.dragging = false;\n    this.size = null;\n    this.startPos = null;\n    this.prevPanelElement = null;\n    this.nextPanelElement = null;\n    this.prevPanelSize = null;\n    this.nextPanelSize = null;\n    this.gutterElement = null;\n    this.prevPanelIndex = null;\n  }\n  isNested() {\n    if (this.el.nativeElement) {\n      let parent = this.el.nativeElement.parentElement;\n      while (parent && !DomHandler.hasClass(parent, 'p-splitter')) {\n        parent = parent.parentElement;\n      }\n      return parent !== null;\n    } else {\n      return false;\n    }\n  }\n  isStateful() {\n    return this.stateKey != null;\n  }\n  getStorage() {\n    if (isPlatformBrowser(this.platformId)) {\n      switch (this.stateStorage) {\n        case 'local':\n          return this.window.localStorage;\n        case 'session':\n          return this.window.sessionStorage;\n        default:\n          throw new Error(this.stateStorage + ' is not a valid value for the state storage, supported values are \"local\" and \"session\".');\n      }\n    } else {\n      throw new Error('Storage is not a available by default on the server.');\n    }\n  }\n  saveState() {\n    this.getStorage().setItem(this.stateKey, JSON.stringify(this._panelSizes));\n  }\n  restoreState() {\n    const storage = this.getStorage();\n    const stateString = storage.getItem(this.stateKey);\n    if (stateString) {\n      this._panelSizes = JSON.parse(stateString);\n      let children = [...this.containerViewChild.nativeElement.children].filter(child => DomHandler.hasClass(child, 'p-splitter-panel'));\n      children.forEach((child, i) => {\n        child.style.flexBasis = 'calc(' + this._panelSizes[i] + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n      });\n      return true;\n    }\n    return false;\n  }\n  containerClass() {\n    return {\n      'p-splitter p-component': true,\n      'p-splitter-horizontal': this.layout === 'horizontal',\n      'p-splitter-vertical': this.layout === 'vertical'\n    };\n  }\n  panelContainerClass() {\n    return {\n      'p-splitter-panel': true,\n      'p-splitter-panel-nested': true\n    };\n  }\n  gutterStyle() {\n    if (this.horizontal()) return {\n      width: this.gutterSize + 'px'\n    };else return {\n      height: this.gutterSize + 'px'\n    };\n  }\n  horizontal() {\n    return this.layout === 'horizontal';\n  }\n  static ɵfac = function Splitter_Factory(t) {\n    return new (t || Splitter)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Splitter,\n    selectors: [[\"p-splitter\"]],\n    contentQueries: function Splitter_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function Splitter_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    hostVars: 2,\n    hostBindings: function Splitter_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"p-splitter-panel-nested\", ctx.nested);\n      }\n    },\n    inputs: {\n      styleClass: \"styleClass\",\n      panelStyleClass: \"panelStyleClass\",\n      style: \"style\",\n      panelStyle: \"panelStyle\",\n      stateStorage: \"stateStorage\",\n      stateKey: \"stateKey\",\n      layout: \"layout\",\n      gutterSize: \"gutterSize\",\n      step: \"step\",\n      minSizes: \"minSizes\",\n      panelSizes: \"panelSizes\"\n    },\n    outputs: {\n      onResizeEnd: \"onResizeEnd\",\n      onResizeStart: \"onResizeStart\"\n    },\n    decls: 3,\n    vars: 8,\n    consts: [[3, \"ngClass\", \"ngStyle\"], [\"container\", \"\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"tabindex\", \"-1\", 3, \"ngClass\", \"ngStyle\"], [4, \"ngTemplateOutlet\"], [\"class\", \"p-splitter-gutter\", \"role\", \"separator\", \"tabindex\", \"-1\", 3, \"mousedown\", \"touchstart\", \"touchmove\", \"touchend\", 4, \"ngIf\"], [\"role\", \"separator\", \"tabindex\", \"-1\", 1, \"p-splitter-gutter\", 3, \"mousedown\", \"touchstart\", \"touchmove\", \"touchend\"], [\"tabindex\", \"0\", 1, \"p-splitter-gutter-handle\", 3, \"ngStyle\", \"keyup\", \"keydown\"]],\n    template: function Splitter_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0, 1);\n        i0.ɵɵtemplate(2, Splitter_ng_template_2_Template, 3, 8, \"ng-template\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", ctx.containerClass())(\"ngStyle\", ctx.style);\n        i0.ɵɵattribute(\"data-pc-name\", \"splitter\")(\"data-p-gutter-resizing\", false)(\"data-pc-section\", \"root\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.panels);\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle],\n    styles: [\"@layer primeng{.p-splitter{display:flex;flex-wrap:nowrap}.p-splitter-vertical{flex-direction:column}.p-splitter-panel{flex-grow:1}.p-splitter-panel-nested{display:flex;min-width:0}.p-splitter-panel p-splitter{flex-grow:1}.p-splitter-panel .p-splitter{flex-grow:1;border:0 none}.p-splitter-gutter{flex-grow:0;flex-shrink:0;display:flex;align-items:center;justify-content:center;cursor:col-resize}.p-splitter-horizontal.p-splitter-resizing{cursor:col-resize;-webkit-user-select:none;user-select:none}.p-splitter-horizontal>.p-splitter-gutter>.p-splitter-gutter-handle{height:24px;width:100%}.p-splitter-horizontal>.p-splitter-gutter{cursor:col-resize}.p-splitter-vertical.p-splitter-resizing{cursor:row-resize;-webkit-user-select:none;user-select:none}.p-splitter-vertical>.p-splitter-gutter{cursor:row-resize}.p-splitter-vertical>.p-splitter-gutter>.p-splitter-gutter-handle{width:24px;height:100%}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Splitter, [{\n    type: Component,\n    args: [{\n      selector: 'p-splitter',\n      template: `\n        <div #container [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\" [attr.data-pc-name]=\"'splitter'\" [attr.data-p-gutter-resizing]=\"false\" [attr.data-pc-section]=\"'root'\">\n            <ng-template ngFor let-panel [ngForOf]=\"panels\" let-i=\"index\">\n                <div [ngClass]=\"panelContainerClass()\" [class]=\"panelStyleClass\" [ngStyle]=\"panelStyle\" tabindex=\"-1\" [attr.data-pc-name]=\"'splitter'\" [attr.data-pc-section]=\"'root'\">\n                    <ng-container *ngTemplateOutlet=\"panel\"></ng-container>\n                </div>\n                <div\n                    *ngIf=\"i !== panels.length - 1\"\n                    class=\"p-splitter-gutter\"\n                    role=\"separator\"\n                    tabindex=\"-1\"\n                    (mousedown)=\"onGutterMouseDown($event, i)\"\n                    (touchstart)=\"onGutterTouchStart($event, i)\"\n                    (touchmove)=\"onGutterTouchMove($event)\"\n                    (touchend)=\"onGutterTouchEnd($event, i)\"\n                    [attr.data-p-gutter-resizing]=\"false\"\n                    [attr.data-pc-section]=\"'gutter'\"\n                >\n                    <div\n                        class=\"p-splitter-gutter-handle\"\n                        tabindex=\"0\"\n                        [ngStyle]=\"gutterStyle()\"\n                        [attr.aria-orientation]=\"layout\"\n                        [attr.aria-valuenow]=\"prevSize\"\n                        [attr.data-pc-section]=\"'gutterhandle'\"\n                        (keyup)=\"onGutterKeyUp($event)\"\n                        (keydown)=\"onGutterKeyDown($event, i)\"\n                    ></div>\n                </div>\n            </ng-template>\n        </div>\n    `,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        class: 'p-element',\n        '[class.p-splitter-panel-nested]': 'nested'\n      },\n      styles: [\"@layer primeng{.p-splitter{display:flex;flex-wrap:nowrap}.p-splitter-vertical{flex-direction:column}.p-splitter-panel{flex-grow:1}.p-splitter-panel-nested{display:flex;min-width:0}.p-splitter-panel p-splitter{flex-grow:1}.p-splitter-panel .p-splitter{flex-grow:1;border:0 none}.p-splitter-gutter{flex-grow:0;flex-shrink:0;display:flex;align-items:center;justify-content:center;cursor:col-resize}.p-splitter-horizontal.p-splitter-resizing{cursor:col-resize;-webkit-user-select:none;user-select:none}.p-splitter-horizontal>.p-splitter-gutter>.p-splitter-gutter-handle{height:24px;width:100%}.p-splitter-horizontal>.p-splitter-gutter{cursor:col-resize}.p-splitter-vertical.p-splitter-resizing{cursor:row-resize;-webkit-user-select:none;user-select:none}.p-splitter-vertical>.p-splitter-gutter{cursor:row-resize}.p-splitter-vertical>.p-splitter-gutter>.p-splitter-gutter-handle{width:24px;height:100%}}\\n\"]\n    }]\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.ElementRef\n  }], {\n    styleClass: [{\n      type: Input\n    }],\n    panelStyleClass: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    panelStyle: [{\n      type: Input\n    }],\n    stateStorage: [{\n      type: Input\n    }],\n    stateKey: [{\n      type: Input\n    }],\n    layout: [{\n      type: Input\n    }],\n    gutterSize: [{\n      type: Input\n    }],\n    step: [{\n      type: Input\n    }],\n    minSizes: [{\n      type: Input\n    }],\n    panelSizes: [{\n      type: Input\n    }],\n    onResizeEnd: [{\n      type: Output\n    }],\n    onResizeStart: [{\n      type: Output\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    containerViewChild: [{\n      type: ViewChild,\n      args: ['container', {\n        static: false\n      }]\n    }]\n  });\n})();\nclass SplitterModule {\n  static ɵfac = function SplitterModule_Factory(t) {\n    return new (t || SplitterModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: SplitterModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SplitterModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [Splitter, SharedModule],\n      declarations: [Splitter]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Splitter, SplitterModule };", "map": {"version": 3, "names": ["i1", "isPlatformBrowser", "DOCUMENT", "CommonModule", "i0", "EventEmitter", "PLATFORM_ID", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Inject", "Input", "Output", "ContentChildren", "ViewChild", "NgModule", "PrimeTemplate", "SharedModule", "<PERSON><PERSON><PERSON><PERSON>", "_c0", "Splitter_ng_template_2_ng_container_1_Template", "rf", "ctx", "ɵɵelementContainer", "Splitter_ng_template_2_div_2_Template", "_r8", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "Splitter_ng_template_2_div_2_Template_div_mousedown_0_listener", "$event", "ɵɵrestoreView", "i_r3", "ɵɵnextContext", "index", "ctx_r6", "ɵɵresetView", "onGutterMouseDown", "Splitter_ng_template_2_div_2_Template_div_touchstart_0_listener", "ctx_r9", "onGutterTouchStart", "Splitter_ng_template_2_div_2_Template_div_touchmove_0_listener", "ctx_r11", "onGutterTouchMove", "Splitter_ng_template_2_div_2_Template_div_touchend_0_listener", "ctx_r12", "onGutterTouchEnd", "Splitter_ng_template_2_div_2_Template_div_keyup_1_listener", "ctx_r14", "onGutterKeyUp", "Splitter_ng_template_2_div_2_Template_div_keydown_1_listener", "ctx_r15", "onGutterKeyDown", "ɵɵelementEnd", "ctx_r5", "ɵɵattribute", "ɵɵadvance", "ɵɵproperty", "gutterStyle", "layout", "prevSize", "Splitter_ng_template_2_Template", "ɵɵtemplate", "panel_r2", "$implicit", "ctx_r1", "ɵɵclassMap", "panelStyleClass", "panelContainerClass", "panelStyle", "panels", "length", "Splitter", "document", "platformId", "renderer", "cd", "el", "styleClass", "style", "stateStorage", "stateKey", "gutterSize", "step", "minSizes", "panelSizes", "_panelSizes", "val", "nativeElement", "children", "filter", "child", "hasClass", "map", "panel", "i", "panelInitialSize", "panelSize", "flexBasis", "onResizeEnd", "onResizeStart", "templates", "containerViewChild", "nested", "dragging", "mouseMoveListener", "mouseUpListener", "touchMoveListener", "touchEndListener", "size", "gutterElement", "startPos", "prevPanelElement", "nextPanelElement", "nextPanelSize", "prevPanelSize", "prevPanelIndex", "timer", "window", "constructor", "defaultView", "ngOnInit", "isNested", "ngAfterContentInit", "for<PERSON>ach", "item", "getType", "push", "template", "ngAfterViewInit", "initialized", "isStateful", "restoreState", "parseFloat", "toFixed", "resizeStart", "event", "isKeyDown", "currentTarget", "target", "parentElement", "horizontal", "getWidth", "getHeight", "MouseEvent", "pageX", "changedTouches", "pageY", "previousElementSibling", "nextElement<PERSON><PERSON>ling", "getOuterWidth", "getOuterHeight", "addClass", "setAttribute", "emit", "originalEvent", "sizes", "onResize", "newPos", "newPrevPanelSize", "newNextPanelSize", "validateResize", "resizeEnd", "saveState", "removeClass", "clear", "bindMouseListeners", "cancelable", "bindTouchListeners", "preventDefault", "unbindTouchListeners", "repeat", "setTimer", "clearTimer", "setTimeout", "clearTimeout", "code", "listen", "unbindMouseListeners", "parent", "getStorage", "localStorage", "sessionStorage", "Error", "setItem", "JSON", "stringify", "storage", "stateString", "getItem", "parse", "containerClass", "width", "height", "ɵfac", "Splitter_Factory", "t", "ɵɵdirectiveInject", "Renderer2", "ChangeDetectorRef", "ElementRef", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "contentQueries", "Splitter_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "viewQuery", "Splitter_Query", "ɵɵviewQuery", "first", "hostAttrs", "hostVars", "hostBindings", "Splitter_HostBindings", "ɵɵclassProp", "inputs", "outputs", "decls", "vars", "consts", "Splitter_Template", "dependencies", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "None", "OnPush", "host", "class", "Document", "decorators", "undefined", "static", "SplitterModule", "SplitterModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["D:/Greg/sakai-ng-master/sakai-ng-master/node_modules/primeng/fesm2022/primeng-splitter.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, Component, ViewEncapsulation, ChangeDetectionStrategy, Inject, Input, Output, ContentChildren, ViewChild, NgModule } from '@angular/core';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\n\n/**\n * Splitter is utilized to separate and resize panels.\n * @group Components\n */\nclass Splitter {\n    document;\n    platformId;\n    renderer;\n    cd;\n    el;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Style class of the panel.\n     * @group Props\n     */\n    panelStyleClass;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Inline style of the panel.\n     * @group Props\n     */\n    panelStyle;\n    /**\n     * Defines where a stateful splitter keeps its state, valid values are 'session' for sessionStorage and 'local' for localStorage.\n     * @group Props\n     */\n    stateStorage = 'session';\n    /**\n     * Storage identifier of a stateful Splitter.\n     * @group Props\n     */\n    stateKey = null;\n    /**\n     * Orientation of the panels. Valid values are 'horizontal' and 'vertical'.\n     * @group Props\n     */\n    layout = 'horizontal';\n    /**\n     * Size of the divider in pixels.\n     * @group Props\n     */\n    gutterSize = 4;\n    /**\n     * Step factor to increment/decrement the size of the panels while pressing the arrow keys.\n     * @group Props\n     */\n    step = 5;\n    /**\n     * Minimum size of the elements relative to 100%.\n     * @group Props\n     */\n    minSizes = [];\n    /**\n     * Size of the elements relative to 100%.\n     * @group Props\n     */\n    get panelSizes() {\n        return this._panelSizes;\n    }\n    set panelSizes(val) {\n        this._panelSizes = val;\n        if (this.el && this.el.nativeElement && this.panels.length > 0) {\n            let children = [...this.el.nativeElement.children[0].children].filter((child) => DomHandler.hasClass(child, 'p-splitter-panel'));\n            let _panelSizes = [];\n            this.panels.map((panel, i) => {\n                let panelInitialSize = this.panelSizes.length - 1 >= i ? this.panelSizes[i] : null;\n                let panelSize = panelInitialSize || 100 / this.panels.length;\n                _panelSizes[i] = panelSize;\n                children[i].style.flexBasis = 'calc(' + panelSize + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n            });\n        }\n    }\n    /**\n     * Callback to invoke when resize ends.\n     * @param {SplitterResizeEndEvent} event - Custom panel resize end event\n     * @group Emits\n     */\n    onResizeEnd = new EventEmitter();\n    /**\n     * Callback to invoke when resize starts.\n     * @param {SplitterResizeStartEvent} event - Custom panel resize start event\n     * @group Emits\n     */\n    onResizeStart = new EventEmitter();\n    templates;\n    containerViewChild;\n    nested = false;\n    panels = [];\n    dragging = false;\n    mouseMoveListener;\n    mouseUpListener;\n    touchMoveListener;\n    touchEndListener;\n    size;\n    gutterElement;\n    startPos;\n    prevPanelElement;\n    nextPanelElement;\n    nextPanelSize;\n    prevPanelSize;\n    _panelSizes = [];\n    prevPanelIndex;\n    timer;\n    prevSize;\n    window;\n    constructor(document, platformId, renderer, cd, el) {\n        this.document = document;\n        this.platformId = platformId;\n        this.renderer = renderer;\n        this.cd = cd;\n        this.el = el;\n        this.window = this.document.defaultView;\n    }\n    ngOnInit() {\n        this.nested = this.isNested();\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'panel':\n                    this.panels.push(item.template);\n                    break;\n                default:\n                    this.panels.push(item.template);\n                    break;\n            }\n        });\n    }\n    ngAfterViewInit() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (this.panels && this.panels.length) {\n                let initialized = false;\n                if (this.isStateful()) {\n                    initialized = this.restoreState();\n                }\n                if (!initialized) {\n                    let children = [...this.el.nativeElement.children[0].children].filter((child) => DomHandler.hasClass(child, 'p-splitter-panel'));\n                    let _panelSizes = [];\n                    this.panels.map((panel, i) => {\n                        let panelInitialSize = this.panelSizes.length - 1 >= i ? this.panelSizes[i] : null;\n                        let panelSize = panelInitialSize || 100 / this.panels.length;\n                        _panelSizes[i] = panelSize;\n                        children[i].style.flexBasis = 'calc(' + panelSize + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n                    });\n                    this._panelSizes = _panelSizes;\n                    this.prevSize = parseFloat(_panelSizes[0]).toFixed(4);\n                }\n            }\n        }\n    }\n    resizeStart(event, index, isKeyDown) {\n        this.gutterElement = event.currentTarget || event.target.parentElement;\n        this.size = this.horizontal() ? DomHandler.getWidth(this.containerViewChild.nativeElement) : DomHandler.getHeight(this.containerViewChild.nativeElement);\n        if (!isKeyDown) {\n            this.dragging = true;\n            this.startPos = this.horizontal() ? (event instanceof MouseEvent ? event.pageX : event.changedTouches[0].pageX) : event instanceof MouseEvent ? event.pageY : event.changedTouches[0].pageY;\n        }\n        this.prevPanelElement = this.gutterElement.previousElementSibling;\n        this.nextPanelElement = this.gutterElement.nextElementSibling;\n        if (isKeyDown) {\n            this.prevPanelSize = this.horizontal() ? DomHandler.getOuterWidth(this.prevPanelElement, true) : DomHandler.getOuterHeight(this.prevPanelElement, true);\n            this.nextPanelSize = this.horizontal() ? DomHandler.getOuterWidth(this.nextPanelElement, true) : DomHandler.getOuterHeight(this.nextPanelElement, true);\n        }\n        else {\n            this.prevPanelSize = (100 * (this.horizontal() ? DomHandler.getOuterWidth(this.prevPanelElement, true) : DomHandler.getOuterHeight(this.prevPanelElement, true))) / this.size;\n            this.nextPanelSize = (100 * (this.horizontal() ? DomHandler.getOuterWidth(this.nextPanelElement, true) : DomHandler.getOuterHeight(this.nextPanelElement, true))) / this.size;\n        }\n        this.prevPanelIndex = index;\n        DomHandler.addClass(this.gutterElement, 'p-splitter-gutter-resizing');\n        this.gutterElement.setAttribute('data-p-gutter-resizing', 'true');\n        DomHandler.addClass(this.containerViewChild.nativeElement, 'p-splitter-resizing');\n        this.containerViewChild.nativeElement.setAttribute('data-p-resizing', 'true');\n        this.onResizeStart.emit({ originalEvent: event, sizes: this._panelSizes });\n    }\n    onResize(event, step, isKeyDown) {\n        let newPos, newPrevPanelSize, newNextPanelSize;\n        if (isKeyDown) {\n            if (this.horizontal()) {\n                newPrevPanelSize = (100 * (this.prevPanelSize + step)) / this.size;\n                newNextPanelSize = (100 * (this.nextPanelSize - step)) / this.size;\n            }\n            else {\n                newPrevPanelSize = (100 * (this.prevPanelSize - step)) / this.size;\n                newNextPanelSize = (100 * (this.nextPanelSize + step)) / this.size;\n            }\n        }\n        else {\n            if (this.horizontal())\n                newPos = (event.pageX * 100) / this.size - (this.startPos * 100) / this.size;\n            else\n                newPos = (event.pageY * 100) / this.size - (this.startPos * 100) / this.size;\n            newPrevPanelSize = this.prevPanelSize + newPos;\n            newNextPanelSize = this.nextPanelSize - newPos;\n        }\n        this.prevSize = parseFloat(newPrevPanelSize).toFixed(4);\n        if (this.validateResize(newPrevPanelSize, newNextPanelSize)) {\n            this.prevPanelElement.style.flexBasis = 'calc(' + newPrevPanelSize + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n            this.nextPanelElement.style.flexBasis = 'calc(' + newNextPanelSize + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n            this._panelSizes[this.prevPanelIndex] = newPrevPanelSize;\n            this._panelSizes[this.prevPanelIndex + 1] = newNextPanelSize;\n        }\n    }\n    resizeEnd(event) {\n        if (this.isStateful()) {\n            this.saveState();\n        }\n        this.onResizeEnd.emit({ originalEvent: event, sizes: this._panelSizes });\n        DomHandler.removeClass(this.gutterElement, 'p-splitter-gutter-resizing');\n        DomHandler.removeClass(this.containerViewChild.nativeElement, 'p-splitter-resizing');\n        this.clear();\n    }\n    onGutterMouseDown(event, index) {\n        this.resizeStart(event, index);\n        this.bindMouseListeners();\n    }\n    onGutterTouchStart(event, index) {\n        if (event.cancelable) {\n            this.resizeStart(event, index);\n            this.bindTouchListeners();\n            event.preventDefault();\n        }\n    }\n    onGutterTouchMove(event) {\n        this.onResize(event);\n        event.preventDefault();\n    }\n    onGutterTouchEnd(event) {\n        this.resizeEnd(event);\n        this.unbindTouchListeners();\n        if (event.cancelable)\n            event.preventDefault();\n    }\n    repeat(event, index, step) {\n        this.resizeStart(event, index, true);\n        this.onResize(event, step, true);\n    }\n    setTimer(event, index, step) {\n        this.clearTimer();\n        this.timer = setTimeout(() => {\n            this.repeat(event, index, step);\n        }, 40);\n    }\n    clearTimer() {\n        if (this.timer) {\n            clearTimeout(this.timer);\n        }\n    }\n    onGutterKeyUp(event) {\n        this.clearTimer();\n        this.resizeEnd(event);\n    }\n    onGutterKeyDown(event, index) {\n        switch (event.code) {\n            case 'ArrowLeft': {\n                if (this.layout === 'horizontal') {\n                    this.setTimer(event, index, this.step * -1);\n                }\n                event.preventDefault();\n                break;\n            }\n            case 'ArrowRight': {\n                if (this.layout === 'horizontal') {\n                    this.setTimer(event, index, this.step);\n                }\n                event.preventDefault();\n                break;\n            }\n            case 'ArrowDown': {\n                if (this.layout === 'vertical') {\n                    this.setTimer(event, index, this.step * -1);\n                }\n                event.preventDefault();\n                break;\n            }\n            case 'ArrowUp': {\n                if (this.layout === 'vertical') {\n                    this.setTimer(event, index, this.step);\n                }\n                event.preventDefault();\n                break;\n            }\n            default:\n                //no op\n                break;\n        }\n    }\n    validateResize(newPrevPanelSize, newNextPanelSize) {\n        if (this.minSizes.length >= 1 && this.minSizes[0] && this.minSizes[0] > newPrevPanelSize) {\n            return false;\n        }\n        if (this.minSizes.length > 1 && this.minSizes[1] && this.minSizes[1] > newNextPanelSize) {\n            return false;\n        }\n        return true;\n    }\n    bindMouseListeners() {\n        if (!this.mouseMoveListener) {\n            this.mouseMoveListener = this.renderer.listen(this.document, 'mousemove', (event) => {\n                this.onResize(event);\n            });\n        }\n        if (!this.mouseUpListener) {\n            this.mouseUpListener = this.renderer.listen(this.document, 'mouseup', (event) => {\n                this.resizeEnd(event);\n                this.unbindMouseListeners();\n            });\n        }\n    }\n    bindTouchListeners() {\n        if (!this.touchMoveListener) {\n            this.touchMoveListener = this.renderer.listen(this.document, 'touchmove', (event) => {\n                this.onResize(event.changedTouches[0]);\n            });\n        }\n        if (!this.touchEndListener) {\n            this.touchEndListener = this.renderer.listen(this.document, 'touchend', (event) => {\n                this.resizeEnd(event);\n                this.unbindTouchListeners();\n            });\n        }\n    }\n    unbindMouseListeners() {\n        if (this.mouseMoveListener) {\n            this.mouseMoveListener();\n            this.mouseMoveListener = null;\n        }\n        if (this.mouseUpListener) {\n            this.mouseUpListener();\n            this.mouseUpListener = null;\n        }\n    }\n    unbindTouchListeners() {\n        if (this.touchMoveListener) {\n            this.touchMoveListener();\n            this.touchMoveListener = null;\n        }\n        if (this.touchEndListener) {\n            this.touchEndListener();\n            this.touchEndListener = null;\n        }\n    }\n    clear() {\n        this.dragging = false;\n        this.size = null;\n        this.startPos = null;\n        this.prevPanelElement = null;\n        this.nextPanelElement = null;\n        this.prevPanelSize = null;\n        this.nextPanelSize = null;\n        this.gutterElement = null;\n        this.prevPanelIndex = null;\n    }\n    isNested() {\n        if (this.el.nativeElement) {\n            let parent = this.el.nativeElement.parentElement;\n            while (parent && !DomHandler.hasClass(parent, 'p-splitter')) {\n                parent = parent.parentElement;\n            }\n            return parent !== null;\n        }\n        else {\n            return false;\n        }\n    }\n    isStateful() {\n        return this.stateKey != null;\n    }\n    getStorage() {\n        if (isPlatformBrowser(this.platformId)) {\n            switch (this.stateStorage) {\n                case 'local':\n                    return this.window.localStorage;\n                case 'session':\n                    return this.window.sessionStorage;\n                default:\n                    throw new Error(this.stateStorage + ' is not a valid value for the state storage, supported values are \"local\" and \"session\".');\n            }\n        }\n        else {\n            throw new Error('Storage is not a available by default on the server.');\n        }\n    }\n    saveState() {\n        this.getStorage().setItem(this.stateKey, JSON.stringify(this._panelSizes));\n    }\n    restoreState() {\n        const storage = this.getStorage();\n        const stateString = storage.getItem(this.stateKey);\n        if (stateString) {\n            this._panelSizes = JSON.parse(stateString);\n            let children = [...this.containerViewChild.nativeElement.children].filter((child) => DomHandler.hasClass(child, 'p-splitter-panel'));\n            children.forEach((child, i) => {\n                child.style.flexBasis = 'calc(' + this._panelSizes[i] + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n            });\n            return true;\n        }\n        return false;\n    }\n    containerClass() {\n        return {\n            'p-splitter p-component': true,\n            'p-splitter-horizontal': this.layout === 'horizontal',\n            'p-splitter-vertical': this.layout === 'vertical'\n        };\n    }\n    panelContainerClass() {\n        return {\n            'p-splitter-panel': true,\n            'p-splitter-panel-nested': true\n        };\n    }\n    gutterStyle() {\n        if (this.horizontal())\n            return { width: this.gutterSize + 'px' };\n        else\n            return { height: this.gutterSize + 'px' };\n    }\n    horizontal() {\n        return this.layout === 'horizontal';\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: Splitter, deps: [{ token: DOCUMENT }, { token: PLATFORM_ID }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.5\", type: Splitter, selector: \"p-splitter\", inputs: { styleClass: \"styleClass\", panelStyleClass: \"panelStyleClass\", style: \"style\", panelStyle: \"panelStyle\", stateStorage: \"stateStorage\", stateKey: \"stateKey\", layout: \"layout\", gutterSize: \"gutterSize\", step: \"step\", minSizes: \"minSizes\", panelSizes: \"panelSizes\" }, outputs: { onResizeEnd: \"onResizeEnd\", onResizeStart: \"onResizeStart\" }, host: { properties: { \"class.p-splitter-panel-nested\": \"nested\" }, classAttribute: \"p-element\" }, queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"containerViewChild\", first: true, predicate: [\"container\"], descendants: true }], ngImport: i0, template: `\n        <div #container [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\" [attr.data-pc-name]=\"'splitter'\" [attr.data-p-gutter-resizing]=\"false\" [attr.data-pc-section]=\"'root'\">\n            <ng-template ngFor let-panel [ngForOf]=\"panels\" let-i=\"index\">\n                <div [ngClass]=\"panelContainerClass()\" [class]=\"panelStyleClass\" [ngStyle]=\"panelStyle\" tabindex=\"-1\" [attr.data-pc-name]=\"'splitter'\" [attr.data-pc-section]=\"'root'\">\n                    <ng-container *ngTemplateOutlet=\"panel\"></ng-container>\n                </div>\n                <div\n                    *ngIf=\"i !== panels.length - 1\"\n                    class=\"p-splitter-gutter\"\n                    role=\"separator\"\n                    tabindex=\"-1\"\n                    (mousedown)=\"onGutterMouseDown($event, i)\"\n                    (touchstart)=\"onGutterTouchStart($event, i)\"\n                    (touchmove)=\"onGutterTouchMove($event)\"\n                    (touchend)=\"onGutterTouchEnd($event, i)\"\n                    [attr.data-p-gutter-resizing]=\"false\"\n                    [attr.data-pc-section]=\"'gutter'\"\n                >\n                    <div\n                        class=\"p-splitter-gutter-handle\"\n                        tabindex=\"0\"\n                        [ngStyle]=\"gutterStyle()\"\n                        [attr.aria-orientation]=\"layout\"\n                        [attr.aria-valuenow]=\"prevSize\"\n                        [attr.data-pc-section]=\"'gutterhandle'\"\n                        (keyup)=\"onGutterKeyUp($event)\"\n                        (keydown)=\"onGutterKeyDown($event, i)\"\n                    ></div>\n                </div>\n            </ng-template>\n        </div>\n    `, isInline: true, styles: [\"@layer primeng{.p-splitter{display:flex;flex-wrap:nowrap}.p-splitter-vertical{flex-direction:column}.p-splitter-panel{flex-grow:1}.p-splitter-panel-nested{display:flex;min-width:0}.p-splitter-panel p-splitter{flex-grow:1}.p-splitter-panel .p-splitter{flex-grow:1;border:0 none}.p-splitter-gutter{flex-grow:0;flex-shrink:0;display:flex;align-items:center;justify-content:center;cursor:col-resize}.p-splitter-horizontal.p-splitter-resizing{cursor:col-resize;-webkit-user-select:none;user-select:none}.p-splitter-horizontal>.p-splitter-gutter>.p-splitter-gutter-handle{height:24px;width:100%}.p-splitter-horizontal>.p-splitter-gutter{cursor:col-resize}.p-splitter-vertical.p-splitter-resizing{cursor:row-resize;-webkit-user-select:none;user-select:none}.p-splitter-vertical>.p-splitter-gutter{cursor:row-resize}.p-splitter-vertical>.p-splitter-gutter>.p-splitter-gutter-handle{width:24px;height:100%}}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: Splitter, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-splitter', template: `\n        <div #container [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\" [attr.data-pc-name]=\"'splitter'\" [attr.data-p-gutter-resizing]=\"false\" [attr.data-pc-section]=\"'root'\">\n            <ng-template ngFor let-panel [ngForOf]=\"panels\" let-i=\"index\">\n                <div [ngClass]=\"panelContainerClass()\" [class]=\"panelStyleClass\" [ngStyle]=\"panelStyle\" tabindex=\"-1\" [attr.data-pc-name]=\"'splitter'\" [attr.data-pc-section]=\"'root'\">\n                    <ng-container *ngTemplateOutlet=\"panel\"></ng-container>\n                </div>\n                <div\n                    *ngIf=\"i !== panels.length - 1\"\n                    class=\"p-splitter-gutter\"\n                    role=\"separator\"\n                    tabindex=\"-1\"\n                    (mousedown)=\"onGutterMouseDown($event, i)\"\n                    (touchstart)=\"onGutterTouchStart($event, i)\"\n                    (touchmove)=\"onGutterTouchMove($event)\"\n                    (touchend)=\"onGutterTouchEnd($event, i)\"\n                    [attr.data-p-gutter-resizing]=\"false\"\n                    [attr.data-pc-section]=\"'gutter'\"\n                >\n                    <div\n                        class=\"p-splitter-gutter-handle\"\n                        tabindex=\"0\"\n                        [ngStyle]=\"gutterStyle()\"\n                        [attr.aria-orientation]=\"layout\"\n                        [attr.aria-valuenow]=\"prevSize\"\n                        [attr.data-pc-section]=\"'gutterhandle'\"\n                        (keyup)=\"onGutterKeyUp($event)\"\n                        (keydown)=\"onGutterKeyDown($event, i)\"\n                    ></div>\n                </div>\n            </ng-template>\n        </div>\n    `, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        class: 'p-element',\n                        '[class.p-splitter-panel-nested]': 'nested'\n                    }, styles: [\"@layer primeng{.p-splitter{display:flex;flex-wrap:nowrap}.p-splitter-vertical{flex-direction:column}.p-splitter-panel{flex-grow:1}.p-splitter-panel-nested{display:flex;min-width:0}.p-splitter-panel p-splitter{flex-grow:1}.p-splitter-panel .p-splitter{flex-grow:1;border:0 none}.p-splitter-gutter{flex-grow:0;flex-shrink:0;display:flex;align-items:center;justify-content:center;cursor:col-resize}.p-splitter-horizontal.p-splitter-resizing{cursor:col-resize;-webkit-user-select:none;user-select:none}.p-splitter-horizontal>.p-splitter-gutter>.p-splitter-gutter-handle{height:24px;width:100%}.p-splitter-horizontal>.p-splitter-gutter{cursor:col-resize}.p-splitter-vertical.p-splitter-resizing{cursor:row-resize;-webkit-user-select:none;user-select:none}.p-splitter-vertical>.p-splitter-gutter{cursor:row-resize}.p-splitter-vertical>.p-splitter-gutter>.p-splitter-gutter-handle{width:24px;height:100%}}\\n\"] }]\n        }], ctorParameters: () => [{ type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: i0.ElementRef }], propDecorators: { styleClass: [{\n                type: Input\n            }], panelStyleClass: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], panelStyle: [{\n                type: Input\n            }], stateStorage: [{\n                type: Input\n            }], stateKey: [{\n                type: Input\n            }], layout: [{\n                type: Input\n            }], gutterSize: [{\n                type: Input\n            }], step: [{\n                type: Input\n            }], minSizes: [{\n                type: Input\n            }], panelSizes: [{\n                type: Input\n            }], onResizeEnd: [{\n                type: Output\n            }], onResizeStart: [{\n                type: Output\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }], containerViewChild: [{\n                type: ViewChild,\n                args: ['container', { static: false }]\n            }] } });\nclass SplitterModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: SplitterModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.5\", ngImport: i0, type: SplitterModule, declarations: [Splitter], imports: [CommonModule], exports: [Splitter, SharedModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: SplitterModule, imports: [CommonModule, SharedModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: SplitterModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [Splitter, SharedModule],\n                    declarations: [Splitter]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Splitter, SplitterModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,iBAAiB,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AAC3E,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,WAAW,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,eAAe,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC7K,SAASC,aAAa,EAAEC,YAAY,QAAQ,aAAa;AACzD,SAASC,UAAU,QAAQ,aAAa;;AAExC;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,SAAAC,+CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA4a6FjB,EAAE,CAAAmB,kBAAA,EAKrB,CAAC;EAAA;AAAA;AAAA,SAAAC,sCAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAI,GAAA,GALkBrB,EAAE,CAAAsB,gBAAA;IAAFtB,EAAE,CAAAuB,cAAA,YAkB/E,CAAC;IAlB4EvB,EAAE,CAAAwB,UAAA,uBAAAC,+DAAAC,MAAA;MAAF1B,EAAE,CAAA2B,aAAA,CAAAN,GAAA;MAAA,MAAAO,IAAA,GAAF5B,EAAE,CAAA6B,aAAA,GAAAC,KAAA;MAAA,MAAAC,MAAA,GAAF/B,EAAE,CAAA6B,aAAA;MAAA,OAAF7B,EAAE,CAAAgC,WAAA,CAY9DD,MAAA,CAAAE,iBAAA,CAAAP,MAAA,EAAAE,IAA2B,EAAC;IAAA,EAAC,wBAAAM,gEAAAR,MAAA;MAZ+B1B,EAAE,CAAA2B,aAAA,CAAAN,GAAA;MAAA,MAAAO,IAAA,GAAF5B,EAAE,CAAA6B,aAAA,GAAAC,KAAA;MAAA,MAAAK,MAAA,GAAFnC,EAAE,CAAA6B,aAAA;MAAA,OAAF7B,EAAE,CAAAgC,WAAA,CAa7DG,MAAA,CAAAC,kBAAA,CAAAV,MAAA,EAAAE,IAA4B,EAAC;IAAA,CADF,CAAC,uBAAAS,+DAAAX,MAAA;MAZ+B1B,EAAE,CAAA2B,aAAA,CAAAN,GAAA;MAAA,MAAAiB,OAAA,GAAFtC,EAAE,CAAA6B,aAAA;MAAA,OAAF7B,EAAE,CAAAgC,WAAA,CAc9DM,OAAA,CAAAC,iBAAA,CAAAb,MAAwB,EAAC;IAAA,CAFG,CAAC,sBAAAc,8DAAAd,MAAA;MAZ+B1B,EAAE,CAAA2B,aAAA,CAAAN,GAAA;MAAA,MAAAO,IAAA,GAAF5B,EAAE,CAAA6B,aAAA,GAAAC,KAAA;MAAA,MAAAW,OAAA,GAAFzC,EAAE,CAAA6B,aAAA;MAAA,OAAF7B,EAAE,CAAAgC,WAAA,CAe/DS,OAAA,CAAAC,gBAAA,CAAAhB,MAAA,EAAAE,IAA0B,EAAC;IAAA,CAHE,CAAC;IAZ+B5B,EAAE,CAAAuB,cAAA,YA4B3E,CAAC;IA5BwEvB,EAAE,CAAAwB,UAAA,mBAAAmB,2DAAAjB,MAAA;MAAF1B,EAAE,CAAA2B,aAAA,CAAAN,GAAA;MAAA,MAAAuB,OAAA,GAAF5C,EAAE,CAAA6B,aAAA;MAAA,OAAF7B,EAAE,CAAAgC,WAAA,CA0B9DY,OAAA,CAAAC,aAAA,CAAAnB,MAAoB,EAAC;IAAA,EAAC,qBAAAoB,6DAAApB,MAAA;MA1BsC1B,EAAE,CAAA2B,aAAA,CAAAN,GAAA;MAAA,MAAAO,IAAA,GAAF5B,EAAE,CAAA6B,aAAA,GAAAC,KAAA;MAAA,MAAAiB,OAAA,GAAF/C,EAAE,CAAA6B,aAAA;MAAA,OAAF7B,EAAE,CAAAgC,WAAA,CA2B5De,OAAA,CAAAC,eAAA,CAAAtB,MAAA,EAAAE,IAAyB,EAAC;IAAA,CADP,CAAC;IA1BsC5B,EAAE,CAAAiD,YAAA,CA4BrE,CAAC,CAAD,CAAC;EAAA;EAAA,IAAAhC,EAAA;IAAA,MAAAiC,MAAA,GA5BkElD,EAAE,CAAA6B,aAAA;IAAF7B,EAAE,CAAAmD,WAAA,gCAgBvC,CAAC,4BAAD,CAAC;IAhBoCnD,EAAE,CAAAoD,SAAA,EAsB/C,CAAC;IAtB4CpD,EAAE,CAAAqD,UAAA,YAAAH,MAAA,CAAAI,WAAA,EAsB/C,CAAC;IAtB4CtD,EAAE,CAAAmD,WAAA,qBAAAD,MAAA,CAAAK,MAuBxC,CAAC,kBAAAL,MAAA,CAAAM,QAAD,CAAC,kCAAD,CAAC;EAAA;AAAA;AAAA,SAAAC,gCAAAxC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvBqCjB,EAAE,CAAAuB,cAAA,YAIuF,CAAC;IAJ1FvB,EAAE,CAAA0D,UAAA,IAAA1C,8CAAA,yBAKrB,CAAC;IALkBhB,EAAE,CAAAiD,YAAA,CAM1E,CAAC;IANuEjD,EAAE,CAAA0D,UAAA,IAAAtC,qCAAA,gBA6B1E,CAAC;EAAA;EAAA,IAAAH,EAAA;IAAA,MAAA0C,QAAA,GAAAzC,GAAA,CAAA0C,SAAA;IAAA,MAAAhC,IAAA,GAAAV,GAAA,CAAAY,KAAA;IAAA,MAAA+B,MAAA,GA7BuE7D,EAAE,CAAA6B,aAAA;IAAF7B,EAAE,CAAA8D,UAAA,CAAAD,MAAA,CAAAE,eAIhB,CAAC;IAJa/D,EAAE,CAAAqD,UAAA,YAAAQ,MAAA,CAAAG,mBAAA,EAI1C,CAAC,YAAAH,MAAA,CAAAI,UAAD,CAAC;IAJuCjE,EAAE,CAAAmD,WAAA,2BAIsD,CAAC,0BAAD,CAAC;IAJzDnD,EAAE,CAAAoD,SAAA,EAKtC,CAAC;IALmCpD,EAAE,CAAAqD,UAAA,qBAAAM,QAKtC,CAAC;IALmC3D,EAAE,CAAAoD,SAAA,EAQ9C,CAAC;IAR2CpD,EAAE,CAAAqD,UAAA,SAAAzB,IAAA,KAAAiC,MAAA,CAAAK,MAAA,CAAAC,MAAA,IAQ9C,CAAC;EAAA;AAAA;AAhblD,MAAMC,QAAQ,CAAC;EACXC,QAAQ;EACRC,UAAU;EACVC,QAAQ;EACRC,EAAE;EACFC,EAAE;EACF;AACJ;AACA;AACA;EACIC,UAAU;EACV;AACJ;AACA;AACA;EACIX,eAAe;EACf;AACJ;AACA;AACA;EACIY,KAAK;EACL;AACJ;AACA;AACA;EACIV,UAAU;EACV;AACJ;AACA;AACA;EACIW,YAAY,GAAG,SAAS;EACxB;AACJ;AACA;AACA;EACIC,QAAQ,GAAG,IAAI;EACf;AACJ;AACA;AACA;EACItB,MAAM,GAAG,YAAY;EACrB;AACJ;AACA;AACA;EACIuB,UAAU,GAAG,CAAC;EACd;AACJ;AACA;AACA;EACIC,IAAI,GAAG,CAAC;EACR;AACJ;AACA;AACA;EACIC,QAAQ,GAAG,EAAE;EACb;AACJ;AACA;AACA;EACI,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACA,IAAID,UAAUA,CAACE,GAAG,EAAE;IAChB,IAAI,CAACD,WAAW,GAAGC,GAAG;IACtB,IAAI,IAAI,CAACV,EAAE,IAAI,IAAI,CAACA,EAAE,CAACW,aAAa,IAAI,IAAI,CAAClB,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;MAC5D,IAAIkB,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACZ,EAAE,CAACW,aAAa,CAACC,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ,CAAC,CAACC,MAAM,CAAEC,KAAK,IAAKzE,UAAU,CAAC0E,QAAQ,CAACD,KAAK,EAAE,kBAAkB,CAAC,CAAC;MAChI,IAAIL,WAAW,GAAG,EAAE;MACpB,IAAI,CAAChB,MAAM,CAACuB,GAAG,CAAC,CAACC,KAAK,EAAEC,CAAC,KAAK;QAC1B,IAAIC,gBAAgB,GAAG,IAAI,CAACX,UAAU,CAACd,MAAM,GAAG,CAAC,IAAIwB,CAAC,GAAG,IAAI,CAACV,UAAU,CAACU,CAAC,CAAC,GAAG,IAAI;QAClF,IAAIE,SAAS,GAAGD,gBAAgB,IAAI,GAAG,GAAG,IAAI,CAAC1B,MAAM,CAACC,MAAM;QAC5De,WAAW,CAACS,CAAC,CAAC,GAAGE,SAAS;QAC1BR,QAAQ,CAACM,CAAC,CAAC,CAAChB,KAAK,CAACmB,SAAS,GAAG,OAAO,GAAGD,SAAS,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC3B,MAAM,CAACC,MAAM,GAAG,CAAC,IAAI,IAAI,CAACW,UAAU,GAAG,KAAK;MACnH,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIiB,WAAW,GAAG,IAAI9F,YAAY,CAAC,CAAC;EAChC;AACJ;AACA;AACA;AACA;EACI+F,aAAa,GAAG,IAAI/F,YAAY,CAAC,CAAC;EAClCgG,SAAS;EACTC,kBAAkB;EAClBC,MAAM,GAAG,KAAK;EACdjC,MAAM,GAAG,EAAE;EACXkC,QAAQ,GAAG,KAAK;EAChBC,iBAAiB;EACjBC,eAAe;EACfC,iBAAiB;EACjBC,gBAAgB;EAChBC,IAAI;EACJC,aAAa;EACbC,QAAQ;EACRC,gBAAgB;EAChBC,gBAAgB;EAChBC,aAAa;EACbC,aAAa;EACb7B,WAAW,GAAG,EAAE;EAChB8B,cAAc;EACdC,KAAK;EACLzD,QAAQ;EACR0D,MAAM;EACNC,WAAWA,CAAC9C,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,EAAE,EAAEC,EAAE,EAAE;IAChD,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACyC,MAAM,GAAG,IAAI,CAAC7C,QAAQ,CAAC+C,WAAW;EAC3C;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAAClB,MAAM,GAAG,IAAI,CAACmB,QAAQ,CAAC,CAAC;EACjC;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACtB,SAAS,CAACuB,OAAO,CAAEC,IAAI,IAAK;MAC7B,QAAQA,IAAI,CAACC,OAAO,CAAC,CAAC;QAClB,KAAK,OAAO;UACR,IAAI,CAACxD,MAAM,CAACyD,IAAI,CAACF,IAAI,CAACG,QAAQ,CAAC;UAC/B;QACJ;UACI,IAAI,CAAC1D,MAAM,CAACyD,IAAI,CAACF,IAAI,CAACG,QAAQ,CAAC;UAC/B;MACR;IACJ,CAAC,CAAC;EACN;EACAC,eAAeA,CAAA,EAAG;IACd,IAAIhI,iBAAiB,CAAC,IAAI,CAACyE,UAAU,CAAC,EAAE;MACpC,IAAI,IAAI,CAACJ,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,MAAM,EAAE;QACnC,IAAI2D,WAAW,GAAG,KAAK;QACvB,IAAI,IAAI,CAACC,UAAU,CAAC,CAAC,EAAE;UACnBD,WAAW,GAAG,IAAI,CAACE,YAAY,CAAC,CAAC;QACrC;QACA,IAAI,CAACF,WAAW,EAAE;UACd,IAAIzC,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACZ,EAAE,CAACW,aAAa,CAACC,QAAQ,CAAC,CAAC,CAAC,CAACA,QAAQ,CAAC,CAACC,MAAM,CAAEC,KAAK,IAAKzE,UAAU,CAAC0E,QAAQ,CAACD,KAAK,EAAE,kBAAkB,CAAC,CAAC;UAChI,IAAIL,WAAW,GAAG,EAAE;UACpB,IAAI,CAAChB,MAAM,CAACuB,GAAG,CAAC,CAACC,KAAK,EAAEC,CAAC,KAAK;YAC1B,IAAIC,gBAAgB,GAAG,IAAI,CAACX,UAAU,CAACd,MAAM,GAAG,CAAC,IAAIwB,CAAC,GAAG,IAAI,CAACV,UAAU,CAACU,CAAC,CAAC,GAAG,IAAI;YAClF,IAAIE,SAAS,GAAGD,gBAAgB,IAAI,GAAG,GAAG,IAAI,CAAC1B,MAAM,CAACC,MAAM;YAC5De,WAAW,CAACS,CAAC,CAAC,GAAGE,SAAS;YAC1BR,QAAQ,CAACM,CAAC,CAAC,CAAChB,KAAK,CAACmB,SAAS,GAAG,OAAO,GAAGD,SAAS,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC3B,MAAM,CAACC,MAAM,GAAG,CAAC,IAAI,IAAI,CAACW,UAAU,GAAG,KAAK;UACnH,CAAC,CAAC;UACF,IAAI,CAACI,WAAW,GAAGA,WAAW;UAC9B,IAAI,CAAC1B,QAAQ,GAAGyE,UAAU,CAAC/C,WAAW,CAAC,CAAC,CAAC,CAAC,CAACgD,OAAO,CAAC,CAAC,CAAC;QACzD;MACJ;IACJ;EACJ;EACAC,WAAWA,CAACC,KAAK,EAAEtG,KAAK,EAAEuG,SAAS,EAAE;IACjC,IAAI,CAAC3B,aAAa,GAAG0B,KAAK,CAACE,aAAa,IAAIF,KAAK,CAACG,MAAM,CAACC,aAAa;IACtE,IAAI,CAAC/B,IAAI,GAAG,IAAI,CAACgC,UAAU,CAAC,CAAC,GAAG3H,UAAU,CAAC4H,QAAQ,CAAC,IAAI,CAACxC,kBAAkB,CAACd,aAAa,CAAC,GAAGtE,UAAU,CAAC6H,SAAS,CAAC,IAAI,CAACzC,kBAAkB,CAACd,aAAa,CAAC;IACxJ,IAAI,CAACiD,SAAS,EAAE;MACZ,IAAI,CAACjC,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACO,QAAQ,GAAG,IAAI,CAAC8B,UAAU,CAAC,CAAC,GAAIL,KAAK,YAAYQ,UAAU,GAAGR,KAAK,CAACS,KAAK,GAAGT,KAAK,CAACU,cAAc,CAAC,CAAC,CAAC,CAACD,KAAK,GAAIT,KAAK,YAAYQ,UAAU,GAAGR,KAAK,CAACW,KAAK,GAAGX,KAAK,CAACU,cAAc,CAAC,CAAC,CAAC,CAACC,KAAK;IAC/L;IACA,IAAI,CAACnC,gBAAgB,GAAG,IAAI,CAACF,aAAa,CAACsC,sBAAsB;IACjE,IAAI,CAACnC,gBAAgB,GAAG,IAAI,CAACH,aAAa,CAACuC,kBAAkB;IAC7D,IAAIZ,SAAS,EAAE;MACX,IAAI,CAACtB,aAAa,GAAG,IAAI,CAAC0B,UAAU,CAAC,CAAC,GAAG3H,UAAU,CAACoI,aAAa,CAAC,IAAI,CAACtC,gBAAgB,EAAE,IAAI,CAAC,GAAG9F,UAAU,CAACqI,cAAc,CAAC,IAAI,CAACvC,gBAAgB,EAAE,IAAI,CAAC;MACvJ,IAAI,CAACE,aAAa,GAAG,IAAI,CAAC2B,UAAU,CAAC,CAAC,GAAG3H,UAAU,CAACoI,aAAa,CAAC,IAAI,CAACrC,gBAAgB,EAAE,IAAI,CAAC,GAAG/F,UAAU,CAACqI,cAAc,CAAC,IAAI,CAACtC,gBAAgB,EAAE,IAAI,CAAC;IAC3J,CAAC,MACI;MACD,IAAI,CAACE,aAAa,GAAI,GAAG,IAAI,IAAI,CAAC0B,UAAU,CAAC,CAAC,GAAG3H,UAAU,CAACoI,aAAa,CAAC,IAAI,CAACtC,gBAAgB,EAAE,IAAI,CAAC,GAAG9F,UAAU,CAACqI,cAAc,CAAC,IAAI,CAACvC,gBAAgB,EAAE,IAAI,CAAC,CAAC,GAAI,IAAI,CAACH,IAAI;MAC7K,IAAI,CAACK,aAAa,GAAI,GAAG,IAAI,IAAI,CAAC2B,UAAU,CAAC,CAAC,GAAG3H,UAAU,CAACoI,aAAa,CAAC,IAAI,CAACrC,gBAAgB,EAAE,IAAI,CAAC,GAAG/F,UAAU,CAACqI,cAAc,CAAC,IAAI,CAACtC,gBAAgB,EAAE,IAAI,CAAC,CAAC,GAAI,IAAI,CAACJ,IAAI;IACjL;IACA,IAAI,CAACO,cAAc,GAAGlF,KAAK;IAC3BhB,UAAU,CAACsI,QAAQ,CAAC,IAAI,CAAC1C,aAAa,EAAE,4BAA4B,CAAC;IACrE,IAAI,CAACA,aAAa,CAAC2C,YAAY,CAAC,wBAAwB,EAAE,MAAM,CAAC;IACjEvI,UAAU,CAACsI,QAAQ,CAAC,IAAI,CAAClD,kBAAkB,CAACd,aAAa,EAAE,qBAAqB,CAAC;IACjF,IAAI,CAACc,kBAAkB,CAACd,aAAa,CAACiE,YAAY,CAAC,iBAAiB,EAAE,MAAM,CAAC;IAC7E,IAAI,CAACrD,aAAa,CAACsD,IAAI,CAAC;MAAEC,aAAa,EAAEnB,KAAK;MAAEoB,KAAK,EAAE,IAAI,CAACtE;IAAY,CAAC,CAAC;EAC9E;EACAuE,QAAQA,CAACrB,KAAK,EAAErD,IAAI,EAAEsD,SAAS,EAAE;IAC7B,IAAIqB,MAAM,EAAEC,gBAAgB,EAAEC,gBAAgB;IAC9C,IAAIvB,SAAS,EAAE;MACX,IAAI,IAAI,CAACI,UAAU,CAAC,CAAC,EAAE;QACnBkB,gBAAgB,GAAI,GAAG,IAAI,IAAI,CAAC5C,aAAa,GAAGhC,IAAI,CAAC,GAAI,IAAI,CAAC0B,IAAI;QAClEmD,gBAAgB,GAAI,GAAG,IAAI,IAAI,CAAC9C,aAAa,GAAG/B,IAAI,CAAC,GAAI,IAAI,CAAC0B,IAAI;MACtE,CAAC,MACI;QACDkD,gBAAgB,GAAI,GAAG,IAAI,IAAI,CAAC5C,aAAa,GAAGhC,IAAI,CAAC,GAAI,IAAI,CAAC0B,IAAI;QAClEmD,gBAAgB,GAAI,GAAG,IAAI,IAAI,CAAC9C,aAAa,GAAG/B,IAAI,CAAC,GAAI,IAAI,CAAC0B,IAAI;MACtE;IACJ,CAAC,MACI;MACD,IAAI,IAAI,CAACgC,UAAU,CAAC,CAAC,EACjBiB,MAAM,GAAItB,KAAK,CAACS,KAAK,GAAG,GAAG,GAAI,IAAI,CAACpC,IAAI,GAAI,IAAI,CAACE,QAAQ,GAAG,GAAG,GAAI,IAAI,CAACF,IAAI,CAAC,KAE7EiD,MAAM,GAAItB,KAAK,CAACW,KAAK,GAAG,GAAG,GAAI,IAAI,CAACtC,IAAI,GAAI,IAAI,CAACE,QAAQ,GAAG,GAAG,GAAI,IAAI,CAACF,IAAI;MAChFkD,gBAAgB,GAAG,IAAI,CAAC5C,aAAa,GAAG2C,MAAM;MAC9CE,gBAAgB,GAAG,IAAI,CAAC9C,aAAa,GAAG4C,MAAM;IAClD;IACA,IAAI,CAAClG,QAAQ,GAAGyE,UAAU,CAAC0B,gBAAgB,CAAC,CAACzB,OAAO,CAAC,CAAC,CAAC;IACvD,IAAI,IAAI,CAAC2B,cAAc,CAACF,gBAAgB,EAAEC,gBAAgB,CAAC,EAAE;MACzD,IAAI,CAAChD,gBAAgB,CAACjC,KAAK,CAACmB,SAAS,GAAG,OAAO,GAAG6D,gBAAgB,GAAG,MAAM,GAAG,CAAC,IAAI,CAACzF,MAAM,CAACC,MAAM,GAAG,CAAC,IAAI,IAAI,CAACW,UAAU,GAAG,KAAK;MAChI,IAAI,CAAC+B,gBAAgB,CAAClC,KAAK,CAACmB,SAAS,GAAG,OAAO,GAAG8D,gBAAgB,GAAG,MAAM,GAAG,CAAC,IAAI,CAAC1F,MAAM,CAACC,MAAM,GAAG,CAAC,IAAI,IAAI,CAACW,UAAU,GAAG,KAAK;MAChI,IAAI,CAACI,WAAW,CAAC,IAAI,CAAC8B,cAAc,CAAC,GAAG2C,gBAAgB;MACxD,IAAI,CAACzE,WAAW,CAAC,IAAI,CAAC8B,cAAc,GAAG,CAAC,CAAC,GAAG4C,gBAAgB;IAChE;EACJ;EACAE,SAASA,CAAC1B,KAAK,EAAE;IACb,IAAI,IAAI,CAACL,UAAU,CAAC,CAAC,EAAE;MACnB,IAAI,CAACgC,SAAS,CAAC,CAAC;IACpB;IACA,IAAI,CAAChE,WAAW,CAACuD,IAAI,CAAC;MAAEC,aAAa,EAAEnB,KAAK;MAAEoB,KAAK,EAAE,IAAI,CAACtE;IAAY,CAAC,CAAC;IACxEpE,UAAU,CAACkJ,WAAW,CAAC,IAAI,CAACtD,aAAa,EAAE,4BAA4B,CAAC;IACxE5F,UAAU,CAACkJ,WAAW,CAAC,IAAI,CAAC9D,kBAAkB,CAACd,aAAa,EAAE,qBAAqB,CAAC;IACpF,IAAI,CAAC6E,KAAK,CAAC,CAAC;EAChB;EACAhI,iBAAiBA,CAACmG,KAAK,EAAEtG,KAAK,EAAE;IAC5B,IAAI,CAACqG,WAAW,CAACC,KAAK,EAAEtG,KAAK,CAAC;IAC9B,IAAI,CAACoI,kBAAkB,CAAC,CAAC;EAC7B;EACA9H,kBAAkBA,CAACgG,KAAK,EAAEtG,KAAK,EAAE;IAC7B,IAAIsG,KAAK,CAAC+B,UAAU,EAAE;MAClB,IAAI,CAAChC,WAAW,CAACC,KAAK,EAAEtG,KAAK,CAAC;MAC9B,IAAI,CAACsI,kBAAkB,CAAC,CAAC;MACzBhC,KAAK,CAACiC,cAAc,CAAC,CAAC;IAC1B;EACJ;EACA9H,iBAAiBA,CAAC6F,KAAK,EAAE;IACrB,IAAI,CAACqB,QAAQ,CAACrB,KAAK,CAAC;IACpBA,KAAK,CAACiC,cAAc,CAAC,CAAC;EAC1B;EACA3H,gBAAgBA,CAAC0F,KAAK,EAAE;IACpB,IAAI,CAAC0B,SAAS,CAAC1B,KAAK,CAAC;IACrB,IAAI,CAACkC,oBAAoB,CAAC,CAAC;IAC3B,IAAIlC,KAAK,CAAC+B,UAAU,EAChB/B,KAAK,CAACiC,cAAc,CAAC,CAAC;EAC9B;EACAE,MAAMA,CAACnC,KAAK,EAAEtG,KAAK,EAAEiD,IAAI,EAAE;IACvB,IAAI,CAACoD,WAAW,CAACC,KAAK,EAAEtG,KAAK,EAAE,IAAI,CAAC;IACpC,IAAI,CAAC2H,QAAQ,CAACrB,KAAK,EAAErD,IAAI,EAAE,IAAI,CAAC;EACpC;EACAyF,QAAQA,CAACpC,KAAK,EAAEtG,KAAK,EAAEiD,IAAI,EAAE;IACzB,IAAI,CAAC0F,UAAU,CAAC,CAAC;IACjB,IAAI,CAACxD,KAAK,GAAGyD,UAAU,CAAC,MAAM;MAC1B,IAAI,CAACH,MAAM,CAACnC,KAAK,EAAEtG,KAAK,EAAEiD,IAAI,CAAC;IACnC,CAAC,EAAE,EAAE,CAAC;EACV;EACA0F,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACxD,KAAK,EAAE;MACZ0D,YAAY,CAAC,IAAI,CAAC1D,KAAK,CAAC;IAC5B;EACJ;EACApE,aAAaA,CAACuF,KAAK,EAAE;IACjB,IAAI,CAACqC,UAAU,CAAC,CAAC;IACjB,IAAI,CAACX,SAAS,CAAC1B,KAAK,CAAC;EACzB;EACApF,eAAeA,CAACoF,KAAK,EAAEtG,KAAK,EAAE;IAC1B,QAAQsG,KAAK,CAACwC,IAAI;MACd,KAAK,WAAW;QAAE;UACd,IAAI,IAAI,CAACrH,MAAM,KAAK,YAAY,EAAE;YAC9B,IAAI,CAACiH,QAAQ,CAACpC,KAAK,EAAEtG,KAAK,EAAE,IAAI,CAACiD,IAAI,GAAG,CAAC,CAAC,CAAC;UAC/C;UACAqD,KAAK,CAACiC,cAAc,CAAC,CAAC;UACtB;QACJ;MACA,KAAK,YAAY;QAAE;UACf,IAAI,IAAI,CAAC9G,MAAM,KAAK,YAAY,EAAE;YAC9B,IAAI,CAACiH,QAAQ,CAACpC,KAAK,EAAEtG,KAAK,EAAE,IAAI,CAACiD,IAAI,CAAC;UAC1C;UACAqD,KAAK,CAACiC,cAAc,CAAC,CAAC;UACtB;QACJ;MACA,KAAK,WAAW;QAAE;UACd,IAAI,IAAI,CAAC9G,MAAM,KAAK,UAAU,EAAE;YAC5B,IAAI,CAACiH,QAAQ,CAACpC,KAAK,EAAEtG,KAAK,EAAE,IAAI,CAACiD,IAAI,GAAG,CAAC,CAAC,CAAC;UAC/C;UACAqD,KAAK,CAACiC,cAAc,CAAC,CAAC;UACtB;QACJ;MACA,KAAK,SAAS;QAAE;UACZ,IAAI,IAAI,CAAC9G,MAAM,KAAK,UAAU,EAAE;YAC5B,IAAI,CAACiH,QAAQ,CAACpC,KAAK,EAAEtG,KAAK,EAAE,IAAI,CAACiD,IAAI,CAAC;UAC1C;UACAqD,KAAK,CAACiC,cAAc,CAAC,CAAC;UACtB;QACJ;MACA;QACI;QACA;IACR;EACJ;EACAR,cAAcA,CAACF,gBAAgB,EAAEC,gBAAgB,EAAE;IAC/C,IAAI,IAAI,CAAC5E,QAAQ,CAACb,MAAM,IAAI,CAAC,IAAI,IAAI,CAACa,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI,CAACA,QAAQ,CAAC,CAAC,CAAC,GAAG2E,gBAAgB,EAAE;MACtF,OAAO,KAAK;IAChB;IACA,IAAI,IAAI,CAAC3E,QAAQ,CAACb,MAAM,GAAG,CAAC,IAAI,IAAI,CAACa,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI,CAACA,QAAQ,CAAC,CAAC,CAAC,GAAG4E,gBAAgB,EAAE;MACrF,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf;EACAM,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAAC7D,iBAAiB,EAAE;MACzB,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAAC9B,QAAQ,CAACsG,MAAM,CAAC,IAAI,CAACxG,QAAQ,EAAE,WAAW,EAAG+D,KAAK,IAAK;QACjF,IAAI,CAACqB,QAAQ,CAACrB,KAAK,CAAC;MACxB,CAAC,CAAC;IACN;IACA,IAAI,CAAC,IAAI,CAAC9B,eAAe,EAAE;MACvB,IAAI,CAACA,eAAe,GAAG,IAAI,CAAC/B,QAAQ,CAACsG,MAAM,CAAC,IAAI,CAACxG,QAAQ,EAAE,SAAS,EAAG+D,KAAK,IAAK;QAC7E,IAAI,CAAC0B,SAAS,CAAC1B,KAAK,CAAC;QACrB,IAAI,CAAC0C,oBAAoB,CAAC,CAAC;MAC/B,CAAC,CAAC;IACN;EACJ;EACAV,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAAC7D,iBAAiB,EAAE;MACzB,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAAChC,QAAQ,CAACsG,MAAM,CAAC,IAAI,CAACxG,QAAQ,EAAE,WAAW,EAAG+D,KAAK,IAAK;QACjF,IAAI,CAACqB,QAAQ,CAACrB,KAAK,CAACU,cAAc,CAAC,CAAC,CAAC,CAAC;MAC1C,CAAC,CAAC;IACN;IACA,IAAI,CAAC,IAAI,CAACtC,gBAAgB,EAAE;MACxB,IAAI,CAACA,gBAAgB,GAAG,IAAI,CAACjC,QAAQ,CAACsG,MAAM,CAAC,IAAI,CAACxG,QAAQ,EAAE,UAAU,EAAG+D,KAAK,IAAK;QAC/E,IAAI,CAAC0B,SAAS,CAAC1B,KAAK,CAAC;QACrB,IAAI,CAACkC,oBAAoB,CAAC,CAAC;MAC/B,CAAC,CAAC;IACN;EACJ;EACAQ,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACzE,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACA,iBAAiB,GAAG,IAAI;IACjC;IACA,IAAI,IAAI,CAACC,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAAC,CAAC;MACtB,IAAI,CAACA,eAAe,GAAG,IAAI;IAC/B;EACJ;EACAgE,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAC/D,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACA,iBAAiB,GAAG,IAAI;IACjC;IACA,IAAI,IAAI,CAACC,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACA,gBAAgB,GAAG,IAAI;IAChC;EACJ;EACAyD,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC7D,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACK,IAAI,GAAG,IAAI;IAChB,IAAI,CAACE,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACE,aAAa,GAAG,IAAI;IACzB,IAAI,CAACD,aAAa,GAAG,IAAI;IACzB,IAAI,CAACJ,aAAa,GAAG,IAAI;IACzB,IAAI,CAACM,cAAc,GAAG,IAAI;EAC9B;EACAM,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAAC7C,EAAE,CAACW,aAAa,EAAE;MACvB,IAAI2F,MAAM,GAAG,IAAI,CAACtG,EAAE,CAACW,aAAa,CAACoD,aAAa;MAChD,OAAOuC,MAAM,IAAI,CAACjK,UAAU,CAAC0E,QAAQ,CAACuF,MAAM,EAAE,YAAY,CAAC,EAAE;QACzDA,MAAM,GAAGA,MAAM,CAACvC,aAAa;MACjC;MACA,OAAOuC,MAAM,KAAK,IAAI;IAC1B,CAAC,MACI;MACD,OAAO,KAAK;IAChB;EACJ;EACAhD,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAClD,QAAQ,IAAI,IAAI;EAChC;EACAmG,UAAUA,CAAA,EAAG;IACT,IAAInL,iBAAiB,CAAC,IAAI,CAACyE,UAAU,CAAC,EAAE;MACpC,QAAQ,IAAI,CAACM,YAAY;QACrB,KAAK,OAAO;UACR,OAAO,IAAI,CAACsC,MAAM,CAAC+D,YAAY;QACnC,KAAK,SAAS;UACV,OAAO,IAAI,CAAC/D,MAAM,CAACgE,cAAc;QACrC;UACI,MAAM,IAAIC,KAAK,CAAC,IAAI,CAACvG,YAAY,GAAG,0FAA0F,CAAC;MACvI;IACJ,CAAC,MACI;MACD,MAAM,IAAIuG,KAAK,CAAC,sDAAsD,CAAC;IAC3E;EACJ;EACApB,SAASA,CAAA,EAAG;IACR,IAAI,CAACiB,UAAU,CAAC,CAAC,CAACI,OAAO,CAAC,IAAI,CAACvG,QAAQ,EAAEwG,IAAI,CAACC,SAAS,CAAC,IAAI,CAACpG,WAAW,CAAC,CAAC;EAC9E;EACA8C,YAAYA,CAAA,EAAG;IACX,MAAMuD,OAAO,GAAG,IAAI,CAACP,UAAU,CAAC,CAAC;IACjC,MAAMQ,WAAW,GAAGD,OAAO,CAACE,OAAO,CAAC,IAAI,CAAC5G,QAAQ,CAAC;IAClD,IAAI2G,WAAW,EAAE;MACb,IAAI,CAACtG,WAAW,GAAGmG,IAAI,CAACK,KAAK,CAACF,WAAW,CAAC;MAC1C,IAAInG,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACa,kBAAkB,CAACd,aAAa,CAACC,QAAQ,CAAC,CAACC,MAAM,CAAEC,KAAK,IAAKzE,UAAU,CAAC0E,QAAQ,CAACD,KAAK,EAAE,kBAAkB,CAAC,CAAC;MACpIF,QAAQ,CAACmC,OAAO,CAAC,CAACjC,KAAK,EAAEI,CAAC,KAAK;QAC3BJ,KAAK,CAACZ,KAAK,CAACmB,SAAS,GAAG,OAAO,GAAG,IAAI,CAACZ,WAAW,CAACS,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,IAAI,CAACzB,MAAM,CAACC,MAAM,GAAG,CAAC,IAAI,IAAI,CAACW,UAAU,GAAG,KAAK;MACvH,CAAC,CAAC;MACF,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACA6G,cAAcA,CAAA,EAAG;IACb,OAAO;MACH,wBAAwB,EAAE,IAAI;MAC9B,uBAAuB,EAAE,IAAI,CAACpI,MAAM,KAAK,YAAY;MACrD,qBAAqB,EAAE,IAAI,CAACA,MAAM,KAAK;IAC3C,CAAC;EACL;EACAS,mBAAmBA,CAAA,EAAG;IAClB,OAAO;MACH,kBAAkB,EAAE,IAAI;MACxB,yBAAyB,EAAE;IAC/B,CAAC;EACL;EACAV,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACmF,UAAU,CAAC,CAAC,EACjB,OAAO;MAAEmD,KAAK,EAAE,IAAI,CAAC9G,UAAU,GAAG;IAAK,CAAC,CAAC,KAEzC,OAAO;MAAE+G,MAAM,EAAE,IAAI,CAAC/G,UAAU,GAAG;IAAK,CAAC;EACjD;EACA2D,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAClF,MAAM,KAAK,YAAY;EACvC;EACA,OAAOuI,IAAI,YAAAC,iBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwF5H,QAAQ,EAAlBpE,EAAE,CAAAiM,iBAAA,CAAkCnM,QAAQ,GAA5CE,EAAE,CAAAiM,iBAAA,CAAuD/L,WAAW,GAApEF,EAAE,CAAAiM,iBAAA,CAA+EjM,EAAE,CAACkM,SAAS,GAA7FlM,EAAE,CAAAiM,iBAAA,CAAwGjM,EAAE,CAACmM,iBAAiB,GAA9HnM,EAAE,CAAAiM,iBAAA,CAAyIjM,EAAE,CAACoM,UAAU;EAAA;EACjP,OAAOC,IAAI,kBAD8ErM,EAAE,CAAAsM,iBAAA;IAAAC,IAAA,EACJnI,QAAQ;IAAAoI,SAAA;IAAAC,cAAA,WAAAC,wBAAAzL,EAAA,EAAAC,GAAA,EAAAyL,QAAA;MAAA,IAAA1L,EAAA;QADNjB,EAAE,CAAA4M,cAAA,CAAAD,QAAA,EAC6gB/L,aAAa;MAAA;MAAA,IAAAK,EAAA;QAAA,IAAA4L,EAAA;QAD5hB7M,EAAE,CAAA8M,cAAA,CAAAD,EAAA,GAAF7M,EAAE,CAAA+M,WAAA,QAAA7L,GAAA,CAAA+E,SAAA,GAAA4G,EAAA;MAAA;IAAA;IAAAG,SAAA,WAAAC,eAAAhM,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFjB,EAAE,CAAAkN,WAAA,CAAAnM,GAAA;MAAA;MAAA,IAAAE,EAAA;QAAA,IAAA4L,EAAA;QAAF7M,EAAE,CAAA8M,cAAA,CAAAD,EAAA,GAAF7M,EAAE,CAAA+M,WAAA,QAAA7L,GAAA,CAAAgF,kBAAA,GAAA2G,EAAA,CAAAM,KAAA;MAAA;IAAA;IAAAC,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,sBAAAtM,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFjB,EAAE,CAAAwN,WAAA,4BAAAtM,GAAA,CAAAiF,MAAA;MAAA;IAAA;IAAAsH,MAAA;MAAA/I,UAAA;MAAAX,eAAA;MAAAY,KAAA;MAAAV,UAAA;MAAAW,YAAA;MAAAC,QAAA;MAAAtB,MAAA;MAAAuB,UAAA;MAAAC,IAAA;MAAAC,QAAA;MAAAC,UAAA;IAAA;IAAAyI,OAAA;MAAA3H,WAAA;MAAAC,aAAA;IAAA;IAAA2H,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAjG,QAAA,WAAAkG,kBAAA7M,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFjB,EAAE,CAAAuB,cAAA,eAEmG,CAAC;QAFtGvB,EAAE,CAAA0D,UAAA,IAAAD,+BAAA,wBA8BtE,CAAC;QA9BmEzD,EAAE,CAAAiD,YAAA,CA+BlF,CAAC;MAAA;MAAA,IAAAhC,EAAA;QA/B+EjB,EAAE,CAAA8D,UAAA,CAAA5C,GAAA,CAAAwD,UAEvB,CAAC;QAFoB1E,EAAE,CAAAqD,UAAA,YAAAnC,GAAA,CAAAyK,cAAA,EAE5C,CAAC,YAAAzK,GAAA,CAAAyD,KAAD,CAAC;QAFyC3E,EAAE,CAAAmD,WAAA,2BAE4B,CAAC,gCAAD,CAAC,0BAAD,CAAC;QAF/BnD,EAAE,CAAAoD,SAAA,EAGrC,CAAC;QAHkCpD,EAAE,CAAAqD,UAAA,YAAAnC,GAAA,CAAAgD,MAGrC,CAAC;MAAA;IAAA;IAAA6J,YAAA,GA6Bw5BnO,EAAE,CAACoO,OAAO,EAAoFpO,EAAE,CAACqO,OAAO,EAAmHrO,EAAE,CAACsO,IAAI,EAA6FtO,EAAE,CAACuO,gBAAgB,EAAoJvO,EAAE,CAACwO,OAAO;IAAAC,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AACn8C;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAlC6FxO,EAAE,CAAAyO,iBAAA,CAkCJrK,QAAQ,EAAc,CAAC;IACtGmI,IAAI,EAAEpM,SAAS;IACfuO,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAY;MAAE/G,QAAQ,EAAG;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MAAE0G,aAAa,EAAElO,iBAAiB,CAACwO,IAAI;MAAEL,eAAe,EAAElO,uBAAuB,CAACwO,MAAM;MAAEC,IAAI,EAAE;QAC7EC,KAAK,EAAE,WAAW;QAClB,iCAAiC,EAAE;MACvC,CAAC;MAAEV,MAAM,EAAE,CAAC,s4BAAs4B;IAAE,CAAC;EACj6B,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE9B,IAAI,EAAEyC,QAAQ;IAAEC,UAAU,EAAE,CAAC;MAC9C1C,IAAI,EAAEjM,MAAM;MACZoO,IAAI,EAAE,CAAC5O,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEyM,IAAI,EAAE2C,SAAS;IAAED,UAAU,EAAE,CAAC;MAClC1C,IAAI,EAAEjM,MAAM;MACZoO,IAAI,EAAE,CAACxO,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAEqM,IAAI,EAAEvM,EAAE,CAACkM;EAAU,CAAC,EAAE;IAAEK,IAAI,EAAEvM,EAAE,CAACmM;EAAkB,CAAC,EAAE;IAAEI,IAAI,EAAEvM,EAAE,CAACoM;EAAW,CAAC,CAAC,EAAkB;IAAE1H,UAAU,EAAE,CAAC;MACvH6H,IAAI,EAAEhM;IACV,CAAC,CAAC;IAAEwD,eAAe,EAAE,CAAC;MAClBwI,IAAI,EAAEhM;IACV,CAAC,CAAC;IAAEoE,KAAK,EAAE,CAAC;MACR4H,IAAI,EAAEhM;IACV,CAAC,CAAC;IAAE0D,UAAU,EAAE,CAAC;MACbsI,IAAI,EAAEhM;IACV,CAAC,CAAC;IAAEqE,YAAY,EAAE,CAAC;MACf2H,IAAI,EAAEhM;IACV,CAAC,CAAC;IAAEsE,QAAQ,EAAE,CAAC;MACX0H,IAAI,EAAEhM;IACV,CAAC,CAAC;IAAEgD,MAAM,EAAE,CAAC;MACTgJ,IAAI,EAAEhM;IACV,CAAC,CAAC;IAAEuE,UAAU,EAAE,CAAC;MACbyH,IAAI,EAAEhM;IACV,CAAC,CAAC;IAAEwE,IAAI,EAAE,CAAC;MACPwH,IAAI,EAAEhM;IACV,CAAC,CAAC;IAAEyE,QAAQ,EAAE,CAAC;MACXuH,IAAI,EAAEhM;IACV,CAAC,CAAC;IAAE0E,UAAU,EAAE,CAAC;MACbsH,IAAI,EAAEhM;IACV,CAAC,CAAC;IAAEwF,WAAW,EAAE,CAAC;MACdwG,IAAI,EAAE/L;IACV,CAAC,CAAC;IAAEwF,aAAa,EAAE,CAAC;MAChBuG,IAAI,EAAE/L;IACV,CAAC,CAAC;IAAEyF,SAAS,EAAE,CAAC;MACZsG,IAAI,EAAE9L,eAAe;MACrBiO,IAAI,EAAE,CAAC9N,aAAa;IACxB,CAAC,CAAC;IAAEsF,kBAAkB,EAAE,CAAC;MACrBqG,IAAI,EAAE7L,SAAS;MACfgO,IAAI,EAAE,CAAC,WAAW,EAAE;QAAES,MAAM,EAAE;MAAM,CAAC;IACzC,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMC,cAAc,CAAC;EACjB,OAAOtD,IAAI,YAAAuD,uBAAArD,CAAA;IAAA,YAAAA,CAAA,IAAwFoD,cAAc;EAAA;EACjH,OAAOE,IAAI,kBAhH8EtP,EAAE,CAAAuP,gBAAA;IAAAhD,IAAA,EAgHS6C;EAAc;EAClH,OAAOI,IAAI,kBAjH8ExP,EAAE,CAAAyP,gBAAA;IAAAC,OAAA,GAiHmC3P,YAAY,EAAEc,YAAY;EAAA;AAC5J;AACA;EAAA,QAAA2N,SAAA,oBAAAA,SAAA,KAnH6FxO,EAAE,CAAAyO,iBAAA,CAmHJW,cAAc,EAAc,CAAC;IAC5G7C,IAAI,EAAE5L,QAAQ;IACd+N,IAAI,EAAE,CAAC;MACCgB,OAAO,EAAE,CAAC3P,YAAY,CAAC;MACvB4P,OAAO,EAAE,CAACvL,QAAQ,EAAEvD,YAAY,CAAC;MACjC+O,YAAY,EAAE,CAACxL,QAAQ;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,QAAQ,EAAEgL,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}