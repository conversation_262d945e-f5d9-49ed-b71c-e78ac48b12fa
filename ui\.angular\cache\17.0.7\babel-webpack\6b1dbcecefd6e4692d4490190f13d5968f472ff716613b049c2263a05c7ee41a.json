{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let ErrorService = /*#__PURE__*/(() => {\n  class ErrorService {\n    constructor() {\n      this.errorsSubject = new BehaviorSubject([]);\n      this.errors$ = this.errorsSubject.asObservable();\n    }\n    addError(error) {\n      const currentErrors = this.errorsSubject.value;\n      this.errorsSubject.next([...currentErrors, error]);\n    }\n    clearErrors() {\n      this.errorsSubject.next([]);\n    }\n    removeError(errorToRemove) {\n      const currentErrors = this.errorsSubject.value;\n      this.errorsSubject.next(currentErrors.filter(error => {\n        // Compare by reference first, then by string representation\n        if (error === errorToRemove) {\n          return false;\n        }\n        // If both are objects, compare their string representations\n        if (typeof error === 'object' && typeof errorToRemove === 'object') {\n          return JSON.stringify(error) !== JSON.stringify(errorToRemove);\n        }\n        // Convert both to strings and compare\n        const errorStr = typeof error === 'string' ? error : JSON.stringify(error);\n        const removeStr = typeof errorToRemove === 'string' ? errorToRemove : JSON.stringify(errorToRemove);\n        return errorStr !== removeStr;\n      }));\n    }\n    // Helper method for backward compatibility\n    addErrorMessage(message) {\n      this.addError(message);\n    }\n    static #_ = this.ɵfac = function ErrorService_Factory(t) {\n      return new (t || ErrorService)();\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ErrorService,\n      factory: ErrorService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return ErrorService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}