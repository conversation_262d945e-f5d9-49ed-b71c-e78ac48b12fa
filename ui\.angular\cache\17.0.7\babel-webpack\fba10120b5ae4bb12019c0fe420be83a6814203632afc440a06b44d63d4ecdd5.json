{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { StyleClassModule } from 'primeng/styleclass';\nimport { DividerModule } from 'primeng/divider';\nimport { ChartModule } from 'primeng/chart';\nimport { PanelModule } from 'primeng/panel';\nimport { ButtonModule } from 'primeng/button';\nimport { ProvidersRoutingModule } from './providers-routing.module';\nimport { ProvidersComponent } from './providers.component';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport * as i0 from \"@angular/core\";\nexport class ProvidersModule {\n  static #_ = this.ɵfac = function ProvidersModule_Factory(t) {\n    return new (t || ProvidersModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ProvidersModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, ProvidersRoutingModule, DividerModule, StyleClassModule, ChartModule, PanelModule, ButtonModule, ReactiveFormsModule, BreadcrumbModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ProvidersModule, {\n    declarations: [ProvidersComponent],\n    imports: [CommonModule, ProvidersRoutingModule, DividerModule, StyleClassModule, ChartModule, PanelModule, ButtonModule, ReactiveFormsModule, BreadcrumbModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "StyleClassModule", "DividerModule", "ChartModule", "PanelModule", "ButtonModule", "ProvidersRoutingModule", "ProvidersComponent", "ReactiveFormsModule", "BreadcrumbModule", "ProvidersModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\providers\\providers.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { StyleClassModule } from 'primeng/styleclass';\r\nimport { DividerModule } from 'primeng/divider';\r\nimport { ChartModule } from 'primeng/chart';\r\nimport { PanelModule } from 'primeng/panel';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { ProvidersRoutingModule } from './providers-routing.module';\r\nimport { ProvidersComponent } from './providers.component';\r\nimport { ReactiveFormsModule } from '@angular/forms';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\n\r\n@NgModule({\r\n    imports: [\r\n        CommonModule,\r\n        ProvidersRoutingModule,\r\n        DividerModule,\r\n        StyleClassModule,\r\n        ChartModule,\r\n        PanelModule,\r\n        ButtonModule,\r\n        ReactiveFormsModule,\r\n        BreadcrumbModule\r\n    ],\r\n    declarations: [ProvidersComponent]\r\n})\r\nexport class ProvidersModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,gBAAgB,QAAQ,oBAAoB;;AAgBrD,OAAM,MAAOC,eAAe;EAAA,QAAAC,CAAA,G;qBAAfD,eAAe;EAAA;EAAA,QAAAE,EAAA,G;UAAfF;EAAe;EAAA,QAAAG,EAAA,G;cAZpBb,YAAY,EACZM,sBAAsB,EACtBJ,aAAa,EACbD,gBAAgB,EAChBE,WAAW,EACXC,WAAW,EACXC,YAAY,EACZG,mBAAmB,EACnBC,gBAAgB;EAAA;;;2EAIXC,eAAe;IAAAI,YAAA,GAFTP,kBAAkB;IAAAQ,OAAA,GAV7Bf,YAAY,EACZM,sBAAsB,EACtBJ,aAAa,EACbD,gBAAgB,EAChBE,WAAW,EACXC,WAAW,EACXC,YAAY,EACZG,mBAAmB,EACnBC,gBAAgB;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}