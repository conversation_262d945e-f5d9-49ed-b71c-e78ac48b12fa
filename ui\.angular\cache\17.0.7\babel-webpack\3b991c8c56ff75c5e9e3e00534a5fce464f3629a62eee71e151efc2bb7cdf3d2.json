{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class DateUtilsService {\n  constructor() {\n    this.ATHENS_TIMEZONE = 'Europe/Athens';\n  }\n  /**\n   * Converts any date to Athens timezone and formats it for display\n   */\n  toAthensTime(date) {\n    const inputDate = typeof date === 'string' ? new Date(date) : date;\n    // Create a new date in Athens timezone\n    const athensDate = new Date(inputDate.toLocaleString('en-US', {\n      timeZone: this.ATHENS_TIMEZONE\n    }));\n    return athensDate;\n  }\n  /**\n   * Formats date for chart labels (time only)\n   */\n  formatTimeForChart(date) {\n    const athensDate = this.toAthensTime(date);\n    return athensDate.toLocaleTimeString('el-GR', {\n      hour: '2-digit',\n      minute: '2-digit',\n      timeZone: this.ATHENS_TIMEZONE\n    });\n  }\n  /**\n   * Formats date for chart tooltips (full date and time)\n   */\n  formatDateTimeForTooltip(date) {\n    const athensDate = this.toAthensTime(date);\n    return athensDate.toLocaleString('el-GR', {\n      year: 'numeric',\n      month: '2-digit',\n      day: '2-digit',\n      hour: '2-digit',\n      minute: '2-digit',\n      timeZone: this.ATHENS_TIMEZONE\n    });\n  }\n  /**\n   * Formats date for table display\n   */\n  formatDateForTable(date) {\n    const athensDate = this.toAthensTime(date);\n    return athensDate.toLocaleDateString('el-GR', {\n      timeZone: this.ATHENS_TIMEZONE\n    });\n  }\n  /**\n   * Formats time for table display\n   */\n  formatTimeForTable(date) {\n    const athensDate = this.toAthensTime(date);\n    return athensDate.toLocaleTimeString('el-GR', {\n      hour: '2-digit',\n      minute: '2-digit',\n      timeZone: this.ATHENS_TIMEZONE\n    });\n  }\n  /**\n   * Converts local date to UTC for API calls\n   */\n  toUtcForApi(date) {\n    return date.toISOString();\n  }\n  /**\n   * Creates a date in Athens timezone for date pickers\n   */\n  createAthensDate(year, month, day, hour = 0, minute = 0) {\n    // Create date string in Athens timezone format\n    const dateString = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}T${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:00`;\n    // Parse as local time and then convert to Athens\n    const localDate = new Date(dateString);\n    return this.toAthensTime(localDate);\n  }\n  static #_ = this.ɵfac = function DateUtilsService_Factory(t) {\n    return new (t || DateUtilsService)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: DateUtilsService,\n    factory: DateUtilsService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["DateUtilsService", "constructor", "ATHENS_TIMEZONE", "toAthensTime", "date", "inputDate", "Date", "athensDate", "toLocaleString", "timeZone", "formatTimeForChart", "toLocaleTimeString", "hour", "minute", "formatDateTimeForTooltip", "year", "month", "day", "formatDateForTable", "toLocaleDateString", "formatTimeForTable", "toUtcForApi", "toISOString", "createAthensDate", "dateString", "toString", "padStart", "localDate", "_", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\service\\date-utils.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class DateUtilsService {\n  private readonly ATHENS_TIMEZONE = 'Europe/Athens';\n\n  constructor() { }\n\n  /**\n   * Converts any date to Athens timezone and formats it for display\n   */\n  toAthensTime(date: string | Date): Date {\n    const inputDate = typeof date === 'string' ? new Date(date) : date;\n    \n    // Create a new date in Athens timezone\n    const athensDate = new Date(inputDate.toLocaleString('en-US', { \n      timeZone: this.ATHENS_TIMEZONE \n    }));\n    \n    return athensDate;\n  }\n\n  /**\n   * Formats date for chart labels (time only)\n   */\n  formatTimeForChart(date: string | Date): string {\n    const athensDate = this.toAthensTime(date);\n    return athensDate.toLocaleTimeString('el-GR', { \n      hour: '2-digit', \n      minute: '2-digit',\n      timeZone: this.ATHENS_TIMEZONE\n    });\n  }\n\n  /**\n   * Formats date for chart tooltips (full date and time)\n   */\n  formatDateTimeForTooltip(date: string | Date): string {\n    const athensDate = this.toAthensTime(date);\n    return athensDate.toLocaleString('el-GR', {\n      year: 'numeric',\n      month: '2-digit',\n      day: '2-digit',\n      hour: '2-digit',\n      minute: '2-digit',\n      timeZone: this.ATHENS_TIMEZONE\n    });\n  }\n\n  /**\n   * Formats date for table display\n   */\n  formatDateForTable(date: string | Date): string {\n    const athensDate = this.toAthensTime(date);\n    return athensDate.toLocaleDateString('el-GR', {\n      timeZone: this.ATHENS_TIMEZONE\n    });\n  }\n\n  /**\n   * Formats time for table display\n   */\n  formatTimeForTable(date: string | Date): string {\n    const athensDate = this.toAthensTime(date);\n    return athensDate.toLocaleTimeString('el-GR', { \n      hour: '2-digit', \n      minute: '2-digit',\n      timeZone: this.ATHENS_TIMEZONE\n    });\n  }\n\n  /**\n   * Converts local date to UTC for API calls\n   */\n  toUtcForApi(date: Date): string {\n    return date.toISOString();\n  }\n\n  /**\n   * Creates a date in Athens timezone for date pickers\n   */\n  createAthensDate(year: number, month: number, day: number, hour: number = 0, minute: number = 0): Date {\n    // Create date string in Athens timezone format\n    const dateString = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}T${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:00`;\n    \n    // Parse as local time and then convert to Athens\n    const localDate = new Date(dateString);\n    return this.toAthensTime(localDate);\n  }\n}\n"], "mappings": ";AAKA,OAAM,MAAOA,gBAAgB;EAG3BC,YAAA;IAFiB,KAAAC,eAAe,GAAG,eAAe;EAElC;EAEhB;;;EAGAC,YAAYA,CAACC,IAAmB;IAC9B,MAAMC,SAAS,GAAG,OAAOD,IAAI,KAAK,QAAQ,GAAG,IAAIE,IAAI,CAACF,IAAI,CAAC,GAAGA,IAAI;IAElE;IACA,MAAMG,UAAU,GAAG,IAAID,IAAI,CAACD,SAAS,CAACG,cAAc,CAAC,OAAO,EAAE;MAC5DC,QAAQ,EAAE,IAAI,CAACP;KAChB,CAAC,CAAC;IAEH,OAAOK,UAAU;EACnB;EAEA;;;EAGAG,kBAAkBA,CAACN,IAAmB;IACpC,MAAMG,UAAU,GAAG,IAAI,CAACJ,YAAY,CAACC,IAAI,CAAC;IAC1C,OAAOG,UAAU,CAACI,kBAAkB,CAAC,OAAO,EAAE;MAC5CC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE,SAAS;MACjBJ,QAAQ,EAAE,IAAI,CAACP;KAChB,CAAC;EACJ;EAEA;;;EAGAY,wBAAwBA,CAACV,IAAmB;IAC1C,MAAMG,UAAU,GAAG,IAAI,CAACJ,YAAY,CAACC,IAAI,CAAC;IAC1C,OAAOG,UAAU,CAACC,cAAc,CAAC,OAAO,EAAE;MACxCO,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,GAAG,EAAE,SAAS;MACdL,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE,SAAS;MACjBJ,QAAQ,EAAE,IAAI,CAACP;KAChB,CAAC;EACJ;EAEA;;;EAGAgB,kBAAkBA,CAACd,IAAmB;IACpC,MAAMG,UAAU,GAAG,IAAI,CAACJ,YAAY,CAACC,IAAI,CAAC;IAC1C,OAAOG,UAAU,CAACY,kBAAkB,CAAC,OAAO,EAAE;MAC5CV,QAAQ,EAAE,IAAI,CAACP;KAChB,CAAC;EACJ;EAEA;;;EAGAkB,kBAAkBA,CAAChB,IAAmB;IACpC,MAAMG,UAAU,GAAG,IAAI,CAACJ,YAAY,CAACC,IAAI,CAAC;IAC1C,OAAOG,UAAU,CAACI,kBAAkB,CAAC,OAAO,EAAE;MAC5CC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE,SAAS;MACjBJ,QAAQ,EAAE,IAAI,CAACP;KAChB,CAAC;EACJ;EAEA;;;EAGAmB,WAAWA,CAACjB,IAAU;IACpB,OAAOA,IAAI,CAACkB,WAAW,EAAE;EAC3B;EAEA;;;EAGAC,gBAAgBA,CAACR,IAAY,EAAEC,KAAa,EAAEC,GAAW,EAAEL,IAAA,GAAe,CAAC,EAAEC,MAAA,GAAiB,CAAC;IAC7F;IACA,MAAMW,UAAU,GAAG,GAAGT,IAAI,IAAIC,KAAK,CAACS,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIT,GAAG,CAACQ,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAId,IAAI,CAACa,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIb,MAAM,CAACY,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK;IAEjL;IACA,MAAMC,SAAS,GAAG,IAAIrB,IAAI,CAACkB,UAAU,CAAC;IACtC,OAAO,IAAI,CAACrB,YAAY,CAACwB,SAAS,CAAC;EACrC;EAAC,QAAAC,CAAA,G;qBArFU5B,gBAAgB;EAAA;EAAA,QAAA6B,EAAA,G;WAAhB7B,gBAAgB;IAAA8B,OAAA,EAAhB9B,gBAAgB,CAAA+B,IAAA;IAAAC,UAAA,EAFf;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}