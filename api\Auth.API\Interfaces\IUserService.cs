﻿using System.Threading.Tasks;
using System;
using Auth.Demo.Models;
using System.Collections.Generic;
using Auth.API.Models;
using Auth.API.Types;

namespace Auth.API.Interfaces
{
    public interface IUserService
    {
        Task<User> GetUserAsync(Guid userId);

        Task<List<UserProvider>> GetUserProvidersAsync(Guid userId);
        Task<List<Station>> GetUserStationsAsync(Guid userId);

        Task<bool> SaveUserProviderAsync(Guid userId, List<NewProvider> userProviders);

        Task<User> UpdateUserAsync(Guid userId, UpdateUserRequest updateRequest);
    }
}
