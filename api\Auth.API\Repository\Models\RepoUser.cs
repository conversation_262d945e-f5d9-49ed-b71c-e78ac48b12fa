﻿using System.ComponentModel.DataAnnotations;
using System;
using System.Collections.Generic;

namespace Auth.API.Repository.Models
{
    public class RepoUser
    {
        [Key]
        public Guid UserId { get; set; } = Guid.NewGuid();

        [Required]
        public string Username { get; set; }

        [Required]
        public string Email { get; set; }

        [Required]
        public string PasswordHash { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public string Role { get; set; } // Μπορεί να είναι "Admin" ή "User"

        public string Status { get; set; } = "Active"; // Active, Inactive, Suspended, Pending

        public string FirstName { get; set; }

        public string LastName { get; set; }

        public ICollection<RepoUserToken> UserTokens { get; set; } // Σχέση 1-Πολλά
    }
}
