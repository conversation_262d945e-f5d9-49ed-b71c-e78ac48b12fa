{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"./demo/components/loading/loading.component\";\nexport class AppComponent {\n  constructor(primengConfig) {\n    this.primengConfig = primengConfig;\n  }\n  ngOnInit() {\n    this.primengConfig.ripple = true;\n  }\n  static #_ = this.ɵfac = function AppComponent_Factory(t) {\n    return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.PrimeNGConfig));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppComponent,\n    selectors: [[\"app-root\"]],\n    decls: 2,\n    vars: 0,\n    template: function AppComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"app-loading\")(1, \"router-outlet\");\n      }\n    },\n    dependencies: [i2.RouterOutlet, i3.LoadingComponent],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "primengConfig", "ngOnInit", "ripple", "_", "i0", "ɵɵdirectiveInject", "i1", "PrimeNGConfig", "_2", "selectors", "decls", "vars", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelement"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\app.component.ts", "D:\\Greg\\solarkapital\\ui\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { PrimeNGConfig } from 'primeng/api';\n\n@Component({\n    selector: 'app-root',\n    templateUrl: './app.component.html'\n})\nexport class AppComponent implements OnInit {\n\n    constructor(private primengConfig: PrimeNGConfig) { }\n\n    ngOnInit() {\n        this.primengConfig.ripple = true;\n    }\n}\n", "<app-loading></app-loading>\n\n<router-outlet></router-outlet>\n"], "mappings": ";;;;AAOA,OAAM,MAAOA,YAAY;EAErBC,YAAoBC,aAA4B;IAA5B,KAAAA,aAAa,GAAbA,aAAa;EAAmB;EAEpDC,QAAQA,CAAA;IACJ,IAAI,CAACD,aAAa,CAACE,MAAM,GAAG,IAAI;EACpC;EAAC,QAAAC,CAAA,G;qBANQL,YAAY,EAAAM,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAZV,YAAY;IAAAW,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCPzBV,EAAA,CAAAY,SAAA,kBAA2B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}