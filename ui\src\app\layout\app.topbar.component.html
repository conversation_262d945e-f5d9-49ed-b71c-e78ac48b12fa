<div class="layout-topbar">
    <!-- Mobile Menu Button -->
    <button #menubutton class="p-link layout-menu-button layout-topbar-button" (click)="layoutService.onMenuToggle()">
        <i class="pi pi-bars"></i>
    </button>

    <!-- Logo Section -->
    <div class="layout-topbar-logo-section">
        <a class="layout-topbar-logo" routerLink="/app/index">
            <img src="assets/layout/images/{{layoutService.config().colorScheme === 'light' ? 'logo-dark' : 'logo-white'}}.png" alt="SolarKapital">
            <span class="logo-text">SolarKapital</span>
        </a>
    </div>

    <!-- Search Bar (Desktop) -->
    <div class="layout-topbar-search">
        <div class="p-input-icon-left">
            <i class="pi pi-search"></i>
            <input type="text"
                   pInputText
                   placeholder="Search stations, devices..."
                   class="search-input"
                   [(ngModel)]="searchQuery"
                   (keyup.enter)="onSearch()">
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="layout-topbar-actions">
        <!-- Notifications -->
        <button class="p-link layout-topbar-button notification-button"
                pTooltip="Notifications"
                tooltipPosition="bottom">
            <i class="pi pi-bell"></i>
            <span class="notification-badge">3</span>
        </button>

        <!-- Help -->
        <button class="p-link layout-topbar-button help-button"
                (click)="navigateToHelp()"
                pTooltip="Help & FAQ"
                tooltipPosition="bottom">
            <i class="pi pi-question-circle"></i>
        </button>

        <!-- Theme Toggle -->
        <button class="p-link layout-topbar-button theme-toggle"
                (click)="toggleTheme()"
                pTooltip="Toggle Theme"
                tooltipPosition="bottom">
            <i class="pi" [ngClass]="layoutService.config().colorScheme === 'light' ? 'pi-moon' : 'pi-sun'"></i>
        </button>

        <!-- User Profile Button -->
        <button class="p-link layout-topbar-button user-profile-btn"
                (click)="navigateToProfile()"
                pTooltip="User Profile"
                tooltipPosition="bottom">
            <i class="pi pi-user"></i>
        </button>

        <!-- Profile Menu Button -->
        <button #topbarmenubutton
                class="p-link layout-topbar-menu-button layout-topbar-button profile-button"
                (click)="layoutService.showProfileSidebar()"
                pTooltip="Profile Menu"
                tooltipPosition="bottom">
            <div class="profile-avatar">
                <i class="pi pi-user"></i>
            </div>
        </button>
    </div>

    <!-- Profile Dropdown Menu -->
    <!-- <div #topbarmenu class="layout-topbar-menu" [ngClass]="{'layout-topbar-menu-mobile-active': layoutService.state.profileSidebarVisible}">
        <div class="topbar-menu-header">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="pi pi-user"></i>
                </div>
                <div class="user-details">
                    <span class="user-name">John Doe</span>
                    <span class="user-email">john&#64;example.com</span>
                </div>
            </div>
        </div>

        <div class="topbar-menu-items">
            <button class="p-link layout-topbar-button menu-item">
                <i class="pi pi-calendar"></i>
                <span>Calendar</span>
            </button>
            <button class="p-link layout-topbar-button menu-item" [routerLink]="'/app/profile'">
                <i class="pi pi-user"></i>
                <span>Profile</span>
            </button>
            <button class="p-link layout-topbar-button menu-item" [routerLink]="'/app/providers'">
                <i class="pi pi-cog"></i>
                <span>Settings</span>
            </button>
            <div class="menu-divider"></div>
            <button class="p-link layout-topbar-button menu-item logout-item">
                <i class="pi pi-power-off"></i>
                <span>Logout</span>
            </button>
        </div>
    </div> -->
</div>