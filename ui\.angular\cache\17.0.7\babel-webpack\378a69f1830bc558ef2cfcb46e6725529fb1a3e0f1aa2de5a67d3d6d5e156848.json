{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { SidebarModule } from 'primeng/sidebar';\nimport { BadgeModule } from 'primeng/badge';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport { RippleModule } from 'primeng/ripple';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { RouterModule } from '@angular/router';\nimport { AppConfigModule } from './config/config.module';\nimport * as i0 from \"@angular/core\";\nexport let AppLayoutModule = /*#__PURE__*/(() => {\n  class AppLayoutModule {\n    static #_ = this.ɵfac = function AppLayoutModule_Factory(t) {\n      return new (t || AppLayoutModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppLayoutModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [BrowserModule, FormsModule, HttpClientModule, BrowserAnimationsModule, InputTextModule, SidebarModule, BadgeModule, RadioButtonModule, InputSwitchModule, RippleModule, TooltipModule, RouterModule, AppConfigModule]\n    });\n  }\n  return AppLayoutModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}