{"ast": null, "code": "import { debounceTime } from 'rxjs';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/layout/service/app.layout.service\";\nimport * as i2 from \"../../service/stations.service\";\nimport * as i3 from \"../../service/providers.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"../../service/cache.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"primeng/dataview\";\nimport * as i10 from \"primeng/inputtext\";\nimport * as i11 from \"primeng/dropdown\";\nimport * as i12 from \"@coreui/angular-chartjs\";\nimport * as i13 from \"primeng/badge\";\nimport * as i14 from \"@fortawesome/angular-fontawesome\";\nfunction IndexComponent_div_21_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"p\", 21);\n    i0.ɵɵtext(2, \" You are not registered to any provider. You can start adding providers now! \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction IndexComponent_div_21_div_5_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const p_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", p_r7.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(p_r7.name);\n  }\n}\nfunction IndexComponent_div_21_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23)(2, \"div\")(3, \"label\", 24);\n    i0.ɵɵtext(4, \"Provider\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"select\", 25);\n    i0.ɵɵtemplate(6, IndexComponent_div_21_div_5_option_6_Template, 2, 2, \"option\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\")(8, \"label\", 24);\n    i0.ɵɵtext(9, \"Username\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\")(12, \"label\", 24);\n    i0.ɵɵtext(13, \"Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\")(16, \"label\", 24);\n    i0.ɵɵtext(17, \"Stations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"p-multiSelect\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const i_r5 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formGroupName\", i_r5);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.availableProviders);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"options\", ctx_r3.stationsOptions)(\"defaultLabel\", \"Select stations\")(\"filter\", true)(\"showToggleAll\", false);\n  }\n}\nfunction IndexComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11);\n    i0.ɵɵtemplate(2, IndexComponent_div_21_div_2_Template, 3, 0, \"div\", 12);\n    i0.ɵɵelementStart(3, \"form\", 13);\n    i0.ɵɵlistener(\"ngSubmit\", function IndexComponent_div_21_Template_form_ngSubmit_3_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.onSubmit());\n    });\n    i0.ɵɵelementStart(4, \"div\", 14);\n    i0.ɵɵtemplate(5, IndexComponent_div_21_div_5_Template, 19, 6, \"div\", 15);\n    i0.ɵɵelementStart(6, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function IndexComponent_div_21_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.removeProvider(ctx_r10.i));\n    });\n    i0.ɵɵtext(7, \" Remove \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function IndexComponent_div_21_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.getStations(ctx_r11.i));\n    });\n    i0.ɵɵtext(9, \" Get Stations \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function IndexComponent_div_21_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.addProvider());\n    });\n    i0.ɵɵtext(11, \" + Add Provider \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 19);\n    i0.ɵɵtext(13, \" Save Providers \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_3_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.userProviders.length == 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.userProvidersForm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.providers.controls);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !((tmp_3_0 = ctx_r0.provider.get(\"providerId\")) == null ? null : tmp_3_0.value));\n  }\n}\nfunction IndexComponent_div_22_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"p-dropdown\", 37);\n    i0.ɵɵlistener(\"onChange\", function IndexComponent_div_22_ng_template_4_Template_p_dropdown_onChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.onSortChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"p-dropdown\", 38);\n    i0.ɵɵlistener(\"onChange\", function IndexComponent_div_22_ng_template_4_Template_p_dropdown_onChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r18.onSortChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 39);\n    i0.ɵɵelement(4, \"i\", 40);\n    i0.ɵɵelementStart(5, \"input\", 41);\n    i0.ɵɵlistener(\"input\", function IndexComponent_div_22_ng_template_4_Template_input_input_5_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      i0.ɵɵnextContext();\n      const _r13 = i0.ɵɵreference(3);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.onFilter(_r13, $event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"options\", ctx_r14.sortOptionsCountry);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"options\", ctx_r14.sortOptionsStatus);\n  }\n}\nconst _c0 = a1 => [\"/app/station\", a1];\nfunction IndexComponent_div_22_ng_template_5_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 43)(2, \"div\", 0)(3, \"div\", 44)(4, \"div\", 45)(5, \"a\", 46);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 47);\n    i0.ɵɵelement(8, \"fa-icon\", 48);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 49)(11, \"div\", 50);\n    i0.ɵɵelement(12, \"fa-icon\", 8);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 49)(15, \"div\", 50);\n    i0.ɵɵelement(16, \"fa-icon\", 51);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 52);\n    i0.ɵɵelement(19, \"c-chart\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 54);\n    i0.ɵɵelement(21, \"p-badge\", 55)(22, \"p-badge\", 56)(23, \"p-badge\", 57)(24, \"p-badge\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 59)(26, \"p\", 60);\n    i0.ɵɵelement(27, \"i\", 61);\n    i0.ɵɵelementStart(28, \"span\", 62);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const station_r22 = ctx.$implicit;\n    const ctx_r21 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(\"station-box-status-\" + station_r22.status.toLowerCase());\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(14, _c0, station_r22.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(station_r22.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", station_r22.updateTime, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", station_r22.power, \"kW\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", station_r22.irradiance, \"kWh/m2\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"data\", ctx_r21.data[1])(\"options\", ctx_r21.lineOptions);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"value\", station_r22.inverter);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"value\", station_r22.mmpt);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"value\", station_r22.string);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"value\", station_r22.pvn);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", station_r22.temperature, \" \\u2103\");\n  }\n}\nfunction IndexComponent_div_22_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, IndexComponent_div_22_ng_template_5_div_0_Template, 30, 16, \"div\", 42);\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r15.stations);\n  }\n}\nfunction IndexComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 31)(2, \"p-dataView\", 32, 33);\n    i0.ɵɵtemplate(4, IndexComponent_div_22_ng_template_4_Template, 6, 2, \"ng-template\", 34)(5, IndexComponent_div_22_ng_template_5_Template, 1, 1, \"ng-template\", 35);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r1.stations)(\"paginator\", true)(\"rows\", 9)(\"sortField\", ctx_r1.sortField)(\"sortOrder\", ctx_r1.sortOrder);\n  }\n}\nconst _c1 = () => ({\n  width: \"2.5rem\",\n  height: \"2.5rem\"\n});\nexport class IndexComponent {\n  constructor(layoutService, stationsService, providersService, messageService, fb, cacheService) {\n    this.layoutService = layoutService;\n    this.stationsService = stationsService;\n    this.providersService = providersService;\n    this.messageService = messageService;\n    this.fb = fb;\n    this.cacheService = cacheService;\n    this.stations = [];\n    this.availableProviders = [];\n    this.userProviders = [];\n    this.sortOptionsCountry = [];\n    this.sortOptionsStatus = [];\n    this.sortOrder = 0;\n    this.sortField = '';\n    this.sourceCities = [];\n    this.targetCities = [];\n    this.orderCities = [];\n    this.subscription = this.layoutService.configUpdate$.pipe(debounceTime(25)).subscribe(config => {\n      //   this.initChart();\n    });\n    this.userProvidersForm = this.fb.group({\n      providers: this.fb.array([])\n    });\n  }\n  ngOnInit() {\n    this.providersService.getProviders().then(data => {\n      this.availableProviders = data;\n    });\n    this.providersService.getUserProviders().then(data => {\n      this.userProviders = data;\n    });\n    this.addProvider(); // Ξεκινάμε με μία προεπιλεγμένη σειρά\n    // this.stationsService.getStations().then(data => {\n    //   this.stations = data\n    //   this.cacheService.setStations(this.stations);\n    // });\n    this.sourceCities = [{\n      name: 'San Francisco',\n      code: 'SF'\n    }, {\n      name: 'London',\n      code: 'LDN'\n    }, {\n      name: 'Paris',\n      code: 'PRS'\n    }, {\n      name: 'Istanbul',\n      code: 'IST'\n    }, {\n      name: 'Berlin',\n      code: 'BRL'\n    }, {\n      name: 'Barcelona',\n      code: 'BRC'\n    }, {\n      name: 'Rome',\n      code: 'RM'\n    }];\n    this.targetCities = [];\n    this.orderCities = [{\n      name: 'San Francisco',\n      code: 'SF'\n    }, {\n      name: 'London',\n      code: 'LDN'\n    }, {\n      name: 'Paris',\n      code: 'PRS'\n    }, {\n      name: 'Istanbul',\n      code: 'IST'\n    }, {\n      name: 'Berlin',\n      code: 'BRL'\n    }, {\n      name: 'Barcelona',\n      code: 'BRC'\n    }, {\n      name: 'Rome',\n      code: 'RM'\n    }];\n    // this.sortOptionsCountry = [\n    //     { label: 'Price High to Low', value: '!price' },\n    //     { label: 'Price Low to High', value: 'price' }\n    // ];\n    this.barOptions = {\n      maintainAspectRatio: false,\n      plugins: {\n        legend: {\n          display: false\n        }\n      },\n      scales: {\n        x: {\n          display: false\n        },\n        y: {\n          display: false\n        }\n      }\n    };\n    this.lineOptions = {\n      maintainAspectRatio: false,\n      elements: {\n        line: {\n          tension: 0.4\n        },\n        point: {\n          radius: 0\n        }\n      },\n      plugins: {\n        legend: {\n          display: false\n        }\n      },\n      scales: {\n        x: {\n          display: false\n        },\n        y: {\n          display: false\n        }\n      }\n    };\n    this.data = [{\n      labels: ['M', 'T', 'W', 'T', 'F', 'S', 'S', 'M', 'T', 'W', 'T', 'F', 'S', 'S', 'M'],\n      datasets: [{\n        backgroundColor: '#321fdb',\n        borderColor: 'transparent',\n        borderWidth: 1,\n        data: [41, 78, 51, 66, 74, 42, 89, 97, 87, 84, 78, 88, 67, 45, 47]\n      }]\n    }, {\n      labels: ['M', 'T', 'W', 'T', 'F', 'S', 'S', 'M', 'T', 'W', 'T', 'F', 'S', 'S', 'M'],\n      datasets: [{\n        backgroundColor: 'transparent',\n        borderColor: '#321fdb',\n        borderWidth: 2,\n        data: [41, 78, 51, 66, 74, 42, 89, 97, 87, 84, 78, 88, 67, 45, 47],\n        pointBackgroundColor: '#321fdb'\n      }]\n    }];\n  }\n  addProvider() {\n    const providerGroup = this.fb.group({\n      providerId: ['', Validators.required],\n      username: ['', Validators.required],\n      password: ['', Validators.required],\n      stations: [[]] // Αρχικά empty array για το multiSelect\n    });\n\n    this.providers.push(providerGroup);\n  }\n  get providers() {\n    return this.userProvidersForm.get('providers');\n  }\n  removeProvider(index) {\n    this.providers.removeAt(index);\n  }\n  getStations(index) {\n    const providerId = this.providers.at(index).get('providerId')?.value;\n    if (!providerId) return;\n    this.stationsService.getStations().then(data => {\n      this.providers.at(index).patchValue({\n        data\n      });\n    });\n  }\n  onSubmit() {\n    if (this.userProvidersForm.valid) {\n      console.log('Form Data:', this.userProvidersForm.value.providers);\n      // Εδώ μπορείς να κάνεις POST τα δεδομένα στο API\n      let request = {\n        providers: this.userProvidersForm.value.providers\n      };\n      this.providersService.saveUserProviders(request).then(data => {\n        console.log(data);\n      });\n    }\n  }\n  onSortChange(event) {\n    const value = event.value;\n    if (value.indexOf('!') === 0) {\n      this.sortOrder = -1;\n      this.sortField = value.substring(1, value.length);\n    } else {\n      this.sortOrder = 1;\n      this.sortField = value;\n    }\n  }\n  onFilter(dv, event) {\n    dv.filter(event.target.value);\n  }\n  initMap() {\n    this.mapSrc = \"https://www.digitaltrends.com/wp-content/uploads/2020/02/custom-map.jpg?fit=720%2C479&p=1\";\n  }\n  // initChart() {\n  //     const documentStyle = getComputedStyle(document.documentElement);\n  //     const textColor = documentStyle.getPropertyValue('--text-color');\n  //     const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n  //     const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\n  //     this.chartData = {\n  //         labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\n  //         datasets: [\n  //             {\n  //                 label: 'First Dataset',\n  //                 data: [65, 59, 80, 81, 56, 55, 40],\n  //                 fill: false,\n  //                 backgroundColor: documentStyle.getPropertyValue('--bluegray-700'),\n  //                 borderColor: documentStyle.getPropertyValue('--bluegray-700'),\n  //                 tension: .4\n  //             },\n  //             {\n  //                 label: 'Second Dataset',\n  //                 data: [28, 48, 40, 19, 86, 27, 90],\n  //                 fill: false,\n  //                 backgroundColor: documentStyle.getPropertyValue('--green-600'),\n  //                 borderColor: documentStyle.getPropertyValue('--green-600'),\n  //                 tension: .4\n  //             }\n  //         ]\n  //     };\n  //     this.chartOptions = {\n  //         plugins: {\n  //             legend: {\n  //                 labels: {\n  //                     color: textColor\n  //                 }\n  //             }\n  //         },\n  //         scales: {\n  //             x: {\n  //                 ticks: {\n  //                     color: textColorSecondary\n  //                 },\n  //                 grid: {\n  //                     color: surfaceBorder,\n  //                     drawBorder: false\n  //                 }\n  //             },\n  //             y: {\n  //                 ticks: {\n  //                     color: textColorSecondary\n  //                 },\n  //                 grid: {\n  //                     color: surfaceBorder,\n  //                     drawBorder: false\n  //                 }\n  //             }\n  //         }\n  //     };\n  // }\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n  static #_ = this.ɵfac = function IndexComponent_Factory(t) {\n    return new (t || IndexComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.StationsService), i0.ɵɵdirectiveInject(i3.ProvidersService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.FormBuilder), i0.ɵɵdirectiveInject(i6.CacheService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: IndexComponent,\n    selectors: [[\"ng-component\"]],\n    decls: 23,\n    vars: 6,\n    consts: [[1, \"grid\"], [1, \"col-12\", \"lg:col-6\", \"xl:col-6\"], [1, \"card\", \"mb-0\"], [1, \"flex\", \"justify-content-between\", \"mb-3\"], [1, \"block\", \"text-500\", \"font-medium\", \"font-bold\", \"mb-3\"], [1, \"text-900\", \"font-medium\", \"text-xl\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-blue-100\", \"border-round\", 3, \"ngStyle\"], [\"icon\", \"solar-panel\"], [\"icon\", \"plug\"], [\"class\", \"col-12 lg:col-12 xl:col-12\", 4, \"ngIf\"], [1, \"col-12\", \"lg:col-12\", \"xl:col-12\"], [1, \"card\", \"p-4\"], [\"class\", \"text-center text-lg\", 4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\"], [\"formArrayName\", \"providers\"], [\"class\", \"mb-4 border p-3 rounded shadow\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 1, \"mt-2\", \"text-red-500\", 3, \"click\"], [\"type\", \"button\", 1, \"mt-2\", \"text-blue-500\", 3, \"disabled\", \"click\"], [\"type\", \"button\", 1, \"mt-4\", \"bg-blue-500\", \"text-white\", \"p-2\", \"rounded\", 3, \"click\"], [\"type\", \"submit\", 1, \"mt-4\", \"bg-green-500\", \"text-white\", \"p-2\", \"rounded\"], [1, \"text-center\", \"text-lg\"], [1, \"text-red-500\", \"font-semibold\"], [1, \"mb-4\", \"border\", \"p-3\", \"rounded\", \"shadow\", 3, \"formGroupName\"], [1, \"grid\", \"grid-cols-3\", \"gap-4\"], [1, \"block\", \"font-medium\", \"mb-1\"], [\"formControlName\", \"providerId\", 1, \"w-full\", \"p-2\", \"border\", \"rounded\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"formControlName\", \"username\", \"placeholder\", \"Enter username\", 1, \"w-full\", \"p-2\", \"border\", \"rounded\"], [\"type\", \"text\", \"formControlName\", \"password\", \"placeholder\", \"Enter Password\", 1, \"w-full\", \"p-2\", \"border\", \"rounded\"], [\"formControlName\", \"stations\", 3, \"options\", \"defaultLabel\", \"filter\", \"showToggleAll\"], [3, \"value\"], [1, \"card\"], [\"filterBy\", \"name\", \"layout\", \"list\", 3, \"value\", \"paginator\", \"rows\", \"sortField\", \"sortOrder\"], [\"dv\", \"\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"listItem\"], [1, \"flex\", \"flex-column\", \"md:flex-row\", \"md:justify-content-between\", \"gap-2\"], [\"placeholder\", \"Country\", 3, \"options\", \"onChange\"], [\"placeholder\", \"Status\", 3, \"options\", \"onChange\"], [1, \"p-input-icon-left\"], [1, \"pi\", \"pi-search\"], [\"type\", \"search\", \"pInputText\", \"\", \"placeholder\", \"Search\", 3, \"input\"], [3, \"class\", 4, \"ngFor\", \"ngForOf\"], [1, \"\"], [1, \"col-12\", \"md:col-6\", \"lg:col-3\"], [1, \"font-bold\", \"text-xl\"], [3, \"routerLink\"], [1, \"font-italic\"], [\"icon\", \"clock\"], [1, \"col-12\", \"md:col-6\", \"lg:col-1\"], [1, \"text-center\", \"font-bold\"], [\"icon\", \"sun\"], [1, \"col-12\", \"md:col-6\", \"lg:col-2\"], [\"type\", \"line\", \"height\", \"40\", \"width\", \"80\", 1, \"mx-auto\", 3, \"data\", \"options\"], [1, \"col-12\", \"md:col-6\", \"lg:col-3\", \"text-center\"], [\"size\", \"large\", 1, \"pad-2\", 3, \"value\"], [\"size\", \"large\", \"severity\", \"success\", 1, \"pad-2\", 3, \"value\"], [\"size\", \"large\", \"severity\", \"info\", 1, \"pad-2\", 3, \"value\"], [\"size\", \"large\", \"severity\", \"warning\", 1, \"pad-2\", 3, \"value\"], [1, \"col-12\", \"md:col-6\", \"lg:col-2\", \"text-center\"], [1, \"text-center\"], [1, \"pi\", \"pi-cloud\", \"text-2xl\"], [1, \"text-2xl\", \"mb-2\", \"align-self-center\", \"md:align-self-end\"]],\n    template: function IndexComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\")(5, \"span\", 4);\n        i0.ɵɵtext(6, \"Active Energy Systems\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"div\", 5);\n        i0.ɵɵtext(8, \"44\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(9, \"div\", 6);\n        i0.ɵɵelement(10, \"fa-icon\", 7);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(11, \"div\", 1)(12, \"div\", 2)(13, \"div\", 3)(14, \"div\")(15, \"span\", 4);\n        i0.ɵɵtext(16, \"Combined Power Permormance\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"div\", 5);\n        i0.ɵɵtext(18, \"33% (4.124kW) \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(19, \"div\", 6);\n        i0.ɵɵelement(20, \"fa-icon\", 8);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵtemplate(21, IndexComponent_div_21_Template, 14, 4, \"div\", 9)(22, IndexComponent_div_22_Template, 6, 5, \"div\", 9);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(4, _c1));\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(5, _c1));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.userProviders.length == 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.userProviders.length > 0);\n      }\n    },\n    dependencies: [i7.NgForOf, i7.NgIf, i7.NgStyle, i5.ɵNgNoValidate, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i8.RouterLink, i4.PrimeTemplate, i9.DataView, i10.InputText, i11.Dropdown, i12.ChartjsComponent, i13.Badge, i14.FaIconComponent, i5.FormGroupDirective, i5.FormControlName, i5.FormGroupName, i5.FormArrayName],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["debounceTime", "Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "p_r7", "id", "ɵɵadvance", "ɵɵtextInterpolate", "name", "ɵɵtemplate", "IndexComponent_div_21_div_5_option_6_Template", "ɵɵelement", "i_r5", "ctx_r3", "availableProviders", "stationsOptions", "IndexComponent_div_21_div_2_Template", "ɵɵlistener", "IndexComponent_div_21_Template_form_ngSubmit_3_listener", "ɵɵrestoreView", "_r9", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "onSubmit", "IndexComponent_div_21_div_5_Template", "IndexComponent_div_21_Template_button_click_6_listener", "ctx_r10", "removeProvider", "i", "IndexComponent_div_21_Template_button_click_8_listener", "ctx_r11", "getStations", "IndexComponent_div_21_Template_button_click_10_listener", "ctx_r12", "addProvider", "ctx_r0", "userProviders", "length", "userProvidersForm", "providers", "controls", "tmp_3_0", "provider", "get", "value", "IndexComponent_div_22_ng_template_4_Template_p_dropdown_onChange_1_listener", "$event", "_r17", "ctx_r16", "onSortChange", "IndexComponent_div_22_ng_template_4_Template_p_dropdown_onChange_2_listener", "ctx_r18", "IndexComponent_div_22_ng_template_4_Template_input_input_5_listener", "_r13", "ɵɵreference", "ctx_r19", "onFilter", "ctx_r14", "sortOptionsCountry", "sortOptionsStatus", "ɵɵclassMap", "station_r22", "status", "toLowerCase", "ɵɵpureFunction1", "_c0", "ɵɵtextInterpolate1", "updateTime", "power", "irradiance", "ctx_r21", "data", "lineOptions", "ɵɵpropertyInterpolate", "inverter", "mmpt", "string", "pvn", "temperature", "IndexComponent_div_22_ng_template_5_div_0_Template", "ctx_r15", "stations", "IndexComponent_div_22_ng_template_4_Template", "IndexComponent_div_22_ng_template_5_Template", "ctx_r1", "sortField", "sortOrder", "IndexComponent", "constructor", "layoutService", "stationsService", "providersService", "messageService", "fb", "cacheService", "sourceCities", "targetCities", "orderCities", "subscription", "configUpdate$", "pipe", "subscribe", "config", "group", "array", "ngOnInit", "getProviders", "then", "getUserProviders", "code", "barOptions", "maintainAspectRatio", "plugins", "legend", "display", "scales", "x", "y", "elements", "line", "tension", "point", "radius", "labels", "datasets", "backgroundColor", "borderColor", "borderWidth", "pointBackgroundColor", "providerGroup", "providerId", "required", "username", "password", "push", "index", "removeAt", "at", "patchValue", "valid", "console", "log", "request", "saveUserProviders", "event", "indexOf", "substring", "dv", "filter", "target", "initMap", "mapSrc", "ngOnDestroy", "unsubscribe", "_", "ɵɵdirectiveInject", "i1", "LayoutService", "i2", "StationsService", "i3", "ProvidersService", "i4", "MessageService", "i5", "FormBuilder", "i6", "CacheService", "_2", "selectors", "decls", "vars", "consts", "template", "IndexComponent_Template", "rf", "ctx", "IndexComponent_div_21_Template", "IndexComponent_div_22_Template", "ɵɵpureFunction0", "_c1"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\index\\index.component.ts", "D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\index\\index.component.html"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';\r\nimport { MenuItem, MessageService } from 'primeng/api';\r\nimport { Product } from '../../api/product';\r\nimport { ProductService } from '../../service/product.service';\r\nimport { Subscription, debounceTime } from 'rxjs';\r\nimport { LayoutService } from 'src/app/layout/service/app.layout.service';\r\nimport { SelectItem } from 'primeng/api';\r\nimport { DataView } from 'primeng/dataview';\r\nimport { StationsService } from '../../service/stations.service';\r\nimport { Station } from '../../api/station';\r\nimport { CacheService } from '../../service/cache.service';\r\nimport { ProvidersService } from '../../service/providers.service';\r\nimport { IProvider, IUserProvider } from '../../api/responses';\r\nimport { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { SaveUserProvidersRequest } from '../../api/requests';\r\n\r\n\r\n@Component({\r\n    templateUrl: './index.component.html',\r\n})\r\nexport class IndexComponent implements OnInit, OnDestroy {\r\n\r\n    items!: MenuItem[];\r\n\r\n    stations: Station[] =[];\r\n    availableProviders:IProvider[] = [];\r\n    userProviders:IUserProvider[] = [];\r\n    userProvidersForm: FormGroup;\r\n\r\n    chartData: any;\r\n\r\n    chartOptions: any;\r\n\r\n    subscription!: Subscription;\r\n\r\n    sortOptionsCountry: SelectItem[] = [];\r\n\r\n    sortOptionsStatus: SelectItem[] = [];\r\n\r\n    sortOrder: number = 0;\r\n\r\n    sortField: string = '';\r\n\r\n    sourceCities: any[] = [];\r\n\r\n    targetCities: any[] = [];\r\n\r\n    orderCities: any[] = [];\r\n\r\n    mapSrc:string;\r\n\r\n    barOptions:any;\r\n    lineOptions:any;\r\n    data: any;\r\n    \r\n\r\n    constructor(public layoutService: LayoutService, \r\n        private stationsService: StationsService,\r\n        private providersService: ProvidersService,\r\n        private messageService: MessageService,\r\n        private fb: FormBuilder,\r\n        private cacheService: CacheService) {\r\n        this.subscription = this.layoutService.configUpdate$\r\n        .pipe(debounceTime(25))\r\n        .subscribe((config) => {\r\n         //   this.initChart();\r\n        });\r\n\r\n\r\n        this.userProvidersForm = this.fb.group({\r\n          providers: this.fb.array([])\r\n        });\r\n    }\r\n\r\n    ngOnInit() {\r\n        this.providersService.getProviders().then(data => {\r\n            this.availableProviders = data;\r\n          });\r\n\r\n          this.providersService.getUserProviders().then(data => {\r\n            this.userProviders = data;\r\n          });\r\n\r\n          this.addProvider(); // Ξεκινάμε με μία προεπιλεγμένη σειρά\r\n\r\n        // this.stationsService.getStations().then(data => {\r\n        //   this.stations = data\r\n        //   this.cacheService.setStations(this.stations);\r\n        // });\r\n        this.sourceCities = [\r\n            { name: 'San Francisco', code: 'SF' },\r\n            { name: 'London', code: 'LDN' },\r\n            { name: 'Paris', code: 'PRS' },\r\n            { name: 'Istanbul', code: 'IST' },\r\n            { name: 'Berlin', code: 'BRL' },\r\n            { name: 'Barcelona', code: 'BRC' },\r\n            { name: 'Rome', code: 'RM' }];\r\n\r\n        this.targetCities = [];\r\n\r\n        this.orderCities = [\r\n            { name: 'San Francisco', code: 'SF' },\r\n            { name: 'London', code: 'LDN' },\r\n            { name: 'Paris', code: 'PRS' },\r\n            { name: 'Istanbul', code: 'IST' },\r\n            { name: 'Berlin', code: 'BRL' },\r\n            { name: 'Barcelona', code: 'BRC' },\r\n            { name: 'Rome', code: 'RM' }];\r\n\r\n        // this.sortOptionsCountry = [\r\n        //     { label: 'Price High to Low', value: '!price' },\r\n        //     { label: 'Price Low to High', value: 'price' }\r\n        // ];\r\n\r\n\r\n        this.barOptions = {\r\n            maintainAspectRatio: false,\r\n            plugins: {\r\n              legend: {\r\n                display: false\r\n              }\r\n            },\r\n            scales: {\r\n              x: {\r\n                display: false\r\n              },\r\n              y: {\r\n                display: false\r\n              }\r\n            }\r\n          };\r\n        \r\n          this.lineOptions = {\r\n            maintainAspectRatio: false,\r\n            elements: {\r\n              line: {\r\n                tension: 0.4\r\n              },\r\n              point: {\r\n                radius: 0\r\n              }\r\n            },\r\n            plugins: {\r\n              legend: {\r\n                display: false\r\n              }\r\n            },\r\n            scales: {\r\n              x: {\r\n                display: false\r\n              },\r\n              y: {\r\n                display: false\r\n              }\r\n            }\r\n          };\r\n        \r\n          this.data = [\r\n            {\r\n              labels: ['M', 'T', 'W', 'T', 'F', 'S', 'S', 'M', 'T', 'W', 'T', 'F', 'S', 'S', 'M'],\r\n              datasets: [\r\n                {\r\n                  backgroundColor: '#321fdb',\r\n                  borderColor: 'transparent',\r\n                  borderWidth: 1,\r\n                  data: [41, 78, 51, 66, 74, 42, 89, 97, 87, 84, 78, 88, 67, 45, 47]\r\n                }\r\n              ]\r\n            }, {\r\n              labels: ['M', 'T', 'W', 'T', 'F', 'S', 'S', 'M', 'T', 'W', 'T', 'F', 'S', 'S', 'M'],\r\n              datasets: [\r\n                {\r\n                  backgroundColor: 'transparent',\r\n                  borderColor: '#321fdb',\r\n                  borderWidth: 2,\r\n                  data: [41, 78, 51, 66, 74, 42, 89, 97, 87, 84, 78, 88, 67, 45, 47],\r\n                  pointBackgroundColor: '#321fdb'\r\n                }\r\n              ]\r\n            }\r\n          ];\r\n    }\r\n\r\n    addProvider() {\r\n      const providerGroup = this.fb.group({\r\n        providerId: ['', Validators.required],\r\n        username: ['', Validators.required],\r\n        password: ['', Validators.required],\r\n        stations: [[]] // Αρχικά empty array για το multiSelect\r\n      });\r\n      this.providers.push(providerGroup);\r\n    }\r\n\r\n    get providers(): FormArray {\r\n      return this.userProvidersForm.get('providers') as FormArray;\r\n    }\r\n  \r\n\r\n\r\n    removeProvider(index: number) {\r\n      this.providers.removeAt(index);\r\n    }\r\n\r\n    getStations(index: number) {\r\n      const providerId = this.providers.at(index).get('providerId')?.value;\r\n  \r\n      if (!providerId) return;\r\n\r\n      this.stationsService.getStations().then(data => {\r\n        this.providers.at(index).patchValue({ data });\r\n      });\r\n    }\r\n  \r\n    onSubmit() {\r\n      if (this.userProvidersForm.valid) {\r\n        console.log('Form Data:', this.userProvidersForm.value.providers);\r\n        // Εδώ μπορείς να κάνεις POST τα δεδομένα στο API\r\n        let request:SaveUserProvidersRequest = {\r\n          providers : this.userProvidersForm.value.providers\r\n        };\r\n\r\n        this.providersService.saveUserProviders(request).then(data => {\r\n          console.log(data);  \r\n        });\r\n\r\n      }\r\n    }\r\n\r\n    onSortChange(event: any) {\r\n        const value = event.value;\r\n\r\n        if (value.indexOf('!') === 0) {\r\n            this.sortOrder = -1;\r\n            this.sortField = value.substring(1, value.length);\r\n        } else {\r\n            this.sortOrder = 1;\r\n            this.sortField = value;\r\n        }\r\n    }\r\n\r\n    onFilter(dv: DataView, event: Event) {\r\n        dv.filter((event.target as HTMLInputElement).value);\r\n    }\r\n\r\n\r\n    initMap(){\r\n        this.mapSrc = \"https://www.digitaltrends.com/wp-content/uploads/2020/02/custom-map.jpg?fit=720%2C479&p=1\"\r\n    }\r\n\r\n    // initChart() {\r\n    //     const documentStyle = getComputedStyle(document.documentElement);\r\n    //     const textColor = documentStyle.getPropertyValue('--text-color');\r\n    //     const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\r\n    //     const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\r\n\r\n    //     this.chartData = {\r\n    //         labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\r\n    //         datasets: [\r\n    //             {\r\n    //                 label: 'First Dataset',\r\n    //                 data: [65, 59, 80, 81, 56, 55, 40],\r\n    //                 fill: false,\r\n    //                 backgroundColor: documentStyle.getPropertyValue('--bluegray-700'),\r\n    //                 borderColor: documentStyle.getPropertyValue('--bluegray-700'),\r\n    //                 tension: .4\r\n    //             },\r\n    //             {\r\n    //                 label: 'Second Dataset',\r\n    //                 data: [28, 48, 40, 19, 86, 27, 90],\r\n    //                 fill: false,\r\n    //                 backgroundColor: documentStyle.getPropertyValue('--green-600'),\r\n    //                 borderColor: documentStyle.getPropertyValue('--green-600'),\r\n    //                 tension: .4\r\n    //             }\r\n    //         ]\r\n    //     };\r\n\r\n    //     this.chartOptions = {\r\n    //         plugins: {\r\n    //             legend: {\r\n    //                 labels: {\r\n    //                     color: textColor\r\n    //                 }\r\n    //             }\r\n    //         },\r\n    //         scales: {\r\n    //             x: {\r\n    //                 ticks: {\r\n    //                     color: textColorSecondary\r\n    //                 },\r\n    //                 grid: {\r\n    //                     color: surfaceBorder,\r\n    //                     drawBorder: false\r\n    //                 }\r\n    //             },\r\n    //             y: {\r\n    //                 ticks: {\r\n    //                     color: textColorSecondary\r\n    //                 },\r\n    //                 grid: {\r\n    //                     color: surfaceBorder,\r\n    //                     drawBorder: false\r\n    //                 }\r\n    //             }\r\n    //         }\r\n    //     };\r\n    // }\r\n\r\n    ngOnDestroy() {\r\n        if (this.subscription) {\r\n            this.subscription.unsubscribe();\r\n        }\r\n    }\r\n}\r\n", "<div class=\"grid\">\r\n    <div class=\"col-12 lg:col-6 xl:col-6\">\r\n        <div class=\"card mb-0\">\r\n            <div class=\"flex justify-content-between mb-3\">\r\n                <div>\r\n                    <span class=\"block text-500 font-medium font-bold mb-3\">Active Energy Systems</span>\r\n                    <div class=\"text-900 font-medium text-xl\">44</div>\r\n                </div>\r\n                <div class=\"flex align-items-center justify-content-center bg-blue-100 border-round\" [ngStyle]=\"{width: '2.5rem', height: '2.5rem'}\">\r\n                    <fa-icon icon=\"solar-panel\"></fa-icon> \r\n                </div>\r\n            </div>\r\n            <!-- <span class=\"text-green-500 font-medium\">24 new </span>\r\n            <span class=\"text-500\">since last visit</span> -->\r\n        </div>\r\n    </div>\r\n    <div class=\"col-12 lg:col-6 xl:col-6\">\r\n        <div class=\"card mb-0\">\r\n            <div class=\"flex justify-content-between mb-3\">\r\n                <div>\r\n                    <span class=\"block text-500 font-medium font-bold mb-3\">Combined Power Permormance</span>\r\n                    <div class=\"text-900 font-medium text-xl\">33% (4.124kW) </div>\r\n                </div>\r\n                <div class=\"flex align-items-center justify-content-center bg-blue-100 border-round\" [ngStyle]=\"{width: '2.5rem', height: '2.5rem'}\">\r\n                    <fa-icon icon=\"plug\"></fa-icon> \r\n                </div>\r\n            </div>\r\n            <!-- <span class=\"text-green-500 font-medium\">24 new </span>\r\n            <span class=\"text-500\">since last visit</span> -->\r\n        </div>\r\n    </div>\r\n\t<div class=\"col-12 lg:col-12 xl:col-12\" *ngIf=\"userProviders.length ==0\">\r\n\t\t<div class=\"card p-4\">\r\n\t\t  <div *ngIf=\"userProviders.length == 0\" class=\"text-center text-lg\">\r\n\t\t\t<p class=\"text-red-500 font-semibold\">\r\n\t\t\t  You are not registered to any provider. You can start adding providers now!\r\n\t\t\t</p>\r\n\t\t  </div>\r\n\t  \r\n\t\t  <!-- Dynamic Form -->\r\n\t\t  <form [formGroup]=\"userProvidersForm\" (ngSubmit)=\"onSubmit()\">\r\n\t\t\t<div formArrayName=\"providers\">\r\n\t\t\t  <div *ngFor=\"let provider of providers.controls; let i = index\" [formGroupName]=\"i\" class=\"mb-4 border p-3 rounded shadow\">\r\n\t\t\t\t<div class=\"grid grid-cols-3 gap-4\">\r\n\t\t\t\t  <!-- Dropdown για επιλογή provider -->\r\n\t\t\t\t  <div>\r\n\t\t\t\t\t<label class=\"block font-medium mb-1\">Provider</label>\r\n\t\t\t\t\t<select formControlName=\"providerId\" class=\"w-full p-2 border rounded\">\r\n\t\t\t\t\t  <option *ngFor=\"let p of availableProviders\" [value]=\"p.id\">{{ p.name }}</option>\r\n\t\t\t\t\t</select>\r\n\t\t\t\t  </div>\r\n\t  \r\n\t\t\t\t  <!-- Input για username -->\r\n\t\t\t\t  <div>\r\n\t\t\t\t\t<label class=\"block font-medium mb-1\">Username</label>\r\n\t\t\t\t\t<input type=\"text\" formControlName=\"username\" class=\"w-full p-2 border rounded\" placeholder=\"Enter username\">\r\n\t\t\t\t  </div>\r\n\t  \r\n\t\t\t\t  <!-- Input για API Key -->\r\n\t\t\t\t  <div>\r\n\t\t\t\t\t<label class=\"block font-medium mb-1\">Password</label>\r\n\t\t\t\t\t<input type=\"text\" formControlName=\"password\" class=\"w-full p-2 border rounded\" placeholder=\"Enter Password\">\r\n\t\t\t\t  </div>\r\n\t\t\t\t\t<!-- MultiSelect για Stations -->\r\n\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<label class=\"block font-medium mb-1\">Stations</label>\r\n\t\t\t\t\t\t<p-multiSelect [options]=\"stationsOptions\" formControlName=\"stations\" [defaultLabel]=\"'Select stations'\" [filter]=\"true\" [showToggleAll]=\"false\"></p-multiSelect>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t  \r\n\t\t\t\t</div>\r\n\t  \r\n\t\t\t\t<!-- Remove Button -->\r\n\t\t\t\t<button type=\"button\" class=\"mt-2 text-red-500\" (click)=\"removeProvider(i)\">\r\n\t\t\t\t  Remove\r\n\t\t\t\t</button>\r\n\t\t\t\t<button type=\"button\" class=\"mt-2 text-blue-500\" \r\n\t\t\t\t\t\t[disabled]=\"!provider.get('providerId')?.value\" \r\n\t\t\t\t\t\t(click)=\"getStations(i)\">\r\n\t\t\t\t\t\tGet Stations\r\n\t\t\t\t</button>\r\n\r\n\t\t\t</div>\r\n\t  \r\n\t\t\t<!-- Add Provider Button -->\r\n\t\t\t<button type=\"button\" class=\"mt-4 bg-blue-500 text-white p-2 rounded\" (click)=\"addProvider()\">\r\n\t\t\t  + Add Provider\r\n\t\t\t</button>\r\n\t  \r\n\t\t\t<!-- Submit Button -->\r\n\t\t\t<button type=\"submit\" class=\"mt-4 bg-green-500 text-white p-2 rounded\">\r\n\t\t\t  Save Providers\r\n\t\t\t</button>\r\n\t\t  </form>\r\n\t\t</div>\r\n\t  </div>\r\n\t  \r\n\r\n    <div class=\"col-12 lg:col-12 xl:col-12\" *ngIf=\"userProviders.length >0\">\r\n\t\t<div class=\"card\">\r\n\t\t\t<p-dataView #dv [value]=\"stations\" [paginator]=\"true\" [rows]=\"9\" filterBy=\"name\" [sortField]=\"sortField\" [sortOrder]=\"sortOrder\" layout=\"list\">\r\n\t\t\t\t<ng-template pTemplate=\"header\">\r\n\t\t\t\t\t<div class=\"flex flex-column md:flex-row md:justify-content-between gap-2\">\r\n\t\t\t\t\t\t<p-dropdown [options]=\"sortOptionsCountry\" placeholder=\"Country\" (onChange)=\"onSortChange($event)\"></p-dropdown>\r\n\t\t\t\t\t\t<p-dropdown [options]=\"sortOptionsStatus\" placeholder=\"Status\" (onChange)=\"onSortChange($event)\"></p-dropdown>\r\n\t\t\t\t\t\t<span class=\"p-input-icon-left\">\r\n                            <i class=\"pi pi-search\"></i>\r\n                            <input type=\"search\" pInputText placeholder=\"Search\" (input)=\"onFilter(dv, $event)\">\r\n                        </span>\t\r\n\t\t\t\t\t\t<!-- <p-dataViewLayoutOptions></p-dataViewLayoutOptions> -->\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</ng-template>\r\n\r\n\t\t\t\t<ng-template let-products pTemplate=\"listItem\">\r\n\t\t\t\t\t<div *ngFor=\"let station of stations\" [class]=\"'station-box-status-' + station.status.toLowerCase()\">\r\n\t\t\t\t\t\t<div class=\"\">\r\n\t\t\t\t\t\t\t<div class=\"grid\">\r\n\t\t\t\t\t\t\t\t<div class=\"col-12 md:col-6 lg:col-3\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"font-bold text-xl\"><a [routerLink]=\"['/app/station',station.id]\">{{station.name}}</a></div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"font-italic\"><fa-icon icon=\"clock\"></fa-icon> {{station.updateTime}} \r\n\t\t\t\t\t\t\t\t\t\t<!-- <span [class]=\"'station-badge status-' + station.status.toLowerCase()\">{{station.status.toLowerCase()}}</span> -->\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"col-12 md:col-6 lg:col-1\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"text-center font-bold\"><fa-icon icon=\"plug\"></fa-icon> {{station.power}}kW</div>\t\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"col-12 md:col-6 lg:col-1\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"text-center font-bold\"><fa-icon icon=\"sun\"></fa-icon>  {{station.irradiance}}kWh/m2</div>\t\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"col-12 md:col-6 lg:col-2\">\r\n\t\t\t\t\t\t\t\t\t<c-chart [data]=\"data[1]\" [options]=\"lineOptions\" type=\"line\" class=\"mx-auto\" height=\"40\" width=\"80\" />\r\n\t\t\t\t\t\t\t\t\t<!-- <div class=\"\">Inverter: {{station.inverter}} | MMPT: {{station.mmpt}} | String: {{station.string}} | PVN: {{station.pvn}}</div> -->\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"col-12 md:col-6 lg:col-3 text-center\">\r\n\t\t\t\t\t\t\t\t\t<p-badge class=\"pad-2 \" value=\"{{station.inverter}}\" size=\"large\"></p-badge>\r\n\t\t\t\t\t\t\t\t\t<p-badge class=\"pad-2\" value=\"{{station.mmpt}}\" size=\"large\"  severity=\"success\"></p-badge>\r\n\t\t\t\t\t\t\t\t\t<p-badge class=\"pad-2\" value=\"{{station.string}}\" size=\"large\" severity=\"info\"></p-badge>\r\n\t\t\t\t\t\t\t\t\t<p-badge class=\"pad-2\" value=\"{{station.pvn}}\" size=\"large\" severity=\"warning\"></p-badge>\r\n\t\t\t\t\t\t\t\t\t<!-- <div class=\"grid\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"col-3 md:col-3 lg:col-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\">Inverter</p>\r\n\t\t\t\t\t\t\t\t\t\t\t<p-badge [value]=\"2\" severity=\"success\" />\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"col-3 md:col-3 lg:col-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\">MMPT</p>\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\"><p-knob [(ngModel)]=\"station.mmpt\" valueTemplate=\"{{station.mmpt}}\" [step]=\"1\" [min]=\"0\" [max]=\"5\" [size]=\"50\"></p-knob></p>\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"col-3 md:col-3 lg:col-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\">String</p>\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\"><p-knob [(ngModel)]=\"station.string\" valueTemplate=\"{{station.string}}\" [step]=\"1\" [min]=\"0\" [max]=\"5\" [size]=\"50\"></p-knob></p>\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"col-3 md:col-3 lg:col-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\">PVN</p>\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\"><p-knob [(ngModel)]=\"station.pvn\" valueTemplate=\"{{station.pvn}}\" [step]=\"1\" [min]=\"0\" [max]=\"5\" [size]=\"50\"></p-knob></p>\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\t -->\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"col-12 md:col-6 lg:col-2 text-center\">\r\n\t\t\t\t\t\t\t\t\t<p class=\"text-center\"><i class=\"pi pi-cloud text-2xl\"></i>\r\n\t\t\t\t\t\t\t\t\t<span class=\"text-2xl mb-2 align-self-center md:align-self-end\"> {{station.temperature}} &#x2103;</span></p>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<!-- <div class=\"card\">\r\n\t\t\t\t\t\t\t<div class=\"flex flex-column md:flex-row align-items-center p-3 w-full\">\r\n\t\t\t\t\t\t\t\t<div class=\"flex-1 flex flex-column md:text-left\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"flex align-items-center\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"font-bold text-xl\"><a [routerLink]=\"['/uikit/station',station.id]\">{{station.name}}</a></div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"font-bold flex-auto text-xl align-items-right text-right\">{{station.updateTime}} <span [class]=\"'station-badge status-' + station.status.toLowerCase()\">{{station.status.toLowerCase()}}</span></div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<hr>\r\n\t\t\t\t\t\t\t\t\t<div class=\"mb-2\">{{station.location}}</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"flex align-items-center mt-2\">\r\n\t\t\t\t\t\t\t\t\t\t<p-knob [(ngModel)]=\"station.percentage\" valueTemplate=\"{value}%\" [step]=\"10\" [min]=\"0\" [max]=\"100\" [size]=\"70\"></p-knob>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"align-items-right\">\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"mt-2 text-xl\">Active Performance</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-2 text-sm\">Power: {{station.power}}kW | Irradiance: {{station.irradiance}}kWh/m2</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"mt-2\">Inverter: {{station.inverter}} | MMPT: {{station.mmpt}} | String: {{station.string}} | PVN: {{station.pvn}}</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"flex flex-row md:flex-column justify-content-between w-full md:w-auto align-items-center md:align-items-end mt-5 md:mt-0\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"pi pi-cloud text-2xl\"></i>\r\n\t\t\t\t\t\t\t\t\t\t<span class=\"text-2xl font-semibold mb-2 align-self-center md:align-self-end\">{{station.temperature}} &#x2103;</span>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div> -->\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</ng-template>\r\n\r\n\t\t\t\t<!-- <ng-template let-products pTemplate=\"listItem\">\r\n\t\t\t\t\t<div class=\"col-12\" *ngFor=\"let station of stations\">\r\n\t\t\t\t\t\t<div class=\"card\">\r\n\t\t\t\t\t\t\t<div class=\"flex flex-column md:flex-row align-items-center p-3 w-full\">\r\n\t\t\t\t\t\t\t\t<div class=\"flex-1 flex flex-column md:text-left\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"flex align-items-center\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"font-bold text-xl\"><a [routerLink]=\"['/uikit/station',station.id]\">{{station.name}}</a></div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"font-bold flex-auto text-xl align-items-right text-right\">{{station.updateTime}} <span [class]=\"'station-badge status-' + station.status.toLowerCase()\">{{station.status.toLowerCase()}}</span></div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<hr>\r\n\t\t\t\t\t\t\t\t\t<div class=\"mb-2\">{{station.location}}</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"flex align-items-center mt-2\">\r\n\t\t\t\t\t\t\t\t\t\t<p-knob [(ngModel)]=\"station.percentage\" valueTemplate=\"{value}%\" [step]=\"10\" [min]=\"0\" [max]=\"100\" [size]=\"70\"></p-knob>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"align-items-right\">\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"mt-2 text-xl\">Active Performance</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-2 text-sm\">Power: {{station.power}}kW | Irradiance: {{station.irradiance}}kWh/m2</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"mt-2\">Inverter: {{station.inverter}} | MMPT: {{station.mmpt}} | String: {{station.string}} | PVN: {{station.pvn}}</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"flex flex-row md:flex-column justify-content-between w-full md:w-auto align-items-center md:align-items-end mt-5 md:mt-0\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"pi pi-cloud text-2xl\"></i>\r\n\t\t\t\t\t\t\t\t\t\t<span class=\"text-2xl font-semibold mb-2 align-self-center md:align-self-end\">{{station.temperature}} &#x2103;</span>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</ng-template> -->\r\n\r\n\t\t\t\t<!-- <ng-template let-products pTemplate=\"gridItem\">\r\n\t\t\t\t\t<div class=\"grid grid-nogutter\">\r\n\t\t\t\t\t<div class=\"col-12 md:col-4\" *ngFor=\"let station of stations\">\r\n\t\t\t\t\t\t<div class=\"card m-3 border-1 surface-border\">\r\n\t\t\t\t\t\t\t<div class=\"flex flex-wrap gap-2 align-items-center justify-content-between mb-2\">\r\n\t\t\t\t\t\t\t\t<div class=\"flex align-items-center\">\r\n\t\t\t\t\t\t\t\t\t<i class=\"pi pi-tag mr-2\"></i>\r\n\t\t\t\t\t\t\t\t\t<span class=\"font-semibold\">{{station.name}}</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"flex flex-column align-items-center text-center mb-3\">\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<div class=\"text-2xl font-bold\">{{station.name}}</div>\r\n\t\t\t\t\t\t\t\t<div class=\"mb-3\">{{station.location}}</div>\r\n\t\t\t\t\t\t\t\t<p-rating [ngModel]=\"station.temperature\" [readonly]=\"true\" [cancel]=\"false\"></p-rating>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"flex align-items-center justify-content-between\">\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</ng-template> -->\r\n\t\t\t</p-dataView>\r\n\t\t</div>\r\n\t</div>\r\n\r\n    <!-- <div class=\"col-12 lg:col-6 xl:col-6\">\r\n\t\t<div class=\"card\">\r\n\t\t\t<img src=\"https://www.digitaltrends.com/wp-content/uploads/2020/02/custom-map.jpg?fit=720%2C479&p=1\" >\r\n\t\t</div>\r\n\t</div> -->\r\n</div>"], "mappings": "AAIA,SAAuBA,YAAY,QAAQ,MAAM;AASjD,SAA4CC,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;ICoB1EC,EAAA,CAAAC,cAAA,cAAmE;IAElED,EAAA,CAAAE,MAAA,oFACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAYAH,EAAA,CAAAC,cAAA,iBAA4D;IAAAD,EAAA,CAAAE,MAAA,GAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAApCH,EAAA,CAAAI,UAAA,UAAAC,IAAA,CAAAC,EAAA,CAAc;IAACN,EAAA,CAAAO,SAAA,GAAY;IAAZP,EAAA,CAAAQ,iBAAA,CAAAH,IAAA,CAAAI,IAAA,CAAY;;;;;IAN1ET,EAAA,CAAAC,cAAA,cAA2H;IAIrFD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtDH,EAAA,CAAAC,cAAA,iBAAuE;IACrED,EAAA,CAAAU,UAAA,IAAAC,6CAAA,qBAAiF;IACnFX,EAAA,CAAAG,YAAA,EAAS;IAIRH,EAAA,CAAAC,cAAA,UAAK;IACgCD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtDH,EAAA,CAAAY,SAAA,iBAA6G;IAC5GZ,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IACgCD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtDH,EAAA,CAAAY,SAAA,iBAA6G;IAC5GZ,EAAA,CAAAG,YAAA,EAAM;IAEPH,EAAA,CAAAC,cAAA,WAAK;IACkCD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtDH,EAAA,CAAAY,SAAA,yBAAiK;IAClKZ,EAAA,CAAAG,YAAA,EAAM;;;;;IAzB0DH,EAAA,CAAAI,UAAA,kBAAAS,IAAA,CAAmB;IAM3Db,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAI,UAAA,YAAAU,MAAA,CAAAC,kBAAA,CAAqB;IAkB7Bf,EAAA,CAAAO,SAAA,IAA2B;IAA3BP,EAAA,CAAAI,UAAA,YAAAU,MAAA,CAAAE,eAAA,CAA2B;;;;;;IAnC/ChB,EAAA,CAAAC,cAAA,cAAyE;IAEtED,EAAA,CAAAU,UAAA,IAAAO,oCAAA,kBAIM;IAGNjB,EAAA,CAAAC,cAAA,eAA8D;IAAxBD,EAAA,CAAAkB,UAAA,sBAAAC,wDAAA;MAAAnB,EAAA,CAAAoB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAAYvB,EAAA,CAAAwB,WAAA,CAAAF,MAAA,CAAAG,QAAA,EAAU;IAAA,EAAC;IAC9DzB,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAAU,UAAA,IAAAgB,oCAAA,mBA4BK;IAGN1B,EAAA,CAAAC,cAAA,iBAA4E;IAA5BD,EAAA,CAAAkB,UAAA,mBAAAS,uDAAA;MAAA3B,EAAA,CAAAoB,aAAA,CAAAC,GAAA;MAAA,MAAAO,OAAA,GAAA5B,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAI,OAAA,CAAAC,cAAA,CAAAD,OAAA,CAAAE,CAAA,CAAiB;IAAA,EAAC;IACzE9B,EAAA,CAAAE,MAAA,eACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAE2B;IAAzBD,EAAA,CAAAkB,UAAA,mBAAAa,uDAAA;MAAA/B,EAAA,CAAAoB,aAAA,CAAAC,GAAA;MAAA,MAAAW,OAAA,GAAAhC,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAQ,OAAA,CAAAC,WAAA,CAAAD,OAAA,CAAAF,CAAA,CAAc;IAAA,EAAC;IACxB9B,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAKVH,EAAA,CAAAC,cAAA,kBAA8F;IAAxBD,EAAA,CAAAkB,UAAA,mBAAAgB,wDAAA;MAAAlC,EAAA,CAAAoB,aAAA,CAAAC,GAAA;MAAA,MAAAc,OAAA,GAAAnC,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAW,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAC3FpC,EAAA,CAAAE,MAAA,wBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,kBAAuE;IACrED,EAAA,CAAAE,MAAA,wBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IA3DFH,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAI,UAAA,SAAAiC,MAAA,CAAAC,aAAA,CAAAC,MAAA,MAA+B;IAO/BvC,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAI,UAAA,cAAAiC,MAAA,CAAAG,iBAAA,CAA+B;IAEVxC,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAI,UAAA,YAAAiC,MAAA,CAAAI,SAAA,CAAAC,QAAA,CAAuB;IAmChD1C,EAAA,CAAAO,SAAA,GAA+C;IAA/CP,EAAA,CAAAI,UAAA,gBAAAuC,OAAA,GAAAN,MAAA,CAAAO,QAAA,CAAAC,GAAA,iCAAAF,OAAA,CAAAG,KAAA,EAA+C;;;;;;IAyBhD9C,EAAA,CAAAC,cAAA,cAA2E;IACTD,EAAA,CAAAkB,UAAA,sBAAA6B,4EAAAC,MAAA;MAAAhD,EAAA,CAAAoB,aAAA,CAAA6B,IAAA;MAAA,MAAAC,OAAA,GAAAlD,EAAA,CAAAuB,aAAA;MAAA,OAAYvB,EAAA,CAAAwB,WAAA,CAAA0B,OAAA,CAAAC,YAAA,CAAAH,MAAA,CAAoB;IAAA,EAAC;IAAChD,EAAA,CAAAG,YAAA,EAAa;IAChHH,EAAA,CAAAC,cAAA,qBAAiG;IAAlCD,EAAA,CAAAkB,UAAA,sBAAAkC,4EAAAJ,MAAA;MAAAhD,EAAA,CAAAoB,aAAA,CAAA6B,IAAA;MAAA,MAAAI,OAAA,GAAArD,EAAA,CAAAuB,aAAA;MAAA,OAAYvB,EAAA,CAAAwB,WAAA,CAAA6B,OAAA,CAAAF,YAAA,CAAAH,MAAA,CAAoB;IAAA,EAAC;IAAChD,EAAA,CAAAG,YAAA,EAAa;IAC9GH,EAAA,CAAAC,cAAA,eAAgC;IACVD,EAAA,CAAAY,SAAA,YAA4B;IAC5BZ,EAAA,CAAAC,cAAA,gBAAoF;IAA/BD,EAAA,CAAAkB,UAAA,mBAAAoC,oEAAAN,MAAA;MAAAhD,EAAA,CAAAoB,aAAA,CAAA6B,IAAA;MAAAjD,EAAA,CAAAuB,aAAA;MAAA,MAAAgC,IAAA,GAAAvD,EAAA,CAAAwD,WAAA;MAAA,MAAAC,OAAA,GAAAzD,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAiC,OAAA,CAAAC,QAAA,CAAAH,IAAA,EAAAP,MAAA,CAAoB;IAAA,EAAC;IAAnFhD,EAAA,CAAAG,YAAA,EAAoF;;;;IAJ9FH,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAI,UAAA,YAAAuD,OAAA,CAAAC,kBAAA,CAA8B;IAC9B5D,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAI,UAAA,YAAAuD,OAAA,CAAAE,iBAAA,CAA6B;;;;;;IAU1C7D,EAAA,CAAAC,cAAA,UAAqG;IAIpBD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACjGH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAY,SAAA,kBAAgC;IAACZ,EAAA,CAAAE,MAAA,GACzD;IACDF,EAAA,CAAAG,YAAA,EAAM;IAEPH,EAAA,CAAAC,cAAA,eAAsC;IACFD,EAAA,CAAAY,SAAA,kBAA+B;IAACZ,EAAA,CAAAE,MAAA,IAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAE7FH,EAAA,CAAAC,cAAA,eAAsC;IACFD,EAAA,CAAAY,SAAA,mBAA8B;IAAEZ,EAAA,CAAAE,MAAA,IAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEtGH,EAAA,CAAAC,cAAA,eAAsC;IACrCD,EAAA,CAAAY,SAAA,mBAAuG;IAExGZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAkD;IACjDD,EAAA,CAAAY,SAAA,mBAA4E;IAyB7EZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAkD;IAC1BD,EAAA,CAAAY,SAAA,aAAoC;IAC3DZ,EAAA,CAAAC,cAAA,gBAAgE;IAACD,EAAA,CAAAE,MAAA,IAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAhDtEH,EAAA,CAAA8D,UAAA,yBAAAC,WAAA,CAAAC,MAAA,CAAAC,WAAA,GAA8D;IAI9DjE,EAAA,CAAAO,SAAA,GAA0C;IAA1CP,EAAA,CAAAI,UAAA,eAAAJ,EAAA,CAAAkE,eAAA,KAAAC,GAAA,EAAAJ,WAAA,CAAAzD,EAAA,EAA0C;IAACN,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAQ,iBAAA,CAAAuD,WAAA,CAAAtD,IAAA,CAAgB;IACnCT,EAAA,CAAAO,SAAA,GACzD;IADyDP,EAAA,CAAAoE,kBAAA,MAAAL,WAAA,CAAAM,UAAA,MACzD;IAIkErE,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAoE,kBAAA,MAAAL,WAAA,CAAAO,KAAA,OAAmB;IAGnBtE,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAoE,kBAAA,MAAAL,WAAA,CAAAQ,UAAA,WAA4B;IAGtFvE,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAI,UAAA,SAAAoE,OAAA,CAAAC,IAAA,IAAgB,YAAAD,OAAA,CAAAE,WAAA;IAID1E,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAA2E,qBAAA,UAAAZ,WAAA,CAAAa,QAAA,CAA4B;IAC7B5E,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAA2E,qBAAA,UAAAZ,WAAA,CAAAc,IAAA,CAAwB;IACxB7E,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAA2E,qBAAA,UAAAZ,WAAA,CAAAe,MAAA,CAA0B;IAC1B9E,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAA2E,qBAAA,UAAAZ,WAAA,CAAAgB,GAAA,CAAuB;IAyBmB/E,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAAoE,kBAAA,MAAAL,WAAA,CAAAiB,WAAA,YAAgC;;;;;IAhDrGhF,EAAA,CAAAU,UAAA,IAAAuE,kDAAA,oBA6EM;;;;IA7EmBjF,EAAA,CAAAI,UAAA,YAAA8E,OAAA,CAAAC,QAAA,CAAW;;;;;IAhBrCnF,EAAA,CAAAC,cAAA,cAAwE;IAGxED,EAAA,CAAAU,UAAA,IAAA0E,4CAAA,0BAUc,IAAAC,4CAAA;IAyIfrF,EAAA,CAAAG,YAAA,EAAa;;;;IApJGH,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAI,UAAA,UAAAkF,MAAA,CAAAH,QAAA,CAAkB,4CAAAG,MAAA,CAAAC,SAAA,eAAAD,MAAA,CAAAE,SAAA;;;;;;;ADhFrC,OAAM,MAAOC,cAAc;EAoCvBC,YAAmBC,aAA4B,EACnCC,eAAgC,EAChCC,gBAAkC,EAClCC,cAA8B,EAC9BC,EAAe,EACfC,YAA0B;IALnB,KAAAL,aAAa,GAAbA,aAAa;IACpB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,YAAY,GAAZA,YAAY;IArCxB,KAAAb,QAAQ,GAAa,EAAE;IACvB,KAAApE,kBAAkB,GAAe,EAAE;IACnC,KAAAuB,aAAa,GAAmB,EAAE;IASlC,KAAAsB,kBAAkB,GAAiB,EAAE;IAErC,KAAAC,iBAAiB,GAAiB,EAAE;IAEpC,KAAA2B,SAAS,GAAW,CAAC;IAErB,KAAAD,SAAS,GAAW,EAAE;IAEtB,KAAAU,YAAY,GAAU,EAAE;IAExB,KAAAC,YAAY,GAAU,EAAE;IAExB,KAAAC,WAAW,GAAU,EAAE;IAenB,IAAI,CAACC,YAAY,GAAG,IAAI,CAACT,aAAa,CAACU,aAAa,CACnDC,IAAI,CAACxG,YAAY,CAAC,EAAE,CAAC,CAAC,CACtByG,SAAS,CAAEC,MAAM,IAAI;MACrB;IAAA,CACA,CAAC;IAGF,IAAI,CAAChE,iBAAiB,GAAG,IAAI,CAACuD,EAAE,CAACU,KAAK,CAAC;MACrChE,SAAS,EAAE,IAAI,CAACsD,EAAE,CAACW,KAAK,CAAC,EAAE;KAC5B,CAAC;EACN;EAEAC,QAAQA,CAAA;IACJ,IAAI,CAACd,gBAAgB,CAACe,YAAY,EAAE,CAACC,IAAI,CAACpC,IAAI,IAAG;MAC7C,IAAI,CAAC1D,kBAAkB,GAAG0D,IAAI;IAChC,CAAC,CAAC;IAEF,IAAI,CAACoB,gBAAgB,CAACiB,gBAAgB,EAAE,CAACD,IAAI,CAACpC,IAAI,IAAG;MACnD,IAAI,CAACnC,aAAa,GAAGmC,IAAI;IAC3B,CAAC,CAAC;IAEF,IAAI,CAACrC,WAAW,EAAE,CAAC,CAAC;IAEtB;IACA;IACA;IACA;IACA,IAAI,CAAC6D,YAAY,GAAG,CAChB;MAAExF,IAAI,EAAE,eAAe;MAAEsG,IAAI,EAAE;IAAI,CAAE,EACrC;MAAEtG,IAAI,EAAE,QAAQ;MAAEsG,IAAI,EAAE;IAAK,CAAE,EAC/B;MAAEtG,IAAI,EAAE,OAAO;MAAEsG,IAAI,EAAE;IAAK,CAAE,EAC9B;MAAEtG,IAAI,EAAE,UAAU;MAAEsG,IAAI,EAAE;IAAK,CAAE,EACjC;MAAEtG,IAAI,EAAE,QAAQ;MAAEsG,IAAI,EAAE;IAAK,CAAE,EAC/B;MAAEtG,IAAI,EAAE,WAAW;MAAEsG,IAAI,EAAE;IAAK,CAAE,EAClC;MAAEtG,IAAI,EAAE,MAAM;MAAEsG,IAAI,EAAE;IAAI,CAAE,CAAC;IAEjC,IAAI,CAACb,YAAY,GAAG,EAAE;IAEtB,IAAI,CAACC,WAAW,GAAG,CACf;MAAE1F,IAAI,EAAE,eAAe;MAAEsG,IAAI,EAAE;IAAI,CAAE,EACrC;MAAEtG,IAAI,EAAE,QAAQ;MAAEsG,IAAI,EAAE;IAAK,CAAE,EAC/B;MAAEtG,IAAI,EAAE,OAAO;MAAEsG,IAAI,EAAE;IAAK,CAAE,EAC9B;MAAEtG,IAAI,EAAE,UAAU;MAAEsG,IAAI,EAAE;IAAK,CAAE,EACjC;MAAEtG,IAAI,EAAE,QAAQ;MAAEsG,IAAI,EAAE;IAAK,CAAE,EAC/B;MAAEtG,IAAI,EAAE,WAAW;MAAEsG,IAAI,EAAE;IAAK,CAAE,EAClC;MAAEtG,IAAI,EAAE,MAAM;MAAEsG,IAAI,EAAE;IAAI,CAAE,CAAC;IAEjC;IACA;IACA;IACA;IAGA,IAAI,CAACC,UAAU,GAAG;MACdC,mBAAmB,EAAE,KAAK;MAC1BC,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE;;OAEZ;MACDC,MAAM,EAAE;QACNC,CAAC,EAAE;UACDF,OAAO,EAAE;SACV;QACDG,CAAC,EAAE;UACDH,OAAO,EAAE;;;KAGd;IAED,IAAI,CAAC1C,WAAW,GAAG;MACjBuC,mBAAmB,EAAE,KAAK;MAC1BO,QAAQ,EAAE;QACRC,IAAI,EAAE;UACJC,OAAO,EAAE;SACV;QACDC,KAAK,EAAE;UACLC,MAAM,EAAE;;OAEX;MACDV,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE;;OAEZ;MACDC,MAAM,EAAE;QACNC,CAAC,EAAE;UACDF,OAAO,EAAE;SACV;QACDG,CAAC,EAAE;UACDH,OAAO,EAAE;;;KAGd;IAED,IAAI,CAAC3C,IAAI,GAAG,CACV;MACEoD,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MACnFC,QAAQ,EAAE,CACR;QACEC,eAAe,EAAE,SAAS;QAC1BC,WAAW,EAAE,aAAa;QAC1BC,WAAW,EAAE,CAAC;QACdxD,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;OAClE;KAEJ,EAAE;MACDoD,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MACnFC,QAAQ,EAAE,CACR;QACEC,eAAe,EAAE,aAAa;QAC9BC,WAAW,EAAE,SAAS;QACtBC,WAAW,EAAE,CAAC;QACdxD,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QAClEyD,oBAAoB,EAAE;OACvB;KAEJ,CACF;EACP;EAEA9F,WAAWA,CAAA;IACT,MAAM+F,aAAa,GAAG,IAAI,CAACpC,EAAE,CAACU,KAAK,CAAC;MAClC2B,UAAU,EAAE,CAAC,EAAE,EAAErI,UAAU,CAACsI,QAAQ,CAAC;MACrCC,QAAQ,EAAE,CAAC,EAAE,EAAEvI,UAAU,CAACsI,QAAQ,CAAC;MACnCE,QAAQ,EAAE,CAAC,EAAE,EAAExI,UAAU,CAACsI,QAAQ,CAAC;MACnClD,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;KAChB,CAAC;;IACF,IAAI,CAAC1C,SAAS,CAAC+F,IAAI,CAACL,aAAa,CAAC;EACpC;EAEA,IAAI1F,SAASA,CAAA;IACX,OAAO,IAAI,CAACD,iBAAiB,CAACK,GAAG,CAAC,WAAW,CAAc;EAC7D;EAIAhB,cAAcA,CAAC4G,KAAa;IAC1B,IAAI,CAAChG,SAAS,CAACiG,QAAQ,CAACD,KAAK,CAAC;EAChC;EAEAxG,WAAWA,CAACwG,KAAa;IACvB,MAAML,UAAU,GAAG,IAAI,CAAC3F,SAAS,CAACkG,EAAE,CAACF,KAAK,CAAC,CAAC5F,GAAG,CAAC,YAAY,CAAC,EAAEC,KAAK;IAEpE,IAAI,CAACsF,UAAU,EAAE;IAEjB,IAAI,CAACxC,eAAe,CAAC3D,WAAW,EAAE,CAAC4E,IAAI,CAACpC,IAAI,IAAG;MAC7C,IAAI,CAAChC,SAAS,CAACkG,EAAE,CAACF,KAAK,CAAC,CAACG,UAAU,CAAC;QAAEnE;MAAI,CAAE,CAAC;IAC/C,CAAC,CAAC;EACJ;EAEAhD,QAAQA,CAAA;IACN,IAAI,IAAI,CAACe,iBAAiB,CAACqG,KAAK,EAAE;MAChCC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACvG,iBAAiB,CAACM,KAAK,CAACL,SAAS,CAAC;MACjE;MACA,IAAIuG,OAAO,GAA4B;QACrCvG,SAAS,EAAG,IAAI,CAACD,iBAAiB,CAACM,KAAK,CAACL;OAC1C;MAED,IAAI,CAACoD,gBAAgB,CAACoD,iBAAiB,CAACD,OAAO,CAAC,CAACnC,IAAI,CAACpC,IAAI,IAAG;QAC3DqE,OAAO,CAACC,GAAG,CAACtE,IAAI,CAAC;MACnB,CAAC,CAAC;;EAGN;EAEAtB,YAAYA,CAAC+F,KAAU;IACnB,MAAMpG,KAAK,GAAGoG,KAAK,CAACpG,KAAK;IAEzB,IAAIA,KAAK,CAACqG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;MAC1B,IAAI,CAAC3D,SAAS,GAAG,CAAC,CAAC;MACnB,IAAI,CAACD,SAAS,GAAGzC,KAAK,CAACsG,SAAS,CAAC,CAAC,EAAEtG,KAAK,CAACP,MAAM,CAAC;KACpD,MAAM;MACH,IAAI,CAACiD,SAAS,GAAG,CAAC;MAClB,IAAI,CAACD,SAAS,GAAGzC,KAAK;;EAE9B;EAEAY,QAAQA,CAAC2F,EAAY,EAAEH,KAAY;IAC/BG,EAAE,CAACC,MAAM,CAAEJ,KAAK,CAACK,MAA2B,CAACzG,KAAK,CAAC;EACvD;EAGA0G,OAAOA,CAAA;IACH,IAAI,CAACC,MAAM,GAAG,2FAA2F;EAC7G;EAEA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEAC,WAAWA,CAAA;IACP,IAAI,IAAI,CAACtD,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAACuD,WAAW,EAAE;;EAEvC;EAAC,QAAAC,CAAA,G;qBApSQnE,cAAc,EAAAzF,EAAA,CAAA6J,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA/J,EAAA,CAAA6J,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAjK,EAAA,CAAA6J,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAnK,EAAA,CAAA6J,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAArK,EAAA,CAAA6J,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAAvK,EAAA,CAAA6J,iBAAA,CAAAW,EAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAdjF,cAAc;IAAAkF,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCpB3BjL,EAAA,CAAAC,cAAA,aAAkB;QAK0DD,EAAA,CAAAE,MAAA,4BAAqB;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACpFH,EAAA,CAAAC,cAAA,aAA0C;QAAAD,EAAA,CAAAE,MAAA,SAAE;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAEtDH,EAAA,CAAAC,cAAA,aAAqI;QACjID,EAAA,CAAAY,SAAA,kBAAsC;QAC1CZ,EAAA,CAAAG,YAAA,EAAM;QAMlBH,EAAA,CAAAC,cAAA,cAAsC;QAIkCD,EAAA,CAAAE,MAAA,kCAA0B;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACzFH,EAAA,CAAAC,cAAA,cAA0C;QAAAD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAElEH,EAAA,CAAAC,cAAA,cAAqI;QACjID,EAAA,CAAAY,SAAA,kBAA+B;QACnCZ,EAAA,CAAAG,YAAA,EAAM;QAMrBH,EAAA,CAAAU,UAAA,KAAAyK,8BAAA,kBAgEQ,KAAAC,8BAAA;QAkKTpL,EAAA,CAAAG,YAAA,EAAM;;;QAzP+FH,EAAA,CAAAO,SAAA,GAA+C;QAA/CP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAqL,eAAA,IAAAC,GAAA,EAA+C;QAe/CtL,EAAA,CAAAO,SAAA,IAA+C;QAA/CP,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAqL,eAAA,IAAAC,GAAA,EAA+C;QAQ1GtL,EAAA,CAAAO,SAAA,GAA8B;QAA9BP,EAAA,CAAAI,UAAA,SAAA8K,GAAA,CAAA5I,aAAA,CAAAC,MAAA,MAA8B;QAmE3BvC,EAAA,CAAAO,SAAA,GAA6B;QAA7BP,EAAA,CAAAI,UAAA,SAAA8K,GAAA,CAAA5I,aAAA,CAAAC,MAAA,KAA6B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}