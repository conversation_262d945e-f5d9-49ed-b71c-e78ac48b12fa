{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./service/app.layout.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/inputtext\";\nimport * as i6 from \"primeng/tooltip\";\nconst _c0 = [\"menubutton\"];\nconst _c1 = [\"topbarmenubutton\"];\nconst _c2 = [\"topbarmenu\"];\nconst _c3 = a0 => ({\n  \"layout-topbar-menu-mobile-active\": a0\n});\nexport let AppTopBarComponent = /*#__PURE__*/(() => {\n  class AppTopBarComponent {\n    constructor(layoutService, router) {\n      this.layoutService = layoutService;\n      this.router = router;\n      this.searchQuery = '';\n    }\n    onSearch() {\n      if (this.searchQuery.trim()) {\n        // Implement search functionality\n        console.log('Searching for:', this.searchQuery);\n        // You can navigate to a search results page or filter current data\n        // this.router.navigate(['/app/search'], { queryParams: { q: this.searchQuery } });\n      }\n    }\n\n    navigateToHelp() {\n      this.router.navigate(['/app/help']);\n    }\n    navigateToProfile() {\n      this.router.navigate(['/app/profile']);\n    }\n    toggleTheme() {\n      const currentTheme = this.layoutService.config().colorScheme;\n      const newTheme = currentTheme === 'light' ? 'dark' : 'light';\n      this.layoutService.config.update(config => ({\n        ...config,\n        colorScheme: newTheme,\n        theme: newTheme === 'light' ? 'lara-light-indigo' : 'lara-dark-indigo'\n      }));\n    }\n    static #_ = this.ɵfac = function AppTopBarComponent_Factory(t) {\n      return new (t || AppTopBarComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppTopBarComponent,\n      selectors: [[\"app-topbar\"]],\n      viewQuery: function AppTopBarComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menuButton = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.topbarMenuButton = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menu = _t.first);\n        }\n      },\n      decls: 57,\n      vars: 8,\n      consts: [[1, \"layout-topbar\"], [1, \"p-link\", \"layout-menu-button\", \"layout-topbar-button\", 3, \"click\"], [\"menubutton\", \"\"], [1, \"pi\", \"pi-bars\"], [1, \"layout-topbar-logo-section\"], [\"routerLink\", \"/app/index\", 1, \"layout-topbar-logo\"], [\"alt\", \"SolarKapital\", 3, \"src\"], [1, \"logo-text\"], [1, \"layout-topbar-search\"], [1, \"p-input-icon-left\"], [1, \"pi\", \"pi-search\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"Search stations, devices...\", 1, \"search-input\", 3, \"ngModel\", \"ngModelChange\", \"keyup.enter\"], [1, \"layout-topbar-actions\"], [\"pTooltip\", \"Notifications\", \"tooltipPosition\", \"bottom\", 1, \"p-link\", \"layout-topbar-button\", \"notification-button\"], [1, \"pi\", \"pi-bell\"], [1, \"notification-badge\"], [\"pTooltip\", \"Help & FAQ\", \"tooltipPosition\", \"bottom\", 1, \"p-link\", \"layout-topbar-button\", \"help-button\", 3, \"click\"], [1, \"pi\", \"pi-question-circle\"], [\"pTooltip\", \"Toggle Theme\", \"tooltipPosition\", \"bottom\", 1, \"p-link\", \"layout-topbar-button\", \"theme-toggle\", 3, \"click\"], [1, \"pi\", 3, \"ngClass\"], [\"pTooltip\", \"User Profile\", \"tooltipPosition\", \"bottom\", 1, \"p-link\", \"layout-topbar-button\", \"user-profile-btn\", 3, \"click\"], [1, \"pi\", \"pi-user\"], [\"pTooltip\", \"Profile Menu\", \"tooltipPosition\", \"bottom\", 1, \"p-link\", \"layout-topbar-menu-button\", \"layout-topbar-button\", \"profile-button\", 3, \"click\"], [\"topbarmenubutton\", \"\"], [1, \"profile-avatar\"], [1, \"layout-topbar-menu\", 3, \"ngClass\"], [\"topbarmenu\", \"\"], [1, \"topbar-menu-header\"], [1, \"user-info\"], [1, \"user-avatar\"], [1, \"user-details\"], [1, \"user-name\"], [1, \"user-email\"], [1, \"topbar-menu-items\"], [1, \"p-link\", \"layout-topbar-button\", \"menu-item\"], [1, \"pi\", \"pi-calendar\"], [1, \"p-link\", \"layout-topbar-button\", \"menu-item\", 3, \"routerLink\"], [1, \"pi\", \"pi-cog\"], [1, \"menu-divider\"], [1, \"p-link\", \"layout-topbar-button\", \"menu-item\", \"logout-item\"], [1, \"pi\", \"pi-power-off\"]],\n      template: function AppTopBarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"button\", 1, 2);\n          i0.ɵɵlistener(\"click\", function AppTopBarComponent_Template_button_click_1_listener() {\n            return ctx.layoutService.onMenuToggle();\n          });\n          i0.ɵɵelement(3, \"i\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"a\", 5);\n          i0.ɵɵelement(6, \"img\", 6);\n          i0.ɵɵelementStart(7, \"span\", 7);\n          i0.ɵɵtext(8, \"SolarKapital\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"div\", 8)(10, \"div\", 9);\n          i0.ɵɵelement(11, \"i\", 10);\n          i0.ɵɵelementStart(12, \"input\", 11);\n          i0.ɵɵlistener(\"ngModelChange\", function AppTopBarComponent_Template_input_ngModelChange_12_listener($event) {\n            return ctx.searchQuery = $event;\n          })(\"keyup.enter\", function AppTopBarComponent_Template_input_keyup_enter_12_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 12)(14, \"button\", 13);\n          i0.ɵɵelement(15, \"i\", 14);\n          i0.ɵɵelementStart(16, \"span\", 15);\n          i0.ɵɵtext(17, \"3\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function AppTopBarComponent_Template_button_click_18_listener() {\n            return ctx.navigateToHelp();\n          });\n          i0.ɵɵelement(19, \"i\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function AppTopBarComponent_Template_button_click_20_listener() {\n            return ctx.toggleTheme();\n          });\n          i0.ɵɵelement(21, \"i\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function AppTopBarComponent_Template_button_click_22_listener() {\n            return ctx.navigateToProfile();\n          });\n          i0.ɵɵelement(23, \"i\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"button\", 22, 23);\n          i0.ɵɵlistener(\"click\", function AppTopBarComponent_Template_button_click_24_listener() {\n            return ctx.layoutService.showProfileSidebar();\n          });\n          i0.ɵɵelementStart(26, \"div\", 24);\n          i0.ɵɵelement(27, \"i\", 21);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(28, \"div\", 25, 26)(30, \"div\", 27)(31, \"div\", 28)(32, \"div\", 29);\n          i0.ɵɵelement(33, \"i\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"div\", 30)(35, \"span\", 31);\n          i0.ɵɵtext(36, \"John Doe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"span\", 32);\n          i0.ɵɵtext(38, \"<EMAIL>\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(39, \"div\", 33)(40, \"button\", 34);\n          i0.ɵɵelement(41, \"i\", 35);\n          i0.ɵɵelementStart(42, \"span\");\n          i0.ɵɵtext(43, \"Calendar\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"button\", 36);\n          i0.ɵɵelement(45, \"i\", 21);\n          i0.ɵɵelementStart(46, \"span\");\n          i0.ɵɵtext(47, \"Profile\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"button\", 36);\n          i0.ɵɵelement(49, \"i\", 37);\n          i0.ɵɵelementStart(50, \"span\");\n          i0.ɵɵtext(51, \"Settings\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(52, \"div\", 38);\n          i0.ɵɵelementStart(53, \"button\", 39);\n          i0.ɵɵelement(54, \"i\", 40);\n          i0.ɵɵelementStart(55, \"span\");\n          i0.ɵɵtext(56, \"Logout\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵpropertyInterpolate1(\"src\", \"assets/layout/images/\", ctx.layoutService.config().colorScheme === \"light\" ? \"logo-dark\" : \"logo-white\", \".png\", i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngClass\", ctx.layoutService.config().colorScheme === \"light\" ? \"pi-moon\" : \"pi-sun\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c3, ctx.layoutService.state.profileSidebarVisible));\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"routerLink\", \"/app/profile\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"routerLink\", \"/app/providers\");\n        }\n      },\n      dependencies: [i3.NgClass, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgModel, i5.InputText, i6.Tooltip, i2.RouterLink],\n      encapsulation: 2\n    });\n  }\n  return AppTopBarComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}