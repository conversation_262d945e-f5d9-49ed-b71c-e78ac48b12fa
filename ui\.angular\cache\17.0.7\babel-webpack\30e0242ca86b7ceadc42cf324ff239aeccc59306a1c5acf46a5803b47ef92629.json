{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../service/auth.service\";\nimport * as i3 from \"../../service/user.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../../../shared/components/modern-breadcrumb/modern-breadcrumb.component\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/inputtext\";\nimport * as i9 from \"primeng/card\";\nimport * as i10 from \"primeng/tag\";\nimport * as i11 from \"primeng/toast\";\nimport * as i12 from \"@fortawesome/angular-fontawesome\";\nfunction ProfileComponent_p_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 21);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_p_button_17_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.toggleEdit());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"p-button\", 23);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_18_Template_p_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.toggleEdit());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"p-button\", 24);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_18_Template_p_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.saveProfile());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"text\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"loading\", ctx_r1.isLoading);\n  }\n}\nfunction ProfileComponent_ng_template_22_p_tag_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-tag\", 30);\n  }\n}\nfunction ProfileComponent_ng_template_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26);\n    i0.ɵɵelement(2, \"fa-icon\", 27);\n    i0.ɵɵelementStart(3, \"h3\", 28);\n    i0.ɵɵtext(4, \"Profile Information\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, ProfileComponent_ng_template_22_p_tag_5_Template, 1, 0, \"p-tag\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isEditing);\n  }\n}\nfunction ProfileComponent_ng_template_23_small_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.getFieldError(\"email\"), \" \");\n  }\n}\nfunction ProfileComponent_ng_template_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"form\", 31)(1, \"div\", 16)(2, \"div\", 32)(3, \"div\", 33)(4, \"label\", 34);\n    i0.ɵɵelement(5, \"fa-icon\", 35);\n    i0.ɵɵtext(6, \" Username \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"input\", 36);\n    i0.ɵɵelementStart(8, \"small\", 37);\n    i0.ɵɵtext(9, \"Username cannot be changed\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 32)(11, \"div\", 33)(12, \"label\", 38);\n    i0.ɵɵelement(13, \"fa-icon\", 39);\n    i0.ɵɵtext(14, \" Email Address \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"input\", 40);\n    i0.ɵɵtemplate(16, ProfileComponent_ng_template_23_small_16_Template, 2, 1, \"small\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 32)(18, \"div\", 33)(19, \"label\", 42);\n    i0.ɵɵelement(20, \"fa-icon\", 43);\n    i0.ɵɵtext(21, \" First Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"input\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 32)(24, \"div\", 33)(25, \"label\", 45);\n    i0.ɵɵelement(26, \"fa-icon\", 43);\n    i0.ɵɵtext(27, \" Last Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(28, \"input\", 46);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r3.profileForm);\n    i0.ɵɵadvance(15);\n    i0.ɵɵclassMap(\"w-full \" + (ctx_r3.getFieldError(\"email\") ? \"ng-invalid ng-dirty\" : \"\"));\n    i0.ɵɵproperty(\"readonly\", !ctx_r3.isEditing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.getFieldError(\"email\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"readonly\", !ctx_r3.isEditing);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"readonly\", !ctx_r3.isEditing);\n  }\n}\nfunction ProfileComponent_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26);\n    i0.ɵɵelement(2, \"fa-icon\", 48);\n    i0.ɵɵelementStart(3, \"h3\", 28);\n    i0.ɵɵtext(4, \"Account Details\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProfileComponent_ng_template_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50)(2, \"div\", 51);\n    i0.ɵɵelement(3, \"fa-icon\", 52);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Member Since\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 53);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 50)(9, \"div\", 51);\n    i0.ɵɵelement(10, \"fa-icon\", 54);\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"Last Login\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 53);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 50)(16, \"div\", 51);\n    i0.ɵɵelement(17, \"fa-icon\", 55);\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19, \"Account Status\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 53);\n    i0.ɵɵelement(21, \"p-tag\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 50)(23, \"div\", 51);\n    i0.ɵɵelement(24, \"fa-icon\", 56);\n    i0.ɵɵelementStart(25, \"span\");\n    i0.ɵɵtext(26, \"Role\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 53);\n    i0.ɵɵelement(28, \"p-tag\", 11);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r5.formatDate(ctx_r5.user.registrationDate));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r5.formatDateTime(ctx_r5.user.lastLogin));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"value\", ctx_r5.user.status || \"Active\")(\"severity\", ctx_r5.getStatusColorByStatus(ctx_r5.user.status || \"Active\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"value\", ctx_r5.user.role)(\"severity\", ctx_r5.getRoleColor(ctx_r5.user.role));\n  }\n}\nexport let ProfileComponent = /*#__PURE__*/(() => {\n  class ProfileComponent {\n    constructor(fb, authService, userService, messageService) {\n      this.fb = fb;\n      this.authService = authService;\n      this.userService = userService;\n      this.messageService = messageService;\n      this.destroy$ = new Subject();\n      this.user = {};\n      this.isEditing = false;\n      this.isLoading = false;\n      this.breadcrumbItems = [{\n        label: 'Dashboard',\n        routerLink: '/app/index'\n      }, {\n        label: 'Settings',\n        routerLink: '/app/settings'\n      }, {\n        label: 'Profile'\n      }];\n      this.initializeForm();\n    }\n    ngOnInit() {\n      this.loadUserData();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    initializeForm() {\n      this.profileForm = this.fb.group({\n        username: [{\n          value: '',\n          disabled: true\n        }],\n        email: ['', [Validators.required, Validators.email]],\n        firstName: [''],\n        lastName: ['']\n      });\n    }\n    loadUserData() {\n      this.isLoading = true;\n      this.userService.getUser().pipe(takeUntil(this.destroy$)).subscribe({\n        next: user => {\n          this.user = {\n            id: user.userId?.toString() || '',\n            username: user.username || '',\n            email: user.email || '',\n            role: user.role || 'User',\n            registrationDate: user.createdAt || new Date().toISOString(),\n            firstName: user.firstName || '',\n            lastName: user.lastName || '',\n            isActive: user.status === 'Active',\n            lastLogin: new Date().toISOString(),\n            status: user.status || 'Active'\n          };\n          // Update form with user data\n          this.profileForm.patchValue({\n            username: this.user.username,\n            email: this.user.email,\n            firstName: this.user.firstName,\n            lastName: this.user.lastName\n          });\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error loading user data:', error);\n          this.messageService.add({\n            severity: 'error',\n            summary: 'Error',\n            detail: 'Failed to load user data. Please try again.'\n          });\n          this.isLoading = false;\n        }\n      });\n    }\n    toggleEdit() {\n      this.isEditing = !this.isEditing;\n      if (!this.isEditing) {\n        // Cancel editing - reset form\n        this.profileForm.patchValue({\n          email: this.user.email,\n          firstName: this.user.firstName,\n          lastName: this.user.lastName\n        });\n      }\n    }\n    saveProfile() {\n      if (this.profileForm.invalid) {\n        this.markFormGroupTouched();\n        return;\n      }\n      this.isLoading = true;\n      const updateRequest = {\n        email: this.profileForm.get('email')?.value,\n        firstName: this.profileForm.get('firstName')?.value,\n        lastName: this.profileForm.get('lastName')?.value\n      };\n      this.userService.updateUser(updateRequest).pipe(takeUntil(this.destroy$)).subscribe({\n        next: response => {\n          if (response.success && response.user) {\n            // Update local user data\n            this.user = {\n              ...this.user,\n              email: response.user.email || this.user.email,\n              firstName: response.user.firstName || this.user.firstName,\n              lastName: response.user.lastName || this.user.lastName\n            };\n            this.messageService.add({\n              severity: 'success',\n              summary: 'Success',\n              detail: response.message || 'Profile updated successfully!'\n            });\n            this.isEditing = false;\n          } else {\n            this.messageService.add({\n              severity: 'error',\n              summary: 'Error',\n              detail: response.message || 'Failed to update profile.'\n            });\n          }\n          this.isLoading = false;\n        },\n        error: error => {\n          console.error('Error updating profile:', error);\n          let errorMessage = 'Failed to update profile. Please try again.';\n          if (error.error && error.error.message) {\n            errorMessage = error.error.message;\n          } else if (error.error && typeof error.error === 'string') {\n            errorMessage = error.error;\n          }\n          this.messageService.add({\n            severity: 'error',\n            summary: 'Error',\n            detail: errorMessage\n          });\n          this.isLoading = false;\n        }\n      });\n    }\n    markFormGroupTouched() {\n      Object.keys(this.profileForm.controls).forEach(key => {\n        const control = this.profileForm.get(key);\n        control?.markAsTouched();\n      });\n    }\n    getFieldError(fieldName) {\n      const control = this.profileForm.get(fieldName);\n      if (control?.errors && control.touched) {\n        if (control.errors['required']) {\n          return `${fieldName} is required`;\n        }\n        if (control.errors['email']) {\n          return 'Please enter a valid email address';\n        }\n      }\n      return '';\n    }\n    formatDate(dateString) {\n      if (!dateString) return 'N/A';\n      return new Date(dateString).toLocaleDateString('en-GB', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n      });\n    }\n    formatDateTime(dateString) {\n      if (!dateString) return 'N/A';\n      return new Date(dateString).toLocaleString('en-GB', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n    getRoleColor(role) {\n      switch (role?.toLowerCase()) {\n        case 'admin':\n          return 'danger';\n        case 'manager':\n          return 'warning';\n        case 'user':\n          return 'info';\n        default:\n          return 'secondary';\n      }\n    }\n    getStatusColor(isActive) {\n      return isActive ? 'success' : 'danger';\n    }\n    getStatusText(isActive) {\n      return isActive ? 'Active' : 'Inactive';\n    }\n    getStatusColorByStatus(status) {\n      switch (status?.toLowerCase()) {\n        case 'active':\n          return 'success';\n        case 'inactive':\n          return 'danger';\n        case 'suspended':\n          return 'warning';\n        case 'pending':\n          return 'info';\n        default:\n          return 'secondary';\n      }\n    }\n    static #_ = this.ɵfac = function ProfileComponent_Factory(t) {\n      return new (t || ProfileComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.UserService), i0.ɵɵdirectiveInject(i4.MessageService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileComponent,\n      selectors: [[\"app-profile\"]],\n      decls: 29,\n      vars: 10,\n      consts: [[1, \"profile-container\"], [1, \"col-12\", \"mb-4\"], [3, \"items\"], [1, \"profile-header\"], [1, \"profile-avatar-section\"], [1, \"profile-avatar-large\"], [\"icon\", \"user\", 1, \"avatar-icon\"], [1, \"profile-header-info\"], [1, \"profile-name\"], [1, \"profile-username\"], [1, \"profile-badges\"], [\"icon\", \"pi pi-shield\", 3, \"value\", \"severity\"], [\"icon\", \"pi pi-circle-fill\", 3, \"value\", \"severity\"], [1, \"profile-actions\"], [\"label\", \"Edit Profile\", \"icon\", \"pi pi-pencil\", \"severity\", \"info\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"edit-actions\", 4, \"ngIf\"], [1, \"grid\"], [1, \"col-12\", \"lg:col-8\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"content\"], [1, \"col-12\", \"lg:col-4\"], [\"label\", \"Edit Profile\", \"icon\", \"pi pi-pencil\", \"severity\", \"info\", 3, \"click\"], [1, \"edit-actions\"], [\"label\", \"Cancel\", \"icon\", \"pi pi-times\", \"severity\", \"secondary\", 3, \"text\", \"click\"], [\"label\", \"Save Changes\", \"icon\", \"pi pi-check\", \"severity\", \"success\", 3, \"loading\", \"click\"], [1, \"card-header\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"icon\", \"user-circle\", 1, \"text-primary\", \"text-xl\"], [1, \"m-0\"], [\"value\", \"Editing\", \"severity\", \"warning\", \"icon\", \"pi pi-pencil\", 4, \"ngIf\"], [\"value\", \"Editing\", \"severity\", \"warning\", \"icon\", \"pi pi-pencil\"], [1, \"profile-form\", 3, \"formGroup\"], [1, \"col-12\", \"md:col-6\"], [1, \"field\"], [\"for\", \"username\", 1, \"field-label\"], [\"icon\", \"user\", 1, \"mr-1\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"username\", \"readonly\", \"\", 1, \"w-full\"], [1, \"field-help\"], [\"for\", \"email\", 1, \"field-label\", \"required\"], [\"icon\", \"envelope\", 1, \"mr-1\"], [\"pInputText\", \"\", \"id\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email address\", 3, \"readonly\"], [\"class\", \"field-error\", 4, \"ngIf\"], [\"for\", \"firstName\", 1, \"field-label\"], [\"icon\", \"id-card\", 1, \"mr-1\"], [\"pInputText\", \"\", \"id\", \"firstName\", \"formControlName\", \"firstName\", \"placeholder\", \"Enter your first name\", 1, \"w-full\", 3, \"readonly\"], [\"for\", \"lastName\", 1, \"field-label\"], [\"pInputText\", \"\", \"id\", \"lastName\", \"formControlName\", \"lastName\", \"placeholder\", \"Enter your last name\", 1, \"w-full\", 3, \"readonly\"], [1, \"field-error\"], [\"icon\", \"info-circle\", 1, \"text-info\", \"text-xl\"], [1, \"account-details\"], [1, \"detail-item\"], [1, \"detail-label\"], [\"icon\", \"calendar-plus\", 1, \"text-green-600\"], [1, \"detail-value\"], [\"icon\", \"clock\", 1, \"text-blue-600\"], [\"icon\", \"shield-alt\", 1, \"text-purple-600\"], [\"icon\", \"user-tag\", 1, \"text-orange-600\"]],\n      template: function ProfileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"app-modern-breadcrumb\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 1)(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5);\n          i0.ɵɵelement(7, \"fa-icon\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 7)(9, \"h1\", 8);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p\", 9);\n          i0.ɵɵtext(12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 10);\n          i0.ɵɵelement(14, \"p-tag\", 11)(15, \"p-tag\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 13);\n          i0.ɵɵtemplate(17, ProfileComponent_p_button_17_Template, 1, 0, \"p-button\", 14)(18, ProfileComponent_div_18_Template, 3, 2, \"div\", 15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 16)(20, \"div\", 17)(21, \"p-card\");\n          i0.ɵɵtemplate(22, ProfileComponent_ng_template_22_Template, 6, 1, \"ng-template\", 18)(23, ProfileComponent_ng_template_23_Template, 29, 7, \"ng-template\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"div\", 20)(25, \"p-card\");\n          i0.ɵɵtemplate(26, ProfileComponent_ng_template_26_Template, 5, 0, \"ng-template\", 18)(27, ProfileComponent_ng_template_27_Template, 29, 6, \"ng-template\", 19);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(28, \"p-toast\");\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"items\", ctx.breadcrumbItems);\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate2(\"\", ctx.user.firstName, \" \", ctx.user.lastName, \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(\"@\" + ctx.user.username);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.user.role)(\"severity\", ctx.getRoleColor(ctx.user.role));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"value\", ctx.user.status || \"Active\")(\"severity\", ctx.getStatusColorByStatus(ctx.user.status || \"Active\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isEditing);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isEditing);\n        }\n      },\n      dependencies: [i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.ModernBreadcrumbComponent, i7.Button, i4.PrimeTemplate, i8.InputText, i9.Card, i10.Tag, i11.Toast, i12.FaIconComponent],\n      styles: [\".profile-container[_ngcontent-%COMP%]{padding:1rem;max-width:1200px;margin:0 auto}.profile-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:2rem;background:linear-gradient(135deg,var(--primary-50) 0%,var(--primary-100) 100%);border-radius:16px;border:1px solid var(--primary-200);box-shadow:0 4px 12px #0000001a;margin-bottom:1.5rem}@media (max-width: 768px){.profile-header[_ngcontent-%COMP%]{flex-direction:column;gap:1.5rem;text-align:center}}.profile-avatar-section[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1.5rem}@media (max-width: 768px){.profile-avatar-section[_ngcontent-%COMP%]{flex-direction:column;gap:1rem}}.profile-avatar-large[_ngcontent-%COMP%]{width:100px;height:100px;border-radius:50%;background:linear-gradient(135deg,var(--primary-500) 0%,var(--primary-600) 100%);display:flex;align-items:center;justify-content:center;box-shadow:0 4px 16px #0003;border:4px solid white}.profile-avatar-large[_ngcontent-%COMP%]   .avatar-icon[_ngcontent-%COMP%]{font-size:2.5rem;color:#fff}.profile-header-info[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;margin:0 0 .5rem;color:var(--text-color)}.profile-header-info[_ngcontent-%COMP%]   .profile-username[_ngcontent-%COMP%]{font-size:1.1rem;color:var(--text-color-secondary);margin:0 0 1rem;font-weight:500}.profile-header-info[_ngcontent-%COMP%]   .profile-badges[_ngcontent-%COMP%]{display:flex;gap:.75rem;flex-wrap:wrap}@media (max-width: 768px){.profile-header-info[_ngcontent-%COMP%]   .profile-badges[_ngcontent-%COMP%]{justify-content:center}}.profile-actions[_ngcontent-%COMP%]   .edit-actions[_ngcontent-%COMP%]{display:flex;gap:.75rem;align-items:center}@media (max-width: 768px){.profile-actions[_ngcontent-%COMP%]   .edit-actions[_ngcontent-%COMP%]{flex-direction:column;width:100%}}.card-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:1.5rem;background:var(--surface-50);border-bottom:1px solid var(--surface-border)}.card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:var(--text-color);font-weight:600}.profile-form[_ngcontent-%COMP%]{padding:1.5rem}.profile-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]{margin-bottom:1.5rem}.profile-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]{display:flex;align-items:center;font-weight:600;color:var(--text-color);margin-bottom:.5rem;font-size:.9rem}.profile-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .field-label.required[_ngcontent-%COMP%]:after{content:\\\" *\\\";color:var(--red-500)}.profile-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .field-help[_ngcontent-%COMP%]{color:var(--text-color-secondary);font-size:.8rem;margin-top:.25rem;display:block}.profile-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .field-error[_ngcontent-%COMP%]{color:var(--red-500);font-size:.8rem;margin-top:.25rem;display:block}.profile-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   input[readonly][_ngcontent-%COMP%]{background-color:var(--surface-100);color:var(--text-color-secondary);cursor:not-allowed}.account-details[_ngcontent-%COMP%]{padding:1.5rem}.account-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:1rem 0;border-bottom:1px solid var(--surface-border)}.account-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.account-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;font-weight:500;color:var(--text-color);font-size:.9rem}.account-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%]{font-weight:600;color:var(--text-color);text-align:right}@media (max-width: 992px){.profile-container[_ngcontent-%COMP%]{padding:.5rem}.profile-header[_ngcontent-%COMP%]{padding:1.5rem}.card-header[_ngcontent-%COMP%]{padding:1rem;flex-direction:column;gap:1rem;text-align:center}.profile-form[_ngcontent-%COMP%], .account-details[_ngcontent-%COMP%]{padding:1rem}}@media (max-width: 576px){.profile-header-info[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%]{font-size:1.5rem}.profile-avatar-large[_ngcontent-%COMP%]{width:80px;height:80px}.profile-avatar-large[_ngcontent-%COMP%]   .avatar-icon[_ngcontent-%COMP%]{font-size:2rem}.detail-item[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:.5rem}.detail-item[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%]{text-align:left}}.profile-header[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInDown .5s ease-out}.profile-form[_ngcontent-%COMP%], .account-details[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out}@keyframes _ngcontent-%COMP%_slideInDown{0%{opacity:0;transform:translateY(-30px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.dark[_nghost-%COMP%]   .profile-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .profile-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--surface-800) 0%,var(--surface-900) 100%);border-color:var(--surface-700)}.dark[_nghost-%COMP%]   .card-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .card-header[_ngcontent-%COMP%]{background:var(--surface-800)}.dark[_nghost-%COMP%]   .profile-form[_ngcontent-%COMP%]   input[readonly][_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .profile-form[_ngcontent-%COMP%]   input[readonly][_ngcontent-%COMP%]{background-color:var(--surface-800)}\"]\n    });\n  }\n  return ProfileComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}