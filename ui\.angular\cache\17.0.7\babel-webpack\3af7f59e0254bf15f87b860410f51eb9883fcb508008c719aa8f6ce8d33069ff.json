{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let UserService = /*#__PURE__*/(() => {\n  class UserService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = environment.solarApi + 'api/user';\n    }\n    getAuthHeaders() {\n      const token = localStorage.getItem('auth_token');\n      return new HttpHeaders({\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      });\n    }\n    getUser() {\n      return this.http.get(this.apiUrl, {\n        headers: this.getAuthHeaders()\n      });\n    }\n    updateUser(updateRequest) {\n      return this.http.put(this.apiUrl, updateRequest, {\n        headers: this.getAuthHeaders()\n      });\n    }\n    static #_ = this.ɵfac = function UserService_Factory(t) {\n      return new (t || UserService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: UserService,\n      factory: UserService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return UserService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}