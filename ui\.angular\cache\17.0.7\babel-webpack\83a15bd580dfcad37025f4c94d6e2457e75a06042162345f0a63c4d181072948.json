{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { debounceTime } from 'rxjs';\nlet IndexComponent = class IndexComponent {\n  constructor(layoutService, stationsService, providersService, messageService, fb, router, cacheService, dateUtils) {\n    this.layoutService = layoutService;\n    this.stationsService = stationsService;\n    this.providersService = providersService;\n    this.messageService = messageService;\n    this.fb = fb;\n    this.router = router;\n    this.cacheService = cacheService;\n    this.dateUtils = dateUtils;\n    this.stations = [];\n    this.filteredStations = [];\n    this.paginatedStations = [];\n    this.sortOptionsCountry = [];\n    this.sortOptionsStatus = [];\n    this.sortOrder = 0;\n    this.sortField = '';\n    // Filtering properties\n    this.searchTerm = '';\n    this.selectedProvider = '';\n    this.selectedStatus = '';\n    // Pagination properties\n    this.currentPage = 0;\n    this.itemsPerPage = 5;\n    this.totalItems = 0;\n    // Loading state\n    this.isRefreshing = false;\n    this.sourceCities = [];\n    this.targetCities = [];\n    this.orderCities = [];\n    this.stationsData = new Map();\n    this.stationsRawData = new Map();\n    this.stationsSumData = new Map();\n    this.subscription = this.layoutService.configUpdate$.pipe(debounceTime(25)).subscribe(config => {\n      //   this.initChart();\n    });\n  }\n  ngOnInit() {\n    this.getUserProviders();\n    this.barOptions = {\n      maintainAspectRatio: false,\n      plugins: {\n        legend: {\n          display: false\n        }\n      },\n      scales: {\n        x: {\n          display: false\n        },\n        y: {\n          display: false\n        }\n      }\n    };\n    this.lineOptions = {\n      responsive: true,\n      maintainAspectRatio: false,\n      interaction: {\n        intersect: false,\n        mode: 'index'\n      },\n      elements: {\n        line: {\n          tension: 0.4,\n          borderWidth: 2\n        },\n        point: {\n          radius: 0,\n          hoverRadius: 4,\n          hitRadius: 10\n        }\n      },\n      plugins: {\n        legend: {\n          display: false\n        },\n        tooltip: {\n          enabled: true,\n          mode: 'index',\n          intersect: false,\n          position: 'nearest',\n          external: null,\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          titleColor: '#ffffff',\n          bodyColor: '#ffffff',\n          borderColor: 'rgba(255, 255, 255, 0.1)',\n          borderWidth: 1,\n          cornerRadius: 8,\n          displayColors: false,\n          padding: 12,\n          caretPadding: 6,\n          caretSize: 5,\n          titleFont: {\n            size: 14,\n            weight: 'bold'\n          },\n          bodyFont: {\n            size: 13\n          },\n          callbacks: {\n            title: function (context) {\n              if (context && context.length > 0) {\n                return 'Time: ' + context[0].label;\n              }\n              return 'Time: --:--';\n            },\n            label: function (context) {\n              const label = context.dataset.label || '';\n              const value = context.parsed.y;\n              return `${label}: ${value.toFixed(2)} kW`;\n            }\n          }\n        }\n      },\n      scales: {\n        x: {\n          display: false,\n          grid: {\n            display: false\n          }\n        },\n        y: {\n          display: false,\n          grid: {\n            display: false\n          }\n        }\n      },\n      animation: {\n        duration: 750,\n        easing: 'easeInOutQuart'\n      }\n    };\n  }\n  getUserProviders() {\n    this.isRefreshing = true;\n    this.providersService.getUserProviders().then(providersData => {\n      if (providersData.length > 0) {\n        this.stationsService.getUserStations().then(stationsData => {\n          this.cacheService.setStations(stationsData);\n          this.stations = stationsData;\n          console.log(\"stations set\");\n          console.log(stationsData);\n          // Initialize filter options\n          this.initializeFilterOptions();\n          // Apply filters and pagination\n          this.applyFilters();\n          // Φορτώνουμε τα δεδομένα για κάθε σταθμό\n          this.loadAllStationsData();\n          this.isRefreshing = false;\n        });\n      } else {\n        this.router.navigate(['/app/providers']);\n      }\n    }).catch(error => {\n      console.error('Error loading providers:', error);\n      this.isRefreshing = false;\n    });\n  }\n  // Νέα μέθοδος για φόρτωση δεδομένων όλων των σταθμών\n  loadAllStationsData() {\n    if (!this.stations || this.stations.length === 0) return;\n    // Φορτώνουμε τα δεδομένα για κάθε σταθμό\n    this.stations.forEach(station => {\n      this.loadStationData(station);\n    });\n  }\n  // Νέα μέθοδος για φόρτωση δεδομένων ενός σταθμού\n  loadStationData(station) {\n    if (!station) return;\n    const now = new Date();\n    const formattedStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0).toISOString();\n    const formattedEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0).toISOString();\n    let request = {\n      devIds: station.deviceIds,\n      devTypeId: 1,\n      startDateTime: formattedStartDate,\n      endDateTime: formattedEndDate,\n      separated: true,\n      searchType: null,\n      stationId: station.id\n    };\n    this.stationsService.getStationHistoricData(request).then(data => {\n      if (data && data.data) {\n        // Filter data to not show beyond current time\n        const filteredData = this.filterDataByCurrentTime(data.data);\n        const documentStyle = getComputedStyle(document.documentElement);\n        const lineData = {\n          labels: filteredData.map((e, index) => index % 2 === 0 ? this.dateUtils.formatTimeForChart(e.dateTime) : ''),\n          datasets: [{\n            label: 'Active Power',\n            data: filteredData.map(e => e.activePower),\n            fill: false,\n            backgroundColor: documentStyle.getPropertyValue('--primary-500'),\n            borderColor: documentStyle.getPropertyValue('--primary-500'),\n            tension: .4\n          }, {\n            label: 'Total Input Power',\n            data: filteredData.map(e => e.totalInputPower),\n            fill: false,\n            backgroundColor: documentStyle.getPropertyValue('--primary-200'),\n            borderColor: documentStyle.getPropertyValue('--primary-200'),\n            tension: .4\n          }]\n        };\n        // Αποθηκεύουμε τα δεδομένα στο Map με κλειδί το ID του σταθμού\n        this.stationsData.set(station.id, lineData);\n        this.stationsRawData.set(station.id, filteredData);\n        this.stationsSumData.set(station.id, data.sum);\n      }\n    }).catch(error => {\n      console.error(`Error loading data for station ${station.id}:`, error);\n    });\n  }\n  // Τροποποιημένη μέθοδος που επιστρέφει τα προ-φορτωμένα δεδομένα\n  getStationsRealTimeData(station) {\n    // Επιστρέφουμε τα δεδομένα από το Map αν υπάρχουν\n    if (station && station.id && this.stationsData.has(station.id)) {\n      return this.stationsData.get(station.id);\n    }\n    // Αν δεν έχουν φορτωθεί ακόμα, επιστρέφουμε null\n    return null;\n  }\n  getStationsSumData(stationId) {\n    return this.stationsSumData.get(stationId);\n  }\n  getStationsInverters(stationId) {\n    var data = this.stationsRawData.get(stationId);\n    if (!data || data.length === 0) {\n      return 0;\n    } else return new Set(data.map(item => item.name)).size;\n  }\n  getStationLastUpdate(stationId) {\n    const data = this.stationsRawData.get(stationId);\n    if (!data || data.length === 0) return \"-\";\n    const latest = data.reduce((latestSoFar, current) => {\n      return new Date(current.dateTime).getTime() > new Date(latestSoFar.dateTime).getTime() ? current : latestSoFar;\n    });\n    return new Date(latest.dateTime).toLocaleString(\"en-GB\", {\n      hour: '2-digit',\n      minute: '2-digit',\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric',\n      timeZone: 'Europe/Athens'\n    });\n  }\n  onSortChange(event) {\n    const value = event.value;\n    if (value.indexOf('!') === 0) {\n      this.sortOrder = -1;\n      this.sortField = value.substring(1, value.length);\n    } else {\n      this.sortOrder = 1;\n      this.sortField = value;\n    }\n  }\n  onFilter(dv, event) {\n    dv.filter(event.target.value);\n  }\n  // Initialize filter options based on available stations\n  initializeFilterOptions() {\n    // Get unique providers\n    const providers = [...new Set(this.stations.map(station => station.provider))];\n    this.sortOptionsCountry = providers.map(provider => ({\n      label: provider,\n      value: provider\n    }));\n    // Get unique statuses\n    const statuses = [...new Set(this.stations.map(station => station.status))];\n    this.sortOptionsStatus = statuses.map(status => ({\n      label: status,\n      value: status\n    }));\n  }\n  // Apply all filters and update pagination\n  applyFilters() {\n    let filtered = [...this.stations];\n    // Apply search filter\n    if (this.searchTerm) {\n      const searchLower = this.searchTerm.toLowerCase();\n      filtered = filtered.filter(station => station.name.toLowerCase().includes(searchLower) || station.provider.toLowerCase().includes(searchLower) || station.location && station.location.toLowerCase().includes(searchLower));\n    }\n    // Apply provider filter\n    if (this.selectedProvider) {\n      filtered = filtered.filter(station => station.provider === this.selectedProvider);\n    }\n    // Apply status filter\n    if (this.selectedStatus) {\n      filtered = filtered.filter(station => station.status === this.selectedStatus);\n    }\n    this.filteredStations = filtered;\n    this.totalItems = filtered.length;\n    this.currentPage = 0; // Reset to first page\n    this.updatePagination();\n  }\n  // Update pagination based on current page\n  updatePagination() {\n    const startIndex = this.currentPage * this.itemsPerPage;\n    const endIndex = startIndex + this.itemsPerPage;\n    this.paginatedStations = this.filteredStations.slice(startIndex, endIndex);\n  }\n  // Handle search input change\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.applyFilters();\n  }\n  // Handle provider filter change\n  onProviderChange(event) {\n    this.selectedProvider = event.value || '';\n    this.applyFilters();\n  }\n  // Handle status filter change\n  onStatusChange(event) {\n    this.selectedStatus = event.value || '';\n    this.applyFilters();\n  }\n  // Handle pagination change\n  onPageChange(event) {\n    this.currentPage = event.page;\n    this.itemsPerPage = event.rows;\n    this.updatePagination();\n  }\n  // Refresh data\n  refreshData() {\n    this.getUserProviders();\n  }\n  // Clear all filters\n  clearFilters() {\n    this.searchTerm = '';\n    this.selectedProvider = '';\n    this.selectedStatus = '';\n    this.applyFilters();\n  }\n  // Check if station has equipment data\n  hasEquipmentData(station) {\n    const inverters = this.getStationsInverters(station.id || '');\n    return inverters > 0 || station.mmpt && station.mmpt > 0 || station.string && station.string > 0 || station.pvn && station.pvn > 0;\n  }\n  filterDataByCurrentTime(data) {\n    const now = new Date();\n    const currentTime = now.getTime();\n    return data.filter(item => {\n      const itemDateTime = new Date(item.dateTime);\n      const itemTime = itemDateTime.getTime();\n      // Only include data points that are not in the future\n      return itemTime <= currentTime;\n    });\n  }\n  initMap() {\n    this.mapSrc = \"https://www.digitaltrends.com/wp-content/uploads/2020/02/custom-map.jpg?fit=720%2C479&p=1\";\n  }\n  // initChart() {\n  //     const documentStyle = getComputedStyle(document.documentElement);\n  //     const textColor = documentStyle.getPropertyValue('--text-color');\n  //     const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n  //     const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\n  //     this.chartData = {\n  //         labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\n  //         datasets: [\n  //             {\n  //                 label: 'First Dataset',\n  //                 data: [65, 59, 80, 81, 56, 55, 40],\n  //                 fill: false,\n  //                 backgroundColor: documentStyle.getPropertyValue('--bluegray-700'),\n  //                 borderColor: documentStyle.getPropertyValue('--bluegray-700'),\n  //                 tension: .4\n  //             },\n  //             {\n  //                 label: 'Second Dataset',\n  //                 data: [28, 48, 40, 19, 86, 27, 90],\n  //                 fill: false,\n  //                 backgroundColor: documentStyle.getPropertyValue('--green-600'),\n  //                 borderColor: documentStyle.getPropertyValue('--green-600'),\n  //                 tension: .4\n  //             }\n  //         ]\n  //     };\n  //     this.chartOptions = {\n  //         plugins: {\n  //             legend: {\n  //                 labels: {\n  //                     color: textColor\n  //                 }\n  //             }\n  //         },\n  //         scales: {\n  //             x: {\n  //                 ticks: {\n  //                     color: textColorSecondary\n  //                 },\n  //                 grid: {\n  //                     color: surfaceBorder,\n  //                     drawBorder: false\n  //                 }\n  //             },\n  //             y: {\n  //                 ticks: {\n  //                     color: textColorSecondary\n  //                 },\n  //                 grid: {\n  //                     color: surfaceBorder,\n  //                     drawBorder: false\n  //                 }\n  //             }\n  //         }\n  //     };\n  // }\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n};\nIndexComponent = __decorate([Component({\n  templateUrl: './index.component.html',\n  styleUrls: ['./index.component.scss']\n})], IndexComponent);\nexport { IndexComponent };", "map": {"version": 3, "names": ["Component", "debounceTime", "IndexComponent", "constructor", "layoutService", "stationsService", "providersService", "messageService", "fb", "router", "cacheService", "dateUtils", "stations", "filteredStations", "paginatedStations", "sortOptionsCountry", "sortOptionsStatus", "sortOrder", "sortField", "searchTerm", "<PERSON><PERSON><PERSON><PERSON>", "selectedStatus", "currentPage", "itemsPerPage", "totalItems", "isRefreshing", "sourceCities", "targetCities", "orderCities", "stationsData", "Map", "stationsRawData", "stationsSumData", "subscription", "configUpdate$", "pipe", "subscribe", "config", "ngOnInit", "getUserProviders", "barOptions", "maintainAspectRatio", "plugins", "legend", "display", "scales", "x", "y", "lineOptions", "responsive", "interaction", "intersect", "mode", "elements", "line", "tension", "borderWidth", "point", "radius", "hoverRadius", "hitRadius", "tooltip", "enabled", "position", "external", "backgroundColor", "titleColor", "bodyColor", "borderColor", "cornerRadius", "displayColors", "padding", "caretPadding", "caretSize", "titleFont", "size", "weight", "bodyFont", "callbacks", "title", "context", "length", "label", "dataset", "value", "parsed", "toFixed", "grid", "animation", "duration", "easing", "then", "providersData", "getUserStations", "setStations", "console", "log", "initializeFilterOptions", "applyFilters", "loadAllStationsData", "navigate", "catch", "error", "for<PERSON>ach", "station", "loadStationData", "now", "Date", "formattedStartDate", "getFullYear", "getMonth", "getDate", "toISOString", "formattedEndDate", "request", "devIds", "deviceIds", "devTypeId", "startDateTime", "endDateTime", "separated", "searchType", "stationId", "id", "getStationHistoricData", "data", "filteredData", "filterDataByCurrentTime", "documentStyle", "getComputedStyle", "document", "documentElement", "lineData", "labels", "map", "e", "index", "formatTimeForChart", "dateTime", "datasets", "activePower", "fill", "getPropertyValue", "totalInputPower", "set", "sum", "getStationsRealTimeData", "has", "get", "getStationsSumData", "getStationsInverters", "Set", "item", "name", "getStationLastUpdate", "latest", "reduce", "latestSoFar", "current", "getTime", "toLocaleString", "hour", "minute", "day", "month", "year", "timeZone", "onSortChange", "event", "indexOf", "substring", "onFilter", "dv", "filter", "target", "providers", "provider", "statuses", "status", "filtered", "searchLower", "toLowerCase", "includes", "location", "updatePagination", "startIndex", "endIndex", "slice", "onSearchChange", "onProviderChange", "onStatusChange", "onPageChange", "page", "rows", "refreshData", "clearFilters", "hasEquipmentData", "inverters", "mmpt", "string", "pvn", "currentTime", "itemDateTime", "itemTime", "initMap", "mapSrc", "ngOnDestroy", "unsubscribe", "__decorate", "templateUrl", "styleUrls"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\index\\index.component.ts"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { MenuItem, MessageService } from 'primeng/api';\r\nimport { Product } from '../../api/product';\r\nimport { ProductService } from '../../service/product.service';\r\nimport { Subscription, debounceTime } from 'rxjs';\r\nimport { LayoutService } from 'src/app/layout/service/app.layout.service';\r\nimport { SelectItem } from 'primeng/api';\r\nimport { DataView } from 'primeng/dataview';\r\nimport { StationsService } from '../../service/stations.service';\r\nimport { Station } from '../../api/station';\r\nimport { CacheService } from '../../service/cache.service';\r\nimport { DateUtilsService } from '../../service/date-utils.service';\r\nimport { ProvidersService } from '../../service/providers.service';\r\nimport { IProvider, IUserProvider } from '../../api/responses';\r\nimport { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { GetHistoricDataRequest, SaveUserProvidersRequest } from '../../api/requests';\r\nimport { Router } from '@angular/router';\r\n\r\n\r\n@Component({\r\n    templateUrl: './index.component.html',\r\n    styleUrls: ['./index.component.scss']\r\n})\r\nexport class IndexComponent implements OnInit, OnDestroy {\r\n\r\n    items!: MenuItem[];\r\n\r\n    stations: Station[] = [];\r\n    filteredStations: Station[] = [];\r\n    paginatedStations: Station[] = [];\r\n\r\n    chartData: any;\r\n\r\n    chartOptions: any;\r\n\r\n    subscription!: Subscription;\r\n\r\n    sortOptionsCountry: SelectItem[] = [];\r\n\r\n    sortOptionsStatus: SelectItem[] = [];\r\n\r\n    sortOrder: number = 0;\r\n\r\n    sortField: string = '';\r\n\r\n    // Filtering properties\r\n    searchTerm: string = '';\r\n    selectedProvider: string = '';\r\n    selectedStatus: string = '';\r\n\r\n    // Pagination properties\r\n    currentPage: number = 0;\r\n    itemsPerPage: number = 5;\r\n    totalItems: number = 0;\r\n\r\n    // Loading state\r\n    isRefreshing: boolean = false;\r\n\r\n    sourceCities: any[] = [];\r\n\r\n    targetCities: any[] = [];\r\n\r\n    orderCities: any[] = [];\r\n\r\n    mapSrc:string;\r\n\r\n    barOptions:any;\r\n    lineOptions:any;\r\n    stationsData:Map<string,any> = new Map();\r\n    stationsRawData:Map<string,any> = new Map();\r\n    stationsSumData:Map<string,number> = new Map();\r\n    \r\n\r\n    constructor(public layoutService: LayoutService,\r\n        private stationsService: StationsService,\r\n        private providersService: ProvidersService,\r\n        private messageService: MessageService,\r\n        private fb: FormBuilder,\r\n        private router: Router,\r\n        private cacheService: CacheService,\r\n        private dateUtils: DateUtilsService) {\r\n        this.subscription = this.layoutService.configUpdate$\r\n        .pipe(debounceTime(25))\r\n        .subscribe((config) => {\r\n         //   this.initChart();\r\n        });\r\n    }\r\n\r\n    ngOnInit() {\r\n        this.getUserProviders();\r\n\r\n        this.barOptions = {\r\n            maintainAspectRatio: false,\r\n            plugins: {\r\n              legend: {\r\n                display: false\r\n              }\r\n            },\r\n            scales: {\r\n              x: {\r\n                display: false\r\n              },\r\n              y: {\r\n                display: false\r\n              }\r\n            }\r\n          };\r\n        \r\n          this.lineOptions = {\r\n            responsive: true,\r\n            maintainAspectRatio: false,\r\n            interaction: {\r\n              intersect: false,\r\n              mode: 'index'\r\n            },\r\n            elements: {\r\n              line: {\r\n                tension: 0.4,\r\n                borderWidth: 2\r\n              },\r\n              point: {\r\n                radius: 0,\r\n                hoverRadius: 4,\r\n                hitRadius: 10\r\n              }\r\n            },\r\n            plugins: {\r\n              legend: {\r\n                display: false\r\n              },\r\n              tooltip: {\r\n                enabled: true,\r\n                mode: 'index',\r\n                intersect: false,\r\n                position: 'nearest',\r\n                external: null, // Ensure we use the default tooltip positioning\r\n                backgroundColor: 'rgba(0, 0, 0, 0.8)',\r\n                titleColor: '#ffffff',\r\n                bodyColor: '#ffffff',\r\n                borderColor: 'rgba(255, 255, 255, 0.1)',\r\n                borderWidth: 1,\r\n                cornerRadius: 8,\r\n                displayColors: false,\r\n                padding: 12,\r\n                caretPadding: 6,\r\n                caretSize: 5,\r\n                titleFont: {\r\n                  size: 14,\r\n                  weight: 'bold'\r\n                },\r\n                bodyFont: {\r\n                  size: 13\r\n                },\r\n                callbacks: {\r\n                  title: function(context: any) {\r\n                    if (context && context.length > 0) {\r\n                      return 'Time: ' + context[0].label;\r\n                    }\r\n                    return 'Time: --:--';\r\n                  },\r\n                  label: function(context: any) {\r\n                    const label = context.dataset.label || '';\r\n                    const value = context.parsed.y;\r\n                    return `${label}: ${value.toFixed(2)} kW`;\r\n                  }\r\n                }\r\n              }\r\n            },\r\n            scales: {\r\n              x: {\r\n                display: false,\r\n                grid: {\r\n                  display: false\r\n                }\r\n              },\r\n              y: {\r\n                display: false,\r\n                grid: {\r\n                  display: false\r\n                }\r\n              }\r\n            },\r\n            animation: {\r\n              duration: 750,\r\n              easing: 'easeInOutQuart'\r\n            }\r\n          };\r\n\r\n\r\n          \r\n        \r\n    }\r\n\r\n    getUserProviders(){\r\n      this.isRefreshing = true;\r\n      this.providersService.getUserProviders().then(providersData => {\r\n        if (providersData.length > 0){\r\n          this.stationsService.getUserStations().then(stationsData => {\r\n            this.cacheService.setStations(stationsData);\r\n            this.stations = stationsData;\r\n            console.log(\"stations set\")\r\n            console.log(stationsData)\r\n\r\n            // Initialize filter options\r\n            this.initializeFilterOptions();\r\n\r\n            // Apply filters and pagination\r\n            this.applyFilters();\r\n\r\n            // Φορτώνουμε τα δεδομένα για κάθε σταθμό\r\n            this.loadAllStationsData();\r\n\r\n            this.isRefreshing = false;\r\n          });\r\n        }else{\r\n          this.router.navigate(['/app/providers']);\r\n        }\r\n      }).catch(error => {\r\n        console.error('Error loading providers:', error);\r\n        this.isRefreshing = false;\r\n      });\r\n    }\r\n\r\n    // Νέα μέθοδος για φόρτωση δεδομένων όλων των σταθμών\r\n    loadAllStationsData() {\r\n        if (!this.stations || this.stations.length === 0) return;\r\n        \r\n        // Φορτώνουμε τα δεδομένα για κάθε σταθμό\r\n        this.stations.forEach(station => {\r\n            this.loadStationData(station);\r\n        });\r\n    }\r\n\r\n    // Νέα μέθοδος για φόρτωση δεδομένων ενός σταθμού\r\n    loadStationData(station: Station) {\r\n        if (!station) return;\r\n        \r\n        const now = new Date();\r\n        const formattedStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0).toISOString();\r\n        const formattedEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0).toISOString();\r\n        \r\n        let request: GetHistoricDataRequest = {\r\n            devIds: station.deviceIds,\r\n            devTypeId: 1,\r\n            startDateTime: formattedStartDate,\r\n            endDateTime: formattedEndDate,\r\n            separated: true,\r\n            searchType: null,\r\n            stationId: station.id\r\n        };\r\n        \r\n        this.stationsService.getStationHistoricData(request).then(data => {\r\n            if (data && data.data) {\r\n                // Filter data to not show beyond current time\r\n                const filteredData = this.filterDataByCurrentTime(data.data);\r\n\r\n                const documentStyle = getComputedStyle(document.documentElement);\r\n\r\n                const lineData = {\r\n                    labels: filteredData.map((e, index) =>\r\n                        index % 2 === 0 ? this.dateUtils.formatTimeForChart(e.dateTime) : ''\r\n                    ),\r\n                    datasets: [\r\n                        {\r\n                            label: 'Active Power',\r\n                            data: filteredData.map(e => e.activePower),\r\n                            fill: false,\r\n                            backgroundColor: documentStyle.getPropertyValue('--primary-500'),\r\n                            borderColor: documentStyle.getPropertyValue('--primary-500'),\r\n                            tension: .4\r\n                        },\r\n                        {\r\n                            label: 'Total Input Power',\r\n                            data: filteredData.map(e => e.totalInputPower),\r\n                            fill: false,\r\n                            backgroundColor: documentStyle.getPropertyValue('--primary-200'),\r\n                            borderColor: documentStyle.getPropertyValue('--primary-200'),\r\n                            tension: .4\r\n                        }\r\n                    ]\r\n                };\r\n\r\n                // Αποθηκεύουμε τα δεδομένα στο Map με κλειδί το ID του σταθμού\r\n                this.stationsData.set(station.id, lineData);\r\n                this.stationsRawData.set(station.id, filteredData);\r\n                this.stationsSumData.set(station.id, data.sum);\r\n            }\r\n        }).catch(error => {\r\n            console.error(`Error loading data for station ${station.id}:`, error);\r\n        });\r\n    }\r\n    \r\n\r\n    // Τροποποιημένη μέθοδος που επιστρέφει τα προ-φορτωμένα δεδομένα\r\n    getStationsRealTimeData(station: Station) {\r\n        // Επιστρέφουμε τα δεδομένα από το Map αν υπάρχουν\r\n        if (station && station.id && this.stationsData.has(station.id)) {\r\n            return this.stationsData.get(station.id);\r\n        }\r\n        \r\n        // Αν δεν έχουν φορτωθεί ακόμα, επιστρέφουμε null\r\n        return null;\r\n    }\r\n\r\n    getStationsSumData(stationId:string){\r\n      return this.stationsSumData.get(stationId);\r\n    }\r\n\r\n    getStationsInverters(stationId:string){\r\n      var data = this.stationsRawData.get(stationId);\r\n      if (!data || data.length === 0) {\r\n        return 0;\r\n      }\r\n      else\r\n        return new Set(data.map(item => item.name)).size;\r\n    }\r\n\r\n    getStationLastUpdate(stationId: string) {\r\n      const data = this.stationsRawData.get(stationId);\r\n      if (!data || data.length === 0) return \"-\";\r\n\r\n      const latest = data.reduce((latestSoFar, current) => {\r\n        return new Date(current.dateTime).getTime() > new Date(latestSoFar.dateTime).getTime()\r\n          ? current\r\n          : latestSoFar;\r\n      });\r\n\r\n      return new Date(latest.dateTime).toLocaleString(\"en-GB\", {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        day: '2-digit',\r\n        month: '2-digit',\r\n        year: 'numeric',\r\n        timeZone: 'Europe/Athens'\r\n      });\r\n    }\r\n    \r\n\r\n    onSortChange(event: any) {\r\n        const value = event.value;\r\n\r\n        if (value.indexOf('!') === 0) {\r\n            this.sortOrder = -1;\r\n            this.sortField = value.substring(1, value.length);\r\n        } else {\r\n            this.sortOrder = 1;\r\n            this.sortField = value;\r\n        }\r\n    }\r\n\r\n    onFilter(dv: DataView, event: Event) {\r\n        dv.filter((event.target as HTMLInputElement).value);\r\n    }\r\n\r\n    // Initialize filter options based on available stations\r\n    initializeFilterOptions() {\r\n        // Get unique providers\r\n        const providers = [...new Set(this.stations.map(station => station.provider))];\r\n        this.sortOptionsCountry = providers.map(provider => ({\r\n            label: provider,\r\n            value: provider\r\n        }));\r\n\r\n        // Get unique statuses\r\n        const statuses = [...new Set(this.stations.map(station => station.status))];\r\n        this.sortOptionsStatus = statuses.map(status => ({\r\n            label: status,\r\n            value: status\r\n        }));\r\n    }\r\n\r\n    // Apply all filters and update pagination\r\n    applyFilters() {\r\n        let filtered = [...this.stations];\r\n\r\n        // Apply search filter\r\n        if (this.searchTerm) {\r\n            const searchLower = this.searchTerm.toLowerCase();\r\n            filtered = filtered.filter(station =>\r\n                station.name.toLowerCase().includes(searchLower) ||\r\n                station.provider.toLowerCase().includes(searchLower) ||\r\n                (station.location && station.location.toLowerCase().includes(searchLower))\r\n            );\r\n        }\r\n\r\n        // Apply provider filter\r\n        if (this.selectedProvider) {\r\n            filtered = filtered.filter(station => station.provider === this.selectedProvider);\r\n        }\r\n\r\n        // Apply status filter\r\n        if (this.selectedStatus) {\r\n            filtered = filtered.filter(station => station.status === this.selectedStatus);\r\n        }\r\n\r\n        this.filteredStations = filtered;\r\n        this.totalItems = filtered.length;\r\n        this.currentPage = 0; // Reset to first page\r\n        this.updatePagination();\r\n    }\r\n\r\n    // Update pagination based on current page\r\n    updatePagination() {\r\n        const startIndex = this.currentPage * this.itemsPerPage;\r\n        const endIndex = startIndex + this.itemsPerPage;\r\n        this.paginatedStations = this.filteredStations.slice(startIndex, endIndex);\r\n    }\r\n\r\n    // Handle search input change\r\n    onSearchChange(event: Event) {\r\n        this.searchTerm = (event.target as HTMLInputElement).value;\r\n        this.applyFilters();\r\n    }\r\n\r\n    // Handle provider filter change\r\n    onProviderChange(event: any) {\r\n        this.selectedProvider = event.value || '';\r\n        this.applyFilters();\r\n    }\r\n\r\n    // Handle status filter change\r\n    onStatusChange(event: any) {\r\n        this.selectedStatus = event.value || '';\r\n        this.applyFilters();\r\n    }\r\n\r\n    // Handle pagination change\r\n    onPageChange(event: any) {\r\n        this.currentPage = event.page;\r\n        this.itemsPerPage = event.rows;\r\n        this.updatePagination();\r\n    }\r\n\r\n    // Refresh data\r\n    refreshData() {\r\n        this.getUserProviders();\r\n    }\r\n\r\n    // Clear all filters\r\n    clearFilters() {\r\n        this.searchTerm = '';\r\n        this.selectedProvider = '';\r\n        this.selectedStatus = '';\r\n        this.applyFilters();\r\n    }\r\n\r\n    // Check if station has equipment data\r\n    hasEquipmentData(station: Station): boolean {\r\n        const inverters = this.getStationsInverters(station.id || '');\r\n        return inverters > 0 ||\r\n               (station.mmpt && station.mmpt > 0) ||\r\n               (station.string && station.string > 0) ||\r\n               (station.pvn && station.pvn > 0);\r\n    }\r\n\r\n    private filterDataByCurrentTime(data: any[]): any[] {\r\n        const now = new Date();\r\n        const currentTime = now.getTime();\r\n\r\n        return data.filter(item => {\r\n            const itemDateTime = new Date(item.dateTime);\r\n            const itemTime = itemDateTime.getTime();\r\n\r\n            // Only include data points that are not in the future\r\n            return itemTime <= currentTime;\r\n        });\r\n    }\r\n\r\n\r\n    initMap(){\r\n        this.mapSrc = \"https://www.digitaltrends.com/wp-content/uploads/2020/02/custom-map.jpg?fit=720%2C479&p=1\"\r\n    }\r\n\r\n    // initChart() {\r\n    //     const documentStyle = getComputedStyle(document.documentElement);\r\n    //     const textColor = documentStyle.getPropertyValue('--text-color');\r\n    //     const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\r\n    //     const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\r\n\r\n    //     this.chartData = {\r\n    //         labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\r\n    //         datasets: [\r\n    //             {\r\n    //                 label: 'First Dataset',\r\n    //                 data: [65, 59, 80, 81, 56, 55, 40],\r\n    //                 fill: false,\r\n    //                 backgroundColor: documentStyle.getPropertyValue('--bluegray-700'),\r\n    //                 borderColor: documentStyle.getPropertyValue('--bluegray-700'),\r\n    //                 tension: .4\r\n    //             },\r\n    //             {\r\n    //                 label: 'Second Dataset',\r\n    //                 data: [28, 48, 40, 19, 86, 27, 90],\r\n    //                 fill: false,\r\n    //                 backgroundColor: documentStyle.getPropertyValue('--green-600'),\r\n    //                 borderColor: documentStyle.getPropertyValue('--green-600'),\r\n    //                 tension: .4\r\n    //             }\r\n    //         ]\r\n    //     };\r\n\r\n    //     this.chartOptions = {\r\n    //         plugins: {\r\n    //             legend: {\r\n    //                 labels: {\r\n    //                     color: textColor\r\n    //                 }\r\n    //             }\r\n    //         },\r\n    //         scales: {\r\n    //             x: {\r\n    //                 ticks: {\r\n    //                     color: textColorSecondary\r\n    //                 },\r\n    //                 grid: {\r\n    //                     color: surfaceBorder,\r\n    //                     drawBorder: false\r\n    //                 }\r\n    //             },\r\n    //             y: {\r\n    //                 ticks: {\r\n    //                     color: textColorSecondary\r\n    //                 },\r\n    //                 grid: {\r\n    //                     color: surfaceBorder,\r\n    //                     drawBorder: false\r\n    //                 }\r\n    //             }\r\n    //         }\r\n    //     };\r\n    // }\r\n\r\n    ngOnDestroy() {\r\n        if (this.subscription) {\r\n            this.subscription.unsubscribe();\r\n        }\r\n    }\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAA2B,eAAe;AAI5D,SAAuBC,YAAY,QAAQ,MAAM;AAmB1C,IAAMC,cAAc,GAApB,MAAMA,cAAc;EAkDvBC,YAAmBC,aAA4B,EACnCC,eAAgC,EAChCC,gBAAkC,EAClCC,cAA8B,EAC9BC,EAAe,EACfC,MAAc,EACdC,YAA0B,EAC1BC,SAA2B;IAPpB,KAAAP,aAAa,GAAbA,aAAa;IACpB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,SAAS,GAATA,SAAS;IArDrB,KAAAC,QAAQ,GAAc,EAAE;IACxB,KAAAC,gBAAgB,GAAc,EAAE;IAChC,KAAAC,iBAAiB,GAAc,EAAE;IAQjC,KAAAC,kBAAkB,GAAiB,EAAE;IAErC,KAAAC,iBAAiB,GAAiB,EAAE;IAEpC,KAAAC,SAAS,GAAW,CAAC;IAErB,KAAAC,SAAS,GAAW,EAAE;IAEtB;IACA,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAC,cAAc,GAAW,EAAE;IAE3B;IACA,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,UAAU,GAAW,CAAC;IAEtB;IACA,KAAAC,YAAY,GAAY,KAAK;IAE7B,KAAAC,YAAY,GAAU,EAAE;IAExB,KAAAC,YAAY,GAAU,EAAE;IAExB,KAAAC,WAAW,GAAU,EAAE;IAMvB,KAAAC,YAAY,GAAmB,IAAIC,GAAG,EAAE;IACxC,KAAAC,eAAe,GAAmB,IAAID,GAAG,EAAE;IAC3C,KAAAE,eAAe,GAAsB,IAAIF,GAAG,EAAE;IAW1C,IAAI,CAACG,YAAY,GAAG,IAAI,CAAC7B,aAAa,CAAC8B,aAAa,CACnDC,IAAI,CAAClC,YAAY,CAAC,EAAE,CAAC,CAAC,CACtBmC,SAAS,CAAEC,MAAM,IAAI;MACrB;IAAA,CACA,CAAC;EACN;EAEAC,QAAQA,CAAA;IACJ,IAAI,CAACC,gBAAgB,EAAE;IAEvB,IAAI,CAACC,UAAU,GAAG;MACdC,mBAAmB,EAAE,KAAK;MAC1BC,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE;;OAEZ;MACDC,MAAM,EAAE;QACNC,CAAC,EAAE;UACDF,OAAO,EAAE;SACV;QACDG,CAAC,EAAE;UACDH,OAAO,EAAE;;;KAGd;IAED,IAAI,CAACI,WAAW,GAAG;MACjBC,UAAU,EAAE,IAAI;MAChBR,mBAAmB,EAAE,KAAK;MAC1BS,WAAW,EAAE;QACXC,SAAS,EAAE,KAAK;QAChBC,IAAI,EAAE;OACP;MACDC,QAAQ,EAAE;QACRC,IAAI,EAAE;UACJC,OAAO,EAAE,GAAG;UACZC,WAAW,EAAE;SACd;QACDC,KAAK,EAAE;UACLC,MAAM,EAAE,CAAC;UACTC,WAAW,EAAE,CAAC;UACdC,SAAS,EAAE;;OAEd;MACDlB,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE;SACV;QACDiB,OAAO,EAAE;UACPC,OAAO,EAAE,IAAI;UACbV,IAAI,EAAE,OAAO;UACbD,SAAS,EAAE,KAAK;UAChBY,QAAQ,EAAE,SAAS;UACnBC,QAAQ,EAAE,IAAI;UACdC,eAAe,EAAE,oBAAoB;UACrCC,UAAU,EAAE,SAAS;UACrBC,SAAS,EAAE,SAAS;UACpBC,WAAW,EAAE,0BAA0B;UACvCZ,WAAW,EAAE,CAAC;UACda,YAAY,EAAE,CAAC;UACfC,aAAa,EAAE,KAAK;UACpBC,OAAO,EAAE,EAAE;UACXC,YAAY,EAAE,CAAC;UACfC,SAAS,EAAE,CAAC;UACZC,SAAS,EAAE;YACTC,IAAI,EAAE,EAAE;YACRC,MAAM,EAAE;WACT;UACDC,QAAQ,EAAE;YACRF,IAAI,EAAE;WACP;UACDG,SAAS,EAAE;YACTC,KAAK,EAAE,SAAAA,CAASC,OAAY;cAC1B,IAAIA,OAAO,IAAIA,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;gBACjC,OAAO,QAAQ,GAAGD,OAAO,CAAC,CAAC,CAAC,CAACE,KAAK;;cAEpC,OAAO,aAAa;YACtB,CAAC;YACDA,KAAK,EAAE,SAAAA,CAASF,OAAY;cAC1B,MAAME,KAAK,GAAGF,OAAO,CAACG,OAAO,CAACD,KAAK,IAAI,EAAE;cACzC,MAAME,KAAK,GAAGJ,OAAO,CAACK,MAAM,CAACtC,CAAC;cAC9B,OAAO,GAAGmC,KAAK,KAAKE,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC,KAAK;YAC3C;;;OAGL;MACDzC,MAAM,EAAE;QACNC,CAAC,EAAE;UACDF,OAAO,EAAE,KAAK;UACd2C,IAAI,EAAE;YACJ3C,OAAO,EAAE;;SAEZ;QACDG,CAAC,EAAE;UACDH,OAAO,EAAE,KAAK;UACd2C,IAAI,EAAE;YACJ3C,OAAO,EAAE;;;OAGd;MACD4C,SAAS,EAAE;QACTC,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAE;;KAEX;EAKP;EAEAnD,gBAAgBA,CAAA;IACd,IAAI,CAACd,YAAY,GAAG,IAAI;IACxB,IAAI,CAACnB,gBAAgB,CAACiC,gBAAgB,EAAE,CAACoD,IAAI,CAACC,aAAa,IAAG;MAC5D,IAAIA,aAAa,CAACX,MAAM,GAAG,CAAC,EAAC;QAC3B,IAAI,CAAC5E,eAAe,CAACwF,eAAe,EAAE,CAACF,IAAI,CAAC9D,YAAY,IAAG;UACzD,IAAI,CAACnB,YAAY,CAACoF,WAAW,CAACjE,YAAY,CAAC;UAC3C,IAAI,CAACjB,QAAQ,GAAGiB,YAAY;UAC5BkE,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;UAC3BD,OAAO,CAACC,GAAG,CAACnE,YAAY,CAAC;UAEzB;UACA,IAAI,CAACoE,uBAAuB,EAAE;UAE9B;UACA,IAAI,CAACC,YAAY,EAAE;UAEnB;UACA,IAAI,CAACC,mBAAmB,EAAE;UAE1B,IAAI,CAAC1E,YAAY,GAAG,KAAK;QAC3B,CAAC,CAAC;OACH,MAAI;QACH,IAAI,CAAChB,MAAM,CAAC2F,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;;IAE5C,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAG;MACfP,OAAO,CAACO,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,IAAI,CAAC7E,YAAY,GAAG,KAAK;IAC3B,CAAC,CAAC;EACJ;EAEA;EACA0E,mBAAmBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACvF,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACqE,MAAM,KAAK,CAAC,EAAE;IAElD;IACA,IAAI,CAACrE,QAAQ,CAAC2F,OAAO,CAACC,OAAO,IAAG;MAC5B,IAAI,CAACC,eAAe,CAACD,OAAO,CAAC;IACjC,CAAC,CAAC;EACN;EAEA;EACAC,eAAeA,CAACD,OAAgB;IAC5B,IAAI,CAACA,OAAO,EAAE;IAEd,MAAME,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,kBAAkB,GAAG,IAAID,IAAI,CAACD,GAAG,CAACG,WAAW,EAAE,EAAEH,GAAG,CAACI,QAAQ,EAAE,EAAEJ,GAAG,CAACK,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,EAAE;IAC5G,MAAMC,gBAAgB,GAAG,IAAIN,IAAI,CAACD,GAAG,CAACG,WAAW,EAAE,EAAEH,GAAG,CAACI,QAAQ,EAAE,EAAEJ,GAAG,CAACK,OAAO,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,EAAE;IAE9G,IAAIE,OAAO,GAA2B;MAClCC,MAAM,EAAEX,OAAO,CAACY,SAAS;MACzBC,SAAS,EAAE,CAAC;MACZC,aAAa,EAAEV,kBAAkB;MACjCW,WAAW,EAAEN,gBAAgB;MAC7BO,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAElB,OAAO,CAACmB;KACtB;IAED,IAAI,CAACtH,eAAe,CAACuH,sBAAsB,CAACV,OAAO,CAAC,CAACvB,IAAI,CAACkC,IAAI,IAAG;MAC7D,IAAIA,IAAI,IAAIA,IAAI,CAACA,IAAI,EAAE;QACnB;QACA,MAAMC,YAAY,GAAG,IAAI,CAACC,uBAAuB,CAACF,IAAI,CAACA,IAAI,CAAC;QAE5D,MAAMG,aAAa,GAAGC,gBAAgB,CAACC,QAAQ,CAACC,eAAe,CAAC;QAEhE,MAAMC,QAAQ,GAAG;UACbC,MAAM,EAAEP,YAAY,CAACQ,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAC9BA,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC7H,SAAS,CAAC8H,kBAAkB,CAACF,CAAC,CAACG,QAAQ,CAAC,GAAG,EAAE,CACvE;UACDC,QAAQ,EAAE,CACN;YACIzD,KAAK,EAAE,cAAc;YACrB2C,IAAI,EAAEC,YAAY,CAACQ,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACK,WAAW,CAAC;YAC1CC,IAAI,EAAE,KAAK;YACX5E,eAAe,EAAE+D,aAAa,CAACc,gBAAgB,CAAC,eAAe,CAAC;YAChE1E,WAAW,EAAE4D,aAAa,CAACc,gBAAgB,CAAC,eAAe,CAAC;YAC5DvF,OAAO,EAAE;WACZ,EACD;YACI2B,KAAK,EAAE,mBAAmB;YAC1B2C,IAAI,EAAEC,YAAY,CAACQ,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACQ,eAAe,CAAC;YAC9CF,IAAI,EAAE,KAAK;YACX5E,eAAe,EAAE+D,aAAa,CAACc,gBAAgB,CAAC,eAAe,CAAC;YAChE1E,WAAW,EAAE4D,aAAa,CAACc,gBAAgB,CAAC,eAAe,CAAC;YAC5DvF,OAAO,EAAE;WACZ;SAER;QAED;QACA,IAAI,CAAC1B,YAAY,CAACmH,GAAG,CAACxC,OAAO,CAACmB,EAAE,EAAES,QAAQ,CAAC;QAC3C,IAAI,CAACrG,eAAe,CAACiH,GAAG,CAACxC,OAAO,CAACmB,EAAE,EAAEG,YAAY,CAAC;QAClD,IAAI,CAAC9F,eAAe,CAACgH,GAAG,CAACxC,OAAO,CAACmB,EAAE,EAAEE,IAAI,CAACoB,GAAG,CAAC;;IAEtD,CAAC,CAAC,CAAC5C,KAAK,CAACC,KAAK,IAAG;MACbP,OAAO,CAACO,KAAK,CAAC,kCAAkCE,OAAO,CAACmB,EAAE,GAAG,EAAErB,KAAK,CAAC;IACzE,CAAC,CAAC;EACN;EAGA;EACA4C,uBAAuBA,CAAC1C,OAAgB;IACpC;IACA,IAAIA,OAAO,IAAIA,OAAO,CAACmB,EAAE,IAAI,IAAI,CAAC9F,YAAY,CAACsH,GAAG,CAAC3C,OAAO,CAACmB,EAAE,CAAC,EAAE;MAC5D,OAAO,IAAI,CAAC9F,YAAY,CAACuH,GAAG,CAAC5C,OAAO,CAACmB,EAAE,CAAC;;IAG5C;IACA,OAAO,IAAI;EACf;EAEA0B,kBAAkBA,CAAC3B,SAAgB;IACjC,OAAO,IAAI,CAAC1F,eAAe,CAACoH,GAAG,CAAC1B,SAAS,CAAC;EAC5C;EAEA4B,oBAAoBA,CAAC5B,SAAgB;IACnC,IAAIG,IAAI,GAAG,IAAI,CAAC9F,eAAe,CAACqH,GAAG,CAAC1B,SAAS,CAAC;IAC9C,IAAI,CAACG,IAAI,IAAIA,IAAI,CAAC5C,MAAM,KAAK,CAAC,EAAE;MAC9B,OAAO,CAAC;KACT,MAEC,OAAO,IAAIsE,GAAG,CAAC1B,IAAI,CAACS,GAAG,CAACkB,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC9E,IAAI;EACpD;EAEA+E,oBAAoBA,CAAChC,SAAiB;IACpC,MAAMG,IAAI,GAAG,IAAI,CAAC9F,eAAe,CAACqH,GAAG,CAAC1B,SAAS,CAAC;IAChD,IAAI,CAACG,IAAI,IAAIA,IAAI,CAAC5C,MAAM,KAAK,CAAC,EAAE,OAAO,GAAG;IAE1C,MAAM0E,MAAM,GAAG9B,IAAI,CAAC+B,MAAM,CAAC,CAACC,WAAW,EAAEC,OAAO,KAAI;MAClD,OAAO,IAAInD,IAAI,CAACmD,OAAO,CAACpB,QAAQ,CAAC,CAACqB,OAAO,EAAE,GAAG,IAAIpD,IAAI,CAACkD,WAAW,CAACnB,QAAQ,CAAC,CAACqB,OAAO,EAAE,GAClFD,OAAO,GACPD,WAAW;IACjB,CAAC,CAAC;IAEF,OAAO,IAAIlD,IAAI,CAACgD,MAAM,CAACjB,QAAQ,CAAC,CAACsB,cAAc,CAAC,OAAO,EAAE;MACvDC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE,SAAS;MACjBC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE;KACX,CAAC;EACJ;EAGAC,YAAYA,CAACC,KAAU;IACnB,MAAMpF,KAAK,GAAGoF,KAAK,CAACpF,KAAK;IAEzB,IAAIA,KAAK,CAACqF,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;MAC1B,IAAI,CAACxJ,SAAS,GAAG,CAAC,CAAC;MACnB,IAAI,CAACC,SAAS,GAAGkE,KAAK,CAACsF,SAAS,CAAC,CAAC,EAAEtF,KAAK,CAACH,MAAM,CAAC;KACpD,MAAM;MACH,IAAI,CAAChE,SAAS,GAAG,CAAC;MAClB,IAAI,CAACC,SAAS,GAAGkE,KAAK;;EAE9B;EAEAuF,QAAQA,CAACC,EAAY,EAAEJ,KAAY;IAC/BI,EAAE,CAACC,MAAM,CAAEL,KAAK,CAACM,MAA2B,CAAC1F,KAAK,CAAC;EACvD;EAEA;EACAa,uBAAuBA,CAAA;IACnB;IACA,MAAM8E,SAAS,GAAG,CAAC,GAAG,IAAIxB,GAAG,CAAC,IAAI,CAAC3I,QAAQ,CAAC0H,GAAG,CAAC9B,OAAO,IAAIA,OAAO,CAACwE,QAAQ,CAAC,CAAC,CAAC;IAC9E,IAAI,CAACjK,kBAAkB,GAAGgK,SAAS,CAACzC,GAAG,CAAC0C,QAAQ,KAAK;MACjD9F,KAAK,EAAE8F,QAAQ;MACf5F,KAAK,EAAE4F;KACV,CAAC,CAAC;IAEH;IACA,MAAMC,QAAQ,GAAG,CAAC,GAAG,IAAI1B,GAAG,CAAC,IAAI,CAAC3I,QAAQ,CAAC0H,GAAG,CAAC9B,OAAO,IAAIA,OAAO,CAAC0E,MAAM,CAAC,CAAC,CAAC;IAC3E,IAAI,CAAClK,iBAAiB,GAAGiK,QAAQ,CAAC3C,GAAG,CAAC4C,MAAM,KAAK;MAC7ChG,KAAK,EAAEgG,MAAM;MACb9F,KAAK,EAAE8F;KACV,CAAC,CAAC;EACP;EAEA;EACAhF,YAAYA,CAAA;IACR,IAAIiF,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACvK,QAAQ,CAAC;IAEjC;IACA,IAAI,IAAI,CAACO,UAAU,EAAE;MACjB,MAAMiK,WAAW,GAAG,IAAI,CAACjK,UAAU,CAACkK,WAAW,EAAE;MACjDF,QAAQ,GAAGA,QAAQ,CAACN,MAAM,CAACrE,OAAO,IAC9BA,OAAO,CAACiD,IAAI,CAAC4B,WAAW,EAAE,CAACC,QAAQ,CAACF,WAAW,CAAC,IAChD5E,OAAO,CAACwE,QAAQ,CAACK,WAAW,EAAE,CAACC,QAAQ,CAACF,WAAW,CAAC,IACnD5E,OAAO,CAAC+E,QAAQ,IAAI/E,OAAO,CAAC+E,QAAQ,CAACF,WAAW,EAAE,CAACC,QAAQ,CAACF,WAAW,CAAE,CAC7E;;IAGL;IACA,IAAI,IAAI,CAAChK,gBAAgB,EAAE;MACvB+J,QAAQ,GAAGA,QAAQ,CAACN,MAAM,CAACrE,OAAO,IAAIA,OAAO,CAACwE,QAAQ,KAAK,IAAI,CAAC5J,gBAAgB,CAAC;;IAGrF;IACA,IAAI,IAAI,CAACC,cAAc,EAAE;MACrB8J,QAAQ,GAAGA,QAAQ,CAACN,MAAM,CAACrE,OAAO,IAAIA,OAAO,CAAC0E,MAAM,KAAK,IAAI,CAAC7J,cAAc,CAAC;;IAGjF,IAAI,CAACR,gBAAgB,GAAGsK,QAAQ;IAChC,IAAI,CAAC3J,UAAU,GAAG2J,QAAQ,CAAClG,MAAM;IACjC,IAAI,CAAC3D,WAAW,GAAG,CAAC,CAAC,CAAC;IACtB,IAAI,CAACkK,gBAAgB,EAAE;EAC3B;EAEA;EACAA,gBAAgBA,CAAA;IACZ,MAAMC,UAAU,GAAG,IAAI,CAACnK,WAAW,GAAG,IAAI,CAACC,YAAY;IACvD,MAAMmK,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAClK,YAAY;IAC/C,IAAI,CAACT,iBAAiB,GAAG,IAAI,CAACD,gBAAgB,CAAC8K,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC9E;EAEA;EACAE,cAAcA,CAACpB,KAAY;IACvB,IAAI,CAACrJ,UAAU,GAAIqJ,KAAK,CAACM,MAA2B,CAAC1F,KAAK;IAC1D,IAAI,CAACc,YAAY,EAAE;EACvB;EAEA;EACA2F,gBAAgBA,CAACrB,KAAU;IACvB,IAAI,CAACpJ,gBAAgB,GAAGoJ,KAAK,CAACpF,KAAK,IAAI,EAAE;IACzC,IAAI,CAACc,YAAY,EAAE;EACvB;EAEA;EACA4F,cAAcA,CAACtB,KAAU;IACrB,IAAI,CAACnJ,cAAc,GAAGmJ,KAAK,CAACpF,KAAK,IAAI,EAAE;IACvC,IAAI,CAACc,YAAY,EAAE;EACvB;EAEA;EACA6F,YAAYA,CAACvB,KAAU;IACnB,IAAI,CAAClJ,WAAW,GAAGkJ,KAAK,CAACwB,IAAI;IAC7B,IAAI,CAACzK,YAAY,GAAGiJ,KAAK,CAACyB,IAAI;IAC9B,IAAI,CAACT,gBAAgB,EAAE;EAC3B;EAEA;EACAU,WAAWA,CAAA;IACP,IAAI,CAAC3J,gBAAgB,EAAE;EAC3B;EAEA;EACA4J,YAAYA,CAAA;IACR,IAAI,CAAChL,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC6E,YAAY,EAAE;EACvB;EAEA;EACAkG,gBAAgBA,CAAC5F,OAAgB;IAC7B,MAAM6F,SAAS,GAAG,IAAI,CAAC/C,oBAAoB,CAAC9C,OAAO,CAACmB,EAAE,IAAI,EAAE,CAAC;IAC7D,OAAO0E,SAAS,GAAG,CAAC,IACZ7F,OAAO,CAAC8F,IAAI,IAAI9F,OAAO,CAAC8F,IAAI,GAAG,CAAE,IACjC9F,OAAO,CAAC+F,MAAM,IAAI/F,OAAO,CAAC+F,MAAM,GAAG,CAAE,IACrC/F,OAAO,CAACgG,GAAG,IAAIhG,OAAO,CAACgG,GAAG,GAAG,CAAE;EAC3C;EAEQzE,uBAAuBA,CAACF,IAAW;IACvC,MAAMnB,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAM8F,WAAW,GAAG/F,GAAG,CAACqD,OAAO,EAAE;IAEjC,OAAOlC,IAAI,CAACgD,MAAM,CAACrB,IAAI,IAAG;MACtB,MAAMkD,YAAY,GAAG,IAAI/F,IAAI,CAAC6C,IAAI,CAACd,QAAQ,CAAC;MAC5C,MAAMiE,QAAQ,GAAGD,YAAY,CAAC3C,OAAO,EAAE;MAEvC;MACA,OAAO4C,QAAQ,IAAIF,WAAW;IAClC,CAAC,CAAC;EACN;EAGAG,OAAOA,CAAA;IACH,IAAI,CAACC,MAAM,GAAG,2FAA2F;EAC7G;EAEA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEAC,WAAWA,CAAA;IACP,IAAI,IAAI,CAAC7K,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAAC8K,WAAW,EAAE;;EAEvC;CACH;AAlgBY7M,cAAc,GAAA8M,UAAA,EAJ1BhN,SAAS,CAAC;EACPiN,WAAW,EAAE,wBAAwB;EACrCC,SAAS,EAAE,CAAC,wBAAwB;CACvC,CAAC,C,EACWhN,cAAc,CAkgB1B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}