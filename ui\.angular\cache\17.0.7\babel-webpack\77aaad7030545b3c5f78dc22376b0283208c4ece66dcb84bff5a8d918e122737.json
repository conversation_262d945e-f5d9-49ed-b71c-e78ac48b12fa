{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { ChartModule } from 'primeng/chart';\nimport { MenuModule } from 'primeng/menu';\nimport { TableModule } from 'primeng/table';\nimport { ButtonModule } from 'primeng/button';\nimport { StyleClassModule } from 'primeng/styleclass';\nimport { PanelMenuModule } from 'primeng/panelmenu';\nimport { ViewStationRoutingModule } from './view-routing.module';\nimport { DataViewModule } from 'primeng/dataview';\nimport { KnobModule } from 'primeng/knob';\nimport { PickListModule } from 'primeng/picklist';\nimport { OrderListModule } from 'primeng/orderlist';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { RatingModule } from 'primeng/rating';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { ToolbarModule } from 'primeng/toolbar';\nimport { FontAwesomeModule } from '@fortawesome/angular-fontawesome';\nimport { fas } from '@fortawesome/free-solid-svg-icons';\nimport { CalendarModule } from 'primeng/calendar';\nimport { CardModule } from 'primeng/card';\nimport { TagModule } from 'primeng/tag';\nimport { BadgeModule } from 'primeng/badge';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\nimport { SkeletonModule } from 'primeng/skeleton';\nimport { RippleModule } from 'primeng/ripple';\nimport { SharedModule } from '../../../shared/shared.module';\nimport { DividerModule } from 'primeng/divider';\nimport { ToastModule } from 'primeng/toast';\nimport { FileUploadModule } from 'primeng/fileupload';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@fortawesome/angular-fontawesome\";\nexport let ViewStationModule = /*#__PURE__*/(() => {\n  class ViewStationModule {\n    constructor(library) {\n      library.addIconPacks(fas);\n    }\n    static #_ = this.ɵfac = function ViewStationModule_Factory(t) {\n      return new (t || ViewStationModule)(i0.ɵɵinject(i1.FaIconLibrary));\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ViewStationModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, ChartModule, MenuModule, TableModule, StyleClassModule, PanelMenuModule, ButtonModule, ViewStationRoutingModule, DataViewModule, KnobModule, FormsModule, DataViewModule, PickListModule, OrderListModule, InputTextModule, DropdownModule, CalendarModule, RatingModule, ButtonModule, BreadcrumbModule, ToolbarModule, FontAwesomeModule, CardModule, TagModule, BadgeModule, TooltipModule, ProgressSpinnerModule, SkeletonModule, RippleModule, DividerModule, ToastModule, FileUploadModule, SharedModule]\n    });\n  }\n  return ViewStationModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}