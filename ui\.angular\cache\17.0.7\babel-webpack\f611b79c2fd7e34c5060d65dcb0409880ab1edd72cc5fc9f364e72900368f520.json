{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../service/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"../../../service/error.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/checkbox\";\nimport * as i9 from \"primeng/inputtext\";\nimport * as i10 from \"primeng/password\";\nfunction LoginComponent_div_25_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Username is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_25_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Username must be at least 3 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelement(1, \"fa-icon\", 34);\n    i0.ɵɵtemplate(2, LoginComponent_div_25_span_2_Template, 2, 0, \"span\", 35)(3, LoginComponent_div_25_span_3_Template, 2, 0, \"span\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r0.loginForm.get(\"username\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r0.loginForm.get(\"username\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n  }\n}\nfunction LoginComponent_div_32_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_32_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Password must be at least 6 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelement(1, \"fa-icon\", 34);\n    i0.ɵɵtemplate(2, LoginComponent_div_32_span_2_Template, 2, 0, \"span\", 35)(3, LoginComponent_div_32_span_3_Template, 2, 0, \"span\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r1.loginForm.get(\"password\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r1.loginForm.get(\"password\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n  }\n}\nfunction LoginComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵelement(1, \"fa-icon\", 37);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.errorMessage);\n  }\n}\nfunction LoginComponent_fa_icon_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"fa-icon\", 38);\n  }\n}\nexport class LoginComponent {\n  constructor(fb, authService, router, messageService, errorService) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.messageService = messageService;\n    this.errorService = errorService;\n    this.errorMessage = '';\n    this.isLoading = false;\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.initializeForm();\n    this.loadRememberedCredentials();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  initializeForm() {\n    this.loginForm = this.fb.group({\n      username: ['', [Validators.required, Validators.minLength(3), Validators.pattern(/^[a-zA-Z0-9_]+$/) // Only alphanumeric and underscore\n      ]],\n\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      rememberMe: [false]\n    });\n    // Clear error message when form values change\n    this.loginForm.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      if (this.errorMessage) {\n        this.errorMessage = '';\n      }\n    });\n  }\n  login() {\n    if (this.loginForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n    this.isLoading = true;\n    this.errorMessage = '';\n    const {\n      username,\n      password,\n      rememberMe\n    } = this.loginForm.value;\n    this.authService.login(username, password).pipe(takeUntil(this.destroy$)).subscribe({\n      next: () => {\n        this.isLoading = false;\n        // Handle remember me functionality\n        if (rememberMe) {\n          localStorage.setItem('rememberMe', 'true');\n          localStorage.setItem('username', username);\n        } else {\n          localStorage.removeItem('rememberMe');\n          localStorage.removeItem('username');\n        }\n        this.messageService.add({\n          severity: 'success',\n          summary: 'Success',\n          detail: 'Login successful! Redirecting...'\n        });\n        // Small delay for better UX\n        setTimeout(() => {\n          this.router.navigate(['/app/index']);\n        }, 1000);\n      },\n      error: error => {\n        this.isLoading = false;\n        this.handleLoginError(error);\n      }\n    });\n  }\n  handleLoginError(error) {\n    let errorMessage = 'Login failed. Please try again.';\n    if (error.status === 401) {\n      errorMessage = 'Invalid username or password. Please check your credentials and try again.';\n    } else if (error.status === 403) {\n      errorMessage = 'Account is disabled. Please contact support.';\n    } else if (error.status === 429) {\n      errorMessage = 'Too many login attempts. Please try again later.';\n    } else if (error.status === 0) {\n      errorMessage = 'Unable to connect to server. Please check your internet connection.';\n    } else if (error.status >= 500) {\n      errorMessage = 'Server error. Please try again later.';\n    }\n    this.errorMessage = errorMessage;\n    // Also add to global error service for the error modal\n    this.errorService.addError(error);\n    // Show toast message\n    this.messageService.add({\n      severity: 'error',\n      summary: 'Login Failed',\n      detail: errorMessage\n    });\n  }\n  markFormGroupTouched() {\n    Object.keys(this.loginForm.controls).forEach(key => {\n      const control = this.loginForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  redirectRegister() {\n    this.router.navigate(['/auth/register']);\n  }\n  forgotPassword() {\n    this.messageService.add({\n      severity: 'info',\n      summary: 'Forgot Password',\n      detail: 'Password reset functionality will be available soon.'\n    });\n  }\n  // Auto-fill remembered username\n  loadRememberedCredentials() {\n    const rememberMe = localStorage.getItem('rememberMe');\n    const savedUsername = localStorage.getItem('username');\n    if (rememberMe === 'true' && savedUsername) {\n      this.loginForm.patchValue({\n        username: savedUsername,\n        rememberMe: true\n      });\n    }\n  }\n  static #_ = this.ɵfac = function LoginComponent_Factory(t) {\n    return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ErrorService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LoginComponent,\n    selectors: [[\"app-login\"]],\n    decls: 48,\n    vars: 14,\n    consts: [[1, \"auth-container\"], [1, \"auth-background\"], [1, \"bg-element\", \"bg-element-1\"], [1, \"bg-element\", \"bg-element-2\"], [1, \"bg-element\", \"bg-element-3\"], [1, \"auth-content\"], [1, \"auth-card\"], [1, \"auth-header\"], [1, \"logo-container\"], [\"src\", \"assets/layout/images/logo-dark.png\", \"alt\", \"SolarKapital\", 1, \"logo\"], [1, \"brand-name\"], [1, \"auth-title\"], [1, \"auth-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"form-group\"], [\"for\", \"username\", 1, \"form-label\"], [\"icon\", \"user\", 1, \"label-icon\"], [1, \"input-wrapper\"], [\"id\", \"username\", \"type\", \"text\", \"formControlName\", \"username\", \"placeholder\", \"Enter your username\", \"pInputText\", \"\", 1, \"form-input\"], [\"icon\", \"user\", 1, \"input-icon\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"for\", \"password\", 1, \"form-label\"], [\"icon\", \"lock\", 1, \"label-icon\"], [\"id\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"Enter your password\", \"styleClass\", \"w-full\", \"inputStyleClass\", \"form-input password-input\", 3, \"toggleMask\", \"feedback\"], [1, \"form-options\"], [1, \"remember-me\"], [\"id\", \"rememberMe\", \"formControlName\", \"rememberMe\", \"styleClass\", \"custom-checkbox\", 3, \"binary\"], [\"for\", \"rememberMe\", 1, \"remember-label\"], [1, \"forgot-password\", 3, \"click\"], [\"class\", \"global-error\", 4, \"ngIf\"], [\"type\", \"submit\", \"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Sign In\", 1, \"auth-button\", 3, \"disabled\", \"loading\"], [\"icon\", \"sign-in-alt\", \"class\", \"button-icon\", 4, \"ngIf\"], [1, \"auth-footer\"], [1, \"register-link\", 3, \"click\"], [1, \"error-message\"], [\"icon\", \"exclamation-circle\", 1, \"error-icon\"], [4, \"ngIf\"], [1, \"global-error\"], [\"icon\", \"exclamation-triangle\", 1, \"error-icon\"], [\"icon\", \"sign-in-alt\", 1, \"button-icon\"]],\n    template: function LoginComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8);\n        i0.ɵɵelement(9, \"img\", 9);\n        i0.ɵɵelementStart(10, \"h1\", 10);\n        i0.ɵɵtext(11, \"SolarKapital\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(12, \"div\", 11)(13, \"h2\");\n        i0.ɵɵtext(14, \"Welcome Back\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"p\");\n        i0.ɵɵtext(16, \"Sign in to your account to continue\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(17, \"form\", 12);\n        i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_17_listener() {\n          return ctx.login();\n        });\n        i0.ɵɵelementStart(18, \"div\", 13)(19, \"label\", 14);\n        i0.ɵɵelement(20, \"fa-icon\", 15);\n        i0.ɵɵtext(21, \" Username \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"div\", 16);\n        i0.ɵɵelement(23, \"input\", 17)(24, \"fa-icon\", 18);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(25, LoginComponent_div_25_Template, 4, 2, \"div\", 19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(26, \"div\", 13)(27, \"label\", 20);\n        i0.ɵɵelement(28, \"fa-icon\", 21);\n        i0.ɵɵtext(29, \" Password \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(30, \"div\", 16);\n        i0.ɵɵelement(31, \"p-password\", 22);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(32, LoginComponent_div_32_Template, 4, 2, \"div\", 19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(33, \"div\", 23)(34, \"div\", 24);\n        i0.ɵɵelement(35, \"p-checkbox\", 25);\n        i0.ɵɵelementStart(36, \"label\", 26);\n        i0.ɵɵtext(37, \"Remember me\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(38, \"a\", 27);\n        i0.ɵɵlistener(\"click\", function LoginComponent_Template_a_click_38_listener() {\n          return ctx.forgotPassword();\n        });\n        i0.ɵɵtext(39, \" Forgot password? \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(40, LoginComponent_div_40_Template, 4, 1, \"div\", 28);\n        i0.ɵɵelementStart(41, \"button\", 29);\n        i0.ɵɵtemplate(42, LoginComponent_fa_icon_42_Template, 1, 0, \"fa-icon\", 30);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(43, \"div\", 31)(44, \"p\");\n        i0.ɵɵtext(45, \"Don't have an account? \");\n        i0.ɵɵelementStart(46, \"a\", 32);\n        i0.ɵɵlistener(\"click\", function LoginComponent_Template_a_click_46_listener() {\n          return ctx.redirectRegister();\n        });\n        i0.ɵɵtext(47, \" Create one here \");\n        i0.ɵɵelementEnd()()()()()()();\n      }\n      if (rf & 2) {\n        let tmp_1_0;\n        let tmp_2_0;\n        let tmp_3_0;\n        let tmp_6_0;\n        i0.ɵɵadvance(17);\n        i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n        i0.ɵɵadvance(6);\n        i0.ɵɵclassProp(\"error\", ((tmp_1_0 = ctx.loginForm.get(\"username\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.loginForm.get(\"username\")) == null ? null : tmp_1_0.touched));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.loginForm.get(\"username\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.loginForm.get(\"username\")) == null ? null : tmp_2_0.touched));\n        i0.ɵɵadvance(6);\n        i0.ɵɵclassProp(\"error\", ((tmp_3_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_3_0.touched));\n        i0.ɵɵproperty(\"toggleMask\", true)(\"feedback\", false);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_6_0.touched));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"binary\", true);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"disabled\", ctx.loginForm.invalid || ctx.isLoading)(\"loading\", ctx.isLoading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n      }\n    },\n    dependencies: [i6.NgIf, i7.ButtonDirective, i8.Checkbox, i9.InputText, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i10.Password],\n    styles: [\"\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\t\\\"use strict\\\";\\n\\n \\t\\n\\n \\t\\n\\n })()[_ngcontent-%COMP%]\\n;\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵtemplate", "LoginComponent_div_25_span_2_Template", "LoginComponent_div_25_span_3_Template", "ɵɵadvance", "ɵɵproperty", "tmp_0_0", "ctx_r0", "loginForm", "get", "errors", "tmp_1_0", "LoginComponent_div_32_span_2_Template", "LoginComponent_div_32_span_3_Template", "ctx_r1", "ɵɵtextInterpolate", "ctx_r2", "errorMessage", "LoginComponent", "constructor", "fb", "authService", "router", "messageService", "errorService", "isLoading", "destroy$", "ngOnInit", "initializeForm", "loadRememberedCredentials", "ngOnDestroy", "next", "complete", "group", "username", "required", "<PERSON><PERSON><PERSON><PERSON>", "pattern", "password", "rememberMe", "valueChanges", "pipe", "subscribe", "login", "invalid", "markFormGroupTouched", "value", "localStorage", "setItem", "removeItem", "add", "severity", "summary", "detail", "setTimeout", "navigate", "error", "handleLoginError", "status", "addError", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "redirectRegister", "forgotPassword", "getItem", "savedUsername", "patchValue", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "i4", "MessageService", "i5", "ErrorService", "_2", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_17_listener", "LoginComponent_div_25_Template", "LoginComponent_div_32_Template", "LoginComponent_Template_a_click_38_listener", "LoginComponent_div_40_Template", "LoginComponent_fa_icon_42_Template", "LoginComponent_Template_a_click_46_listener", "ɵɵclassProp", "touched", "tmp_2_0", "tmp_3_0", "tmp_6_0"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\auth\\login\\login.component.ts", "D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\auth\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { MessageService } from 'primeng/api';\nimport { AuthService } from '../../../service/auth.service';\nimport { ErrorService } from '../../../service/error.service';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.scss']\n})\nexport class LoginComponent implements OnInit, OnDestroy {\n  loginForm!: FormGroup;\n  errorMessage = '';\n  isLoading = false;\n  private destroy$ = new Subject<void>();\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private messageService: MessageService,\n    private errorService: ErrorService\n  ) {}\n\n  ngOnInit() {\n    this.initializeForm();\n    this.loadRememberedCredentials();\n  }\n\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private initializeForm() {\n    this.loginForm = this.fb.group({\n      username: ['', [\n        Validators.required,\n        Validators.minLength(3),\n        Validators.pattern(/^[a-zA-Z0-9_]+$/) // Only alphanumeric and underscore\n      ]],\n      password: ['', [\n        Validators.required,\n        Validators.minLength(6)\n      ]],\n      rememberMe: [false]\n    });\n\n    // Clear error message when form values change\n    this.loginForm.valueChanges\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(() => {\n        if (this.errorMessage) {\n          this.errorMessage = '';\n        }\n      });\n  }\n\n  login() {\n    if (this.loginForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n\n    this.isLoading = true;\n    this.errorMessage = '';\n\n    const { username, password, rememberMe } = this.loginForm.value;\n\n    this.authService.login(username, password)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: () => {\n          this.isLoading = false;\n\n          // Handle remember me functionality\n          if (rememberMe) {\n            localStorage.setItem('rememberMe', 'true');\n            localStorage.setItem('username', username);\n          } else {\n            localStorage.removeItem('rememberMe');\n            localStorage.removeItem('username');\n          }\n\n          this.messageService.add({\n            severity: 'success',\n            summary: 'Success',\n            detail: 'Login successful! Redirecting...'\n          });\n\n          // Small delay for better UX\n          setTimeout(() => {\n            this.router.navigate(['/app/index']);\n          }, 1000);\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.handleLoginError(error);\n        }\n      });\n  }\n\n  private handleLoginError(error: any) {\n    let errorMessage = 'Login failed. Please try again.';\n\n    if (error.status === 401) {\n      errorMessage = 'Invalid username or password. Please check your credentials and try again.';\n    } else if (error.status === 403) {\n      errorMessage = 'Account is disabled. Please contact support.';\n    } else if (error.status === 429) {\n      errorMessage = 'Too many login attempts. Please try again later.';\n    } else if (error.status === 0) {\n      errorMessage = 'Unable to connect to server. Please check your internet connection.';\n    } else if (error.status >= 500) {\n      errorMessage = 'Server error. Please try again later.';\n    }\n\n    this.errorMessage = errorMessage;\n\n    // Also add to global error service for the error modal\n    this.errorService.addError(error);\n\n    // Show toast message\n    this.messageService.add({\n      severity: 'error',\n      summary: 'Login Failed',\n      detail: errorMessage\n    });\n  }\n\n  private markFormGroupTouched() {\n    Object.keys(this.loginForm.controls).forEach(key => {\n      const control = this.loginForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  redirectRegister() {\n    this.router.navigate(['/auth/register']);\n  }\n\n  forgotPassword() {\n    this.messageService.add({\n      severity: 'info',\n      summary: 'Forgot Password',\n      detail: 'Password reset functionality will be available soon.'\n    });\n  }\n\n  // Auto-fill remembered username\n  private loadRememberedCredentials() {\n    const rememberMe = localStorage.getItem('rememberMe');\n    const savedUsername = localStorage.getItem('username');\n\n    if (rememberMe === 'true' && savedUsername) {\n      this.loginForm.patchValue({\n        username: savedUsername,\n        rememberMe: true\n      });\n    }\n  }\n}\n\n", "<div class=\"auth-container\">\n    <div class=\"auth-background\">\n        <!-- Background Elements -->\n        <div class=\"bg-element bg-element-1\"></div>\n        <div class=\"bg-element bg-element-2\"></div>\n        <div class=\"bg-element bg-element-3\"></div>\n    </div>\n\n    <div class=\"auth-content\">\n        <div class=\"auth-card\">\n            <!-- Header -->\n            <div class=\"auth-header\">\n                <div class=\"logo-container\">\n                    <img src=\"assets/layout/images/logo-dark.png\" alt=\"SolarKapital\" class=\"logo\">\n                    <h1 class=\"brand-name\">SolarKapital</h1>\n                </div>\n                <div class=\"auth-title\">\n                    <h2>Welcome Back</h2>\n                    <p>Sign in to your account to continue</p>\n                </div>\n            </div>\n\n            <!-- Login Form -->\n            <form [formGroup]=\"loginForm\" (ngSubmit)=\"login()\" class=\"auth-form\">\n                <!-- Username Field -->\n                <div class=\"form-group\">\n                    <label for=\"username\" class=\"form-label\">\n                        <fa-icon icon=\"user\" class=\"label-icon\"></fa-icon>\n                        Username\n                    </label>\n                    <div class=\"input-wrapper\">\n                        <input id=\"username\"\n                               type=\"text\"\n                               formControlName=\"username\"\n                               placeholder=\"Enter your username\"\n                               pInputText\n                               class=\"form-input\"\n                               [class.error]=\"loginForm.get('username')?.invalid && loginForm.get('username')?.touched\">\n                        <fa-icon icon=\"user\" class=\"input-icon\"></fa-icon>\n                    </div>\n                    <div class=\"error-message\" *ngIf=\"loginForm.get('username')?.invalid && loginForm.get('username')?.touched\">\n                        <fa-icon icon=\"exclamation-circle\" class=\"error-icon\"></fa-icon>\n                        <span *ngIf=\"loginForm.get('username')?.errors?.['required']\">Username is required</span>\n                        <span *ngIf=\"loginForm.get('username')?.errors?.['minlength']\">Username must be at least 3 characters</span>\n                    </div>\n                </div>\n\n                <!-- Password Field -->\n                <div class=\"form-group\">\n                    <label for=\"password\" class=\"form-label\">\n                        <fa-icon icon=\"lock\" class=\"label-icon\"></fa-icon>\n                        Password\n                    </label>\n                    <div class=\"input-wrapper\">\n                        <p-password id=\"password\"\n                                   formControlName=\"password\"\n                                   placeholder=\"Enter your password\"\n                                   [toggleMask]=\"true\"\n                                   [feedback]=\"false\"\n                                   styleClass=\"w-full\"\n                                   inputStyleClass=\"form-input password-input\"\n                                   [class.error]=\"loginForm.get('password')?.invalid && loginForm.get('password')?.touched\">\n                        </p-password>\n                    </div>\n                    <div class=\"error-message\" *ngIf=\"loginForm.get('password')?.invalid && loginForm.get('password')?.touched\">\n                        <fa-icon icon=\"exclamation-circle\" class=\"error-icon\"></fa-icon>\n                        <span *ngIf=\"loginForm.get('password')?.errors?.['required']\">Password is required</span>\n                        <span *ngIf=\"loginForm.get('password')?.errors?.['minlength']\">Password must be at least 6 characters</span>\n                    </div>\n                </div>\n\n                <!-- Remember Me & Links -->\n                <div class=\"form-options\">\n                    <div class=\"remember-me\">\n                        <p-checkbox id=\"rememberMe\"\n                                   formControlName=\"rememberMe\"\n                                   [binary]=\"true\"\n                                   styleClass=\"custom-checkbox\">\n                        </p-checkbox>\n                        <label for=\"rememberMe\" class=\"remember-label\">Remember me</label>\n                    </div>\n                    <a class=\"forgot-password\" (click)=\"forgotPassword()\">\n                        Forgot password?\n                    </a>\n                </div>\n\n                <!-- Error Message -->\n                <div class=\"global-error\" *ngIf=\"errorMessage\">\n                    <fa-icon icon=\"exclamation-triangle\" class=\"error-icon\"></fa-icon>\n                    <span>{{ errorMessage }}</span>\n                </div>\n\n                <!-- Submit Button -->\n                <button type=\"submit\"\n                        pButton\n                        pRipple\n                        label=\"Sign In\"\n                        class=\"auth-button\"\n                        [disabled]=\"loginForm.invalid || isLoading\"\n                        [loading]=\"isLoading\">\n                    <fa-icon icon=\"sign-in-alt\" class=\"button-icon\" *ngIf=\"!isLoading\"></fa-icon>\n                </button>\n\n                <!-- Register Link -->\n                <div class=\"auth-footer\">\n                    <p>Don't have an account?\n                        <a class=\"register-link\" (click)=\"redirectRegister()\">\n                            Create one here\n                        </a>\n                    </p>\n                </div>\n            </form>\n        </div>\n    </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAKnE,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;;;;;;;;;;;;;;ICmClBC,EAAA,CAAAC,cAAA,WAA8D;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACzFH,EAAA,CAAAC,cAAA,WAA+D;IAAAD,EAAA,CAAAE,MAAA,6CAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAHhHH,EAAA,CAAAC,cAAA,cAA4G;IACxGD,EAAA,CAAAI,SAAA,kBAAgE;IAChEJ,EAAA,CAAAK,UAAA,IAAAC,qCAAA,mBAAyF,IAAAC,qCAAA;IAE7FP,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFKH,EAAA,CAAAQ,SAAA,GAAqD;IAArDR,EAAA,CAAAS,UAAA,UAAAC,OAAA,GAAAC,MAAA,CAAAC,SAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAqD;IACrDd,EAAA,CAAAQ,SAAA,GAAsD;IAAtDR,EAAA,CAAAS,UAAA,UAAAM,OAAA,GAAAJ,MAAA,CAAAC,SAAA,CAAAC,GAAA,+BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,cAAsD;;;;;IAuB7Dd,EAAA,CAAAC,cAAA,WAA8D;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACzFH,EAAA,CAAAC,cAAA,WAA+D;IAAAD,EAAA,CAAAE,MAAA,6CAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAHhHH,EAAA,CAAAC,cAAA,cAA4G;IACxGD,EAAA,CAAAI,SAAA,kBAAgE;IAChEJ,EAAA,CAAAK,UAAA,IAAAW,qCAAA,mBAAyF,IAAAC,qCAAA;IAE7FjB,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFKH,EAAA,CAAAQ,SAAA,GAAqD;IAArDR,EAAA,CAAAS,UAAA,UAAAC,OAAA,GAAAQ,MAAA,CAAAN,SAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAqD;IACrDd,EAAA,CAAAQ,SAAA,GAAsD;IAAtDR,EAAA,CAAAS,UAAA,UAAAM,OAAA,GAAAG,MAAA,CAAAN,SAAA,CAAAC,GAAA,+BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,cAAsD;;;;;IAoBrEd,EAAA,CAAAC,cAAA,cAA+C;IAC3CD,EAAA,CAAAI,SAAA,kBAAkE;IAClEJ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAzBH,EAAA,CAAAQ,SAAA,GAAkB;IAAlBR,EAAA,CAAAmB,iBAAA,CAAAC,MAAA,CAAAC,YAAA,CAAkB;;;;;IAWxBrB,EAAA,CAAAI,SAAA,kBAA6E;;;ADtFjG,OAAM,MAAOkB,cAAc;EAMzBC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc,EACdC,cAA8B,EAC9BC,YAA0B;IAJ1B,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IATtB,KAAAP,YAAY,GAAG,EAAE;IACjB,KAAAQ,SAAS,GAAG,KAAK;IACT,KAAAC,QAAQ,GAAG,IAAIhC,OAAO,EAAQ;EAQnC;EAEHiC,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,yBAAyB,EAAE;EAClC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACJ,QAAQ,CAACK,IAAI,EAAE;IACpB,IAAI,CAACL,QAAQ,CAACM,QAAQ,EAAE;EAC1B;EAEQJ,cAAcA,CAAA;IACpB,IAAI,CAACpB,SAAS,GAAG,IAAI,CAACY,EAAE,CAACa,KAAK,CAAC;MAC7BC,QAAQ,EAAE,CAAC,EAAE,EAAE,CACbzC,UAAU,CAAC0C,QAAQ,EACnB1C,UAAU,CAAC2C,SAAS,CAAC,CAAC,CAAC,EACvB3C,UAAU,CAAC4C,OAAO,CAAC,iBAAiB,CAAC,CAAC;MAAA,CACvC,CAAC;;MACFC,QAAQ,EAAE,CAAC,EAAE,EAAE,CACb7C,UAAU,CAAC0C,QAAQ,EACnB1C,UAAU,CAAC2C,SAAS,CAAC,CAAC,CAAC,CACxB,CAAC;MACFG,UAAU,EAAE,CAAC,KAAK;KACnB,CAAC;IAEF;IACA,IAAI,CAAC/B,SAAS,CAACgC,YAAY,CACxBC,IAAI,CAAC9C,SAAS,CAAC,IAAI,CAAC+B,QAAQ,CAAC,CAAC,CAC9BgB,SAAS,CAAC,MAAK;MACd,IAAI,IAAI,CAACzB,YAAY,EAAE;QACrB,IAAI,CAACA,YAAY,GAAG,EAAE;;IAE1B,CAAC,CAAC;EACN;EAEA0B,KAAKA,CAAA;IACH,IAAI,IAAI,CAACnC,SAAS,CAACoC,OAAO,EAAE;MAC1B,IAAI,CAACC,oBAAoB,EAAE;MAC3B;;IAGF,IAAI,CAACpB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACR,YAAY,GAAG,EAAE;IAEtB,MAAM;MAAEiB,QAAQ;MAAEI,QAAQ;MAAEC;IAAU,CAAE,GAAG,IAAI,CAAC/B,SAAS,CAACsC,KAAK;IAE/D,IAAI,CAACzB,WAAW,CAACsB,KAAK,CAACT,QAAQ,EAAEI,QAAQ,CAAC,CACvCG,IAAI,CAAC9C,SAAS,CAAC,IAAI,CAAC+B,QAAQ,CAAC,CAAC,CAC9BgB,SAAS,CAAC;MACTX,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACN,SAAS,GAAG,KAAK;QAEtB;QACA,IAAIc,UAAU,EAAE;UACdQ,YAAY,CAACC,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC;UAC1CD,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEd,QAAQ,CAAC;SAC3C,MAAM;UACLa,YAAY,CAACE,UAAU,CAAC,YAAY,CAAC;UACrCF,YAAY,CAACE,UAAU,CAAC,UAAU,CAAC;;QAGrC,IAAI,CAAC1B,cAAc,CAAC2B,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,SAAS;UAClBC,MAAM,EAAE;SACT,CAAC;QAEF;QACAC,UAAU,CAAC,MAAK;UACd,IAAI,CAAChC,MAAM,CAACiC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACtC,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC/B,SAAS,GAAG,KAAK;QACtB,IAAI,CAACgC,gBAAgB,CAACD,KAAK,CAAC;MAC9B;KACD,CAAC;EACN;EAEQC,gBAAgBA,CAACD,KAAU;IACjC,IAAIvC,YAAY,GAAG,iCAAiC;IAEpD,IAAIuC,KAAK,CAACE,MAAM,KAAK,GAAG,EAAE;MACxBzC,YAAY,GAAG,4EAA4E;KAC5F,MAAM,IAAIuC,KAAK,CAACE,MAAM,KAAK,GAAG,EAAE;MAC/BzC,YAAY,GAAG,8CAA8C;KAC9D,MAAM,IAAIuC,KAAK,CAACE,MAAM,KAAK,GAAG,EAAE;MAC/BzC,YAAY,GAAG,kDAAkD;KAClE,MAAM,IAAIuC,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;MAC7BzC,YAAY,GAAG,qEAAqE;KACrF,MAAM,IAAIuC,KAAK,CAACE,MAAM,IAAI,GAAG,EAAE;MAC9BzC,YAAY,GAAG,uCAAuC;;IAGxD,IAAI,CAACA,YAAY,GAAGA,YAAY;IAEhC;IACA,IAAI,CAACO,YAAY,CAACmC,QAAQ,CAACH,KAAK,CAAC;IAEjC;IACA,IAAI,CAACjC,cAAc,CAAC2B,GAAG,CAAC;MACtBC,QAAQ,EAAE,OAAO;MACjBC,OAAO,EAAE,cAAc;MACvBC,MAAM,EAAEpC;KACT,CAAC;EACJ;EAEQ4B,oBAAoBA,CAAA;IAC1Be,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrD,SAAS,CAACsD,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACjD,MAAMC,OAAO,GAAG,IAAI,CAACzD,SAAS,CAACC,GAAG,CAACuD,GAAG,CAAC;MACvCC,OAAO,EAAEC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAAC7C,MAAM,CAACiC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEAa,cAAcA,CAAA;IACZ,IAAI,CAAC7C,cAAc,CAAC2B,GAAG,CAAC;MACtBC,QAAQ,EAAE,MAAM;MAChBC,OAAO,EAAE,iBAAiB;MAC1BC,MAAM,EAAE;KACT,CAAC;EACJ;EAEA;EACQxB,yBAAyBA,CAAA;IAC/B,MAAMU,UAAU,GAAGQ,YAAY,CAACsB,OAAO,CAAC,YAAY,CAAC;IACrD,MAAMC,aAAa,GAAGvB,YAAY,CAACsB,OAAO,CAAC,UAAU,CAAC;IAEtD,IAAI9B,UAAU,KAAK,MAAM,IAAI+B,aAAa,EAAE;MAC1C,IAAI,CAAC9D,SAAS,CAAC+D,UAAU,CAAC;QACxBrC,QAAQ,EAAEoC,aAAa;QACvB/B,UAAU,EAAE;OACb,CAAC;;EAEN;EAAC,QAAAiC,CAAA,G;qBAtJUtD,cAAc,EAAAtB,EAAA,CAAA6E,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA/E,EAAA,CAAA6E,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAjF,EAAA,CAAA6E,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAnF,EAAA,CAAA6E,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAArF,EAAA,CAAA6E,iBAAA,CAAAS,EAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAdlE,cAAc;IAAAmE,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCd3B/F,EAAA,CAAAC,cAAA,aAA4B;QAGpBD,EAAA,CAAAI,SAAA,aAA2C;QAG/CJ,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,aAA0B;QAKVD,EAAA,CAAAI,SAAA,aAA8E;QAC9EJ,EAAA,CAAAC,cAAA,cAAuB;QAAAD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAE5CH,EAAA,CAAAC,cAAA,eAAwB;QAChBD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACrBH,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAE,MAAA,2CAAmC;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAKlDH,EAAA,CAAAC,cAAA,gBAAqE;QAAvCD,EAAA,CAAAiG,UAAA,sBAAAC,kDAAA;UAAA,OAAYF,GAAA,CAAAjD,KAAA,EAAO;QAAA,EAAC;QAE9C/C,EAAA,CAAAC,cAAA,eAAwB;QAEhBD,EAAA,CAAAI,SAAA,mBAAkD;QAClDJ,EAAA,CAAAE,MAAA,kBACJ;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACRH,EAAA,CAAAC,cAAA,eAA2B;QACvBD,EAAA,CAAAI,SAAA,iBAMgG;QAEpGJ,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAK,UAAA,KAAA8F,8BAAA,kBAIM;QACVnG,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAC,cAAA,eAAwB;QAEhBD,EAAA,CAAAI,SAAA,mBAAkD;QAClDJ,EAAA,CAAAE,MAAA,kBACJ;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACRH,EAAA,CAAAC,cAAA,eAA2B;QACvBD,EAAA,CAAAI,SAAA,sBAQa;QACjBJ,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAK,UAAA,KAAA+F,8BAAA,kBAIM;QACVpG,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAC,cAAA,eAA0B;QAElBD,EAAA,CAAAI,SAAA,sBAIa;QACbJ,EAAA,CAAAC,cAAA,iBAA+C;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAEtEH,EAAA,CAAAC,cAAA,aAAsD;QAA3BD,EAAA,CAAAiG,UAAA,mBAAAI,4CAAA;UAAA,OAASL,GAAA,CAAAxB,cAAA,EAAgB;QAAA,EAAC;QACjDxE,EAAA,CAAAE,MAAA,0BACJ;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAIRH,EAAA,CAAAK,UAAA,KAAAiG,8BAAA,kBAGM;QAGNtG,EAAA,CAAAC,cAAA,kBAM8B;QAC1BD,EAAA,CAAAK,UAAA,KAAAkG,kCAAA,sBAA6E;QACjFvG,EAAA,CAAAG,YAAA,EAAS;QAGTH,EAAA,CAAAC,cAAA,eAAyB;QAClBD,EAAA,CAAAE,MAAA,+BACC;QAAAF,EAAA,CAAAC,cAAA,aAAsD;QAA7BD,EAAA,CAAAiG,UAAA,mBAAAO,4CAAA;UAAA,OAASR,GAAA,CAAAzB,gBAAA,EAAkB;QAAA,EAAC;QACjDvE,EAAA,CAAAE,MAAA,yBACJ;QAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;;QArFVH,EAAA,CAAAQ,SAAA,IAAuB;QAAvBR,EAAA,CAAAS,UAAA,cAAAuF,GAAA,CAAApF,SAAA,CAAuB;QAcVZ,EAAA,CAAAQ,SAAA,GAAwF;QAAxFR,EAAA,CAAAyG,WAAA,YAAA1F,OAAA,GAAAiF,GAAA,CAAApF,SAAA,CAAAC,GAAA,+BAAAE,OAAA,CAAAiC,OAAA,OAAAjC,OAAA,GAAAiF,GAAA,CAAApF,SAAA,CAAAC,GAAA,+BAAAE,OAAA,CAAA2F,OAAA,EAAwF;QAGvE1G,EAAA,CAAAQ,SAAA,GAA8E;QAA9ER,EAAA,CAAAS,UAAA,WAAAkG,OAAA,GAAAX,GAAA,CAAApF,SAAA,CAAAC,GAAA,+BAAA8F,OAAA,CAAA3D,OAAA,OAAA2D,OAAA,GAAAX,GAAA,CAAApF,SAAA,CAAAC,GAAA,+BAAA8F,OAAA,CAAAD,OAAA,EAA8E;QAqB3F1G,EAAA,CAAAQ,SAAA,GAAwF;QAAxFR,EAAA,CAAAyG,WAAA,YAAAG,OAAA,GAAAZ,GAAA,CAAApF,SAAA,CAAAC,GAAA,+BAAA+F,OAAA,CAAA5D,OAAA,OAAA4D,OAAA,GAAAZ,GAAA,CAAApF,SAAA,CAAAC,GAAA,+BAAA+F,OAAA,CAAAF,OAAA,EAAwF;QAJxF1G,EAAA,CAAAS,UAAA,oBAAmB;QAONT,EAAA,CAAAQ,SAAA,GAA8E;QAA9ER,EAAA,CAAAS,UAAA,WAAAoG,OAAA,GAAAb,GAAA,CAAApF,SAAA,CAAAC,GAAA,+BAAAgG,OAAA,CAAA7D,OAAA,OAAA6D,OAAA,GAAAb,GAAA,CAAApF,SAAA,CAAAC,GAAA,+BAAAgG,OAAA,CAAAH,OAAA,EAA8E;QAY3F1G,EAAA,CAAAQ,SAAA,GAAe;QAAfR,EAAA,CAAAS,UAAA,gBAAe;QAWPT,EAAA,CAAAQ,SAAA,GAAkB;QAAlBR,EAAA,CAAAS,UAAA,SAAAuF,GAAA,CAAA3E,YAAA,CAAkB;QAWrCrB,EAAA,CAAAQ,SAAA,GAA2C;QAA3CR,EAAA,CAAAS,UAAA,aAAAuF,GAAA,CAAApF,SAAA,CAAAoC,OAAA,IAAAgD,GAAA,CAAAnE,SAAA,CAA2C,YAAAmE,GAAA,CAAAnE,SAAA;QAEE7B,EAAA,CAAAQ,SAAA,GAAgB;QAAhBR,EAAA,CAAAS,UAAA,UAAAuF,GAAA,CAAAnE,SAAA,CAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}