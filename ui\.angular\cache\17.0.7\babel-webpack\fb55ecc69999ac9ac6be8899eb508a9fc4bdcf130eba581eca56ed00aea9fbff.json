{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { EmptyDemoRoutingModule } from './emptydemo-routing.module';\nimport * as i0 from \"@angular/core\";\nexport let EmptyDemoModule = /*#__PURE__*/(() => {\n  class EmptyDemoModule {\n    static #_ = this.ɵfac = function EmptyDemoModule_Factory(t) {\n      return new (t || EmptyDemoModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: EmptyDemoModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, EmptyDemoRoutingModule]\n    });\n  }\n  return EmptyDemoModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}