{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ChartModule } from 'primeng/chart';\nimport { MenuModule } from 'primeng/menu';\nimport { TableModule } from 'primeng/table';\nimport { ButtonModule } from 'primeng/button';\nimport { StyleClassModule } from 'primeng/styleclass';\nimport { PanelMenuModule } from 'primeng/panelmenu';\nimport { IndexRoutingModule } from './index-routing.module';\nimport { DataViewModule } from 'primeng/dataview';\nimport { KnobModule } from 'primeng/knob';\nimport { PickListModule } from 'primeng/picklist';\nimport { OrderListModule } from 'primeng/orderlist';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { RatingModule } from 'primeng/rating';\nimport { FontAwesomeModule } from '@fortawesome/angular-fontawesome';\nimport { fas } from '@fortawesome/free-solid-svg-icons';\nimport { ChartjsComponent } from '@coreui/angular-chartjs';\nimport { ColComponent, RowComponent, WidgetStatEComponent } from '@coreui/angular';\nimport { BadgeModule } from 'primeng/badge';\nimport { CardModule } from 'primeng/card';\nimport { TagModule } from 'primeng/tag';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\nimport { SkeletonModule } from 'primeng/skeleton';\nimport { RippleModule } from 'primeng/ripple';\nimport { PaginatorModule } from 'primeng/paginator';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@fortawesome/angular-fontawesome\";\nexport let IndexModule = /*#__PURE__*/(() => {\n  class IndexModule {\n    constructor(library) {\n      //library.addIcons(faSmile);\n      library.addIconPacks(fas);\n    }\n    static #_ = this.ɵfac = function IndexModule_Factory(t) {\n      return new (t || IndexModule)(i0.ɵɵinject(i1.FaIconLibrary));\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: IndexModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, ChartModule, MenuModule, TableModule, StyleClassModule, PanelMenuModule, ButtonModule, IndexRoutingModule, DataViewModule, KnobModule, CommonModule, FormsModule, DataViewModule, PickListModule, OrderListModule, InputTextModule, DropdownModule, RatingModule, ButtonModule, ChartjsComponent, WidgetStatEComponent, ColComponent, RowComponent, BadgeModule, FontAwesomeModule, ReactiveFormsModule, CardModule, TagModule, TooltipModule, ProgressSpinnerModule, SkeletonModule, RippleModule, PaginatorModule]\n    });\n  }\n  return IndexModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}