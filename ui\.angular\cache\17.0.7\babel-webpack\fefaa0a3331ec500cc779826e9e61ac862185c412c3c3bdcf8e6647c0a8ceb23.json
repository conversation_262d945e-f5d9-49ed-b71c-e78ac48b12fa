{"ast": null, "code": "import * as i2 from '@angular/common';\nimport { DOCUMENT, isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Component, ViewEncapsulation, Inject, Input, Output, ViewChild, ContentChildren, PLATFORM_ID, Directive, HostListener, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { ArrowDownIcon } from 'primeng/icons/arrowdown';\nimport { ArrowUpIcon } from 'primeng/icons/arrowup';\nimport { CheckIcon } from 'primeng/icons/check';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport { MinusIcon } from 'primeng/icons/minus';\nimport { SortAltIcon } from 'primeng/icons/sortalt';\nimport { SortAmountDownIcon } from 'primeng/icons/sortamountdown';\nimport { SortAmountUpAltIcon } from 'primeng/icons/sortamountupalt';\nimport { SpinnerIcon } from 'primeng/icons/spinner';\nimport * as i3 from 'primeng/paginator';\nimport { PaginatorModule } from 'primeng/paginator';\nimport * as i5 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i4 from 'primeng/scroller';\nimport { ScrollerModule } from 'primeng/scroller';\nimport { ObjectUtils } from 'primeng/utils';\nimport { Subject } from 'rxjs';\nconst _c0 = [\"container\"];\nconst _c1 = [\"resizeHelper\"];\nconst _c2 = [\"reorderIndicatorUp\"];\nconst _c3 = [\"reorderIndicatorDown\"];\nconst _c4 = [\"table\"];\nconst _c5 = [\"scrollableView\"];\nconst _c6 = [\"scrollableFrozenView\"];\nfunction TreeTable_div_2_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\");\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(\"p-treetable-loading-icon pi-spin \" + ctx_r11.loadingIcon);\n  }\n}\nfunction TreeTable_div_2_ng_container_3_SpinnerIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"SpinnerIcon\", 18);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"spin\", true)(\"styleClass\", \"p-treetable-loading-icon\");\n  }\n}\nfunction TreeTable_div_2_ng_container_3_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction TreeTable_div_2_ng_container_3_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TreeTable_div_2_ng_container_3_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction TreeTable_div_2_ng_container_3_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵtemplate(1, TreeTable_div_2_ng_container_3_span_2_1_Template, 1, 0, null, 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r14.loadingIconTemplate);\n  }\n}\nfunction TreeTable_div_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TreeTable_div_2_ng_container_3_SpinnerIcon_1_Template, 1, 2, \"SpinnerIcon\", 16)(2, TreeTable_div_2_ng_container_3_span_2_Template, 2, 1, \"span\", 17);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r12.loadingIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.loadingIconTemplate);\n  }\n}\nfunction TreeTable_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13);\n    i0.ɵɵtemplate(2, TreeTable_div_2_i_2_Template, 1, 2, \"i\", 14)(3, TreeTable_div_2_ng_container_3_Template, 3, 2, \"ng-container\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loadingIcon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loadingIcon);\n  }\n}\nfunction TreeTable_div_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TreeTable_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtemplate(1, TreeTable_div_3_ng_container_1_Template, 1, 0, \"ng-container\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.captionTemplate);\n  }\n}\nfunction TreeTable_p_paginator_4_1_ng_template_0_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TreeTable_p_paginator_4_1_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TreeTable_p_paginator_4_1_ng_template_0_ng_container_0_Template, 1, 0, \"ng-container\", 20);\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r22.paginatorFirstPageLinkIconTemplate);\n  }\n}\nfunction TreeTable_p_paginator_4_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TreeTable_p_paginator_4_1_ng_template_0_Template, 1, 1, \"ng-template\", 23);\n  }\n}\nfunction TreeTable_p_paginator_4_2_ng_template_0_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TreeTable_p_paginator_4_2_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TreeTable_p_paginator_4_2_ng_template_0_ng_container_0_Template, 1, 0, \"ng-container\", 20);\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r24.paginatorPreviousPageLinkIconTemplate);\n  }\n}\nfunction TreeTable_p_paginator_4_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TreeTable_p_paginator_4_2_ng_template_0_Template, 1, 1, \"ng-template\", 24);\n  }\n}\nfunction TreeTable_p_paginator_4_3_ng_template_0_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TreeTable_p_paginator_4_3_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TreeTable_p_paginator_4_3_ng_template_0_ng_container_0_Template, 1, 0, \"ng-container\", 20);\n  }\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r26.paginatorLastPageLinkIconTemplate);\n  }\n}\nfunction TreeTable_p_paginator_4_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TreeTable_p_paginator_4_3_ng_template_0_Template, 1, 1, \"ng-template\", 25);\n  }\n}\nfunction TreeTable_p_paginator_4_4_ng_template_0_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TreeTable_p_paginator_4_4_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TreeTable_p_paginator_4_4_ng_template_0_ng_container_0_Template, 1, 0, \"ng-container\", 20);\n  }\n  if (rf & 2) {\n    const ctx_r28 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r28.paginatorNextPageLinkIconTemplate);\n  }\n}\nfunction TreeTable_p_paginator_4_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TreeTable_p_paginator_4_4_ng_template_0_Template, 1, 1, \"ng-template\", 26);\n  }\n}\nfunction TreeTable_p_paginator_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-paginator\", 22);\n    i0.ɵɵlistener(\"onPageChange\", function TreeTable_p_paginator_4_Template_p_paginator_onPageChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.onPageChange($event));\n    });\n    i0.ɵɵtemplate(1, TreeTable_p_paginator_4_1_Template, 1, 0, null, 15)(2, TreeTable_p_paginator_4_2_Template, 1, 0, null, 15)(3, TreeTable_p_paginator_4_3_Template, 1, 0, null, 15)(4, TreeTable_p_paginator_4_4_Template, 1, 0, null, 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"rows\", ctx_r3.rows)(\"first\", ctx_r3.first)(\"totalRecords\", ctx_r3.totalRecords)(\"pageLinkSize\", ctx_r3.pageLinks)(\"alwaysShow\", ctx_r3.alwaysShowPaginator)(\"rowsPerPageOptions\", ctx_r3.rowsPerPageOptions)(\"templateLeft\", ctx_r3.paginatorLeftTemplate)(\"templateRight\", ctx_r3.paginatorRightTemplate)(\"dropdownAppendTo\", ctx_r3.paginatorDropdownAppendTo)(\"currentPageReportTemplate\", ctx_r3.currentPageReportTemplate)(\"showFirstLastIcon\", ctx_r3.showFirstLastIcon)(\"dropdownItemTemplate\", ctx_r3.paginatorDropdownItemTemplate)(\"showCurrentPageReport\", ctx_r3.showCurrentPageReport)(\"showJumpToPageDropdown\", ctx_r3.showJumpToPageDropdown)(\"showPageLinks\", ctx_r3.showPageLinks)(\"styleClass\", ctx_r3.paginatorStyleClass)(\"locale\", ctx_r3.paginatorLocale);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.paginatorFirstPageLinkIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.paginatorPreviousPageLinkIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.paginatorLastPageLinkIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.paginatorNextPageLinkIconTemplate);\n  }\n}\nfunction TreeTable_div_5_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TreeTable_div_5_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TreeTable_div_5_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c7 = a0 => ({\n  $implicit: a0\n});\nfunction TreeTable_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"table\", 28, 29);\n    i0.ɵɵtemplate(3, TreeTable_div_5_ng_container_3_Template, 1, 0, \"ng-container\", 30);\n    i0.ɵɵelementStart(4, \"thead\", 31);\n    i0.ɵɵtemplate(5, TreeTable_div_5_ng_container_5_Template, 1, 0, \"ng-container\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"tbody\", 32);\n    i0.ɵɵelementStart(7, \"tfoot\", 33);\n    i0.ɵɵtemplate(8, TreeTable_div_5_ng_container_8_Template, 1, 0, \"ng-container\", 30);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r4.tableStyleClass)(\"ngStyle\", ctx_r4.tableStyle);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r4.colGroupTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(10, _c7, ctx_r4.columns));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r4.headerTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(12, _c7, ctx_r4.columns));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"pTreeTableBody\", ctx_r4.columns)(\"pTreeTableBodyTemplate\", ctx_r4.bodyTemplate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r4.footerTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(14, _c7, ctx_r4.columns));\n  }\n}\nconst _c8 = a0 => ({\n  width: a0\n});\nfunction TreeTable_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 38, 39);\n  }\n  if (rf & 2) {\n    const ctx_r36 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ttScrollableView\", ctx_r36.frozenColumns)(\"frozen\", true)(\"ngStyle\", i0.ɵɵpureFunction1(4, _c8, ctx_r36.frozenWidth))(\"scrollHeight\", ctx_r36.scrollHeight);\n  }\n}\nconst _c9 = (a0, a1) => ({\n  left: a0,\n  width: a1\n});\nfunction TreeTable_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtemplate(1, TreeTable_div_6_div_1_Template, 2, 6, \"div\", 35);\n    i0.ɵɵelement(2, \"div\", 36, 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.frozenColumns || ctx_r5.frozenBodyTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ttScrollableView\", ctx_r5.columns)(\"frozen\", false)(\"scrollHeight\", ctx_r5.scrollHeight)(\"ngStyle\", i0.ɵɵpureFunction2(5, _c9, ctx_r5.frozenWidth, \"calc(100% - \" + ctx_r5.frozenWidth + \")\"));\n  }\n}\nfunction TreeTable_p_paginator_7_1_ng_template_0_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TreeTable_p_paginator_7_1_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TreeTable_p_paginator_7_1_ng_template_0_ng_container_0_Template, 1, 0, \"ng-container\", 20);\n  }\n  if (rf & 2) {\n    const ctx_r43 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r43.paginatorFirstPageLinkIconTemplate);\n  }\n}\nfunction TreeTable_p_paginator_7_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TreeTable_p_paginator_7_1_ng_template_0_Template, 1, 1, \"ng-template\", 23);\n  }\n}\nfunction TreeTable_p_paginator_7_2_ng_template_0_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TreeTable_p_paginator_7_2_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TreeTable_p_paginator_7_2_ng_template_0_ng_container_0_Template, 1, 0, \"ng-container\", 20);\n  }\n  if (rf & 2) {\n    const ctx_r45 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r45.paginatorPreviousPageLinkIconTemplate);\n  }\n}\nfunction TreeTable_p_paginator_7_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TreeTable_p_paginator_7_2_ng_template_0_Template, 1, 1, \"ng-template\", 24);\n  }\n}\nfunction TreeTable_p_paginator_7_3_ng_template_0_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TreeTable_p_paginator_7_3_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TreeTable_p_paginator_7_3_ng_template_0_ng_container_0_Template, 1, 0, \"ng-container\", 20);\n  }\n  if (rf & 2) {\n    const ctx_r47 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r47.paginatorLastPageLinkIconTemplate);\n  }\n}\nfunction TreeTable_p_paginator_7_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TreeTable_p_paginator_7_3_ng_template_0_Template, 1, 1, \"ng-template\", 25);\n  }\n}\nfunction TreeTable_p_paginator_7_4_ng_template_0_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TreeTable_p_paginator_7_4_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TreeTable_p_paginator_7_4_ng_template_0_ng_container_0_Template, 1, 0, \"ng-container\", 20);\n  }\n  if (rf & 2) {\n    const ctx_r49 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r49.paginatorNextPageLinkIconTemplate);\n  }\n}\nfunction TreeTable_p_paginator_7_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TreeTable_p_paginator_7_4_ng_template_0_Template, 1, 1, \"ng-template\", 26);\n  }\n}\nfunction TreeTable_p_paginator_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-paginator\", 40);\n    i0.ɵɵlistener(\"onPageChange\", function TreeTable_p_paginator_7_Template_p_paginator_onPageChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r51 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r51.onPageChange($event));\n    });\n    i0.ɵɵtemplate(1, TreeTable_p_paginator_7_1_Template, 1, 0, null, 15)(2, TreeTable_p_paginator_7_2_Template, 1, 0, null, 15)(3, TreeTable_p_paginator_7_3_Template, 1, 0, null, 15)(4, TreeTable_p_paginator_7_4_Template, 1, 0, null, 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"rows\", ctx_r6.rows)(\"first\", ctx_r6.first)(\"totalRecords\", ctx_r6.totalRecords)(\"pageLinkSize\", ctx_r6.pageLinks)(\"alwaysShow\", ctx_r6.alwaysShowPaginator)(\"rowsPerPageOptions\", ctx_r6.rowsPerPageOptions)(\"templateLeft\", ctx_r6.paginatorLeftTemplate)(\"templateRight\", ctx_r6.paginatorRightTemplate)(\"dropdownAppendTo\", ctx_r6.paginatorDropdownAppendTo)(\"currentPageReportTemplate\", ctx_r6.currentPageReportTemplate)(\"showFirstLastIcon\", ctx_r6.showFirstLastIcon)(\"dropdownItemTemplate\", ctx_r6.paginatorDropdownItemTemplate)(\"showCurrentPageReport\", ctx_r6.showCurrentPageReport)(\"showJumpToPageDropdown\", ctx_r6.showJumpToPageDropdown)(\"showPageLinks\", ctx_r6.showPageLinks)(\"styleClass\", ctx_r6.paginatorStyleClass)(\"locale\", ctx_r6.paginatorLocale);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.paginatorFirstPageLinkIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.paginatorPreviousPageLinkIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.paginatorLastPageLinkIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.paginatorNextPageLinkIconTemplate);\n  }\n}\nfunction TreeTable_div_8_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TreeTable_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵtemplate(1, TreeTable_div_8_ng_container_1_Template, 1, 0, \"ng-container\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r7.summaryTemplate);\n  }\n}\nfunction TreeTable_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 42, 43);\n  }\n}\nfunction TreeTable_span_10_ArrowDownIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ArrowDownIcon\");\n  }\n}\nfunction TreeTable_span_10_3_ng_template_0_Template(rf, ctx) {}\nfunction TreeTable_span_10_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TreeTable_span_10_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction TreeTable_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 44, 45);\n    i0.ɵɵtemplate(2, TreeTable_span_10_ArrowDownIcon_2_Template, 1, 0, \"ArrowDownIcon\", 15)(3, TreeTable_span_10_3_Template, 1, 0, null, 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.reorderIndicatorUpIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r9.reorderIndicatorUpIconTemplate);\n  }\n}\nfunction TreeTable_span_11_ArrowUpIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ArrowUpIcon\");\n  }\n}\nfunction TreeTable_span_11_3_ng_template_0_Template(rf, ctx) {}\nfunction TreeTable_span_11_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TreeTable_span_11_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction TreeTable_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 46, 47);\n    i0.ɵɵtemplate(2, TreeTable_span_11_ArrowUpIcon_2_Template, 1, 0, \"ArrowUpIcon\", 15)(3, TreeTable_span_11_3_Template, 1, 0, null, 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.reorderIndicatorDownIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r10.reorderIndicatorDownIconTemplate);\n  }\n}\nconst _c10 = (a1, a2, a3, a4, a5) => ({\n  \"p-treetable p-component\": true,\n  \"p-treetable-hoverable-rows\": a1,\n  \"p-treetable-auto-layout\": a2,\n  \"p-treetable-resizable\": a3,\n  \"p-treetable-resizable-fit\": a4,\n  \"p-treetable-flex-scrollable\": a5\n});\nconst _c11 = [\"pTreeTableBody\", \"\"];\nfunction TTBody_ng_template_0_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c12 = (a0, a1, a2, a3) => ({\n  $implicit: a0,\n  node: a1,\n  rowData: a2,\n  columns: a3\n});\nfunction TTBody_ng_template_0_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TTBody_ng_template_0_ng_container_0_ng_container_1_Template, 1, 0, \"ng-container\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const serializedNode_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r4.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction4(2, _c12, serializedNode_r2, serializedNode_r2.node, serializedNode_r2.node.data, ctx_r4.columns));\n  }\n}\nfunction TTBody_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TTBody_ng_template_0_ng_container_0_Template, 2, 7, \"ng-container\", 1);\n  }\n  if (rf & 2) {\n    const serializedNode_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", serializedNode_r2.visible);\n  }\n}\nfunction TTBody_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c13 = (a0, a1) => ({\n  $implicit: a0,\n  frozen: a1\n});\nfunction TTBody_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TTBody_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.tt.emptyMessageTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c13, ctx_r1.columns, ctx_r1.frozen));\n  }\n}\nconst _c14 = [\"scrollHeader\"];\nconst _c15 = [\"scrollHeaderBox\"];\nconst _c16 = [\"scrollBody\"];\nconst _c17 = [\"scrollTable\"];\nconst _c18 = [\"loadingTable\"];\nconst _c19 = [\"scrollFooter\"];\nconst _c20 = [\"scrollFooterBox\"];\nconst _c21 = [\"scrollableAligner\"];\nconst _c22 = [\"scroller\"];\nconst _c23 = [\"ttScrollableView\", \"\"];\nfunction TTScrollableView_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TTScrollableView_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TTScrollableView_p_scroller_8_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c24 = (a0, a1) => ({\n  $implicit: a0,\n  options: a1\n});\nfunction TTScrollableView_p_scroller_8_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TTScrollableView_p_scroller_8_ng_template_2_ng_container_0_Template, 1, 0, \"ng-container\", 5);\n  }\n  if (rf & 2) {\n    const items_r12 = ctx.$implicit;\n    const scrollerOptions_r13 = ctx.options;\n    i0.ɵɵnextContext(2);\n    const _r7 = i0.ɵɵreference(11);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r7)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c24, items_r12, scrollerOptions_r13));\n  }\n}\nfunction TTScrollableView_p_scroller_8_ng_container_3_ng_template_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c25 = a0 => ({\n  options: a0\n});\nfunction TTScrollableView_p_scroller_8_ng_container_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TTScrollableView_p_scroller_8_ng_container_3_ng_template_1_ng_container_0_Template, 1, 0, \"ng-container\", 5);\n  }\n  if (rf & 2) {\n    const scrollerOptions_r16 = ctx.options;\n    const ctx_r15 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r15.loaderTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c25, scrollerOptions_r16));\n  }\n}\nfunction TTScrollableView_p_scroller_8_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TTScrollableView_p_scroller_8_ng_container_3_ng_template_1_Template, 1, 4, \"ng-template\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nconst _c26 = a0 => ({\n  height: a0\n});\nfunction TTScrollableView_p_scroller_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-scroller\", 11, 12);\n    i0.ɵɵlistener(\"onLazyLoad\", function TTScrollableView_p_scroller_8_Template_p_scroller_onLazyLoad_0_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.tt.onLazyItemLoad($event));\n    });\n    i0.ɵɵtemplate(2, TTScrollableView_p_scroller_8_ng_template_2_Template, 1, 5, \"ng-template\", 13)(3, TTScrollableView_p_scroller_8_ng_container_3_Template, 2, 0, \"ng-container\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction1(8, _c26, ctx_r4.tt.scrollHeight !== \"flex\" ? ctx_r4.tt.scrollHeight : undefined));\n    i0.ɵɵproperty(\"items\", ctx_r4.tt.serializedValue)(\"scrollHeight\", ctx_r4.scrollHeight !== \"flex\" ? undefined : \"100%\")(\"itemSize\", ctx_r4.tt.virtualScrollItemSize || ctx_r4.tt._virtualRowHeight)(\"lazy\", ctx_r4.tt.lazy)(\"options\", ctx_r4.tt.virtualScrollOptions);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.loaderTemplate);\n  }\n}\nfunction TTScrollableView_ng_container_9_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c27 = (a0, a1) => ({\n  \"max-height\": a0,\n  \"overflow-y\": a1\n});\nconst _c28 = () => ({});\nfunction TTScrollableView_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 15, 16);\n    i0.ɵɵtemplate(3, TTScrollableView_ng_container_9_ng_container_3_Template, 1, 0, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    const _r7 = i0.ɵɵreference(11);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction2(3, _c27, ctx_r5.tt.scrollHeight !== \"flex\" ? ctx_r5.scrollHeight : undefined, !ctx_r5.frozen && ctx_r5.tt.scrollHeight ? \"scroll\" : undefined));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r7)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(7, _c24, ctx_r5.serializedValue, i0.ɵɵpureFunction0(6, _c28)));\n  }\n}\nfunction TTScrollableView_ng_template_10_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TTScrollableView_ng_template_10_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 21, 22);\n  }\n}\nfunction TTScrollableView_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"table\", 17, 18);\n    i0.ɵɵtemplate(2, TTScrollableView_ng_template_10_ng_container_2_Template, 1, 0, \"ng-container\", 5);\n    i0.ɵɵelement(3, \"tbody\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TTScrollableView_ng_template_10_div_4_Template, 2, 0, \"div\", 20);\n  }\n  if (rf & 2) {\n    const items_r22 = ctx.$implicit;\n    const scrollerOptions_r23 = ctx.options;\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(scrollerOptions_r23.contentStyle);\n    i0.ɵɵclassMap(ctx_r6.tt.tableStyleClass);\n    i0.ɵɵproperty(\"ngClass\", scrollerOptions_r23.contentStyleClass)(\"ngStyle\", ctx_r6.tt.tableStyle);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r6.frozen ? ctx_r6.tt.frozenColGroupTemplate || ctx_r6.tt.colGroupTemplate : ctx_r6.tt.colGroupTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(13, _c7, ctx_r6.columns));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"pTreeTableBody\", ctx_r6.columns)(\"pTreeTableBodyTemplate\", ctx_r6.frozen ? ctx_r6.tt.frozenBodyTemplate || ctx_r6.tt.bodyTemplate : ctx_r6.tt.bodyTemplate)(\"serializedNodes\", items_r22)(\"frozen\", ctx_r6.frozen);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.frozen);\n  }\n}\nfunction TTScrollableView_div_12_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TTScrollableView_div_12_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TTScrollableView_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23, 24)(2, \"div\", 25, 26)(4, \"table\", 27);\n    i0.ɵɵtemplate(5, TTScrollableView_div_12_ng_container_5_Template, 1, 0, \"ng-container\", 5);\n    i0.ɵɵelementStart(6, \"tfoot\", 28);\n    i0.ɵɵtemplate(7, TTScrollableView_div_12_ng_container_7_Template, 1, 0, \"ng-container\", 5);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r8.tt.tableStyleClass)(\"ngStyle\", ctx_r8.tt.tableStyle);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r8.frozen ? ctx_r8.tt.frozenColGroupTemplate || ctx_r8.tt.colGroupTemplate : ctx_r8.tt.colGroupTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(6, _c7, ctx_r8.columns));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r8.frozen ? ctx_r8.tt.frozenFooterTemplate || ctx_r8.tt.footerTemplate : ctx_r8.tt.footerTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(8, _c7, ctx_r8.columns));\n  }\n}\nfunction TTSortIcon_ng_container_0_SortAltIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"SortAltIcon\", 3);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-sortable-column-icon\");\n  }\n}\nfunction TTSortIcon_ng_container_0_SortAmountUpAltIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"SortAmountUpAltIcon\", 3);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-sortable-column-icon\");\n  }\n}\nfunction TTSortIcon_ng_container_0_SortAmountDownIcon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"SortAmountDownIcon\", 3);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-sortable-column-icon\");\n  }\n}\nfunction TTSortIcon_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TTSortIcon_ng_container_0_SortAltIcon_1_Template, 1, 1, \"SortAltIcon\", 2)(2, TTSortIcon_ng_container_0_SortAmountUpAltIcon_2_Template, 1, 1, \"SortAmountUpAltIcon\", 2)(3, TTSortIcon_ng_container_0_SortAmountDownIcon_3_Template, 1, 1, \"SortAmountDownIcon\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortOrder === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortOrder === 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sortOrder === -1);\n  }\n}\nfunction TTSortIcon_span_1_1_ng_template_0_Template(rf, ctx) {}\nfunction TTSortIcon_span_1_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TTSortIcon_span_1_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction TTSortIcon_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 4);\n    i0.ɵɵtemplate(1, TTSortIcon_span_1_1_Template, 1, 0, null, 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.tt.sortIconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c7, ctx_r1.sortOrder));\n  }\n}\nfunction TTCheckbox_ng_container_5_CheckIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"CheckIcon\", 7);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-checkbox-icon\");\n  }\n}\nfunction TTCheckbox_ng_container_5_MinusIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"MinusIcon\", 7);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-checkbox-icon\");\n  }\n}\nfunction TTCheckbox_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TTCheckbox_ng_container_5_CheckIcon_1_Template, 1, 1, \"CheckIcon\", 6)(2, TTCheckbox_ng_container_5_MinusIcon_2_Template, 1, 1, \"MinusIcon\", 6);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.checked);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.rowNode.node.partialSelected);\n  }\n}\nfunction TTCheckbox_span_6_1_ng_template_0_Template(rf, ctx) {}\nfunction TTCheckbox_span_6_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TTCheckbox_span_6_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nconst _c29 = (a0, a1) => ({\n  $implicit: a0,\n  partialSelected: a1\n});\nfunction TTCheckbox_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtemplate(1, TTCheckbox_span_6_1_Template, 1, 0, null, 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.tt.checkboxIconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c29, ctx_r2.checked, ctx_r2.rowNode.node.partialSelected));\n  }\n}\nconst _c30 = a0 => ({\n  \"p-checkbox-focused\": a0\n});\nconst _c31 = (a1, a2, a3, a4) => ({\n  \"p-checkbox-box\": true,\n  \"p-highlight\": a1,\n  \"p-focus\": a2,\n  \"p-indeterminate\": a3,\n  \"p-disabled\": a4\n});\nconst _c32 = [\"box\"];\nfunction TTHeaderCheckbox_ng_container_6_CheckIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"CheckIcon\", 9);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-checkbox-icon\");\n  }\n}\nfunction TTHeaderCheckbox_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TTHeaderCheckbox_ng_container_6_CheckIcon_1_Template, 1, 1, \"CheckIcon\", 8);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.checked);\n  }\n}\nfunction TTHeaderCheckbox_span_7_1_ng_template_0_Template(rf, ctx) {}\nfunction TTHeaderCheckbox_span_7_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TTHeaderCheckbox_span_7_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction TTHeaderCheckbox_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵtemplate(1, TTHeaderCheckbox_span_7_1_Template, 1, 0, null, 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.tt.headerCheckboxIconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c7, ctx_r3.checked));\n  }\n}\nconst _c33 = (a1, a2, a3) => ({\n  \"p-checkbox-box\": true,\n  \"p-highlight\": a1,\n  \"p-focus\": a2,\n  \"p-disabled\": a3\n});\nfunction TreeTableCellEditor_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TreeTableCellEditor_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TreeTableCellEditor_ng_container_0_ng_container_1_Template, 1, 0, \"ng-container\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.inputTemplate);\n  }\n}\nfunction TreeTableCellEditor_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction TreeTableCellEditor_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TreeTableCellEditor_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.outputTemplate);\n  }\n}\nfunction TreeTableToggler_ng_container_1_ChevronDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronDownIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction TreeTableToggler_ng_container_1_ChevronRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\");\n  }\n  if (rf & 2) {\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction TreeTableToggler_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TreeTableToggler_ng_container_1_ChevronDownIcon_1_Template, 1, 1, \"ChevronDownIcon\", 1)(2, TreeTableToggler_ng_container_1_ChevronRightIcon_2_Template, 1, 1, \"ChevronRightIcon\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.rowNode.node.expanded);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.rowNode.node.expanded);\n  }\n}\nfunction TreeTableToggler_2_ng_template_0_Template(rf, ctx) {}\nfunction TreeTableToggler_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TreeTableToggler_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nlet TreeTableService = /*#__PURE__*/(() => {\n  class TreeTableService {\n    sortSource = new Subject();\n    selectionSource = new Subject();\n    contextMenuSource = new Subject();\n    uiUpdateSource = new Subject();\n    totalRecordsSource = new Subject();\n    sortSource$ = this.sortSource.asObservable();\n    selectionSource$ = this.selectionSource.asObservable();\n    contextMenuSource$ = this.contextMenuSource.asObservable();\n    uiUpdateSource$ = this.uiUpdateSource.asObservable();\n    totalRecordsSource$ = this.totalRecordsSource.asObservable();\n    onSort(sortMeta) {\n      this.sortSource.next(sortMeta);\n    }\n    onSelectionChange() {\n      this.selectionSource.next(null);\n    }\n    onContextMenu(node) {\n      this.contextMenuSource.next(node);\n    }\n    onUIUpdate(value) {\n      this.uiUpdateSource.next(value);\n    }\n    onTotalRecordsChange(value) {\n      this.totalRecordsSource.next(value);\n    }\n    static ɵfac = function TreeTableService_Factory(t) {\n      return new (t || TreeTableService)();\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: TreeTableService,\n      factory: TreeTableService.ɵfac\n    });\n  }\n  return TreeTableService;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * TreeTable is used to display hierarchical data in tabular format.\n * @group Components\n */\nlet TreeTable = /*#__PURE__*/(() => {\n  class TreeTable {\n    document;\n    renderer;\n    el;\n    cd;\n    zone;\n    tableService;\n    filterService;\n    /**\n     * An array of objects to represent dynamic columns.\n     * @group Props\n     */\n    columns;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Inline style of the table.\n     * @group Props\n     */\n    tableStyle;\n    /**\n     * Style class of the table.\n     * @group Props\n     */\n    tableStyleClass;\n    /**\n     * Whether the cell widths scale according to their content or not.\n     * @group Props\n     */\n    autoLayout;\n    /**\n     * Defines if data is loaded and interacted with in lazy manner.\n     * @group Props\n     */\n    lazy = false;\n    /**\n     * Whether to call lazy loading on initialization.\n     * @group Props\n     */\n    lazyLoadOnInit = true;\n    /**\n     * When specified as true, enables the pagination.\n     * @group Props\n     */\n    paginator;\n    /**\n     * Number of rows to display per page.\n     * @group Props\n     */\n    rows;\n    /**\n     * Index of the first row to be displayed.\n     * @group Props\n     */\n    first = 0;\n    /**\n     * Number of page links to display in paginator.\n     * @group Props\n     */\n    pageLinks = 5;\n    /**\n     * Array of integer/object values to display inside rows per page dropdown of paginator\n     * @group Props\n     */\n    rowsPerPageOptions;\n    /**\n     * Whether to show it even there is only one page.\n     * @group Props\n     */\n    alwaysShowPaginator = true;\n    /**\n     * Position of the paginator.\n     * @group Props\n     */\n    paginatorPosition = 'bottom';\n    /**\n     * Custom style class for paginator\n     * @group Props\n     */\n    paginatorStyleClass;\n    /**\n     * Target element to attach the paginator dropdown overlay, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    paginatorDropdownAppendTo;\n    /**\n     * Template of the current page report element. Available placeholders are {currentPage},{totalPages},{rows},{first},{last} and {totalRecords}\n     * @group Props\n     */\n    currentPageReportTemplate = '{currentPage} of {totalPages}';\n    /**\n     * Whether to display current page report.\n     * @group Props\n     */\n    showCurrentPageReport;\n    /**\n     * Whether to display a dropdown to navigate to any page.\n     * @group Props\n     */\n    showJumpToPageDropdown;\n    /**\n     * When enabled, icons are displayed on paginator to go first and last page.\n     * @group Props\n     */\n    showFirstLastIcon = true;\n    /**\n     * Whether to show page links.\n     * @group Props\n     */\n    showPageLinks = true;\n    /**\n     * Sort order to use when an unsorted column gets sorted by user interaction.\n     * @group Props\n     */\n    defaultSortOrder = 1;\n    /**\n     * Defines whether sorting works on single column or on multiple columns.\n     * @group Props\n     */\n    sortMode = 'single';\n    /**\n     * When true, resets paginator to first page after sorting.\n     * @group Props\n     */\n    resetPageOnSort = true;\n    /**\n     * Whether to use the default sorting or a custom one using sortFunction.\n     * @group Props\n     */\n    customSort;\n    /**\n     * Specifies the selection mode, valid values are \"single\" and \"multiple\".\n     * @group Props\n     */\n    selectionMode;\n    /**\n     * Selected row with a context menu.\n     * @group Props\n     */\n    contextMenuSelection;\n    /**\n     * Mode of the contet menu selection.\n     * @group Props\n     */\n    contextMenuSelectionMode = 'separate';\n    /**\n     * A property to uniquely identify a record in data.\n     * @group Props\n     */\n    dataKey;\n    /**\n     * Defines whether metaKey is should be considered for the selection. On touch enabled devices, metaKeySelection is turned off automatically.\n     * @group Props\n     */\n    metaKeySelection = true;\n    /**\n     * Algorithm to define if a row is selected, valid values are \"equals\" that compares by reference and \"deepEquals\" that compares all fields.\n     * @group Props\n     */\n    compareSelectionBy = 'deepEquals';\n    /**\n     * Adds hover effect to rows without the need for selectionMode.\n     * @group Props\n     */\n    rowHover;\n    /**\n     * Displays a loader to indicate data load is in progress.\n     * @group Props\n     */\n    loading;\n    /**\n     * The icon to show while indicating data load is in progress.\n     * @group Props\n     */\n    loadingIcon;\n    /**\n     * Whether to show the loading mask when loading property is true.\n     * @group Props\n     */\n    showLoader = true;\n    /**\n     * When specifies, enables horizontal and/or vertical scrolling.\n     * @group Props\n     */\n    scrollable;\n    /**\n     * Height of the scroll viewport in fixed pixels or the \"flex\" keyword for a dynamic size.\n     * @group Props\n     */\n    scrollHeight;\n    /**\n     * Whether the data should be loaded on demand during scroll.\n     * @group Props\n     */\n    virtualScroll;\n    /**\n     * Height of a row to use in calculations of virtual scrolling.\n     * @group Props\n     */\n    virtualScrollItemSize;\n    /**\n     * Whether to use the scroller feature. The properties of scroller component can be used like an object in it.\n     * @group Props\n     */\n    virtualScrollOptions;\n    /**\n     * The delay (in milliseconds) before triggering the virtual scroll. This determines the time gap between the user's scroll action and the actual rendering of the next set of items in the virtual scroll.\n     * @group Props\n     */\n    virtualScrollDelay = 150;\n    /**\n     * Width of the frozen columns container.\n     * @group Props\n     */\n    frozenWidth;\n    /**\n     * An array of objects to represent dynamic columns that are frozen.\n     * @group Props\n     */\n    frozenColumns;\n    /**\n     * When enabled, columns can be resized using drag and drop.\n     * @group Props\n     */\n    resizableColumns;\n    /**\n     * Defines whether the overall table width should change on column resize, valid values are \"fit\" and \"expand\".\n     * @group Props\n     */\n    columnResizeMode = 'fit';\n    /**\n     * When enabled, columns can be reordered using drag and drop.\n     * @group Props\n     */\n    reorderableColumns;\n    /**\n     * Local ng-template varilable of a ContextMenu.\n     * @group Props\n     */\n    contextMenu;\n    /**\n     * Function to optimize the dom operations by delegating to ngForTrackBy, default algorithm checks for object identity.\n     * @group Props\n     */\n    rowTrackBy = (index, item) => item;\n    /**\n     * An array of FilterMetadata objects to provide external filters.\n     * @group Props\n     */\n    filters = {};\n    /**\n     * An array of fields as string to use in global filtering.\n     * @group Props\n     */\n    globalFilterFields;\n    /**\n     * Delay in milliseconds before filtering the data.\n     * @group Props\n     */\n    filterDelay = 300;\n    /**\n     * Mode for filtering valid values are \"lenient\" and \"strict\". Default is lenient.\n     * @group Props\n     */\n    filterMode = 'lenient';\n    /**\n     * Locale to use in filtering. The default locale is the host environment's current locale.\n     * @group Props\n     */\n    filterLocale;\n    /**\n     * Locale to be used in paginator formatting.\n     * @group Props\n     */\n    paginatorLocale;\n    /**\n     * Number of total records, defaults to length of value when not defined.\n     * @group Props\n     */\n    get totalRecords() {\n      return this._totalRecords;\n    }\n    set totalRecords(val) {\n      this._totalRecords = val;\n      this.tableService.onTotalRecordsChange(this._totalRecords);\n    }\n    /**\n     * Name of the field to sort data by default.\n     * @group Props\n     */\n    get sortField() {\n      return this._sortField;\n    }\n    set sortField(val) {\n      this._sortField = val;\n    }\n    /**\n     * Order to sort when default sorting is enabled.\n     * @defaultValue 1\n     * @group Props\n     */\n    get sortOrder() {\n      return this._sortOrder;\n    }\n    set sortOrder(val) {\n      this._sortOrder = val;\n    }\n    /**\n     * An array of SortMeta objects to sort the data by default in multiple sort mode.\n     * @defaultValue null\n     * @group Props\n     */\n    get multiSortMeta() {\n      return this._multiSortMeta;\n    }\n    set multiSortMeta(val) {\n      this._multiSortMeta = val;\n    }\n    /**\n     * Selected row in single mode or an array of values in multiple mode.\n     * @defaultValue null\n     * @group Props\n     */\n    get selection() {\n      return this._selection;\n    }\n    set selection(val) {\n      this._selection = val;\n    }\n    /**\n     * An array of objects to display.\n     * @defaultValue null\n     * @group Props\n     */\n    get value() {\n      return this._value;\n    }\n    set value(val) {\n      this._value = val;\n    }\n    /**\n     * Indicates the height of rows to be scrolled.\n     * @defaultValue 28\n     * @group Props\n     * @deprecated use virtualScrollItemSize property instead.\n     */\n    get virtualRowHeight() {\n      return this._virtualRowHeight;\n    }\n    set virtualRowHeight(val) {\n      this._virtualRowHeight = val;\n      console.warn('The virtualRowHeight property is deprecated, use virtualScrollItemSize property instead.');\n    }\n    _virtualRowHeight = 28;\n    /**\n     * Callback to invoke on selected node change.\n     * @param {TreeTableNode} object - Node instance.\n     * @group Emits\n     */\n    selectionChange = new EventEmitter();\n    /**\n     * Callback to invoke on context menu selection change.\n     * @param {TreeTableNode} object - Node instance.\n     * @group Emits\n     */\n    contextMenuSelectionChange = new EventEmitter();\n    /**\n     * Callback to invoke when data is filtered.\n     * @param {TreeTableFilterEvent} event - Custom filter event.\n     * @group Emits\n     */\n    onFilter = new EventEmitter();\n    /**\n     * Callback to invoke when a node is expanded.\n     * @param {TreeTableNode} object - Node instance.\n     * @group Emits\n     */\n    onNodeExpand = new EventEmitter();\n    /**\n     * Callback to invoke when a node is collapsed.\n     * @param {TreeTableNodeCollapseEvent} event - Node collapse event.\n     * @group Emits\n     */\n    onNodeCollapse = new EventEmitter();\n    /**\n     * Callback to invoke when pagination occurs.\n     * @param {TreeTablePaginatorState} object - Paginator state.\n     * @group Emits\n     */\n    onPage = new EventEmitter();\n    /**\n     * Callback to invoke when a column gets sorted.\n     * @param {Object} Object - Sort data.\n     * @group Emits\n     */\n    onSort = new EventEmitter();\n    /**\n     * Callback to invoke when paging, sorting or filtering happens in lazy mode.\n     * @param {TreeTableLazyLoadEvent} event - Custom lazy load event.\n     * @group Emits\n     */\n    onLazyLoad = new EventEmitter();\n    /**\n     * An event emitter to invoke on custom sorting, refer to sorting section for details.\n     * @param {TreeTableSortEvent} event - Custom sort event.\n     * @group Emits\n     */\n    sortFunction = new EventEmitter();\n    /**\n     * Callback to invoke when a column is resized.\n     * @param {TreeTableColResizeEvent} event - Custom column resize event.\n     * @group Emits\n     */\n    onColResize = new EventEmitter();\n    /**\n     * Callback to invoke when a column is reordered.\n     * @param {TreeTableColumnReorderEvent} event - Custom column reorder.\n     * @group Emits\n     */\n    onColReorder = new EventEmitter();\n    /**\n     * Callback to invoke when a node is selected.\n     * @param {TreeTableNode} object - Node instance.\n     * @group Emits\n     */\n    onNodeSelect = new EventEmitter();\n    /**\n     * Callback to invoke when a node is unselected.\n     * @param {TreeTableNodeUnSelectEvent} event - Custom node unselect event.\n     * @group Emits\n     */\n    onNodeUnselect = new EventEmitter();\n    /**\n     * Callback to invoke when a node is selected with right click.\n     * @param {TreeTableContextMenuSelectEvent} event - Custom context menu select event.\n     * @group Emits\n     */\n    onContextMenuSelect = new EventEmitter();\n    /**\n     * Callback to invoke when state of header checkbox changes.\n     * @param {TreeTableHeaderCheckboxToggleEvent} event - Custom checkbox toggle event.\n     * @group Emits\n     */\n    onHeaderCheckboxToggle = new EventEmitter();\n    /**\n     * Callback to invoke when a cell switches to edit mode.\n     * @param {TreeTableEditEvent} event - Custom edit event.\n     * @group Emits\n     */\n    onEditInit = new EventEmitter();\n    /**\n     * Callback to invoke when cell edit is completed.\n     * @param {TreeTableEditEvent} event - Custom edit event.\n     * @group Emits\n     */\n    onEditComplete = new EventEmitter();\n    /**\n     * Callback to invoke when cell edit is cancelled with escape key.\n     * @param {TreeTableEditEvent} event - Custom edit event.\n     * @group Emits\n     */\n    onEditCancel = new EventEmitter();\n    containerViewChild;\n    resizeHelperViewChild;\n    reorderIndicatorUpViewChild;\n    reorderIndicatorDownViewChild;\n    tableViewChild;\n    scrollableViewChild;\n    scrollableFrozenViewChild;\n    templates;\n    _value = [];\n    serializedValue;\n    _totalRecords = 0;\n    _multiSortMeta;\n    _sortField;\n    _sortOrder = 1;\n    filteredNodes;\n    filterTimeout;\n    colGroupTemplate;\n    captionTemplate;\n    headerTemplate;\n    bodyTemplate;\n    footerTemplate;\n    summaryTemplate;\n    emptyMessageTemplate;\n    paginatorLeftTemplate;\n    paginatorRightTemplate;\n    paginatorDropdownItemTemplate;\n    frozenHeaderTemplate;\n    frozenBodyTemplate;\n    frozenFooterTemplate;\n    frozenColGroupTemplate;\n    loadingIconTemplate;\n    reorderIndicatorUpIconTemplate;\n    reorderIndicatorDownIconTemplate;\n    sortIconTemplate;\n    checkboxIconTemplate;\n    headerCheckboxIconTemplate;\n    togglerIconTemplate;\n    paginatorFirstPageLinkIconTemplate;\n    paginatorLastPageLinkIconTemplate;\n    paginatorPreviousPageLinkIconTemplate;\n    paginatorNextPageLinkIconTemplate;\n    lastResizerHelperX;\n    reorderIconWidth;\n    reorderIconHeight;\n    draggedColumn;\n    dropPosition;\n    preventSelectionSetterPropagation;\n    _selection;\n    selectionKeys = {};\n    rowTouched;\n    editingCell;\n    editingCellData;\n    editingCellField;\n    editingCellClick;\n    documentEditListener;\n    initialized;\n    toggleRowIndex;\n    ngOnInit() {\n      if (this.lazy && this.lazyLoadOnInit && !this.virtualScroll) {\n        this.onLazyLoad.emit(this.createLazyLoadMetadata());\n      }\n      this.initialized = true;\n    }\n    ngAfterContentInit() {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'caption':\n            this.captionTemplate = item.template;\n            break;\n          case 'header':\n            this.headerTemplate = item.template;\n            break;\n          case 'body':\n            this.bodyTemplate = item.template;\n            break;\n          case 'footer':\n            this.footerTemplate = item.template;\n            break;\n          case 'summary':\n            this.summaryTemplate = item.template;\n            break;\n          case 'colgroup':\n            this.colGroupTemplate = item.template;\n            break;\n          case 'emptymessage':\n            this.emptyMessageTemplate = item.template;\n            break;\n          case 'paginatorleft':\n            this.paginatorLeftTemplate = item.template;\n            break;\n          case 'paginatorright':\n            this.paginatorRightTemplate = item.template;\n            break;\n          case 'paginatordropdownitem':\n            this.paginatorDropdownItemTemplate = item.template;\n            break;\n          case 'frozenheader':\n            this.frozenHeaderTemplate = item.template;\n            break;\n          case 'frozenbody':\n            this.frozenBodyTemplate = item.template;\n            break;\n          case 'frozenfooter':\n            this.frozenFooterTemplate = item.template;\n            break;\n          case 'frozencolgroup':\n            this.frozenColGroupTemplate = item.template;\n            break;\n          case 'loadingicon':\n            this.loadingIconTemplate = item.template;\n            break;\n          case 'reorderindicatorupicon':\n            this.reorderIndicatorUpIconTemplate = item.template;\n            break;\n          case 'reorderindicatordownicon':\n            this.reorderIndicatorDownIconTemplate = item.template;\n            break;\n          case 'sorticon':\n            this.sortIconTemplate = item.template;\n            break;\n          case 'checkboxicon':\n            this.checkboxIconTemplate = item.template;\n            break;\n          case 'headercheckboxicon':\n            this.headerCheckboxIconTemplate = item.template;\n            break;\n          case 'togglericon':\n            this.togglerIconTemplate = item.template;\n            break;\n          case 'paginatorfirstpagelinkicon':\n            this.paginatorFirstPageLinkIconTemplate = item.template;\n            break;\n          case 'paginatorlastpagelinkicon':\n            this.paginatorLastPageLinkIconTemplate = item.template;\n            break;\n          case 'paginatorpreviouspagelinkicon':\n            this.paginatorPreviousPageLinkIconTemplate = item.template;\n            break;\n          case 'paginatornextpagelinkicon':\n            this.paginatorNextPageLinkIconTemplate = item.template;\n            break;\n        }\n      });\n    }\n    constructor(document, renderer, el, cd, zone, tableService, filterService) {\n      this.document = document;\n      this.renderer = renderer;\n      this.el = el;\n      this.cd = cd;\n      this.zone = zone;\n      this.tableService = tableService;\n      this.filterService = filterService;\n    }\n    ngOnChanges(simpleChange) {\n      if (simpleChange.value) {\n        this._value = simpleChange.value.currentValue;\n        if (!this.lazy) {\n          this.totalRecords = this._value ? this._value.length : 0;\n          if (this.sortMode == 'single' && this.sortField) this.sortSingle();else if (this.sortMode == 'multiple' && this.multiSortMeta) this.sortMultiple();else if (this.hasFilter())\n            //sort already filters\n            this._filter();\n        }\n        this.updateSerializedValue();\n        this.tableService.onUIUpdate(this.value);\n      }\n      if (simpleChange.sortField) {\n        this._sortField = simpleChange.sortField.currentValue;\n        //avoid triggering lazy load prior to lazy initialization at onInit\n        if (!this.lazy || this.initialized) {\n          if (this.sortMode === 'single') {\n            this.sortSingle();\n          }\n        }\n      }\n      if (simpleChange.sortOrder) {\n        this._sortOrder = simpleChange.sortOrder.currentValue;\n        //avoid triggering lazy load prior to lazy initialization at onInit\n        if (!this.lazy || this.initialized) {\n          if (this.sortMode === 'single') {\n            this.sortSingle();\n          }\n        }\n      }\n      if (simpleChange.multiSortMeta) {\n        this._multiSortMeta = simpleChange.multiSortMeta.currentValue;\n        if (this.sortMode === 'multiple') {\n          this.sortMultiple();\n        }\n      }\n      if (simpleChange.selection) {\n        this._selection = simpleChange.selection.currentValue;\n        if (!this.preventSelectionSetterPropagation) {\n          this.updateSelectionKeys();\n          this.tableService.onSelectionChange();\n        }\n        this.preventSelectionSetterPropagation = false;\n      }\n    }\n    updateSerializedValue() {\n      this.serializedValue = [];\n      if (this.paginator) this.serializePageNodes();else this.serializeNodes(null, this.filteredNodes || this.value, 0, true);\n    }\n    serializeNodes(parent, nodes, level, visible) {\n      if (nodes && nodes.length) {\n        for (let node of nodes) {\n          node.parent = parent;\n          const rowNode = {\n            node: node,\n            parent: parent,\n            level: level,\n            visible: visible && (parent ? parent.expanded : true)\n          };\n          this.serializedValue.push(rowNode);\n          if (rowNode.visible && node.expanded) {\n            this.serializeNodes(node, node.children, level + 1, rowNode.visible);\n          }\n        }\n      }\n    }\n    serializePageNodes() {\n      let data = this.filteredNodes || this.value;\n      this.serializedValue = [];\n      if (data && data.length) {\n        const first = this.lazy ? 0 : this.first;\n        for (let i = first; i < first + this.rows; i++) {\n          let node = data[i];\n          if (node) {\n            this.serializedValue.push({\n              node: node,\n              parent: null,\n              level: 0,\n              visible: true\n            });\n            this.serializeNodes(node, node.children, 1, true);\n          }\n        }\n      }\n    }\n    updateSelectionKeys() {\n      if (this.dataKey && this._selection) {\n        this.selectionKeys = {};\n        if (Array.isArray(this._selection)) {\n          for (let node of this._selection) {\n            this.selectionKeys[String(ObjectUtils.resolveFieldData(node.data, this.dataKey))] = 1;\n          }\n        } else {\n          this.selectionKeys[String(ObjectUtils.resolveFieldData(this._selection.data, this.dataKey))] = 1;\n        }\n      }\n    }\n    onPageChange(event) {\n      this.first = event.first;\n      this.rows = event.rows;\n      if (this.lazy) this.onLazyLoad.emit(this.createLazyLoadMetadata());else this.serializePageNodes();\n      this.onPage.emit({\n        first: this.first,\n        rows: this.rows\n      });\n      this.tableService.onUIUpdate(this.value);\n      if (this.scrollable) {\n        this.resetScrollTop();\n      }\n    }\n    sort(event) {\n      let originalEvent = event.originalEvent;\n      if (this.sortMode === 'single') {\n        this._sortOrder = this.sortField === event.field ? this.sortOrder * -1 : this.defaultSortOrder;\n        this._sortField = event.field;\n        this.sortSingle();\n        if (this.resetPageOnSort && this.scrollable) {\n          this.resetScrollTop();\n        }\n      }\n      if (this.sortMode === 'multiple') {\n        let metaKey = originalEvent.metaKey || originalEvent.ctrlKey;\n        let sortMeta = this.getSortMeta(event.field);\n        if (sortMeta) {\n          if (!metaKey) {\n            this._multiSortMeta = [{\n              field: event.field,\n              order: sortMeta.order * -1\n            }];\n            if (this.resetPageOnSort && this.scrollable) {\n              this.resetScrollTop();\n            }\n          } else {\n            sortMeta.order = sortMeta.order * -1;\n          }\n        } else {\n          if (!metaKey || !this.multiSortMeta) {\n            this._multiSortMeta = [];\n            if (this.resetPageOnSort && this.scrollable) {\n              this.resetScrollTop();\n            }\n          }\n          this.multiSortMeta.push({\n            field: event.field,\n            order: this.defaultSortOrder\n          });\n        }\n        this.sortMultiple();\n      }\n    }\n    sortSingle() {\n      if (this.sortField && this.sortOrder) {\n        if (this.lazy) {\n          this.onLazyLoad.emit(this.createLazyLoadMetadata());\n        } else if (this.value) {\n          this.sortNodes(this.value);\n          if (this.hasFilter()) {\n            this._filter();\n          }\n        }\n        let sortMeta = {\n          field: this.sortField,\n          order: this.sortOrder\n        };\n        this.onSort.emit(sortMeta);\n        this.tableService.onSort(sortMeta);\n        this.updateSerializedValue();\n      }\n    }\n    sortNodes(nodes) {\n      if (!nodes || nodes.length === 0) {\n        return;\n      }\n      if (this.customSort) {\n        this.sortFunction.emit({\n          data: nodes,\n          mode: this.sortMode,\n          field: this.sortField,\n          order: this.sortOrder\n        });\n      } else {\n        nodes.sort((node1, node2) => {\n          let value1 = ObjectUtils.resolveFieldData(node1.data, this.sortField);\n          let value2 = ObjectUtils.resolveFieldData(node2.data, this.sortField);\n          let result = null;\n          if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2, undefined, {\n            numeric: true\n          });else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n          return this.sortOrder * result;\n        });\n      }\n      for (let node of nodes) {\n        this.sortNodes(node.children);\n      }\n    }\n    sortMultiple() {\n      if (this.multiSortMeta) {\n        if (this.lazy) {\n          this.onLazyLoad.emit(this.createLazyLoadMetadata());\n        } else if (this.value) {\n          this.sortMultipleNodes(this.value);\n          if (this.hasFilter()) {\n            this._filter();\n          }\n        }\n        this.onSort.emit({\n          multisortmeta: this.multiSortMeta\n        });\n        this.updateSerializedValue();\n        this.tableService.onSort(this.multiSortMeta);\n      }\n    }\n    sortMultipleNodes(nodes) {\n      if (!nodes || nodes.length === 0) {\n        return;\n      }\n      if (this.customSort) {\n        this.sortFunction.emit({\n          data: this.value,\n          mode: this.sortMode,\n          multiSortMeta: this.multiSortMeta\n        });\n      } else {\n        nodes.sort((node1, node2) => {\n          return this.multisortField(node1, node2, this.multiSortMeta, 0);\n        });\n      }\n      for (let node of nodes) {\n        this.sortMultipleNodes(node.children);\n      }\n    }\n    multisortField(node1, node2, multiSortMeta, index) {\n      if (ObjectUtils.isEmpty(this.multiSortMeta) || ObjectUtils.isEmpty(multiSortMeta[index])) {\n        return 0;\n      }\n      let value1 = ObjectUtils.resolveFieldData(node1.data, multiSortMeta[index].field);\n      let value2 = ObjectUtils.resolveFieldData(node2.data, multiSortMeta[index].field);\n      let result = null;\n      if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;\n      if (typeof value1 == 'string' || value1 instanceof String) {\n        if (value1.localeCompare && value1 != value2) {\n          return multiSortMeta[index].order * value1.localeCompare(value2, undefined, {\n            numeric: true\n          });\n        }\n      } else {\n        result = value1 < value2 ? -1 : 1;\n      }\n      if (value1 == value2) {\n        return multiSortMeta.length - 1 > index ? this.multisortField(node1, node2, multiSortMeta, index + 1) : 0;\n      }\n      return multiSortMeta[index].order * result;\n    }\n    getSortMeta(field) {\n      if (this.multiSortMeta && this.multiSortMeta.length) {\n        for (let i = 0; i < this.multiSortMeta.length; i++) {\n          if (this.multiSortMeta[i].field === field) {\n            return this.multiSortMeta[i];\n          }\n        }\n      }\n      return null;\n    }\n    isSorted(field) {\n      if (this.sortMode === 'single') {\n        return this.sortField && this.sortField === field;\n      } else if (this.sortMode === 'multiple') {\n        let sorted = false;\n        if (this.multiSortMeta) {\n          for (let i = 0; i < this.multiSortMeta.length; i++) {\n            if (this.multiSortMeta[i].field == field) {\n              sorted = true;\n              break;\n            }\n          }\n        }\n        return sorted;\n      }\n    }\n    createLazyLoadMetadata() {\n      return {\n        first: this.first,\n        rows: this.rows,\n        sortField: this.sortField,\n        sortOrder: this.sortOrder,\n        filters: this.filters,\n        globalFilter: this.filters && this.filters['global'] ? this.filters['global'].value : null,\n        multiSortMeta: this.multiSortMeta,\n        forceUpdate: () => this.cd.detectChanges()\n      };\n    }\n    onLazyItemLoad(event) {\n      this.onLazyLoad.emit({\n        ...this.createLazyLoadMetadata(),\n        ...event,\n        rows: event.last - event.first\n      });\n    }\n    /**\n     * Resets scroll to top.\n     * @group Method\n     */\n    resetScrollTop() {\n      if (this.virtualScroll) this.scrollToVirtualIndex(0);else this.scrollTo({\n        top: 0\n      });\n    }\n    /**\n     * Scrolls to given index when using virtual scroll.\n     * @param {number} index - index of the element.\n     * @group Method\n     */\n    scrollToVirtualIndex(index) {\n      if (this.scrollableViewChild) {\n        this.scrollableViewChild.scrollToVirtualIndex(index);\n      }\n      if (this.scrollableFrozenViewChild) {\n        this.scrollableViewChild.scrollToVirtualIndex(index);\n      }\n    }\n    /**\n     * Scrolls to given index.\n     * @param {ScrollToOptions} options - Scroll options.\n     * @group Method\n     */\n    scrollTo(options) {\n      if (this.scrollableViewChild) {\n        this.scrollableViewChild.scrollTo(options);\n      }\n      if (this.scrollableFrozenViewChild) {\n        this.scrollableViewChild.scrollTo(options);\n      }\n    }\n    isEmpty() {\n      let data = this.filteredNodes || this.value;\n      return data == null || data.length == 0;\n    }\n    getBlockableElement() {\n      return this.el.nativeElement.children[0];\n    }\n    onColumnResizeBegin(event) {\n      let containerLeft = DomHandler.getOffset(this.containerViewChild?.nativeElement).left;\n      this.lastResizerHelperX = event.pageX - containerLeft + this.containerViewChild?.nativeElement.scrollLeft;\n      event.preventDefault();\n    }\n    onColumnResize(event) {\n      let containerLeft = DomHandler.getOffset(this.containerViewChild?.nativeElement).left;\n      DomHandler.addClass(this.containerViewChild?.nativeElement, 'p-unselectable-text');\n      this.resizeHelperViewChild.nativeElement.style.height = this.containerViewChild?.nativeElement.offsetHeight + 'px';\n      this.resizeHelperViewChild.nativeElement.style.top = 0 + 'px';\n      this.resizeHelperViewChild.nativeElement.style.left = event.pageX - containerLeft + this.containerViewChild?.nativeElement.scrollLeft + 'px';\n      this.resizeHelperViewChild.nativeElement.style.display = 'block';\n    }\n    onColumnResizeEnd(event, column) {\n      let delta = this.resizeHelperViewChild.nativeElement.offsetLeft - this.lastResizerHelperX;\n      let columnWidth = column.offsetWidth;\n      let newColumnWidth = columnWidth + delta;\n      let minWidth = column.style.minWidth || 15;\n      if (columnWidth + delta > parseInt(minWidth)) {\n        if (this.columnResizeMode === 'fit') {\n          let nextColumn = column.nextElementSibling;\n          while (!nextColumn.offsetParent) {\n            nextColumn = nextColumn.nextElementSibling;\n          }\n          if (nextColumn) {\n            let nextColumnWidth = nextColumn.offsetWidth - delta;\n            let nextColumnMinWidth = nextColumn.style.minWidth || 15;\n            if (newColumnWidth > 15 && nextColumnWidth > parseInt(nextColumnMinWidth)) {\n              if (this.scrollable) {\n                let scrollableView = this.findParentScrollableView(column);\n                let scrollableBodyTable = DomHandler.findSingle(scrollableView, '.p-treetable-scrollable-body table') || DomHandler.findSingle(scrollableView, '.p-scroller-viewport table');\n                let scrollableHeaderTable = DomHandler.findSingle(scrollableView, 'table.p-treetable-scrollable-header-table');\n                let scrollableFooterTable = DomHandler.findSingle(scrollableView, 'table.p-treetable-scrollable-footer-table');\n                let resizeColumnIndex = DomHandler.index(column);\n                this.resizeColGroup(scrollableHeaderTable, resizeColumnIndex, newColumnWidth, nextColumnWidth);\n                this.resizeColGroup(scrollableBodyTable, resizeColumnIndex, newColumnWidth, nextColumnWidth);\n                this.resizeColGroup(scrollableFooterTable, resizeColumnIndex, newColumnWidth, nextColumnWidth);\n              } else {\n                column.style.width = newColumnWidth + 'px';\n                if (nextColumn) {\n                  nextColumn.style.width = nextColumnWidth + 'px';\n                }\n              }\n            }\n          }\n        } else if (this.columnResizeMode === 'expand') {\n          if (this.scrollable) {\n            let scrollableView = this.findParentScrollableView(column);\n            let scrollableBody = DomHandler.findSingle(scrollableView, '.p-treetable-scrollable-body') || DomHandler.findSingle(scrollableView, '.p-scroller-viewport');\n            let scrollableHeader = DomHandler.findSingle(scrollableView, '.p-treetable-scrollable-header');\n            let scrollableFooter = DomHandler.findSingle(scrollableView, '.p-treetable-scrollable-footer');\n            let scrollableBodyTable = DomHandler.findSingle(scrollableView, '.p-treetable-scrollable-body table') || DomHandler.findSingle(scrollableView, '.p-scroller-viewport table');\n            let scrollableHeaderTable = DomHandler.findSingle(scrollableView, 'table.p-treetable-scrollable-header-table');\n            let scrollableFooterTable = DomHandler.findSingle(scrollableView, 'table.p-treetable-scrollable-footer-table');\n            scrollableBodyTable.style.width = scrollableBodyTable.offsetWidth + delta + 'px';\n            scrollableHeaderTable.style.width = scrollableHeaderTable.offsetWidth + delta + 'px';\n            if (scrollableFooterTable) {\n              scrollableFooterTable.style.width = scrollableFooterTable.offsetWidth + delta + 'px';\n            }\n            let resizeColumnIndex = DomHandler.index(column);\n            const scrollableBodyTableWidth = column ? scrollableBodyTable.offsetWidth + delta : newColumnWidth;\n            const scrollableHeaderTableWidth = column ? scrollableHeaderTable.offsetWidth + delta : newColumnWidth;\n            const isContainerInViewport = this.containerViewChild?.nativeElement.offsetWidth >= scrollableBodyTableWidth;\n            let setWidth = (container, table, width, isContainerInViewport) => {\n              if (container && table) {\n                container.style.width = isContainerInViewport ? width + DomHandler.calculateScrollbarWidth(scrollableBody) + 'px' : 'auto';\n                table.style.width = width + 'px';\n              }\n            };\n            setWidth(scrollableBody, scrollableBodyTable, scrollableBodyTableWidth, isContainerInViewport);\n            setWidth(scrollableHeader, scrollableHeaderTable, scrollableHeaderTableWidth, isContainerInViewport);\n            setWidth(scrollableFooter, scrollableFooterTable, scrollableHeaderTableWidth, isContainerInViewport);\n            this.resizeColGroup(scrollableHeaderTable, resizeColumnIndex, newColumnWidth, null);\n            this.resizeColGroup(scrollableBodyTable, resizeColumnIndex, newColumnWidth, null);\n            this.resizeColGroup(scrollableFooterTable, resizeColumnIndex, newColumnWidth, null);\n          } else {\n            this.tableViewChild.nativeElement.style.width = this.tableViewChild?.nativeElement.offsetWidth + delta + 'px';\n            column.style.width = newColumnWidth + 'px';\n            let containerWidth = this.tableViewChild?.nativeElement.style.width;\n            this.containerViewChild.nativeElement.style.width = containerWidth + 'px';\n          }\n        }\n        this.onColResize.emit({\n          element: column,\n          delta: delta\n        });\n      }\n      this.resizeHelperViewChild.nativeElement.style.display = 'none';\n      DomHandler.removeClass(this.containerViewChild?.nativeElement, 'p-unselectable-text');\n    }\n    findParentScrollableView(column) {\n      if (column) {\n        let parent = column.parentElement;\n        while (parent && !DomHandler.hasClass(parent, 'p-treetable-scrollable-view')) {\n          parent = parent.parentElement;\n        }\n        return parent;\n      } else {\n        return null;\n      }\n    }\n    resizeColGroup(table, resizeColumnIndex, newColumnWidth, nextColumnWidth) {\n      if (table) {\n        let colGroup = table.children[0].nodeName === 'COLGROUP' ? table.children[0] : null;\n        if (colGroup) {\n          let col = colGroup.children[resizeColumnIndex];\n          let nextCol = col.nextElementSibling;\n          col.style.width = newColumnWidth + 'px';\n          if (nextCol && nextColumnWidth) {\n            nextCol.style.width = nextColumnWidth + 'px';\n          }\n        } else {\n          throw 'Scrollable tables require a colgroup to support resizable columns';\n        }\n      }\n    }\n    onColumnDragStart(event, columnElement) {\n      this.reorderIconWidth = DomHandler.getHiddenElementOuterWidth(this.reorderIndicatorUpViewChild?.nativeElement);\n      this.reorderIconHeight = DomHandler.getHiddenElementOuterHeight(this.reorderIndicatorDownViewChild?.nativeElement);\n      this.draggedColumn = columnElement;\n      event.dataTransfer.setData('text', 'b'); // For firefox\n    }\n\n    onColumnDragEnter(event, dropHeader) {\n      if (this.reorderableColumns && this.draggedColumn && dropHeader) {\n        event.preventDefault();\n        let containerOffset = DomHandler.getOffset(this.containerViewChild?.nativeElement);\n        let dropHeaderOffset = DomHandler.getOffset(dropHeader);\n        if (this.draggedColumn != dropHeader) {\n          let targetLeft = dropHeaderOffset.left - containerOffset.left;\n          let targetTop = containerOffset.top - dropHeaderOffset.top;\n          let columnCenter = dropHeaderOffset.left + dropHeader.offsetWidth / 2;\n          this.reorderIndicatorUpViewChild.nativeElement.style.top = dropHeaderOffset.top - containerOffset.top - (this.reorderIconHeight - 1) + 'px';\n          this.reorderIndicatorDownViewChild.nativeElement.style.top = dropHeaderOffset.top - containerOffset.top + dropHeader.offsetHeight + 'px';\n          if (event.pageX > columnCenter) {\n            this.reorderIndicatorUpViewChild.nativeElement.style.left = targetLeft + dropHeader.offsetWidth - Math.ceil(this.reorderIconWidth / 2) + 'px';\n            this.reorderIndicatorDownViewChild.nativeElement.style.left = targetLeft + dropHeader.offsetWidth - Math.ceil(this.reorderIconWidth / 2) + 'px';\n            this.dropPosition = 1;\n          } else {\n            this.reorderIndicatorUpViewChild.nativeElement.style.left = targetLeft - Math.ceil(this.reorderIconWidth / 2) + 'px';\n            this.reorderIndicatorDownViewChild.nativeElement.style.left = targetLeft - Math.ceil(this.reorderIconWidth / 2) + 'px';\n            this.dropPosition = -1;\n          }\n          this.reorderIndicatorUpViewChild.nativeElement.style.display = 'block';\n          this.reorderIndicatorDownViewChild.nativeElement.style.display = 'block';\n        } else {\n          event.dataTransfer.dropEffect = 'none';\n        }\n      }\n    }\n    onColumnDragLeave(event) {\n      if (this.reorderableColumns && this.draggedColumn) {\n        event.preventDefault();\n        this.reorderIndicatorUpViewChild.nativeElement.style.display = 'none';\n        this.reorderIndicatorDownViewChild.nativeElement.style.display = 'none';\n      }\n    }\n    onColumnDrop(event, dropColumn) {\n      event.preventDefault();\n      if (this.draggedColumn) {\n        let dragIndex = DomHandler.indexWithinGroup(this.draggedColumn, 'ttreorderablecolumn');\n        let dropIndex = DomHandler.indexWithinGroup(dropColumn, 'ttreorderablecolumn');\n        let allowDrop = dragIndex != dropIndex;\n        if (allowDrop && (dropIndex - dragIndex == 1 && this.dropPosition === -1 || dragIndex - dropIndex == 1 && this.dropPosition === 1)) {\n          allowDrop = false;\n        }\n        if (allowDrop && dropIndex < dragIndex && this.dropPosition === 1) {\n          dropIndex = dropIndex + 1;\n        }\n        if (allowDrop && dropIndex > dragIndex && this.dropPosition === -1) {\n          dropIndex = dropIndex - 1;\n        }\n        if (allowDrop) {\n          ObjectUtils.reorderArray(this.columns, dragIndex, dropIndex);\n          this.onColReorder.emit({\n            dragIndex: dragIndex,\n            dropIndex: dropIndex,\n            columns: this.columns\n          });\n        }\n        this.reorderIndicatorUpViewChild.nativeElement.style.display = 'none';\n        this.reorderIndicatorDownViewChild.nativeElement.style.display = 'none';\n        this.draggedColumn.draggable = false;\n        this.draggedColumn = null;\n        this.dropPosition = null;\n      }\n    }\n    handleRowClick(event) {\n      let targetNode = event.originalEvent.target.nodeName;\n      if (targetNode == 'INPUT' || targetNode == 'BUTTON' || targetNode == 'A' || DomHandler.hasClass(event.originalEvent.target, 'p-clickable')) {\n        return;\n      }\n      if (this.selectionMode) {\n        this.preventSelectionSetterPropagation = true;\n        let rowNode = event.rowNode;\n        let selected = this.isSelected(rowNode.node);\n        let metaSelection = this.rowTouched ? false : this.metaKeySelection;\n        let dataKeyValue = this.dataKey ? String(ObjectUtils.resolveFieldData(rowNode.node.data, this.dataKey)) : null;\n        if (metaSelection) {\n          let keyboardEvent = event.originalEvent;\n          let metaKey = keyboardEvent.metaKey || keyboardEvent.ctrlKey;\n          if (selected && metaKey) {\n            if (this.isSingleSelectionMode()) {\n              this._selection = null;\n              this.selectionKeys = {};\n              this.selectionChange.emit(null);\n            } else {\n              let selectionIndex = this.findIndexInSelection(rowNode.node);\n              this._selection = this.selection.filter((val, i) => i != selectionIndex);\n              this.selectionChange.emit(this.selection);\n              if (dataKeyValue) {\n                delete this.selectionKeys[dataKeyValue];\n              }\n            }\n            this.onNodeUnselect.emit({\n              originalEvent: event.originalEvent,\n              node: rowNode.node,\n              type: 'row'\n            });\n          } else {\n            if (this.isSingleSelectionMode()) {\n              this._selection = rowNode.node;\n              this.selectionChange.emit(rowNode.node);\n              if (dataKeyValue) {\n                this.selectionKeys = {};\n                this.selectionKeys[dataKeyValue] = 1;\n              }\n            } else if (this.isMultipleSelectionMode()) {\n              if (metaKey) {\n                this._selection = this.selection || [];\n              } else {\n                this._selection = [];\n                this.selectionKeys = {};\n              }\n              this._selection = [...this.selection, rowNode.node];\n              this.selectionChange.emit(this.selection);\n              if (dataKeyValue) {\n                this.selectionKeys[dataKeyValue] = 1;\n              }\n            }\n            this.onNodeSelect.emit({\n              originalEvent: event.originalEvent,\n              node: rowNode.node,\n              type: 'row',\n              index: event.rowIndex\n            });\n          }\n        } else {\n          if (this.selectionMode === 'single') {\n            if (selected) {\n              this._selection = null;\n              this.selectionKeys = {};\n              this.selectionChange.emit(this.selection);\n              this.onNodeUnselect.emit({\n                originalEvent: event.originalEvent,\n                node: rowNode.node,\n                type: 'row'\n              });\n            } else {\n              this._selection = rowNode.node;\n              this.selectionChange.emit(this.selection);\n              this.onNodeSelect.emit({\n                originalEvent: event.originalEvent,\n                node: rowNode.node,\n                type: 'row',\n                index: event.rowIndex\n              });\n              if (dataKeyValue) {\n                this.selectionKeys = {};\n                this.selectionKeys[dataKeyValue] = 1;\n              }\n            }\n          } else if (this.selectionMode === 'multiple') {\n            if (selected) {\n              let selectionIndex = this.findIndexInSelection(rowNode.node);\n              this._selection = this.selection.filter((val, i) => i != selectionIndex);\n              this.selectionChange.emit(this.selection);\n              this.onNodeUnselect.emit({\n                originalEvent: event.originalEvent,\n                node: rowNode.node,\n                type: 'row'\n              });\n              if (dataKeyValue) {\n                delete this.selectionKeys[dataKeyValue];\n              }\n            } else {\n              this._selection = this.selection ? [...this.selection, rowNode.node] : [rowNode.node];\n              this.selectionChange.emit(this.selection);\n              this.onNodeSelect.emit({\n                originalEvent: event.originalEvent,\n                node: rowNode.node,\n                type: 'row',\n                index: event.rowIndex\n              });\n              if (dataKeyValue) {\n                this.selectionKeys[dataKeyValue] = 1;\n              }\n            }\n          }\n        }\n        this.tableService.onSelectionChange();\n      }\n      this.rowTouched = false;\n    }\n    handleRowTouchEnd(event) {\n      this.rowTouched = true;\n    }\n    handleRowRightClick(event) {\n      if (this.contextMenu) {\n        const node = event.rowNode.node;\n        if (this.contextMenuSelectionMode === 'separate') {\n          this.contextMenuSelection = node;\n          this.contextMenuSelectionChange.emit(node);\n          this.onContextMenuSelect.emit({\n            originalEvent: event.originalEvent,\n            node: node\n          });\n          this.contextMenu.show(event.originalEvent);\n          this.tableService.onContextMenu(node);\n        } else if (this.contextMenuSelectionMode === 'joint') {\n          this.preventSelectionSetterPropagation = true;\n          let selected = this.isSelected(node);\n          let dataKeyValue = this.dataKey ? String(ObjectUtils.resolveFieldData(node.data, this.dataKey)) : null;\n          if (!selected) {\n            if (this.isSingleSelectionMode()) {\n              this.selection = node;\n              this.selectionChange.emit(node);\n            } else if (this.isMultipleSelectionMode()) {\n              this.selection = [node];\n              this.selectionChange.emit(this.selection);\n            }\n            if (dataKeyValue) {\n              this.selectionKeys[dataKeyValue] = 1;\n            }\n          }\n          this.contextMenu.show(event.originalEvent);\n          this.onContextMenuSelect.emit({\n            originalEvent: event.originalEvent,\n            node: node\n          });\n        }\n      }\n    }\n    toggleNodeWithCheckbox(event) {\n      this.selection = this.selection || [];\n      this.preventSelectionSetterPropagation = true;\n      let node = event.rowNode.node;\n      let selected = this.isSelected(node);\n      if (selected) {\n        this.propagateSelectionDown(node, false);\n        if (event.rowNode.parent) {\n          this.propagateSelectionUp(node.parent, false);\n        }\n        this.selectionChange.emit(this.selection);\n        this.onNodeUnselect.emit({\n          originalEvent: event,\n          node: node\n        });\n      } else {\n        this.propagateSelectionDown(node, true);\n        if (event.rowNode.parent) {\n          this.propagateSelectionUp(node.parent, true);\n        }\n        this.selectionChange.emit(this.selection);\n        this.onNodeSelect.emit({\n          originalEvent: event,\n          node: node\n        });\n      }\n      this.tableService.onSelectionChange();\n    }\n    toggleNodesWithCheckbox(event, check) {\n      let data = this.filteredNodes || this.value;\n      this._selection = check && data ? data.slice() : [];\n      if (check) {\n        if (data && data.length) {\n          for (let node of data) {\n            this.propagateSelectionDown(node, true);\n          }\n        }\n      } else {\n        this._selection = [];\n        this.selectionKeys = {};\n      }\n      this.preventSelectionSetterPropagation = true;\n      this.selectionChange.emit(this._selection);\n      this.tableService.onSelectionChange();\n      this.onHeaderCheckboxToggle.emit({\n        originalEvent: event,\n        checked: check\n      });\n    }\n    propagateSelectionUp(node, select) {\n      if (node.children && node.children.length) {\n        let selectedChildCount = 0;\n        let childPartialSelected = false;\n        let dataKeyValue = this.dataKey ? String(ObjectUtils.resolveFieldData(node.data, this.dataKey)) : null;\n        for (let child of node.children) {\n          if (this.isSelected(child)) selectedChildCount++;else if (child.partialSelected) childPartialSelected = true;\n        }\n        if (select && selectedChildCount == node.children.length) {\n          this._selection = [...(this.selection || []), node];\n          node.partialSelected = false;\n          if (dataKeyValue) {\n            this.selectionKeys[dataKeyValue] = 1;\n          }\n        } else {\n          if (!select) {\n            let index = this.findIndexInSelection(node);\n            if (index >= 0) {\n              this._selection = this.selection.filter((val, i) => i != index);\n              if (dataKeyValue) {\n                delete this.selectionKeys[dataKeyValue];\n              }\n            }\n          }\n          if (childPartialSelected || selectedChildCount > 0 && selectedChildCount != node.children.length) node.partialSelected = true;else node.partialSelected = false;\n        }\n      }\n      let parent = node.parent;\n      if (parent) {\n        this.propagateSelectionUp(parent, select);\n      }\n    }\n    propagateSelectionDown(node, select) {\n      let index = this.findIndexInSelection(node);\n      let dataKeyValue = this.dataKey ? String(ObjectUtils.resolveFieldData(node.data, this.dataKey)) : null;\n      if (select && index == -1) {\n        this._selection = [...(this.selection || []), node];\n        if (dataKeyValue) {\n          this.selectionKeys[dataKeyValue] = 1;\n        }\n      } else if (!select && index > -1) {\n        this._selection = this.selection.filter((val, i) => i != index);\n        if (dataKeyValue) {\n          delete this.selectionKeys[dataKeyValue];\n        }\n      }\n      node.partialSelected = false;\n      if (node.children && node.children.length) {\n        for (let child of node.children) {\n          this.propagateSelectionDown(child, select);\n        }\n      }\n    }\n    isSelected(node) {\n      if (node && this.selection) {\n        if (this.dataKey) {\n          return this.selectionKeys[ObjectUtils.resolveFieldData(node.data, this.dataKey)] !== undefined;\n        } else {\n          if (Array.isArray(this.selection)) return this.findIndexInSelection(node) > -1;else return this.equals(node, this.selection);\n        }\n      }\n      return false;\n    }\n    findIndexInSelection(node) {\n      let index = -1;\n      if (this.selection && this.selection.length) {\n        for (let i = 0; i < this.selection.length; i++) {\n          if (this.equals(node, this.selection[i])) {\n            index = i;\n            break;\n          }\n        }\n      }\n      return index;\n    }\n    isSingleSelectionMode() {\n      return this.selectionMode === 'single';\n    }\n    isMultipleSelectionMode() {\n      return this.selectionMode === 'multiple';\n    }\n    equals(node1, node2) {\n      return this.compareSelectionBy === 'equals' ? node1 === node2 : ObjectUtils.equals(node1.data, node2.data, this.dataKey);\n    }\n    filter(value, field, matchMode) {\n      if (this.filterTimeout) {\n        clearTimeout(this.filterTimeout);\n      }\n      if (!this.isFilterBlank(value)) {\n        this.filters[field] = {\n          value: value,\n          matchMode: matchMode\n        };\n      } else if (this.filters[field]) {\n        delete this.filters[field];\n      }\n      this.filterTimeout = setTimeout(() => {\n        this._filter();\n        this.filterTimeout = null;\n      }, this.filterDelay);\n    }\n    filterGlobal(value, matchMode) {\n      this.filter(value, 'global', matchMode);\n    }\n    isFilterBlank(filter) {\n      if (filter !== null && filter !== undefined) {\n        if (typeof filter === 'string' && filter.trim().length == 0 || Array.isArray(filter) && filter.length == 0) return true;else return false;\n      }\n      return true;\n    }\n    _filter() {\n      if (this.lazy) {\n        this.onLazyLoad.emit(this.createLazyLoadMetadata());\n      } else {\n        if (!this.value) {\n          return;\n        }\n        if (!this.hasFilter()) {\n          this.filteredNodes = null;\n          if (this.paginator) {\n            this.totalRecords = this.value ? this.value.length : 0;\n          }\n        } else {\n          let globalFilterFieldsArray;\n          if (this.filters['global']) {\n            if (!this.columns && !this.globalFilterFields) throw new Error('Global filtering requires dynamic columns or globalFilterFields to be defined.');else globalFilterFieldsArray = this.globalFilterFields || this.columns;\n          }\n          this.filteredNodes = [];\n          const isStrictMode = this.filterMode === 'strict';\n          let isValueChanged = false;\n          for (let node of this.value) {\n            let copyNode = {\n              ...node\n            };\n            let localMatch = true;\n            let globalMatch = false;\n            let paramsWithoutNode;\n            for (let prop in this.filters) {\n              if (this.filters.hasOwnProperty(prop) && prop !== 'global') {\n                let filterMeta = this.filters[prop];\n                let filterField = prop;\n                let filterValue = filterMeta.value;\n                let filterMatchMode = filterMeta.matchMode || 'startsWith';\n                let filterConstraint = this.filterService.filters[filterMatchMode];\n                paramsWithoutNode = {\n                  filterField,\n                  filterValue,\n                  filterConstraint,\n                  isStrictMode\n                };\n                if (isStrictMode && !(this.findFilteredNodes(copyNode, paramsWithoutNode) || this.isFilterMatched(copyNode, paramsWithoutNode)) || !isStrictMode && !(this.isFilterMatched(copyNode, paramsWithoutNode) || this.findFilteredNodes(copyNode, paramsWithoutNode))) {\n                  localMatch = false;\n                }\n                if (!localMatch) {\n                  break;\n                }\n              }\n            }\n            if (this.filters['global'] && !globalMatch && globalFilterFieldsArray) {\n              let copyNodeForGlobal = {\n                ...copyNode\n              };\n              let filterField = undefined;\n              let filterValue = this.filters['global'].value;\n              let filterConstraint = this.filterService.filters[this.filters['global'].matchMode];\n              paramsWithoutNode = {\n                filterField,\n                filterValue,\n                filterConstraint,\n                isStrictMode,\n                globalFilterFieldsArray\n              };\n              if (isStrictMode && (this.findFilteredNodes(copyNodeForGlobal, paramsWithoutNode) || this.isFilterMatched(copyNodeForGlobal, paramsWithoutNode)) || !isStrictMode && (this.isFilterMatched(copyNodeForGlobal, paramsWithoutNode) || this.findFilteredNodes(copyNodeForGlobal, paramsWithoutNode))) {\n                globalMatch = true;\n                copyNode = copyNodeForGlobal;\n              }\n            }\n            let matches = localMatch;\n            if (this.filters['global']) {\n              matches = localMatch && globalMatch;\n            }\n            if (matches) {\n              this.filteredNodes.push(copyNode);\n            }\n            isValueChanged = isValueChanged || !localMatch || globalMatch || localMatch && this.filteredNodes.length > 0 || !globalMatch && this.filteredNodes.length === 0;\n          }\n          if (!isValueChanged) {\n            this.filteredNodes = null;\n          }\n          if (this.paginator) {\n            this.totalRecords = this.filteredNodes ? this.filteredNodes.length : this.value ? this.value.length : 0;\n          }\n        }\n      }\n      this.first = 0;\n      const filteredValue = this.filteredNodes || this.value;\n      this.onFilter.emit({\n        filters: this.filters,\n        filteredValue: filteredValue\n      });\n      this.tableService.onUIUpdate(filteredValue);\n      this.updateSerializedValue();\n      if (this.scrollable) {\n        this.resetScrollTop();\n      }\n    }\n    findFilteredNodes(node, paramsWithoutNode) {\n      if (node) {\n        let matched = false;\n        if (node.children) {\n          let childNodes = [...node.children];\n          node.children = [];\n          for (let childNode of childNodes) {\n            let copyChildNode = {\n              ...childNode\n            };\n            if (this.isFilterMatched(copyChildNode, paramsWithoutNode)) {\n              matched = true;\n              node.children.push(copyChildNode);\n            }\n          }\n        }\n        if (matched) {\n          return true;\n        }\n      }\n    }\n    isFilterMatched(node, filterOptions) {\n      let {\n        filterField,\n        filterValue,\n        filterConstraint,\n        isStrictMode,\n        globalFilterFieldsArray\n      } = filterOptions;\n      let matched = false;\n      const isMatched = field => filterConstraint(ObjectUtils.resolveFieldData(node.data, field), filterValue, this.filterLocale);\n      matched = globalFilterFieldsArray?.length ? globalFilterFieldsArray.some(globalFilterField => isMatched(globalFilterField.field || globalFilterField)) : isMatched(filterField);\n      if (!matched || isStrictMode && !this.isNodeLeaf(node)) {\n        matched = this.findFilteredNodes(node, {\n          filterField,\n          filterValue,\n          filterConstraint,\n          isStrictMode,\n          globalFilterFieldsArray\n        }) || matched;\n      }\n      return matched;\n    }\n    isNodeLeaf(node) {\n      return node.leaf === false ? false : !(node.children && node.children.length);\n    }\n    hasFilter() {\n      let empty = true;\n      for (let prop in this.filters) {\n        if (this.filters.hasOwnProperty(prop)) {\n          empty = false;\n          break;\n        }\n      }\n      return !empty;\n    }\n    /**\n     * Clears the sort and paginator state.\n     * @group Method\n     */\n    reset() {\n      this._sortField = null;\n      this._sortOrder = 1;\n      this._multiSortMeta = null;\n      this.tableService.onSort(null);\n      this.filteredNodes = null;\n      this.filters = {};\n      this.first = 0;\n      if (this.lazy) {\n        this.onLazyLoad.emit(this.createLazyLoadMetadata());\n      } else {\n        this.totalRecords = this._value ? this._value.length : 0;\n      }\n    }\n    updateEditingCell(cell, data, field) {\n      this.editingCell = cell;\n      this.editingCellData = data;\n      this.editingCellField = field;\n      this.bindDocumentEditListener();\n    }\n    isEditingCellValid() {\n      return this.editingCell && DomHandler.find(this.editingCell, '.ng-invalid.ng-dirty').length === 0;\n    }\n    bindDocumentEditListener() {\n      if (!this.documentEditListener) {\n        this.documentEditListener = this.renderer.listen(this.document, 'click', event => {\n          if (this.editingCell && !this.editingCellClick && this.isEditingCellValid()) {\n            DomHandler.removeClass(this.editingCell, 'p-cell-editing');\n            this.editingCell = null;\n            this.onEditComplete.emit({\n              field: this.editingCellField,\n              data: this.editingCellData\n            });\n            this.editingCellField = null;\n            this.editingCellData = null;\n            this.unbindDocumentEditListener();\n          }\n          this.editingCellClick = false;\n        });\n      }\n    }\n    unbindDocumentEditListener() {\n      if (this.documentEditListener) {\n        this.documentEditListener();\n        this.documentEditListener = null;\n      }\n    }\n    ngOnDestroy() {\n      this.unbindDocumentEditListener();\n      this.editingCell = null;\n      this.editingCellField = null;\n      this.editingCellData = null;\n      this.initialized = null;\n    }\n    static ɵfac = function TreeTable_Factory(t) {\n      return new (t || TreeTable)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(TreeTableService), i0.ɵɵdirectiveInject(i1.FilterService));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TreeTable,\n      selectors: [[\"p-treeTable\"]],\n      contentQueries: function TreeTable_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function TreeTable_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n          i0.ɵɵviewQuery(_c3, 5);\n          i0.ɵɵviewQuery(_c4, 5);\n          i0.ɵɵviewQuery(_c5, 5);\n          i0.ɵɵviewQuery(_c6, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.resizeHelperViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.reorderIndicatorUpViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.reorderIndicatorDownViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tableViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollableViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollableFrozenViewChild = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        columns: \"columns\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        tableStyle: \"tableStyle\",\n        tableStyleClass: \"tableStyleClass\",\n        autoLayout: \"autoLayout\",\n        lazy: \"lazy\",\n        lazyLoadOnInit: \"lazyLoadOnInit\",\n        paginator: \"paginator\",\n        rows: \"rows\",\n        first: \"first\",\n        pageLinks: \"pageLinks\",\n        rowsPerPageOptions: \"rowsPerPageOptions\",\n        alwaysShowPaginator: \"alwaysShowPaginator\",\n        paginatorPosition: \"paginatorPosition\",\n        paginatorStyleClass: \"paginatorStyleClass\",\n        paginatorDropdownAppendTo: \"paginatorDropdownAppendTo\",\n        currentPageReportTemplate: \"currentPageReportTemplate\",\n        showCurrentPageReport: \"showCurrentPageReport\",\n        showJumpToPageDropdown: \"showJumpToPageDropdown\",\n        showFirstLastIcon: \"showFirstLastIcon\",\n        showPageLinks: \"showPageLinks\",\n        defaultSortOrder: \"defaultSortOrder\",\n        sortMode: \"sortMode\",\n        resetPageOnSort: \"resetPageOnSort\",\n        customSort: \"customSort\",\n        selectionMode: \"selectionMode\",\n        contextMenuSelection: \"contextMenuSelection\",\n        contextMenuSelectionMode: \"contextMenuSelectionMode\",\n        dataKey: \"dataKey\",\n        metaKeySelection: \"metaKeySelection\",\n        compareSelectionBy: \"compareSelectionBy\",\n        rowHover: \"rowHover\",\n        loading: \"loading\",\n        loadingIcon: \"loadingIcon\",\n        showLoader: \"showLoader\",\n        scrollable: \"scrollable\",\n        scrollHeight: \"scrollHeight\",\n        virtualScroll: \"virtualScroll\",\n        virtualScrollItemSize: \"virtualScrollItemSize\",\n        virtualScrollOptions: \"virtualScrollOptions\",\n        virtualScrollDelay: \"virtualScrollDelay\",\n        frozenWidth: \"frozenWidth\",\n        frozenColumns: \"frozenColumns\",\n        resizableColumns: \"resizableColumns\",\n        columnResizeMode: \"columnResizeMode\",\n        reorderableColumns: \"reorderableColumns\",\n        contextMenu: \"contextMenu\",\n        rowTrackBy: \"rowTrackBy\",\n        filters: \"filters\",\n        globalFilterFields: \"globalFilterFields\",\n        filterDelay: \"filterDelay\",\n        filterMode: \"filterMode\",\n        filterLocale: \"filterLocale\",\n        paginatorLocale: \"paginatorLocale\",\n        totalRecords: \"totalRecords\",\n        sortField: \"sortField\",\n        sortOrder: \"sortOrder\",\n        multiSortMeta: \"multiSortMeta\",\n        selection: \"selection\",\n        value: \"value\",\n        virtualRowHeight: \"virtualRowHeight\"\n      },\n      outputs: {\n        selectionChange: \"selectionChange\",\n        contextMenuSelectionChange: \"contextMenuSelectionChange\",\n        onFilter: \"onFilter\",\n        onNodeExpand: \"onNodeExpand\",\n        onNodeCollapse: \"onNodeCollapse\",\n        onPage: \"onPage\",\n        onSort: \"onSort\",\n        onLazyLoad: \"onLazyLoad\",\n        sortFunction: \"sortFunction\",\n        onColResize: \"onColResize\",\n        onColReorder: \"onColReorder\",\n        onNodeSelect: \"onNodeSelect\",\n        onNodeUnselect: \"onNodeUnselect\",\n        onContextMenuSelect: \"onContextMenuSelect\",\n        onHeaderCheckboxToggle: \"onHeaderCheckboxToggle\",\n        onEditInit: \"onEditInit\",\n        onEditComplete: \"onEditComplete\",\n        onEditCancel: \"onEditCancel\"\n      },\n      features: [i0.ɵɵProvidersFeature([TreeTableService]), i0.ɵɵNgOnChangesFeature],\n      decls: 12,\n      vars: 20,\n      consts: [[\"data-scrollselectors\", \".p-treetable-scrollable-body\", 3, \"ngStyle\", \"ngClass\"], [\"container\", \"\"], [\"class\", \"p-treetable-loading\", 4, \"ngIf\"], [\"class\", \"p-treetable-header\", 4, \"ngIf\"], [\"styleClass\", \"p-paginator-top\", 3, \"rows\", \"first\", \"totalRecords\", \"pageLinkSize\", \"alwaysShow\", \"rowsPerPageOptions\", \"templateLeft\", \"templateRight\", \"dropdownAppendTo\", \"currentPageReportTemplate\", \"showFirstLastIcon\", \"dropdownItemTemplate\", \"showCurrentPageReport\", \"showJumpToPageDropdown\", \"showPageLinks\", \"styleClass\", \"locale\", \"onPageChange\", 4, \"ngIf\"], [\"class\", \"p-treetable-wrapper\", 4, \"ngIf\"], [\"class\", \"p-treetable-scrollable-wrapper\", 4, \"ngIf\"], [\"styleClass\", \"p-paginator-bottom\", 3, \"rows\", \"first\", \"totalRecords\", \"pageLinkSize\", \"alwaysShow\", \"rowsPerPageOptions\", \"templateLeft\", \"templateRight\", \"dropdownAppendTo\", \"currentPageReportTemplate\", \"showFirstLastIcon\", \"dropdownItemTemplate\", \"showCurrentPageReport\", \"showJumpToPageDropdown\", \"showPageLinks\", \"styleClass\", \"locale\", \"onPageChange\", 4, \"ngIf\"], [\"class\", \"p-treetable-footer\", 4, \"ngIf\"], [\"class\", \"p-column-resizer-helper\", \"style\", \"display:none\", 4, \"ngIf\"], [\"class\", \"p-treetable-reorder-indicator-up\", \"style\", \"display: none;\", 4, \"ngIf\"], [\"class\", \"p-treetable-reorder-indicator-down\", \"style\", \"display: none;\", 4, \"ngIf\"], [1, \"p-treetable-loading\"], [1, \"p-treetable-loading-overlay\", \"p-component-overlay\"], [3, \"class\", 4, \"ngIf\"], [4, \"ngIf\"], [3, \"spin\", \"styleClass\", 4, \"ngIf\"], [\"class\", \"p-treetable-loading-icon\", 4, \"ngIf\"], [3, \"spin\", \"styleClass\"], [1, \"p-treetable-loading-icon\"], [4, \"ngTemplateOutlet\"], [1, \"p-treetable-header\"], [\"styleClass\", \"p-paginator-top\", 3, \"rows\", \"first\", \"totalRecords\", \"pageLinkSize\", \"alwaysShow\", \"rowsPerPageOptions\", \"templateLeft\", \"templateRight\", \"dropdownAppendTo\", \"currentPageReportTemplate\", \"showFirstLastIcon\", \"dropdownItemTemplate\", \"showCurrentPageReport\", \"showJumpToPageDropdown\", \"showPageLinks\", \"styleClass\", \"locale\", \"onPageChange\"], [\"pTemplate\", \"firstpagelinkicon\"], [\"pTemplate\", \"previouspagelinkicon\"], [\"pTemplate\", \"lastpagelinkicon\"], [\"pTemplate\", \"nextpagelinkicon\"], [1, \"p-treetable-wrapper\"], [\"role\", \"table\", 3, \"ngClass\", \"ngStyle\"], [\"table\", \"\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"role\", \"rowgroup\", 1, \"p-treetable-thead\"], [\"role\", \"rowgroup\", 1, \"p-treetable-tbody\", 3, \"pTreeTableBody\", \"pTreeTableBodyTemplate\"], [\"role\", \"rowgroup\", 1, \"p-treetable-tfoot\"], [1, \"p-treetable-scrollable-wrapper\"], [\"class\", \"p-treetable-scrollable-view p-treetable-frozen-view\", 3, \"ttScrollableView\", \"frozen\", \"ngStyle\", \"scrollHeight\", 4, \"ngIf\"], [1, \"p-treetable-scrollable-view\", 3, \"ttScrollableView\", \"frozen\", \"scrollHeight\", \"ngStyle\"], [\"scrollableView\", \"\"], [1, \"p-treetable-scrollable-view\", \"p-treetable-frozen-view\", 3, \"ttScrollableView\", \"frozen\", \"ngStyle\", \"scrollHeight\"], [\"scrollableFrozenView\", \"\"], [\"styleClass\", \"p-paginator-bottom\", 3, \"rows\", \"first\", \"totalRecords\", \"pageLinkSize\", \"alwaysShow\", \"rowsPerPageOptions\", \"templateLeft\", \"templateRight\", \"dropdownAppendTo\", \"currentPageReportTemplate\", \"showFirstLastIcon\", \"dropdownItemTemplate\", \"showCurrentPageReport\", \"showJumpToPageDropdown\", \"showPageLinks\", \"styleClass\", \"locale\", \"onPageChange\"], [1, \"p-treetable-footer\"], [1, \"p-column-resizer-helper\", 2, \"display\", \"none\"], [\"resizeHelper\", \"\"], [1, \"p-treetable-reorder-indicator-up\", 2, \"display\", \"none\"], [\"reorderIndicatorUp\", \"\"], [1, \"p-treetable-reorder-indicator-down\", 2, \"display\", \"none\"], [\"reorderIndicatorDown\", \"\"]],\n      template: function TreeTable_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0, 1);\n          i0.ɵɵtemplate(2, TreeTable_div_2_Template, 4, 2, \"div\", 2)(3, TreeTable_div_3_Template, 2, 1, \"div\", 3)(4, TreeTable_p_paginator_4_Template, 5, 21, \"p-paginator\", 4)(5, TreeTable_div_5_Template, 9, 16, \"div\", 5)(6, TreeTable_div_6_Template, 4, 8, \"div\", 6)(7, TreeTable_p_paginator_7_Template, 5, 21, \"p-paginator\", 7)(8, TreeTable_div_8_Template, 2, 1, \"div\", 8)(9, TreeTable_div_9_Template, 2, 0, \"div\", 9)(10, TreeTable_span_10_Template, 4, 2, \"span\", 10)(11, TreeTable_span_11_Template, 4, 2, \"span\", 11);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"ngClass\", i0.ɵɵpureFunction5(14, _c10, ctx.rowHover || ctx.selectionMode === \"single\" || ctx.selectionMode === \"multiple\", ctx.autoLayout, ctx.resizableColumns, ctx.resizableColumns && ctx.columnResizeMode === \"fit\", ctx.scrollable && ctx.scrollHeight === \"flex\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading && ctx.showLoader);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.captionTemplate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.paginator && (ctx.paginatorPosition === \"top\" || ctx.paginatorPosition == \"both\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.scrollable);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.scrollable);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.paginator && (ctx.paginatorPosition === \"bottom\" || ctx.paginatorPosition == \"both\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.summaryTemplate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.resizableColumns);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.reorderableColumns);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.reorderableColumns);\n        }\n      },\n      dependencies: () => [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.Paginator, i1.PrimeTemplate, SpinnerIcon, ArrowDownIcon, ArrowUpIcon, TTScrollableView, TTBody],\n      styles: [\"@layer primeng{.p-treetable{position:relative}.p-treetable table{border-collapse:collapse;width:100%;table-layout:fixed}.p-treetable .p-sortable-column{cursor:pointer;-webkit-user-select:none;user-select:none}.p-treetable .p-sortable-column .p-column-title,.p-treetable .p-sortable-column .p-sortable-column-icon,.p-treetable .p-sortable-column .p-sortable-column-badge{vertical-align:middle}.p-treetable .p-sortable-column .p-sortable-column-badge{display:inline-flex;align-items:center;justify-content:center}.p-treetable-auto-layout>.p-treetable-wrapper{overflow-x:auto}.p-treetable-auto-layout>.p-treetable-wrapper>table{table-layout:auto}.p-treetable-hoverable-rows .p-treetable-tbody>tr{cursor:pointer}.p-treetable-toggler{cursor:pointer;-webkit-user-select:none;user-select:none;display:inline-flex;align-items:center;justify-content:center;vertical-align:middle;overflow:hidden;position:relative}p-treetabletoggler+p-treetablecheckbox .p-checkbox{vertical-align:middle}p-treetabletoggler+p-treetablecheckbox+span{vertical-align:middle}.p-treetable-scrollable-wrapper{position:relative}.p-treetable-scrollable-header,.p-treetable-scrollable-footer{overflow:hidden}.p-treetable-scrollable-body{overflow:auto;position:relative}.p-treetable-scrollable-body>table>.p-treetable-tbody>tr:first-child>td{border-top:0 none}.p-treetable-virtual-table{position:absolute}.p-treetable-frozen-view .p-treetable-scrollable-body{overflow:hidden}.p-treetable-frozen-view>.p-treetable-scrollable-body>table>.p-treetable-tbody>tr>td:last-child{border-right:0 none}.p-treetable-unfrozen-view{position:absolute;top:0}.p-treetable-flex-scrollable,.p-treetable-flex-scrollable .p-treetable-scrollable-wrapper,.p-treetable-flex-scrollable .p-treetable-scrollable-view{display:flex;flex-direction:column;flex:1;height:100%}.p-treetable-flex-scrollable .p-treetable-virtual-scrollable-body{flex:1}.p-treetable-resizable>.p-treetable-wrapper{overflow-x:auto}.p-treetable-resizable .p-treetable-thead>tr>th,.p-treetable-resizable .p-treetable-tfoot>tr>td,.p-treetable-resizable .p-treetable-tbody>tr>td{overflow:hidden}.p-treetable-resizable .p-resizable-column{background-clip:padding-box;position:relative}.p-treetable-resizable-fit .p-resizable-column:last-child .p-column-resizer{display:none}.p-treetable .p-column-resizer{display:block;position:absolute!important;top:0;right:0;margin:0;width:.5rem;height:100%;padding:0;cursor:col-resize;border:1px solid transparent}.p-treetable .p-column-resizer-helper{width:1px;position:absolute;z-index:10;display:none}.p-treetable .p-row-editor-init,.p-treetable .p-row-editor-save,.p-treetable .p-row-editor-cancel,.p-treetable .p-row-toggler{display:inline-flex;align-items:center;justify-content:center;overflow:hidden;position:relative}.p-treetable-reorder-indicator-up,.p-treetable-reorder-indicator-down{position:absolute;display:none}[ttReorderableColumn]{cursor:move}.p-treetable .p-treetable-loading-overlay{position:absolute;display:flex;align-items:center;justify-content:center;z-index:2}.p-treetable .p-scroller-loading{transform:none!important;min-height:0;position:sticky;top:0;left:0}}\\n\"],\n      encapsulation: 2\n    });\n  }\n  return TreeTable;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TTBody = /*#__PURE__*/(() => {\n  class TTBody {\n    tt;\n    treeTableService;\n    cd;\n    columns;\n    template;\n    frozen;\n    serializedNodes;\n    scrollerOptions;\n    subscription;\n    constructor(tt, treeTableService, cd) {\n      this.tt = tt;\n      this.treeTableService = treeTableService;\n      this.cd = cd;\n      this.subscription = this.tt.tableService.uiUpdateSource$.subscribe(() => {\n        if (this.tt.virtualScroll) {\n          this.cd.detectChanges();\n        }\n      });\n    }\n    getScrollerOption(option, options) {\n      if (this.tt.virtualScroll) {\n        options = options || this.scrollerOptions;\n        return options ? options[option] : null;\n      }\n      return null;\n    }\n    getRowIndex(rowIndex) {\n      const getItemOptions = this.getScrollerOption('getItemOptions');\n      return getItemOptions ? getItemOptions(rowIndex).index : rowIndex;\n    }\n    ngOnDestroy() {\n      if (this.subscription) {\n        this.subscription.unsubscribe();\n      }\n    }\n    static ɵfac = function TTBody_Factory(t) {\n      return new (t || TTBody)(i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(TreeTableService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TTBody,\n      selectors: [[\"\", \"pTreeTableBody\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        columns: [\"pTreeTableBody\", \"columns\"],\n        template: [\"pTreeTableBodyTemplate\", \"template\"],\n        frozen: \"frozen\",\n        serializedNodes: \"serializedNodes\",\n        scrollerOptions: \"scrollerOptions\"\n      },\n      attrs: _c11,\n      decls: 2,\n      vars: 3,\n      consts: [[\"ngFor\", \"\", 3, \"ngForOf\", \"ngForTrackBy\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n      template: function TTBody_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TTBody_ng_template_0_Template, 1, 1, \"ng-template\", 0)(1, TTBody_ng_container_1_Template, 2, 5, \"ng-container\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngForOf\", ctx.serializedNodes || ctx.tt.serializedValue)(\"ngForTrackBy\", ctx.tt.rowTrackBy);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.tt.isEmpty());\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet],\n      encapsulation: 2\n    });\n  }\n  return TTBody;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TTScrollableView = /*#__PURE__*/(() => {\n  class TTScrollableView {\n    platformId;\n    renderer;\n    tt;\n    el;\n    zone;\n    columns;\n    frozen;\n    scrollHeaderViewChild;\n    scrollHeaderBoxViewChild;\n    scrollBodyViewChild;\n    scrollTableViewChild;\n    scrollLoadingTableViewChild;\n    scrollFooterViewChild;\n    scrollFooterBoxViewChild;\n    scrollableAlignerViewChild;\n    scroller;\n    headerScrollListener;\n    bodyScrollListener;\n    footerScrollListener;\n    frozenSiblingBody;\n    totalRecordsSubscription;\n    _scrollHeight;\n    preventBodyScrollPropagation;\n    get scrollHeight() {\n      return this._scrollHeight;\n    }\n    set scrollHeight(val) {\n      this._scrollHeight = val;\n      if (val != null && (val.includes('%') || val.includes('calc'))) {\n        console.log('Percentage scroll height calculation is removed in favor of the more performant CSS based flex mode, use scrollHeight=\"flex\" instead.');\n      }\n    }\n    constructor(platformId, renderer, tt, el, zone) {\n      this.platformId = platformId;\n      this.renderer = renderer;\n      this.tt = tt;\n      this.el = el;\n      this.zone = zone;\n    }\n    ngAfterViewInit() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (!this.frozen) {\n          if (this.tt.frozenColumns || this.tt.frozenBodyTemplate) {\n            DomHandler.addClass(this.el.nativeElement, 'p-treetable-unfrozen-view');\n          }\n          let frozenView = this.el.nativeElement.previousElementSibling;\n          if (frozenView) {\n            if (this.tt.virtualScroll) this.frozenSiblingBody = DomHandler.findSingle(frozenView, '.p-scroller-viewport');else this.frozenSiblingBody = DomHandler.findSingle(frozenView, '.p-treetable-scrollable-body');\n          }\n          let scrollBarWidth = DomHandler.calculateScrollbarWidth();\n          this.scrollHeaderBoxViewChild.nativeElement.style.paddingRight = scrollBarWidth + 'px';\n          if (this.scrollFooterBoxViewChild && this.scrollFooterBoxViewChild.nativeElement) {\n            this.scrollFooterBoxViewChild.nativeElement.style.paddingRight = scrollBarWidth + 'px';\n          }\n        } else {\n          if (this.scrollableAlignerViewChild && this.scrollableAlignerViewChild.nativeElement) {\n            this.scrollableAlignerViewChild.nativeElement.style.height = DomHandler.calculateScrollbarHeight() + 'px';\n          }\n        }\n        this.bindEvents();\n      }\n    }\n    bindEvents() {\n      if (isPlatformBrowser(this.platformId)) {\n        this.zone.runOutsideAngular(() => {\n          if (this.scrollHeaderViewChild && this.scrollHeaderViewChild.nativeElement) {\n            this.headerScrollListener = this.renderer.listen(this.scrollHeaderBoxViewChild?.nativeElement, 'scroll', this.onHeaderScroll.bind(this));\n          }\n          if (this.scrollFooterViewChild && this.scrollFooterViewChild.nativeElement) {\n            this.footerScrollListener = this.renderer.listen(this.scrollFooterViewChild.nativeElement, 'scroll', this.onFooterScroll.bind(this));\n          }\n          if (!this.frozen) {\n            if (this.tt.virtualScroll) {\n              this.bodyScrollListener = this.renderer.listen((this.scroller?.getElementRef()).nativeElement, 'scroll', this.onBodyScroll.bind(this));\n            } else {\n              this.bodyScrollListener = this.renderer.listen(this.scrollBodyViewChild?.nativeElement, 'scroll', this.onBodyScroll.bind(this));\n            }\n          }\n        });\n      }\n    }\n    unbindEvents() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (this.scrollHeaderViewChild && this.scrollHeaderViewChild.nativeElement) {\n          if (this.headerScrollListener) {\n            this.headerScrollListener();\n            this.headerScrollListener = null;\n          }\n        }\n        if (this.scrollFooterViewChild && this.scrollFooterViewChild.nativeElement) {\n          if (this.footerScrollListener) {\n            this.footerScrollListener();\n            this.footerScrollListener = null;\n          }\n        }\n        if (this.scrollBodyViewChild && this.scrollBodyViewChild.nativeElement) {\n          if (this.bodyScrollListener) {\n            this.bodyScrollListener();\n            this.bodyScrollListener = null;\n          }\n        }\n        if (this.scroller && this.scroller.getElementRef()) {\n          if (this.bodyScrollListener) {\n            this.bodyScrollListener();\n            this.bodyScrollListener = null;\n          }\n        }\n      }\n    }\n    onHeaderScroll() {\n      const scrollLeft = this.scrollHeaderViewChild?.nativeElement.scrollLeft;\n      this.scrollBodyViewChild.nativeElement.scrollLeft = scrollLeft;\n      if (this.scrollFooterViewChild && this.scrollFooterViewChild.nativeElement) {\n        this.scrollFooterViewChild.nativeElement.scrollLeft = scrollLeft;\n      }\n      this.preventBodyScrollPropagation = true;\n    }\n    onFooterScroll() {\n      const scrollLeft = this.scrollFooterViewChild?.nativeElement.scrollLeft;\n      this.scrollBodyViewChild.nativeElement.scrollLeft = scrollLeft;\n      if (this.scrollHeaderViewChild && this.scrollHeaderViewChild.nativeElement) {\n        this.scrollHeaderViewChild.nativeElement.scrollLeft = scrollLeft;\n      }\n      this.preventBodyScrollPropagation = true;\n    }\n    onBodyScroll(event) {\n      if (this.preventBodyScrollPropagation) {\n        this.preventBodyScrollPropagation = false;\n        return;\n      }\n      if (this.scrollHeaderViewChild && this.scrollHeaderViewChild.nativeElement) {\n        this.scrollHeaderBoxViewChild.nativeElement.style.marginLeft = -1 * event.target.scrollLeft + 'px';\n      }\n      if (this.scrollFooterViewChild && this.scrollFooterViewChild.nativeElement) {\n        this.scrollFooterBoxViewChild.nativeElement.style.marginLeft = -1 * event.target.scrollLeft + 'px';\n      }\n      if (this.frozenSiblingBody) {\n        this.frozenSiblingBody.scrollTop = event.target.scrollTop;\n      }\n    }\n    scrollToVirtualIndex(index) {\n      if (this.scroller) {\n        this.scroller.scrollToIndex(index);\n      }\n    }\n    scrollTo(options) {\n      if (this.scroller) {\n        this.scroller.scrollTo(options);\n      } else {\n        if (this.scrollBodyViewChild?.nativeElement.scrollTo) {\n          this.scrollBodyViewChild.nativeElement.scrollTo(options);\n        } else {\n          this.scrollBodyViewChild.nativeElement.scrollLeft = options.left;\n          this.scrollBodyViewChild.nativeElement.scrollTop = options.top;\n        }\n      }\n    }\n    ngOnDestroy() {\n      this.unbindEvents();\n      this.frozenSiblingBody = null;\n    }\n    static ɵfac = function TTScrollableView_Factory(t) {\n      return new (t || TTScrollableView)(i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TTScrollableView,\n      selectors: [[\"\", \"ttScrollableView\", \"\"]],\n      viewQuery: function TTScrollableView_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c14, 5);\n          i0.ɵɵviewQuery(_c15, 5);\n          i0.ɵɵviewQuery(_c16, 5);\n          i0.ɵɵviewQuery(_c17, 5);\n          i0.ɵɵviewQuery(_c18, 5);\n          i0.ɵɵviewQuery(_c19, 5);\n          i0.ɵɵviewQuery(_c20, 5);\n          i0.ɵɵviewQuery(_c21, 5);\n          i0.ɵɵviewQuery(_c22, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollHeaderViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollHeaderBoxViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollBodyViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollTableViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollLoadingTableViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollFooterViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollFooterBoxViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollableAlignerViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scroller = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        columns: [\"ttScrollableView\", \"columns\"],\n        frozen: \"frozen\",\n        scrollHeight: \"scrollHeight\"\n      },\n      attrs: _c23,\n      decls: 13,\n      vars: 13,\n      consts: [[1, \"p-treetable-scrollable-header\"], [\"scrollHeader\", \"\"], [1, \"p-treetable-scrollable-header-box\"], [\"scrollHeaderBox\", \"\"], [1, \"p-treetable-scrollable-header-table\", 3, \"ngClass\", \"ngStyle\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"role\", \"rowgroup\", 1, \"p-treetable-thead\"], [\"styleClass\", \"p-treetable-scrollable-body\", 3, \"items\", \"style\", \"scrollHeight\", \"itemSize\", \"lazy\", \"options\", \"onLazyLoad\", 4, \"ngIf\"], [4, \"ngIf\"], [\"buildInItems\", \"\"], [\"class\", \"p-treetable-scrollable-footer\", 4, \"ngIf\"], [\"styleClass\", \"p-treetable-scrollable-body\", 3, \"items\", \"scrollHeight\", \"itemSize\", \"lazy\", \"options\", \"onLazyLoad\"], [\"scroller\", \"\"], [\"pTemplate\", \"content\"], [\"pTemplate\", \"loader\"], [1, \"p-treetable-scrollable-body\", 3, \"ngStyle\"], [\"scrollBody\", \"\"], [\"role\", \"table\", 3, \"ngClass\", \"ngStyle\"], [\"scrollTable\", \"\"], [\"role\", \"rowgroup\", 1, \"p-treetable-tbody\", 3, \"pTreeTableBody\", \"pTreeTableBodyTemplate\", \"serializedNodes\", \"frozen\"], [\"style\", \"background-color:transparent\", 4, \"ngIf\"], [2, \"background-color\", \"transparent\"], [\"scrollableAligner\", \"\"], [1, \"p-treetable-scrollable-footer\"], [\"scrollFooter\", \"\"], [1, \"p-treetable-scrollable-footer-box\"], [\"scrollFooterBox\", \"\"], [1, \"p-treetable-scrollable-footer-table\", 3, \"ngClass\", \"ngStyle\"], [\"role\", \"rowgroup\", 1, \"p-treetable-tfoot\"]],\n      template: function TTScrollableView_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0, 1)(2, \"div\", 2, 3)(4, \"table\", 4);\n          i0.ɵɵtemplate(5, TTScrollableView_ng_container_5_Template, 1, 0, \"ng-container\", 5);\n          i0.ɵɵelementStart(6, \"thead\", 6);\n          i0.ɵɵtemplate(7, TTScrollableView_ng_container_7_Template, 1, 0, \"ng-container\", 5);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(8, TTScrollableView_p_scroller_8_Template, 4, 10, \"p-scroller\", 7)(9, TTScrollableView_ng_container_9_Template, 4, 10, \"ng-container\", 8)(10, TTScrollableView_ng_template_10_Template, 5, 15, \"ng-template\", null, 9, i0.ɵɵtemplateRefExtractor)(12, TTScrollableView_div_12_Template, 8, 10, \"div\", 10);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", ctx.tt.tableStyleClass)(\"ngStyle\", ctx.tt.tableStyle);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.frozen ? ctx.tt.frozenColGroupTemplate || ctx.tt.colGroupTemplate : ctx.tt.colGroupTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(9, _c7, ctx.columns));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.frozen ? ctx.tt.frozenHeaderTemplate || ctx.tt.headerTemplate : ctx.tt.headerTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(11, _c7, ctx.columns));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.tt.virtualScroll);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.tt.virtualScroll);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.tt.footerTemplate);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i1.PrimeTemplate, i4.Scroller, TTBody],\n      encapsulation: 2\n    });\n  }\n  return TTScrollableView;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TTSortableColumn = /*#__PURE__*/(() => {\n  class TTSortableColumn {\n    tt;\n    field;\n    ttSortableColumnDisabled;\n    sorted;\n    subscription;\n    get ariaSorted() {\n      if (this.sorted && this.tt.sortOrder < 0) return 'descending';else if (this.sorted && this.tt.sortOrder > 0) return 'ascending';else return 'none';\n    }\n    constructor(tt) {\n      this.tt = tt;\n      if (this.isEnabled()) {\n        this.subscription = this.tt.tableService.sortSource$.subscribe(sortMeta => {\n          this.updateSortState();\n        });\n      }\n    }\n    ngOnInit() {\n      if (this.isEnabled()) {\n        this.updateSortState();\n      }\n    }\n    updateSortState() {\n      this.sorted = this.tt.isSorted(this.field);\n    }\n    onClick(event) {\n      if (this.isEnabled()) {\n        this.updateSortState();\n        this.tt.sort({\n          originalEvent: event,\n          field: this.field\n        });\n        DomHandler.clearSelection();\n      }\n    }\n    onEnterKey(event) {\n      this.onClick(event);\n    }\n    isEnabled() {\n      return this.ttSortableColumnDisabled !== true;\n    }\n    ngOnDestroy() {\n      if (this.subscription) {\n        this.subscription.unsubscribe();\n      }\n    }\n    static ɵfac = function TTSortableColumn_Factory(t) {\n      return new (t || TTSortableColumn)(i0.ɵɵdirectiveInject(TreeTable));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TTSortableColumn,\n      selectors: [[\"\", \"ttSortableColumn\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      hostVars: 7,\n      hostBindings: function TTSortableColumn_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function TTSortableColumn_click_HostBindingHandler($event) {\n            return ctx.onClick($event);\n          })(\"keydown.enter\", function TTSortableColumn_keydown_enter_HostBindingHandler($event) {\n            return ctx.onEnterKey($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"tabindex\", ctx.isEnabled() ? \"0\" : null)(\"role\", \"columnheader\")(\"aria-sort\", ctx.ariaSorted);\n          i0.ɵɵclassProp(\"p-sortable-column\", ctx.isEnabled())(\"p-highlight\", ctx.sorted);\n        }\n      },\n      inputs: {\n        field: [\"ttSortableColumn\", \"field\"],\n        ttSortableColumnDisabled: \"ttSortableColumnDisabled\"\n      }\n    });\n  }\n  return TTSortableColumn;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TTSortIcon = /*#__PURE__*/(() => {\n  class TTSortIcon {\n    tt;\n    cd;\n    field;\n    ariaLabelDesc;\n    ariaLabelAsc;\n    subscription;\n    sortOrder;\n    constructor(tt, cd) {\n      this.tt = tt;\n      this.cd = cd;\n      this.subscription = this.tt.tableService.sortSource$.subscribe(sortMeta => {\n        this.updateSortState();\n        this.cd.markForCheck();\n      });\n    }\n    ngOnInit() {\n      this.updateSortState();\n    }\n    onClick(event) {\n      event.preventDefault();\n    }\n    updateSortState() {\n      if (this.tt.sortMode === 'single') {\n        this.sortOrder = this.tt.isSorted(this.field) ? this.tt.sortOrder : 0;\n      } else if (this.tt.sortMode === 'multiple') {\n        let sortMeta = this.tt.getSortMeta(this.field);\n        this.sortOrder = sortMeta ? sortMeta.order : 0;\n      }\n    }\n    ngOnDestroy() {\n      if (this.subscription) {\n        this.subscription.unsubscribe();\n      }\n    }\n    static ɵfac = function TTSortIcon_Factory(t) {\n      return new (t || TTSortIcon)(i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TTSortIcon,\n      selectors: [[\"p-treeTableSortIcon\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        field: \"field\",\n        ariaLabelDesc: \"ariaLabelDesc\",\n        ariaLabelAsc: \"ariaLabelAsc\"\n      },\n      decls: 2,\n      vars: 2,\n      consts: [[4, \"ngIf\"], [\"class\", \"p-sortable-column-icon\", 4, \"ngIf\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"styleClass\"], [1, \"p-sortable-column-icon\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n      template: function TTSortIcon_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TTSortIcon_ng_container_0_Template, 4, 3, \"ng-container\", 0)(1, TTSortIcon_span_1_Template, 2, 4, \"span\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.tt.sortIconTemplate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.tt.sortIconTemplate);\n        }\n      },\n      dependencies: () => [i2.NgIf, i2.NgTemplateOutlet, SortAltIcon, SortAmountUpAltIcon, SortAmountDownIcon],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return TTSortIcon;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TTResizableColumn = /*#__PURE__*/(() => {\n  class TTResizableColumn {\n    document;\n    platformId;\n    renderer;\n    tt;\n    el;\n    zone;\n    ttResizableColumnDisabled;\n    resizer;\n    resizerMouseDownListener;\n    documentMouseMoveListener;\n    documentMouseUpListener;\n    constructor(document, platformId, renderer, tt, el, zone) {\n      this.document = document;\n      this.platformId = platformId;\n      this.renderer = renderer;\n      this.tt = tt;\n      this.el = el;\n      this.zone = zone;\n    }\n    ngAfterViewInit() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (this.isEnabled()) {\n          DomHandler.addClass(this.el.nativeElement, 'p-resizable-column');\n          this.resizer = this.renderer.createElement('span');\n          this.renderer.addClass(this.resizer, 'p-column-resizer');\n          this.renderer.appendChild(this.el.nativeElement, this.resizer);\n          this.zone.runOutsideAngular(() => {\n            this.resizerMouseDownListener = this.renderer.listen(this.resizer, 'mousedown', this.onMouseDown.bind(this));\n          });\n        }\n      }\n    }\n    bindDocumentEvents() {\n      this.zone.runOutsideAngular(() => {\n        this.documentMouseMoveListener = this.renderer.listen(this.document, 'mousemove', this.onDocumentMouseMove.bind(this));\n        this.documentMouseUpListener = this.renderer.listen(this.document, 'mouseup', this.onDocumentMouseUp.bind(this));\n      });\n    }\n    unbindDocumentEvents() {\n      if (this.documentMouseMoveListener) {\n        this.documentMouseMoveListener();\n        this.documentMouseMoveListener = null;\n      }\n      if (this.documentMouseUpListener) {\n        this.documentMouseUpListener();\n        this.documentMouseUpListener = null;\n      }\n    }\n    onMouseDown(event) {\n      this.tt.onColumnResizeBegin(event);\n      this.bindDocumentEvents();\n    }\n    onDocumentMouseMove(event) {\n      this.tt.onColumnResize(event);\n    }\n    onDocumentMouseUp(event) {\n      this.tt.onColumnResizeEnd(event, this.el.nativeElement);\n      this.unbindDocumentEvents();\n    }\n    isEnabled() {\n      return this.ttResizableColumnDisabled !== true;\n    }\n    ngOnDestroy() {\n      if (this.resizerMouseDownListener) {\n        this.resizerMouseDownListener();\n        this.resizerMouseDownListener = null;\n      }\n      this.unbindDocumentEvents();\n    }\n    static ɵfac = function TTResizableColumn_Factory(t) {\n      return new (t || TTResizableColumn)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TTResizableColumn,\n      selectors: [[\"\", \"ttResizableColumn\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        ttResizableColumnDisabled: \"ttResizableColumnDisabled\"\n      }\n    });\n  }\n  return TTResizableColumn;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TTReorderableColumn = /*#__PURE__*/(() => {\n  class TTReorderableColumn {\n    document;\n    platformId;\n    renderer;\n    tt;\n    el;\n    zone;\n    ttReorderableColumnDisabled;\n    dragStartListener;\n    dragOverListener;\n    dragEnterListener;\n    dragLeaveListener;\n    mouseDownListener;\n    constructor(document, platformId, renderer, tt, el, zone) {\n      this.document = document;\n      this.platformId = platformId;\n      this.renderer = renderer;\n      this.tt = tt;\n      this.el = el;\n      this.zone = zone;\n    }\n    ngAfterViewInit() {\n      if (this.isEnabled()) {\n        this.bindEvents();\n      }\n    }\n    bindEvents() {\n      if (isPlatformBrowser(this.platformId)) {\n        this.zone.runOutsideAngular(() => {\n          this.mouseDownListener = this.renderer.listen(this.el.nativeElement, 'mousedown', this.onMouseDown.bind(this));\n          this.dragStartListener = this.renderer.listen(this.el.nativeElement, 'dragstart', this.onDragStart.bind(this));\n          this.dragOverListener = this.renderer.listen(this.el.nativeElement, 'dragover', this.onDragEnter.bind(this));\n          this.dragEnterListener = this.renderer.listen(this.el.nativeElement, 'dragenter', this.onDragEnter.bind(this));\n          this.dragLeaveListener = this.renderer.listen(this.el.nativeElement, 'dragleave', this.onDragLeave.bind(this));\n        });\n      }\n    }\n    unbindEvents() {\n      if (isPlatformBrowser(this.platformId)) {\n        if (this.mouseDownListener) {\n          this.mouseDownListener();\n          this.mouseDownListener = null;\n        }\n        if (this.dragOverListener) {\n          this.dragOverListener();\n          this.dragOverListener = null;\n        }\n        if (this.dragEnterListener) {\n          this.dragEnterListener();\n          this.dragEnterListener = null;\n        }\n        if (this.dragLeaveListener) {\n          this.dragLeaveListener();\n          this.dragLeaveListener = null;\n        }\n      }\n    }\n    onMouseDown(event) {\n      if (event.target.nodeName === 'INPUT' || event.target.nodeName === 'TEXTAREA' || DomHandler.hasClass(event.target, 'p-column-resizer')) this.el.nativeElement.draggable = false;else this.el.nativeElement.draggable = true;\n    }\n    onDragStart(event) {\n      this.tt.onColumnDragStart(event, this.el.nativeElement);\n    }\n    onDragOver(event) {\n      event.preventDefault();\n    }\n    onDragEnter(event) {\n      this.tt.onColumnDragEnter(event, this.el.nativeElement);\n    }\n    onDragLeave(event) {\n      this.tt.onColumnDragLeave(event);\n    }\n    onDrop(event) {\n      if (this.isEnabled()) {\n        this.tt.onColumnDrop(event, this.el.nativeElement);\n      }\n    }\n    isEnabled() {\n      return this.ttReorderableColumnDisabled !== true;\n    }\n    ngOnDestroy() {\n      this.unbindEvents();\n    }\n    static ɵfac = function TTReorderableColumn_Factory(t) {\n      return new (t || TTReorderableColumn)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TTReorderableColumn,\n      selectors: [[\"\", \"ttReorderableColumn\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      hostBindings: function TTReorderableColumn_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"drop\", function TTReorderableColumn_drop_HostBindingHandler($event) {\n            return ctx.onDrop($event);\n          });\n        }\n      },\n      inputs: {\n        ttReorderableColumnDisabled: \"ttReorderableColumnDisabled\"\n      }\n    });\n  }\n  return TTReorderableColumn;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TTSelectableRow = /*#__PURE__*/(() => {\n  class TTSelectableRow {\n    tt;\n    tableService;\n    rowNode;\n    ttSelectableRowDisabled;\n    selected;\n    subscription;\n    constructor(tt, tableService) {\n      this.tt = tt;\n      this.tableService = tableService;\n      if (this.isEnabled()) {\n        this.subscription = this.tt.tableService.selectionSource$.subscribe(() => {\n          this.selected = this.tt.isSelected(this.rowNode.node);\n        });\n      }\n    }\n    ngOnInit() {\n      if (this.isEnabled()) {\n        this.selected = this.tt.isSelected(this.rowNode.node);\n      }\n    }\n    onClick(event) {\n      if (this.isEnabled()) {\n        this.tt.handleRowClick({\n          originalEvent: event,\n          rowNode: this.rowNode\n        });\n      }\n    }\n    onKeyDown(event) {\n      switch (event.code) {\n        case 'Enter':\n        case 'Space':\n          this.onEnterKey(event);\n          break;\n        default:\n          break;\n      }\n    }\n    onTouchEnd(event) {\n      if (this.isEnabled()) {\n        this.tt.handleRowTouchEnd(event);\n      }\n    }\n    onEnterKey(event) {\n      if (this.tt.selectionMode === 'checkbox') {\n        this.tt.toggleNodeWithCheckbox({\n          originalEvent: event,\n          rowNode: this.rowNode\n        });\n      } else {\n        this.onClick(event);\n      }\n      event.preventDefault();\n    }\n    isEnabled() {\n      return this.ttSelectableRowDisabled !== true;\n    }\n    ngOnDestroy() {\n      if (this.subscription) {\n        this.subscription.unsubscribe();\n      }\n    }\n    static ɵfac = function TTSelectableRow_Factory(t) {\n      return new (t || TTSelectableRow)(i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(TreeTableService));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TTSelectableRow,\n      selectors: [[\"\", \"ttSelectableRow\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      hostVars: 4,\n      hostBindings: function TTSelectableRow_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function TTSelectableRow_click_HostBindingHandler($event) {\n            return ctx.onClick($event);\n          })(\"keydown\", function TTSelectableRow_keydown_HostBindingHandler($event) {\n            return ctx.onKeyDown($event);\n          })(\"touchend\", function TTSelectableRow_touchend_HostBindingHandler($event) {\n            return ctx.onTouchEnd($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-p-highlight\", ctx.selected)(\"aria-checked\", ctx.selected);\n          i0.ɵɵclassProp(\"p-highlight\", ctx.selected);\n        }\n      },\n      inputs: {\n        rowNode: [\"ttSelectableRow\", \"rowNode\"],\n        ttSelectableRowDisabled: \"ttSelectableRowDisabled\"\n      }\n    });\n  }\n  return TTSelectableRow;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TTSelectableRowDblClick = /*#__PURE__*/(() => {\n  class TTSelectableRowDblClick {\n    tt;\n    tableService;\n    rowNode;\n    ttSelectableRowDisabled;\n    selected;\n    subscription;\n    constructor(tt, tableService) {\n      this.tt = tt;\n      this.tableService = tableService;\n      if (this.isEnabled()) {\n        this.subscription = this.tt.tableService.selectionSource$.subscribe(() => {\n          this.selected = this.tt.isSelected(this.rowNode.node);\n        });\n      }\n    }\n    ngOnInit() {\n      if (this.isEnabled()) {\n        this.selected = this.tt.isSelected(this.rowNode.node);\n      }\n    }\n    onClick(event) {\n      if (this.isEnabled()) {\n        this.tt.handleRowClick({\n          originalEvent: event,\n          rowNode: this.rowNode\n        });\n      }\n    }\n    isEnabled() {\n      return this.ttSelectableRowDisabled !== true;\n    }\n    ngOnDestroy() {\n      if (this.subscription) {\n        this.subscription.unsubscribe();\n      }\n    }\n    static ɵfac = function TTSelectableRowDblClick_Factory(t) {\n      return new (t || TTSelectableRowDblClick)(i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(TreeTableService));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TTSelectableRowDblClick,\n      selectors: [[\"\", \"ttSelectableRowDblClick\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      hostVars: 2,\n      hostBindings: function TTSelectableRowDblClick_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"dblclick\", function TTSelectableRowDblClick_dblclick_HostBindingHandler($event) {\n            return ctx.onClick($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"p-highlight\", ctx.selected);\n        }\n      },\n      inputs: {\n        rowNode: [\"ttSelectableRowDblClick\", \"rowNode\"],\n        ttSelectableRowDisabled: \"ttSelectableRowDisabled\"\n      }\n    });\n  }\n  return TTSelectableRowDblClick;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TTContextMenuRow = /*#__PURE__*/(() => {\n  class TTContextMenuRow {\n    tt;\n    tableService;\n    el;\n    rowNode;\n    ttContextMenuRowDisabled;\n    selected;\n    subscription;\n    constructor(tt, tableService, el) {\n      this.tt = tt;\n      this.tableService = tableService;\n      this.el = el;\n      if (this.isEnabled()) {\n        this.subscription = this.tt.tableService.contextMenuSource$.subscribe(node => {\n          this.selected = this.tt.equals(this.rowNode.node, node);\n        });\n      }\n    }\n    onContextMenu(event) {\n      if (this.isEnabled()) {\n        this.tt.handleRowRightClick({\n          originalEvent: event,\n          rowNode: this.rowNode\n        });\n        this.el.nativeElement.focus();\n        event.preventDefault();\n      }\n    }\n    isEnabled() {\n      return this.ttContextMenuRowDisabled !== true;\n    }\n    ngOnDestroy() {\n      if (this.subscription) {\n        this.subscription.unsubscribe();\n      }\n    }\n    static ɵfac = function TTContextMenuRow_Factory(t) {\n      return new (t || TTContextMenuRow)(i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(TreeTableService), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TTContextMenuRow,\n      selectors: [[\"\", \"ttContextMenuRow\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      hostVars: 3,\n      hostBindings: function TTContextMenuRow_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"contextmenu\", function TTContextMenuRow_contextmenu_HostBindingHandler($event) {\n            return ctx.onContextMenu($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"tabindex\", ctx.isEnabled() ? 0 : undefined);\n          i0.ɵɵclassProp(\"p-highlight-contextmenu\", ctx.selected);\n        }\n      },\n      inputs: {\n        rowNode: [\"ttContextMenuRow\", \"rowNode\"],\n        ttContextMenuRowDisabled: \"ttContextMenuRowDisabled\"\n      }\n    });\n  }\n  return TTContextMenuRow;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TTCheckbox = /*#__PURE__*/(() => {\n  class TTCheckbox {\n    tt;\n    tableService;\n    cd;\n    disabled;\n    rowNode;\n    checked;\n    focused;\n    subscription;\n    constructor(tt, tableService, cd) {\n      this.tt = tt;\n      this.tableService = tableService;\n      this.cd = cd;\n      this.subscription = this.tt.tableService.selectionSource$.subscribe(() => {\n        this.checked = this.tt.isSelected(this.rowNode.node);\n        this.cd.markForCheck();\n      });\n    }\n    ngOnInit() {\n      this.checked = this.tt.isSelected(this.rowNode.node);\n    }\n    onClick(event) {\n      if (!this.disabled) {\n        this.tt.toggleNodeWithCheckbox({\n          originalEvent: event,\n          rowNode: this.rowNode\n        });\n      }\n      DomHandler.clearSelection();\n    }\n    onFocus() {\n      this.focused = true;\n    }\n    onBlur() {\n      this.focused = false;\n    }\n    ngOnDestroy() {\n      if (this.subscription) {\n        this.subscription.unsubscribe();\n      }\n    }\n    static ɵfac = function TTCheckbox_Factory(t) {\n      return new (t || TTCheckbox)(i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(TreeTableService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TTCheckbox,\n      selectors: [[\"p-treeTableCheckbox\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        disabled: \"disabled\",\n        rowNode: [\"value\", \"rowNode\"]\n      },\n      decls: 7,\n      vars: 13,\n      consts: [[1, \"p-checkbox\", \"p-component\", 3, \"ngClass\", \"click\"], [1, \"p-hidden-accessible\"], [\"type\", \"checkbox\", \"tabindex\", \"-1\", 3, \"checked\", \"focus\", \"blur\"], [\"role\", \"checkbox\", 3, \"ngClass\"], [\"box\", \"\"], [4, \"ngIf\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"styleClass\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n      template: function TTCheckbox_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵlistener(\"click\", function TTCheckbox_Template_div_click_0_listener($event) {\n            return ctx.onClick($event);\n          });\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"input\", 2);\n          i0.ɵɵlistener(\"focus\", function TTCheckbox_Template_input_focus_2_listener() {\n            return ctx.onFocus();\n          })(\"blur\", function TTCheckbox_Template_input_blur_2_listener() {\n            return ctx.onBlur();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(3, \"div\", 3, 4);\n          i0.ɵɵtemplate(5, TTCheckbox_ng_container_5_Template, 3, 2, \"ng-container\", 5)(6, TTCheckbox_span_6_Template, 2, 5, \"span\", 5);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c30, ctx.focused));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"checked\", ctx.checked);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(8, _c31, ctx.checked, ctx.focused, ctx.rowNode.node.partialSelected, ctx.disabled));\n          i0.ɵɵattribute(\"aria-checked\", ctx.checked);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.tt.checkboxIconTemplate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.tt.checkboxIconTemplate);\n        }\n      },\n      dependencies: () => [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, CheckIcon, MinusIcon],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return TTCheckbox;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TTHeaderCheckbox = /*#__PURE__*/(() => {\n  class TTHeaderCheckbox {\n    tt;\n    tableService;\n    cd;\n    boxViewChild;\n    checked;\n    focused;\n    disabled;\n    selectionChangeSubscription;\n    valueChangeSubscription;\n    constructor(tt, tableService, cd) {\n      this.tt = tt;\n      this.tableService = tableService;\n      this.cd = cd;\n      this.valueChangeSubscription = this.tt.tableService.uiUpdateSource$.subscribe(() => {\n        this.checked = this.updateCheckedState();\n      });\n      this.selectionChangeSubscription = this.tt.tableService.selectionSource$.subscribe(() => {\n        this.checked = this.updateCheckedState();\n      });\n    }\n    ngOnInit() {\n      this.checked = this.updateCheckedState();\n    }\n    onClick(event, checked) {\n      if (this.tt.value && this.tt.value.length > 0) {\n        this.tt.toggleNodesWithCheckbox(event, !checked);\n      }\n      DomHandler.clearSelection();\n    }\n    onFocus() {\n      this.focused = true;\n    }\n    onBlur() {\n      this.focused = false;\n    }\n    ngOnDestroy() {\n      if (this.selectionChangeSubscription) {\n        this.selectionChangeSubscription.unsubscribe();\n      }\n      if (this.valueChangeSubscription) {\n        this.valueChangeSubscription.unsubscribe();\n      }\n    }\n    updateCheckedState() {\n      this.cd.markForCheck();\n      let checked;\n      const data = this.tt.filteredNodes || this.tt.value;\n      if (data) {\n        for (let node of data) {\n          if (this.tt.isSelected(node)) {\n            checked = true;\n          } else {\n            checked = false;\n            break;\n          }\n        }\n      } else {\n        checked = false;\n      }\n      return checked;\n    }\n    static ɵfac = function TTHeaderCheckbox_Factory(t) {\n      return new (t || TTHeaderCheckbox)(i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(TreeTableService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TTHeaderCheckbox,\n      selectors: [[\"p-treeTableHeaderCheckbox\"]],\n      viewQuery: function TTHeaderCheckbox_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c32, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.boxViewChild = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      decls: 8,\n      vars: 13,\n      consts: [[1, \"p-checkbox\", \"p-component\", 3, \"ngClass\", \"click\"], [1, \"p-hidden-accessible\"], [\"type\", \"checkbox\", 3, \"checked\", \"disabled\", \"focus\", \"blur\"], [\"cb\", \"\"], [\"role\", \"checkbox\", 3, \"ngClass\"], [\"box\", \"\"], [4, \"ngIf\"], [\"class\", \"p-checkbox-icon\", 4, \"ngIf\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"styleClass\"], [1, \"p-checkbox-icon\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n      template: function TTHeaderCheckbox_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r7 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵlistener(\"click\", function TTHeaderCheckbox_Template_div_click_0_listener($event) {\n            i0.ɵɵrestoreView(_r7);\n            const _r0 = i0.ɵɵreference(3);\n            return i0.ɵɵresetView(ctx.onClick($event, _r0.checked));\n          });\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"input\", 2, 3);\n          i0.ɵɵlistener(\"focus\", function TTHeaderCheckbox_Template_input_focus_2_listener() {\n            return ctx.onFocus();\n          })(\"blur\", function TTHeaderCheckbox_Template_input_blur_2_listener() {\n            return ctx.onBlur();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 4, 5);\n          i0.ɵɵtemplate(6, TTHeaderCheckbox_ng_container_6_Template, 2, 1, \"ng-container\", 6)(7, TTHeaderCheckbox_span_7_Template, 2, 4, \"span\", 7);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c30, ctx.focused));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"checked\", ctx.checked)(\"disabled\", !ctx.tt.value || ctx.tt.value.length === 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(9, _c33, ctx.checked, ctx.focused, !ctx.tt.value || ctx.tt.value.length === 0));\n          i0.ɵɵattribute(\"aria-checked\", ctx.checked);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.tt.headerCheckboxIconTemplate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.tt.headerCheckboxIconTemplate);\n        }\n      },\n      dependencies: () => [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, CheckIcon],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return TTHeaderCheckbox;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TTEditableColumn = /*#__PURE__*/(() => {\n  class TTEditableColumn {\n    tt;\n    el;\n    zone;\n    data;\n    field;\n    ttEditableColumnDisabled;\n    constructor(tt, el, zone) {\n      this.tt = tt;\n      this.el = el;\n      this.zone = zone;\n    }\n    ngAfterViewInit() {\n      if (this.isEnabled()) {\n        DomHandler.addClass(this.el.nativeElement, 'p-editable-column');\n      }\n    }\n    onClick(event) {\n      if (this.isEnabled()) {\n        this.tt.editingCellClick = true;\n        if (this.tt.editingCell) {\n          if (this.tt.editingCell !== this.el.nativeElement) {\n            if (!this.tt.isEditingCellValid()) {\n              return;\n            }\n            DomHandler.removeClass(this.tt.editingCell, 'p-cell-editing');\n            this.openCell();\n          }\n        } else {\n          this.openCell();\n        }\n      }\n    }\n    openCell() {\n      this.tt.updateEditingCell(this.el.nativeElement, this.data, this.field);\n      DomHandler.addClass(this.el.nativeElement, 'p-cell-editing');\n      this.tt.onEditInit.emit({\n        field: this.field,\n        data: this.data\n      });\n      this.tt.editingCellClick = true;\n      this.zone.runOutsideAngular(() => {\n        setTimeout(() => {\n          let focusable = DomHandler.findSingle(this.el.nativeElement, 'input, textarea');\n          if (focusable) {\n            focusable.focus();\n          }\n        }, 50);\n      });\n    }\n    closeEditingCell() {\n      DomHandler.removeClass(this.tt.editingCell, 'p-checkbox-icon');\n      this.tt.editingCell = null;\n      this.tt.unbindDocumentEditListener();\n    }\n    onKeyDown(event) {\n      if (this.isEnabled()) {\n        //enter\n        if (event.keyCode == 13 && !event.shiftKey) {\n          if (this.tt.isEditingCellValid()) {\n            DomHandler.removeClass(this.tt.editingCell, 'p-cell-editing');\n            this.closeEditingCell();\n            this.tt.onEditComplete.emit({\n              field: this.field,\n              data: this.data\n            });\n          }\n          event.preventDefault();\n        }\n        //escape\n        else if (event.keyCode == 27) {\n          if (this.tt.isEditingCellValid()) {\n            DomHandler.removeClass(this.tt.editingCell, 'p-cell-editing');\n            this.closeEditingCell();\n            this.tt.onEditCancel.emit({\n              field: this.field,\n              data: this.data\n            });\n          }\n          event.preventDefault();\n        }\n        //tab\n        else if (event.keyCode == 9) {\n          this.tt.onEditComplete.emit({\n            field: this.field,\n            data: this.data\n          });\n          if (event.shiftKey) this.moveToPreviousCell(event);else this.moveToNextCell(event);\n        }\n      }\n    }\n    findCell(element) {\n      if (element) {\n        let cell = element;\n        while (cell && !DomHandler.hasClass(cell, 'p-cell-editing')) {\n          cell = cell.parentElement;\n        }\n        return cell;\n      } else {\n        return null;\n      }\n    }\n    moveToPreviousCell(event) {\n      let currentCell = this.findCell(event.target);\n      let row = currentCell.parentElement;\n      let targetCell = this.findPreviousEditableColumn(currentCell);\n      if (targetCell) {\n        DomHandler.invokeElementMethod(targetCell, 'click');\n        event.preventDefault();\n      }\n    }\n    moveToNextCell(event) {\n      let currentCell = this.findCell(event.target);\n      let row = currentCell.parentElement;\n      let targetCell = this.findNextEditableColumn(currentCell);\n      if (targetCell) {\n        DomHandler.invokeElementMethod(targetCell, 'click');\n        event.preventDefault();\n      }\n    }\n    findPreviousEditableColumn(cell) {\n      let prevCell = cell.previousElementSibling;\n      if (!prevCell) {\n        let previousRow = cell.parentElement ? cell.parentElement.previousElementSibling : null;\n        if (previousRow) {\n          prevCell = previousRow.lastElementChild;\n        }\n      }\n      if (prevCell) {\n        if (DomHandler.hasClass(prevCell, 'p-editable-column')) return prevCell;else return this.findPreviousEditableColumn(prevCell);\n      } else {\n        return null;\n      }\n    }\n    findNextEditableColumn(cell) {\n      let nextCell = cell.nextElementSibling;\n      if (!nextCell) {\n        let nextRow = cell.parentElement ? cell.parentElement.nextElementSibling : null;\n        if (nextRow) {\n          nextCell = nextRow.firstElementChild;\n        }\n      }\n      if (nextCell) {\n        if (DomHandler.hasClass(nextCell, 'p-editable-column')) return nextCell;else return this.findNextEditableColumn(nextCell);\n      } else {\n        return null;\n      }\n    }\n    isEnabled() {\n      return this.ttEditableColumnDisabled !== true;\n    }\n    static ɵfac = function TTEditableColumn_Factory(t) {\n      return new (t || TTEditableColumn)(i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TTEditableColumn,\n      selectors: [[\"\", \"ttEditableColumn\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      hostBindings: function TTEditableColumn_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function TTEditableColumn_click_HostBindingHandler($event) {\n            return ctx.onClick($event);\n          })(\"keydown\", function TTEditableColumn_keydown_HostBindingHandler($event) {\n            return ctx.onKeyDown($event);\n          });\n        }\n      },\n      inputs: {\n        data: [\"ttEditableColumn\", \"data\"],\n        field: [\"ttEditableColumnField\", \"field\"],\n        ttEditableColumnDisabled: \"ttEditableColumnDisabled\"\n      }\n    });\n  }\n  return TTEditableColumn;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TreeTableCellEditor = /*#__PURE__*/(() => {\n  class TreeTableCellEditor {\n    tt;\n    editableColumn;\n    templates;\n    inputTemplate;\n    outputTemplate;\n    constructor(tt, editableColumn) {\n      this.tt = tt;\n      this.editableColumn = editableColumn;\n    }\n    ngAfterContentInit() {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'input':\n            this.inputTemplate = item.template;\n            break;\n          case 'output':\n            this.outputTemplate = item.template;\n            break;\n        }\n      });\n    }\n    static ɵfac = function TreeTableCellEditor_Factory(t) {\n      return new (t || TreeTableCellEditor)(i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(TTEditableColumn));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TreeTableCellEditor,\n      selectors: [[\"p-treeTableCellEditor\"]],\n      contentQueries: function TreeTableCellEditor_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      decls: 2,\n      vars: 2,\n      consts: [[4, \"ngIf\"], [4, \"ngTemplateOutlet\"]],\n      template: function TreeTableCellEditor_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TreeTableCellEditor_ng_container_0_Template, 2, 1, \"ng-container\", 0)(1, TreeTableCellEditor_ng_container_1_Template, 2, 1, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.tt.editingCell === ctx.editableColumn.el.nativeElement);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.tt.editingCell || ctx.tt.editingCell !== ctx.editableColumn.el.nativeElement);\n        }\n      },\n      dependencies: [i2.NgIf, i2.NgTemplateOutlet],\n      encapsulation: 2\n    });\n  }\n  return TreeTableCellEditor;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TTRow = /*#__PURE__*/(() => {\n  class TTRow {\n    tt;\n    el;\n    zone;\n    get level() {\n      return this.rowNode?.['level'] + 1;\n    }\n    get expanded() {\n      return this.rowNode?.node['expanded'];\n    }\n    rowNode;\n    constructor(tt, el, zone) {\n      this.tt = tt;\n      this.el = el;\n      this.zone = zone;\n    }\n    onKeyDown(event) {\n      switch (event.code) {\n        case 'ArrowDown':\n          this.onArrowDownKey(event);\n          break;\n        case 'ArrowUp':\n          this.onArrowUpKey(event);\n          break;\n        case 'ArrowRight':\n          this.onArrowRightKey(event);\n          break;\n        case 'ArrowLeft':\n          this.onArrowLeftKey(event);\n          break;\n        case 'Tab':\n          this.onTabKey(event);\n          break;\n        case 'Home':\n          this.onHomeKey(event);\n          break;\n        case 'End':\n          this.onEndKey(event);\n          break;\n        default:\n          break;\n      }\n    }\n    onArrowDownKey(event) {\n      let nextRow = this.el?.nativeElement?.nextElementSibling;\n      if (nextRow) {\n        this.focusRowChange(event.currentTarget, nextRow);\n      }\n      event.preventDefault();\n    }\n    onArrowUpKey(event) {\n      let prevRow = this.el?.nativeElement?.previousElementSibling;\n      if (prevRow) {\n        this.focusRowChange(event.currentTarget, prevRow);\n      }\n      event.preventDefault();\n    }\n    onArrowRightKey(event) {\n      const currentTarget = event.currentTarget;\n      const isHiddenIcon = DomHandler.findSingle(currentTarget, 'button').style.visibility === 'hidden';\n      if (!isHiddenIcon && !this.expanded && this.rowNode.node['children']) {\n        this.expand(event);\n        currentTarget.tabIndex = -1;\n      }\n      event.preventDefault();\n    }\n    onArrowLeftKey(event) {\n      const container = this.tt.containerViewChild?.nativeElement;\n      const expandedRows = DomHandler.find(container, '[aria-expanded=\"true\"]');\n      const lastExpandedRow = expandedRows[expandedRows.length - 1];\n      if (this.expanded) {\n        this.collapse(event);\n      }\n      if (lastExpandedRow) {\n        this.tt.toggleRowIndex = DomHandler.index(lastExpandedRow);\n      }\n      this.restoreFocus();\n      event.preventDefault();\n    }\n    onHomeKey(event) {\n      const firstElement = DomHandler.findSingle(this.tt.containerViewChild?.nativeElement, `tr[aria-level=\"${this.level}\"]`);\n      firstElement && DomHandler.focus(firstElement);\n      event.preventDefault();\n    }\n    onEndKey(event) {\n      const nodes = DomHandler.find(this.tt.containerViewChild?.nativeElement, `tr[aria-level=\"${this.level}\"]`);\n      const lastElement = nodes[nodes.length - 1];\n      DomHandler.focus(lastElement);\n      event.preventDefault();\n    }\n    onTabKey(event) {\n      const rows = this.el.nativeElement ? [...DomHandler.find(this.el.nativeElement.parentNode, 'tr')] : undefined;\n      if (rows && ObjectUtils.isNotEmpty(rows)) {\n        const hasSelectedRow = rows.some(row => DomHandler.getAttribute(row, 'data-p-highlight') || row.getAttribute('aria-checked') === 'true');\n        rows.forEach(row => {\n          row.tabIndex = -1;\n        });\n        if (hasSelectedRow) {\n          const selectedNodes = rows.filter(node => DomHandler.getAttribute(node, 'data-p-highlight') || node.getAttribute('aria-checked') === 'true');\n          selectedNodes[0].tabIndex = 0;\n          return;\n        }\n        rows[0].tabIndex = 0;\n      }\n    }\n    expand(event) {\n      this.tt.toggleRowIndex = DomHandler.index(this.el.nativeElement);\n      this.rowNode.node['expanded'] = true;\n      this.tt.updateSerializedValue();\n      this.tt.tableService.onUIUpdate(this.tt.value);\n      this.rowNode.node['children'] ? this.restoreFocus(this.tt.toggleRowIndex + 1) : this.restoreFocus();\n      this.tt.onNodeExpand.emit({\n        originalEvent: event,\n        node: this.rowNode.node\n      });\n    }\n    collapse(event) {\n      this.rowNode.node['expanded'] = false;\n      this.tt.updateSerializedValue();\n      this.tt.tableService.onUIUpdate(this.tt.value);\n      this.tt.onNodeCollapse.emit({\n        originalEvent: event,\n        node: this.rowNode.node\n      });\n    }\n    focusRowChange(firstFocusableRow, currentFocusedRow, lastVisibleDescendant) {\n      firstFocusableRow.tabIndex = '-1';\n      currentFocusedRow.tabIndex = '0';\n      DomHandler.focus(currentFocusedRow);\n    }\n    restoreFocus(index) {\n      this.zone.runOutsideAngular(() => {\n        setTimeout(() => {\n          const container = this.tt.containerViewChild?.nativeElement;\n          const row = DomHandler.findSingle(container, '.p-treetable-tbody').children[index || this.tt.toggleRowIndex];\n          const rows = [...DomHandler.find(container, 'tr')];\n          rows && rows.forEach(r => {\n            if (!row.isSameNode(r)) {\n              r.tabIndex = -1;\n            }\n          });\n          if (row) {\n            row.tabIndex = 0;\n            row.focus();\n          }\n        }, 25);\n      });\n    }\n    static ɵfac = function TTRow_Factory(t) {\n      return new (t || TTRow)(i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: TTRow,\n      selectors: [[\"\", \"ttRow\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      hostVars: 5,\n      hostBindings: function TTRow_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function TTRow_keydown_HostBindingHandler($event) {\n            return ctx.onKeyDown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"tabindex\", \"0\")(\"aria-expanded\", ctx.expanded)(\"aria-level\", ctx.level)(\"data-pc-section\", ctx.row)(\"role\", ctx.row);\n        }\n      },\n      inputs: {\n        rowNode: [\"ttRow\", \"rowNode\"]\n      }\n    });\n  }\n  return TTRow;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TreeTableToggler = /*#__PURE__*/(() => {\n  class TreeTableToggler {\n    tt;\n    config;\n    rowNode;\n    constructor(tt, config) {\n      this.tt = tt;\n      this.config = config;\n    }\n    get toggleButtonAriaLabel() {\n      return this.config.translation ? this.rowNode.expanded ? this.config.translation.aria.collapseRow : this.config.translation.aria.expandRow : undefined;\n    }\n    onClick(event) {\n      this.rowNode.node.expanded = !this.rowNode.node.expanded;\n      if (this.rowNode.node.expanded) {\n        this.tt.onNodeExpand.emit({\n          originalEvent: event,\n          node: this.rowNode.node\n        });\n      } else {\n        this.tt.onNodeCollapse.emit({\n          originalEvent: event,\n          node: this.rowNode.node\n        });\n      }\n      this.tt.updateSerializedValue();\n      this.tt.tableService.onUIUpdate(this.tt.value);\n      event.preventDefault();\n    }\n    static ɵfac = function TreeTableToggler_Factory(t) {\n      return new (t || TreeTableToggler)(i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(i1.PrimeNGConfig));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TreeTableToggler,\n      selectors: [[\"p-treeTableToggler\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        rowNode: \"rowNode\"\n      },\n      decls: 3,\n      vars: 12,\n      consts: [[\"type\", \"button\", \"tabindex\", \"-1\", \"pRipple\", \"\", 1, \"p-treetable-toggler\", \"p-link\", 3, \"click\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n      template: function TreeTableToggler_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"button\", 0);\n          i0.ɵɵlistener(\"click\", function TreeTableToggler_Template_button_click_0_listener($event) {\n            return ctx.onClick($event);\n          });\n          i0.ɵɵtemplate(1, TreeTableToggler_ng_container_1_Template, 3, 2, \"ng-container\", 1)(2, TreeTableToggler_2_Template, 1, 0, null, 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"visibility\", ctx.rowNode.node.leaf === false || ctx.rowNode.node.children && ctx.rowNode.node.children.length ? \"visible\" : \"hidden\")(\"margin-left\", ctx.rowNode.level * 16 + \"px\");\n          i0.ɵɵattribute(\"data-pc-section\", \"rowtoggler\")(\"data-pc-group-section\", \"rowactionbutton\")(\"aria-label\", ctx.toggleButtonAriaLabel);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.tt.togglerIconTemplate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.tt.togglerIconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(10, _c7, ctx.rowNode.node.expanded));\n        }\n      },\n      dependencies: () => [i2.NgIf, i2.NgTemplateOutlet, i5.Ripple, ChevronDownIcon, ChevronRightIcon],\n      encapsulation: 2\n    });\n  }\n  return TreeTableToggler;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TreeTableModule = /*#__PURE__*/(() => {\n  class TreeTableModule {\n    static ɵfac = function TreeTableModule_Factory(t) {\n      return new (t || TreeTableModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TreeTableModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, PaginatorModule, RippleModule, ScrollerModule, SpinnerIcon, ArrowDownIcon, ArrowUpIcon, SortAltIcon, SortAmountUpAltIcon, SortAmountDownIcon, CheckIcon, MinusIcon, ChevronDownIcon, ChevronRightIcon, SharedModule, ScrollerModule]\n    });\n  }\n  return TreeTableModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TTBody, TTCheckbox, TTContextMenuRow, TTEditableColumn, TTHeaderCheckbox, TTReorderableColumn, TTResizableColumn, TTRow, TTScrollableView, TTSelectableRow, TTSelectableRowDblClick, TTSortIcon, TTSortableColumn, TreeTable, TreeTableCellEditor, TreeTableModule, TreeTableService, TreeTableToggler };\n//# sourceMappingURL=primeng-treetable.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}