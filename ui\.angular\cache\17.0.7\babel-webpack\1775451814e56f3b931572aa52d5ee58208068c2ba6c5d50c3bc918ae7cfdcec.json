{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { NotfoundComponent } from './demo/components/notfound/notfound.component';\nimport { AppLayoutComponent } from \"./layout/app.layout.component\";\nimport { AuthGuard } from './demo/guards/auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let AppRoutingModule = /*#__PURE__*/(() => {\n  class AppRoutingModule {\n    static #_ = this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot([{\n        path: '',\n        component: AppLayoutComponent,\n        children: [{\n          path: '',\n          redirectTo: 'app/index',\n          pathMatch: 'full'\n        }, {\n          path: 'app',\n          loadChildren: () => import('./demo/components/uikit/uikit.module').then(m => m.UIModule),\n          canActivate: [AuthGuard]\n        }, {\n          path: 'utilities',\n          loadChildren: () => import('./demo/components/utilities/utilities.module').then(m => m.UtilitiesModule)\n        }, {\n          path: 'documentation',\n          loadChildren: () => import('./demo/components/documentation/documentation.module').then(m => m.DocumentationModule)\n        }, {\n          path: 'blocks',\n          loadChildren: () => import('./demo/components/primeblocks/primeblocks.module').then(m => m.PrimeBlocksModule)\n        }, {\n          path: 'pages',\n          loadChildren: () => import('./demo/components/pages/pages.module').then(m => m.PagesModule)\n        }]\n      }, {\n        path: 'auth',\n        loadChildren: () => import('./demo/components/auth/auth.module').then(m => m.AuthModule)\n      }, {\n        path: 'landing',\n        loadChildren: () => import('./demo/components/landing/landing.module').then(m => m.LandingModule)\n      }, {\n        path: 'notfound',\n        component: NotfoundComponent\n      }, {\n        path: '**',\n        redirectTo: '/notfound'\n      }], {\n        scrollPositionRestoration: 'enabled',\n        anchorScrolling: 'enabled',\n        onSameUrlNavigation: 'reload',\n        useHash: true // Enable hash-based routing for production deployment\n      }), RouterModule]\n    });\n  }\n  return AppRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}