{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nexport let EnhancedMessageService = /*#__PURE__*/(() => {\n  class EnhancedMessageService {\n    constructor(messageService) {\n      this.messageService = messageService;\n    }\n    /**\n     * Show a success message\n     */\n    showSuccess(summary, detail, life = 4000) {\n      this.messageService.add({\n        severity: 'success',\n        summary,\n        detail,\n        life,\n        icon: 'pi pi-check-circle'\n      });\n    }\n    /**\n     * Show an info message\n     */\n    showInfo(summary, detail, life = 4000) {\n      this.messageService.add({\n        severity: 'info',\n        summary,\n        detail,\n        life,\n        icon: 'pi pi-info-circle'\n      });\n    }\n    /**\n     * Show a warning message\n     */\n    showWarning(summary, detail, life = 5000) {\n      this.messageService.add({\n        severity: 'warn',\n        summary,\n        detail,\n        life,\n        icon: 'pi pi-exclamation-triangle'\n      });\n    }\n    /**\n     * Show an error message\n     */\n    showError(summary, detail, life = 6000) {\n      this.messageService.add({\n        severity: 'error',\n        summary,\n        detail,\n        life,\n        icon: 'pi pi-times-circle'\n      });\n    }\n    /**\n     * Show a sticky message (doesn't auto-hide)\n     */\n    showSticky(severity, summary, detail) {\n      this.messageService.add({\n        severity,\n        summary,\n        detail,\n        sticky: true,\n        icon: this.getIconForSeverity(severity)\n      });\n    }\n    /**\n     * Show a custom message with full control\n     */\n    showCustom(message) {\n      this.messageService.add({\n        ...message,\n        icon: message.icon || this.getIconForSeverity(message.severity)\n      });\n    }\n    /**\n     * Show multiple messages at once\n     */\n    showMultiple(messages) {\n      messages.forEach(message => this.showCustom(message));\n    }\n    /**\n     * Clear all messages\n     */\n    clear(key) {\n      this.messageService.clear(key);\n    }\n    /**\n     * Predefined common messages\n     */\n    showLoginSuccess() {\n      this.showSuccess('Welcome!', 'Login successful. Redirecting...', 3000);\n    }\n    showLogoutSuccess() {\n      this.showInfo('Goodbye!', 'You have been logged out successfully.', 3000);\n    }\n    showSaveSuccess() {\n      this.showSuccess('Saved!', 'Your changes have been saved successfully.', 3000);\n    }\n    showDeleteSuccess() {\n      this.showSuccess('Deleted!', 'Item has been deleted successfully.', 3000);\n    }\n    showValidationError() {\n      this.showError('Validation Error', 'Please check your input and try again.', 5000);\n    }\n    showNetworkError() {\n      this.showError('Connection Error', 'Unable to connect to server. Please check your internet connection.', 6000);\n    }\n    showServerError() {\n      this.showError('Server Error', 'An internal server error occurred. Please try again later.', 6000);\n    }\n    showUnauthorizedError() {\n      this.showError('Access Denied', 'You are not authorized to perform this action.', 5000);\n    }\n    showNotFoundError() {\n      this.showError('Not Found', 'The requested resource could not be found.', 5000);\n    }\n    showLoadingError() {\n      this.showError('Loading Failed', 'Failed to load data. Please try again.', 5000);\n    }\n    showNoDataWarning() {\n      this.showWarning('No Data', 'No data available to display.', 4000);\n    }\n    showUnsavedChangesWarning() {\n      this.showWarning('Unsaved Changes', 'You have unsaved changes. Please save before leaving.', 6000);\n    }\n    /**\n     * Show progress messages for long operations\n     */\n    showProgress(summary, detail) {\n      this.showInfo(summary, detail, 0); // 0 means no auto-hide\n    }\n    /**\n     * Show operation completed message\n     */\n    showOperationComplete(operation) {\n      this.showSuccess('Complete!', `${operation} completed successfully.`, 3000);\n    }\n    /**\n     * Show operation failed message\n     */\n    showOperationFailed(operation, error) {\n      this.showError('Failed!', `${operation} failed. ${error || 'Please try again.'}`, 5000);\n    }\n    /**\n     * Get appropriate icon for severity\n     */\n    getIconForSeverity(severity) {\n      switch (severity) {\n        case 'success':\n          return 'pi pi-check-circle';\n        case 'info':\n          return 'pi pi-info-circle';\n        case 'warn':\n          return 'pi pi-exclamation-triangle';\n        case 'error':\n          return 'pi pi-times-circle';\n        default:\n          return 'pi pi-info-circle';\n      }\n    }\n    /**\n     * Show message based on HTTP status code\n     */\n    showHttpError(status, customMessage) {\n      switch (status) {\n        case 400:\n          this.showValidationError();\n          break;\n        case 401:\n          this.showUnauthorizedError();\n          break;\n        case 403:\n          this.showError('Forbidden', 'You do not have permission to access this resource.');\n          break;\n        case 404:\n          this.showNotFoundError();\n          break;\n        case 409:\n          this.showError('Conflict', 'The resource already exists or there is a conflict.');\n          break;\n        case 422:\n          this.showError('Validation Failed', 'The submitted data is invalid.');\n          break;\n        case 429:\n          this.showWarning('Too Many Requests', 'Please wait before trying again.');\n          break;\n        case 500:\n          this.showServerError();\n          break;\n        case 502:\n          this.showError('Bad Gateway', 'The server is temporarily unavailable.');\n          break;\n        case 503:\n          this.showError('Service Unavailable', 'The service is temporarily unavailable.');\n          break;\n        default:\n          this.showError('Error', customMessage || `An error occurred (${status}). Please try again.`);\n      }\n    }\n    /**\n     * Show contextual messages for different operations\n     */\n    showDataLoaded(count, type = 'items') {\n      if (count === 0) {\n        this.showNoDataWarning();\n      } else {\n        this.showInfo('Data Loaded', `${count} ${type} loaded successfully.`, 3000);\n      }\n    }\n    showFormSubmitted() {\n      this.showSuccess('Submitted!', 'Form submitted successfully.', 3000);\n    }\n    showFileUploaded(filename) {\n      this.showSuccess('Upload Complete!', filename ? `${filename} uploaded successfully.` : 'File uploaded successfully.', 4000);\n    }\n    showFileUploadFailed(filename) {\n      this.showError('Upload Failed!', filename ? `Failed to upload ${filename}.` : 'File upload failed.', 5000);\n    }\n    static #_ = this.ɵfac = function EnhancedMessageService_Factory(t) {\n      return new (t || EnhancedMessageService)(i0.ɵɵinject(i1.MessageService));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: EnhancedMessageService,\n      factory: EnhancedMessageService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return EnhancedMessageService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}