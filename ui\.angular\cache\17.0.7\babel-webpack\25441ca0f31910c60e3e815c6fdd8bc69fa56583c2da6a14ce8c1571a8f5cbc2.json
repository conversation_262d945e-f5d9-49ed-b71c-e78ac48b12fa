{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\n\n/**\n * ProgressSpinner is a process status indicator.\n * @group Components\n */\nlet ProgressSpinner = /*#__PURE__*/(() => {\n  class ProgressSpinner {\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    style;\n    /**\n     * Width of the circle stroke.\n     * @group Props\n     */\n    strokeWidth = '2';\n    /**\n     * Color for the background of the circle.\n     * @group Props\n     */\n    fill = 'none';\n    /**\n     * Duration of the rotate animation.\n     * @group Props\n     */\n    animationDuration = '2s';\n    static ɵfac = function ProgressSpinner_Factory(t) {\n      return new (t || ProgressSpinner)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ProgressSpinner,\n      selectors: [[\"p-progressSpinner\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        styleClass: \"styleClass\",\n        style: \"style\",\n        strokeWidth: \"strokeWidth\",\n        fill: \"fill\",\n        animationDuration: \"animationDuration\"\n      },\n      decls: 3,\n      vars: 10,\n      consts: [[\"role\", \"progressbar\", 1, \"p-progress-spinner\", 3, \"ngStyle\", \"ngClass\"], [\"viewBox\", \"25 25 50 50\", 1, \"p-progress-spinner-svg\"], [\"cx\", \"50\", \"cy\", \"50\", \"r\", \"20\", \"stroke-miterlimit\", \"10\", 1, \"p-progress-spinner-circle\"]],\n      template: function ProgressSpinner_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(1, \"svg\", 1);\n          i0.ɵɵelement(2, \"circle\", 2);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"ngClass\", ctx.styleClass);\n          i0.ɵɵattribute(\"aria-busy\", true)(\"data-pc-name\", \"progressspinner\")(\"data-pc-section\", \"root\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"animation-duration\", ctx.animationDuration);\n          i0.ɵɵattribute(\"data-pc-section\", \"root\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵattribute(\"fill\", ctx.fill)(\"stroke-width\", ctx.strokeWidth);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgStyle],\n      styles: [\"@layer primeng{.p-progress-spinner{position:relative;margin:0 auto;width:100px;height:100px;display:inline-block}.p-progress-spinner:before{content:\\\"\\\";display:block;padding-top:100%}.p-progress-spinner-svg{animation:p-progress-spinner-rotate 2s linear infinite;height:100%;transform-origin:center center;width:100%;position:absolute;inset:0;margin:auto}.p-progress-spinner-circle{stroke-dasharray:89,200;stroke-dashoffset:0;stroke:#d62d20;animation:p-progress-spinner-dash 1.5s ease-in-out infinite,p-progress-spinner-color 6s ease-in-out infinite;stroke-linecap:round}}@keyframes p-progress-spinner-rotate{to{transform:rotate(360deg)}}@keyframes p-progress-spinner-dash{0%{stroke-dasharray:1,200;stroke-dashoffset:0}50%{stroke-dasharray:89,200;stroke-dashoffset:-35px}to{stroke-dasharray:89,200;stroke-dashoffset:-124px}}@keyframes p-progress-spinner-color{to,0%{stroke:#d62d20}40%{stroke:#0057e7}66%{stroke:#008744}80%,90%{stroke:#ffa700}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return ProgressSpinner;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ProgressSpinnerModule = /*#__PURE__*/(() => {\n  class ProgressSpinnerModule {\n    static ɵfac = function ProgressSpinnerModule_Factory(t) {\n      return new (t || ProgressSpinnerModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ProgressSpinnerModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n  return ProgressSpinnerModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ProgressSpinner, ProgressSpinnerModule };\n//# sourceMappingURL=primeng-progressspinner.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}