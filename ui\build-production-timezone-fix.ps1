# PowerShell script to build Angular app for production with timezone fixes
# Run this script from the ui directory

Write-Host "Building Angular application for production with Athens timezone support..." -ForegroundColor Green

# Clean previous build
if (Test-Path "dist") {
    Write-Host "Cleaning previous build..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force "dist"
}

# Build the application
Write-Host "Running ng build with timezone fixes..." -ForegroundColor Blue
Write-Host "Note: All dates will now be displayed in Athens timezone" -ForegroundColor Cyan
ng build --configuration production --base-href /solarkapital.ui/

if ($LASTEXITCODE -eq 0) {
    Write-Host "Build completed successfully!" -ForegroundColor Green
    Write-Host "Timezone fixes applied:" -ForegroundColor Cyan
    Write-Host "  - Charts will show Athens time" -ForegroundColor White
    Write-Host "  - Tooltips will show Athens date/time" -ForegroundColor White
    Write-Host "  - Tables will show Athens time" -ForegroundColor White
    Write-Host "  - API data conversion to Athens timezone" -ForegroundColor White
    
    Write-Host "Production build is ready in dist/solarkapital-ui/" -ForegroundColor Green
    Write-Host "Deploy the contents of this folder to your web server" -ForegroundColor Cyan
} else {
    Write-Host "Build failed!" -ForegroundColor Red
    exit 1
}
