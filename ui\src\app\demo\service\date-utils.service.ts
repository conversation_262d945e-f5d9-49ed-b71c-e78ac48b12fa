import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class DateUtilsService {
  private readonly ATHENS_TIMEZONE = 'Europe/Athens';

  constructor() { }

  /**
   * Converts any date to Athens timezone and formats it for display
   */
  toAthensTime(date: string | Date): Date {
    const inputDate = typeof date === 'string' ? new Date(date) : date;
    
    // Create a new date in Athens timezone
    const athensDate = new Date(inputDate.toLocaleString('en-US', { 
      timeZone: this.ATHENS_TIMEZONE 
    }));
    
    return athensDate;
  }

  /**
   * Formats date for chart labels (time only)
   */
  formatTimeForChart(date: string | Date): string {
    const athensDate = this.toAthensTime(date);
    return athensDate.toLocaleTimeString('el-GR', { 
      hour: '2-digit', 
      minute: '2-digit',
      timeZone: this.ATHENS_TIMEZONE
    });
  }

  /**
   * Formats date for chart tooltips (full date and time)
   */
  formatDateTimeForTooltip(date: string | Date): string {
    const athensDate = this.toAthensTime(date);
    return athensDate.toLocaleString('el-GR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: this.ATHENS_TIMEZONE
    });
  }

  /**
   * Formats date for table display
   */
  formatDateForTable(date: string | Date): string {
    const athensDate = this.toAthensTime(date);
    return athensDate.toLocaleDateString('el-GR', {
      timeZone: this.ATHENS_TIMEZONE
    });
  }

  /**
   * Formats time for table display
   */
  formatTimeForTable(date: string | Date): string {
    const athensDate = this.toAthensTime(date);
    return athensDate.toLocaleTimeString('el-GR', { 
      hour: '2-digit', 
      minute: '2-digit',
      timeZone: this.ATHENS_TIMEZONE
    });
  }

  /**
   * Converts local date to UTC for API calls
   */
  toUtcForApi(date: Date): string {
    return date.toISOString();
  }

  /**
   * Creates a date in Athens timezone for date pickers
   */
  createAthensDate(year: number, month: number, day: number, hour: number = 0, minute: number = 0): Date {
    // Create date string in Athens timezone format
    const dateString = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}T${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:00`;
    
    // Parse as local time and then convert to Athens
    const localDate = new Date(dateString);
    return this.toAthensTime(localDate);
  }
}
