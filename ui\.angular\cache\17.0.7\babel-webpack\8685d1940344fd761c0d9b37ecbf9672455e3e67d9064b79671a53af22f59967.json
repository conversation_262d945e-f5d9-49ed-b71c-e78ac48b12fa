{"ast": null, "code": "import { Subject } from 'rxjs';\nimport { takeUntil, delay } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../service/loading.service\";\nimport * as i2 from \"@angular/common\";\nfunction LoadingComponent_div_0_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.loadingService.activeRequests, \" requests in progress \");\n  }\n}\nfunction LoadingComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n    i0.ɵɵelement(2, \"div\", 3);\n    i0.ɵɵelementStart(3, \"div\", 4);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, LoadingComponent_div_0_div_5_Template, 2, 1, \"div\", 5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loadingService.activeRequests > 1);\n  }\n}\nexport let LoadingComponent = /*#__PURE__*/(() => {\n  class LoadingComponent {\n    constructor(loadingService) {\n      this.loadingService = loadingService;\n      this.isLoading = false;\n      this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n      // Προσθέτουμε ένα μικρό delay για να αποφύγουμε το \"flashing\" effect\n      // για πολύ γρήγορες κλήσεις\n      this.loadingService.loading$.pipe(delay(100),\n      // 100ms delay\n      takeUntil(this.destroy$)).subscribe(loading => {\n        this.isLoading = loading;\n      });\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    static #_ = this.ɵfac = function LoadingComponent_Factory(t) {\n      return new (t || LoadingComponent)(i0.ɵɵdirectiveInject(i1.LoadingService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoadingComponent,\n      selectors: [[\"app-loading\"]],\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"loading-overlay\", 4, \"ngIf\"], [1, \"loading-overlay\"], [1, \"loading-content\"], [1, \"spinner\"], [1, \"loading-text\"], [\"class\", \"loading-subtext\", 4, \"ngIf\"], [1, \"loading-subtext\"]],\n      template: function LoadingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, LoadingComponent_div_0_Template, 6, 1, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [i2.NgIf],\n      styles: [\".loading-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,.6);display:flex;align-items:center;justify-content:center;z-index:9999;-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px);transition:opacity .3s ease-in-out}.loading-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:1rem;background:rgba(255,255,255,.95);padding:2rem;border-radius:12px;box-shadow:0 8px 32px #0000001a;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.2)}.spinner[_ngcontent-%COMP%]{width:60px;height:60px;border:4px solid #e3f2fd;border-top:4px solid var(--primary-color, #2196f3);border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite}.loading-text[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:600;color:var(--text-color, #333);margin:0}.loading-subtext[_ngcontent-%COMP%]{font-size:.9rem;color:var(--text-color-secondary, #666);margin:0;opacity:.8}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.dark[_nghost-%COMP%]   .loading-content[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .loading-content[_ngcontent-%COMP%]{background:rgba(30,30,30,.95);border:1px solid rgba(255,255,255,.1)}.dark[_nghost-%COMP%]   .loading-text[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .loading-text[_ngcontent-%COMP%]{color:#fff}.dark[_nghost-%COMP%]   .loading-subtext[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .loading-subtext[_ngcontent-%COMP%]{color:#ccc}.dark[_nghost-%COMP%]   .spinner[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .spinner[_ngcontent-%COMP%]{border:4px solid #424242;border-top:4px solid var(--primary-color, #2196f3)}@media (max-width: 768px){.loading-content[_ngcontent-%COMP%]{padding:1.5rem;margin:1rem}.spinner[_ngcontent-%COMP%]{width:50px;height:50px}.loading-text[_ngcontent-%COMP%]{font-size:1rem}.loading-subtext[_ngcontent-%COMP%]{font-size:.8rem}}\"]\n    });\n  }\n  return LoadingComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}