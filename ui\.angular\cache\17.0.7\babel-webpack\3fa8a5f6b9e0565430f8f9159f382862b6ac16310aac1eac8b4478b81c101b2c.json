{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../service/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"../../../service/error.service\";\nimport * as i6 from \"../../../service/enhanced-message.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/checkbox\";\nimport * as i10 from \"primeng/inputtext\";\nimport * as i11 from \"primeng/password\";\nfunction RegisterComponent_small_18_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"First name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_18_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"First name must be at least 2 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_18_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"First name cannot exceed 50 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 33);\n    i0.ɵɵtemplate(1, RegisterComponent_small_18_span_1_Template, 2, 0, \"span\", 34)(2, RegisterComponent_small_18_span_2_Template, 2, 0, \"span\", 34)(3, RegisterComponent_small_18_span_3_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r0.registerForm.get(\"firstName\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r0.registerForm.get(\"firstName\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.registerForm.get(\"firstName\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"maxlength\"]);\n  }\n}\nfunction RegisterComponent_small_23_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Last name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_23_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Last name must be at least 2 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_23_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Last name cannot exceed 50 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 33);\n    i0.ɵɵtemplate(1, RegisterComponent_small_23_span_1_Template, 2, 0, \"span\", 34)(2, RegisterComponent_small_23_span_2_Template, 2, 0, \"span\", 34)(3, RegisterComponent_small_23_span_3_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r1.registerForm.get(\"lastName\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r1.registerForm.get(\"lastName\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r1.registerForm.get(\"lastName\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"maxlength\"]);\n  }\n}\nfunction RegisterComponent_small_28_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Username is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_28_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Username must be at least 3 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_28_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Username can only contain letters, numbers, and underscores\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 33);\n    i0.ɵɵtemplate(1, RegisterComponent_small_28_span_1_Template, 2, 0, \"span\", 34)(2, RegisterComponent_small_28_span_2_Template, 2, 0, \"span\", 34)(3, RegisterComponent_small_28_span_3_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r2.registerForm.get(\"username\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r2.registerForm.get(\"username\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r2.registerForm.get(\"username\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"pattern\"]);\n  }\n}\nfunction RegisterComponent_small_33_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_33_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Please enter a valid email address\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 33);\n    i0.ɵɵtemplate(1, RegisterComponent_small_33_span_1_Template, 2, 0, \"span\", 34)(2, RegisterComponent_small_33_span_2_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r3.registerForm.get(\"email\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r3.registerForm.get(\"email\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"email\"]);\n  }\n}\nfunction RegisterComponent_small_38_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_38_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Password must be at least 8 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_38_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Password must contain uppercase, lowercase, number and special character\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 33);\n    i0.ɵɵtemplate(1, RegisterComponent_small_38_span_1_Template, 2, 0, \"span\", 34)(2, RegisterComponent_small_38_span_2_Template, 2, 0, \"span\", 34)(3, RegisterComponent_small_38_span_3_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r4.registerForm.get(\"password\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r4.registerForm.get(\"password\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r4.registerForm.get(\"password\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"pattern\"]);\n  }\n}\nfunction RegisterComponent_small_43_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Please confirm your password\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_43_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Passwords do not match\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 33);\n    i0.ɵɵtemplate(1, RegisterComponent_small_43_span_1_Template, 2, 0, \"span\", 34)(2, RegisterComponent_small_43_span_2_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r5.registerForm.get(\"confirmPassword\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.registerForm.errors == null ? null : ctx_r5.registerForm.errors[\"passwordMismatch\"]);\n  }\n}\nfunction RegisterComponent_small_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 33);\n    i0.ɵɵtext(1, \" You must accept the terms and conditions \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36);\n    i0.ɵɵelement(2, \"span\", 37);\n    i0.ɵɵelementStart(3, \"span\", 38);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r7.errorMessage);\n  }\n}\nexport class RegisterComponent {\n  constructor(fb, authService, router, messageService, errorService, enhancedMessage) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.messageService = messageService;\n    this.errorService = errorService;\n    this.enhancedMessage = enhancedMessage;\n    this.errorMessage = '';\n    this.isLoading = false;\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.initializeForm();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  initializeForm() {\n    this.registerForm = this.fb.group({\n      firstName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n      lastName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n      username: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(20), Validators.pattern(/^[a-zA-Z0-9_]+$/) // Only alphanumeric and underscore\n      ]],\n\n      email: ['', [Validators.required, Validators.email, this.emailDomainValidator]],\n      password: ['', [Validators.required, Validators.minLength(8), this.strongPasswordValidator]],\n      confirmPassword: ['', [Validators.required]],\n      acceptTerms: [false, [Validators.requiredTrue]]\n    }, {\n      validators: this.passwordMatchValidator\n    });\n    // Clear error message when form values change\n    this.registerForm.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      if (this.errorMessage) {\n        this.errorMessage = '';\n      }\n    });\n  }\n  // Custom validator for strong password\n  strongPasswordValidator(control) {\n    const value = control.value;\n    if (!value) return null;\n    const hasUpperCase = /[A-Z]/.test(value);\n    const hasLowerCase = /[a-z]/.test(value);\n    const hasNumeric = /[0-9]/.test(value);\n    const hasSpecialChar = /[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(value);\n    const valid = hasUpperCase && hasLowerCase && hasNumeric && hasSpecialChar;\n    return valid ? null : {\n      pattern: true\n    };\n  }\n  // Custom validator for email domain\n  emailDomainValidator(control) {\n    const value = control.value;\n    if (!value) return null;\n    const blockedDomains = ['tempmail.com', '10minutemail.com', 'guerrillamail.com'];\n    const domain = value.split('@')[1];\n    return blockedDomains.includes(domain) ? {\n      blockedDomain: true\n    } : null;\n  }\n  // Custom validator for password match\n  passwordMatchValidator(form) {\n    const password = form.get('password')?.value;\n    const confirmPassword = form.get('confirmPassword')?.value;\n    if (!password || !confirmPassword) return null;\n    return password === confirmPassword ? null : {\n      passwordMismatch: true\n    };\n  }\n  register() {\n    if (this.registerForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n    this.isLoading = true;\n    this.errorMessage = '';\n    const {\n      firstName,\n      lastName,\n      username,\n      email,\n      password\n    } = this.registerForm.value;\n    this.authService.register(username, password, email, firstName, lastName).pipe(takeUntil(this.destroy$)).subscribe({\n      next: () => {\n        this.isLoading = false;\n        this.enhancedMessage.showSuccess('Account Created!', 'Your account has been created successfully. Please sign in.');\n        // Small delay for better UX\n        setTimeout(() => {\n          this.router.navigate(['/auth/login']);\n        }, 1500);\n      },\n      error: error => {\n        this.isLoading = false;\n        this.handleRegistrationError(error);\n      }\n    });\n  }\n  handleRegistrationError(error) {\n    let errorMessage = 'Registration failed. Please try again.';\n    if (error.status === 409) {\n      errorMessage = 'Username or email already exists. Please choose different credentials.';\n    } else if (error.status === 400) {\n      errorMessage = 'Invalid registration data. Please check your information and try again.';\n    } else if (error.status === 422) {\n      errorMessage = 'Validation failed. Please ensure all fields are filled correctly.';\n    } else if (error.status === 0) {\n      errorMessage = 'Unable to connect to server. Please check your internet connection.';\n    } else if (error.status >= 500) {\n      errorMessage = 'Server error. Please try again later.';\n    }\n    this.errorMessage = errorMessage;\n    // Also add to global error service\n    this.errorService.addError(error);\n    // Show enhanced toast message\n    this.enhancedMessage.showError('Registration Failed', errorMessage);\n  }\n  markFormGroupTouched() {\n    Object.keys(this.registerForm.controls).forEach(key => {\n      const control = this.registerForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  redirectLogin() {\n    this.router.navigate(['/auth/login']);\n  }\n  showTerms() {\n    this.enhancedMessage.showInfo('Terms of Service', 'Terms of Service page will be available soon.');\n  }\n  showPrivacy() {\n    this.enhancedMessage.showInfo('Privacy Policy', 'Privacy Policy page will be available soon.');\n  }\n  static #_ = this.ɵfac = function RegisterComponent_Factory(t) {\n    return new (t || RegisterComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ErrorService), i0.ɵɵdirectiveInject(i6.EnhancedMessageService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: RegisterComponent,\n    selectors: [[\"app-register\"]],\n    decls: 62,\n    vars: 28,\n    consts: [[1, \"auth-container\"], [1, \"auth-background\"], [1, \"bg-element\", \"bg-element-1\"], [1, \"bg-element\", \"bg-element-2\"], [1, \"bg-element\", \"bg-element-3\"], [1, \"auth-content\"], [1, \"auth-card\"], [1, \"text-center\", \"mb-4\"], [\"src\", \"assets/layout/images/logo-dark.png\", \"alt\", \"SolarKapital\", \"height\", \"50\", 1, \"mb-3\"], [1, \"text-900\", \"text-2xl\", \"font-semibold\", \"mb-2\"], [1, \"text-600\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"mb-4\"], [\"for\", \"firstName\", 1, \"block\", \"text-900\", \"font-medium\", \"mb-2\"], [\"id\", \"firstName\", \"type\", \"text\", \"formControlName\", \"firstName\", \"placeholder\", \"Enter your first name\", \"pInputText\", \"\", 1, \"w-full\", \"p-3\"], [\"class\", \"p-error\", 4, \"ngIf\"], [\"for\", \"lastName\", 1, \"block\", \"text-900\", \"font-medium\", \"mb-2\"], [\"id\", \"lastName\", \"type\", \"text\", \"formControlName\", \"lastName\", \"placeholder\", \"Enter your last name\", \"pInputText\", \"\", 1, \"w-full\", \"p-3\"], [\"for\", \"username\", 1, \"block\", \"text-900\", \"font-medium\", \"mb-2\"], [\"id\", \"username\", \"type\", \"text\", \"formControlName\", \"username\", \"placeholder\", \"Choose a username\", \"pInputText\", \"\", 1, \"w-full\", \"p-3\"], [\"for\", \"email\", 1, \"block\", \"text-900\", \"font-medium\", \"mb-2\"], [\"id\", \"email\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email address\", \"pInputText\", \"\", 1, \"w-full\", \"p-3\"], [\"for\", \"password\", 1, \"block\", \"text-900\", \"font-medium\", \"mb-2\"], [\"id\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"Create a strong password\", \"styleClass\", \"w-full\", \"inputStyleClass\", \"w-full p-3\", 3, \"toggleMask\", \"feedback\"], [\"for\", \"confirmPassword\", 1, \"block\", \"text-900\", \"font-medium\", \"mb-2\"], [\"id\", \"confirmPassword\", \"formControlName\", \"confirmPassword\", \"placeholder\", \"Confirm your password\", \"styleClass\", \"w-full\", \"inputStyleClass\", \"w-full p-3\", 3, \"toggleMask\", \"feedback\"], [1, \"flex\", \"align-items-center\"], [\"id\", \"acceptTerms\", \"formControlName\", \"acceptTerms\", \"styleClass\", \"mr-2\", 3, \"binary\"], [\"for\", \"acceptTerms\", 1, \"text-900\"], [1, \"font-medium\", \"no-underline\", \"cursor-pointer\", 2, \"color\", \"var(--primary-color)\", 3, \"click\"], [\"class\", \"p-message p-message-error mb-4\", 4, \"ngIf\"], [\"type\", \"submit\", \"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Create Account\", 1, \"w-full\", \"p-3\", \"text-xl\", \"mb-4\", 3, \"disabled\", \"loading\"], [1, \"text-center\"], [1, \"p-error\"], [4, \"ngIf\"], [1, \"p-message\", \"p-message-error\", \"mb-4\"], [1, \"p-message-wrapper\"], [1, \"p-message-icon\", \"pi\", \"pi-exclamation-triangle\"], [1, \"p-message-text\"]],\n    template: function RegisterComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7);\n        i0.ɵɵelement(8, \"img\", 8);\n        i0.ɵɵelementStart(9, \"h2\", 9);\n        i0.ɵɵtext(10, \"Create Account\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"p\", 10);\n        i0.ɵɵtext(12, \"Join us to manage your solar energy systems\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(13, \"form\", 11);\n        i0.ɵɵlistener(\"ngSubmit\", function RegisterComponent_Template_form_ngSubmit_13_listener() {\n          return ctx.register();\n        });\n        i0.ɵɵelementStart(14, \"div\", 12)(15, \"label\", 13);\n        i0.ɵɵtext(16, \"First Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(17, \"input\", 14);\n        i0.ɵɵtemplate(18, RegisterComponent_small_18_Template, 4, 3, \"small\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"div\", 12)(20, \"label\", 16);\n        i0.ɵɵtext(21, \"Last Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(22, \"input\", 17);\n        i0.ɵɵtemplate(23, RegisterComponent_small_23_Template, 4, 3, \"small\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(24, \"div\", 12)(25, \"label\", 18);\n        i0.ɵɵtext(26, \"Username\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(27, \"input\", 19);\n        i0.ɵɵtemplate(28, RegisterComponent_small_28_Template, 4, 3, \"small\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(29, \"div\", 12)(30, \"label\", 20);\n        i0.ɵɵtext(31, \"Email Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(32, \"input\", 21);\n        i0.ɵɵtemplate(33, RegisterComponent_small_33_Template, 3, 2, \"small\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(34, \"div\", 12)(35, \"label\", 22);\n        i0.ɵɵtext(36, \"Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(37, \"p-password\", 23);\n        i0.ɵɵtemplate(38, RegisterComponent_small_38_Template, 4, 3, \"small\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(39, \"div\", 12)(40, \"label\", 24);\n        i0.ɵɵtext(41, \"Confirm Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(42, \"p-password\", 25);\n        i0.ɵɵtemplate(43, RegisterComponent_small_43_Template, 3, 2, \"small\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(44, \"div\", 12)(45, \"div\", 26);\n        i0.ɵɵelement(46, \"p-checkbox\", 27);\n        i0.ɵɵelementStart(47, \"label\", 28);\n        i0.ɵɵtext(48, \" I agree to the \");\n        i0.ɵɵelementStart(49, \"a\", 29);\n        i0.ɵɵlistener(\"click\", function RegisterComponent_Template_a_click_49_listener() {\n          return ctx.showTerms();\n        });\n        i0.ɵɵtext(50, \"Terms of Service\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(51, \" and \");\n        i0.ɵɵelementStart(52, \"a\", 29);\n        i0.ɵɵlistener(\"click\", function RegisterComponent_Template_a_click_52_listener() {\n          return ctx.showPrivacy();\n        });\n        i0.ɵɵtext(53, \"Privacy Policy\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(54, RegisterComponent_small_54_Template, 2, 0, \"small\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(55, RegisterComponent_div_55_Template, 5, 1, \"div\", 30);\n        i0.ɵɵelement(56, \"button\", 31);\n        i0.ɵɵelementStart(57, \"div\", 32)(58, \"span\", 10);\n        i0.ɵɵtext(59, \"Already have an account? \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(60, \"a\", 29);\n        i0.ɵɵlistener(\"click\", function RegisterComponent_Template_a_click_60_listener() {\n          return ctx.redirectLogin();\n        });\n        i0.ɵɵtext(61, \" Sign in here \");\n        i0.ɵɵelementEnd()()()()()();\n      }\n      if (rf & 2) {\n        let tmp_1_0;\n        let tmp_2_0;\n        let tmp_3_0;\n        let tmp_4_0;\n        let tmp_5_0;\n        let tmp_6_0;\n        let tmp_7_0;\n        let tmp_8_0;\n        let tmp_9_0;\n        let tmp_12_0;\n        let tmp_13_0;\n        let tmp_16_0;\n        let tmp_18_0;\n        i0.ɵɵadvance(13);\n        i0.ɵɵproperty(\"formGroup\", ctx.registerForm);\n        i0.ɵɵadvance(4);\n        i0.ɵɵclassProp(\"p-invalid\", ((tmp_1_0 = ctx.registerForm.get(\"firstName\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.registerForm.get(\"firstName\")) == null ? null : tmp_1_0.touched));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.registerForm.get(\"firstName\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.registerForm.get(\"firstName\")) == null ? null : tmp_2_0.touched));\n        i0.ɵɵadvance(4);\n        i0.ɵɵclassProp(\"p-invalid\", ((tmp_3_0 = ctx.registerForm.get(\"lastName\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.registerForm.get(\"lastName\")) == null ? null : tmp_3_0.touched));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.registerForm.get(\"lastName\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.registerForm.get(\"lastName\")) == null ? null : tmp_4_0.touched));\n        i0.ɵɵadvance(4);\n        i0.ɵɵclassProp(\"p-invalid\", ((tmp_5_0 = ctx.registerForm.get(\"username\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.registerForm.get(\"username\")) == null ? null : tmp_5_0.touched));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.registerForm.get(\"username\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.registerForm.get(\"username\")) == null ? null : tmp_6_0.touched));\n        i0.ɵɵadvance(4);\n        i0.ɵɵclassProp(\"p-invalid\", ((tmp_7_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_7_0.touched));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_8_0.touched));\n        i0.ɵɵadvance(4);\n        i0.ɵɵclassProp(\"p-invalid\", ((tmp_9_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_9_0.invalid) && ((tmp_9_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_9_0.touched));\n        i0.ɵɵproperty(\"toggleMask\", true)(\"feedback\", true);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_12_0.invalid) && ((tmp_12_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_12_0.touched));\n        i0.ɵɵadvance(4);\n        i0.ɵɵclassProp(\"p-invalid\", ((tmp_13_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_13_0.invalid) && ((tmp_13_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_13_0.touched));\n        i0.ɵɵproperty(\"toggleMask\", true)(\"feedback\", false);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_16_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_16_0.invalid) && ((tmp_16_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_16_0.touched) || (ctx.registerForm.errors == null ? null : ctx.registerForm.errors[\"passwordMismatch\"]) && ((tmp_16_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_16_0.touched));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"binary\", true);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngIf\", ((tmp_18_0 = ctx.registerForm.get(\"acceptTerms\")) == null ? null : tmp_18_0.invalid) && ((tmp_18_0 = ctx.registerForm.get(\"acceptTerms\")) == null ? null : tmp_18_0.touched));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"disabled\", ctx.registerForm.invalid || ctx.isLoading)(\"loading\", ctx.isLoading);\n      }\n    },\n    dependencies: [i7.NgIf, i8.ButtonDirective, i9.Checkbox, i10.InputText, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i11.Password],\n    styles: [\".auth-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  overflow: hidden;\\n  background: var(--surface-ground);\\n  padding: 1rem;\\n}\\n\\n.auth-background[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  z-index: 1;\\n  opacity: 0.1;\\n}\\n.auth-background[_ngcontent-%COMP%]   .bg-element[_ngcontent-%COMP%] {\\n  position: absolute;\\n  border-radius: 50%;\\n  background: var(--primary-color);\\n  animation: _ngcontent-%COMP%_float 6s ease-in-out infinite;\\n}\\n.auth-background[_ngcontent-%COMP%]   .bg-element.bg-element-1[_ngcontent-%COMP%] {\\n  width: 200px;\\n  height: 200px;\\n  top: 10%;\\n  left: 10%;\\n  animation-delay: 0s;\\n}\\n.auth-background[_ngcontent-%COMP%]   .bg-element.bg-element-2[_ngcontent-%COMP%] {\\n  width: 150px;\\n  height: 150px;\\n  top: 60%;\\n  right: 10%;\\n  animation-delay: 2s;\\n}\\n.auth-background[_ngcontent-%COMP%]   .bg-element.bg-element-3[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  bottom: 20%;\\n  left: 60%;\\n  animation-delay: 4s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_float {\\n  0%, 100% {\\n    transform: translateY(0px);\\n  }\\n  50% {\\n    transform: translateY(-20px);\\n  }\\n}\\n.auth-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 2;\\n  width: 100%;\\n  max-width: 500px;\\n}\\n\\n.auth-card[_ngcontent-%COMP%] {\\n  background: var(--surface-card);\\n  border-radius: 16px;\\n  padding: 2rem;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  border: 1px solid var(--surface-border);\\n}\\n\\n.p-message-error[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  margin-bottom: 1rem;\\n}\\n.p-message-error[_ngcontent-%COMP%]   .p-message-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n[_nghost-%COMP%]     .p-password-panel {\\n  background: var(--surface-overlay);\\n  border: 1px solid var(--surface-border);\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  padding: 1rem;\\n  margin-top: 0.5rem;\\n}\\n[_nghost-%COMP%]     .p-password-panel .p-password-meter {\\n  margin-bottom: 0.5rem;\\n}\\n[_nghost-%COMP%]     .p-password-panel .p-password-meter .p-password-strength {\\n  height: 4px;\\n  border-radius: 2px;\\n  transition: all 0.3s ease;\\n}\\n[_nghost-%COMP%]     .p-password-panel .p-password-info {\\n  font-size: 0.75rem;\\n  color: var(--text-color-secondary);\\n  line-height: 1.4;\\n}\\n\\n@media (max-width: 768px) {\\n  .auth-content[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n    max-width: 100%;\\n  }\\n  .auth-card[_ngcontent-%COMP%] {\\n    padding: 1.5rem;\\n    margin: 1rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "RegisterComponent_small_18_span_1_Template", "RegisterComponent_small_18_span_2_Template", "RegisterComponent_small_18_span_3_Template", "ɵɵadvance", "ɵɵproperty", "tmp_0_0", "ctx_r0", "registerForm", "get", "errors", "tmp_1_0", "tmp_2_0", "RegisterComponent_small_23_span_1_Template", "RegisterComponent_small_23_span_2_Template", "RegisterComponent_small_23_span_3_Template", "ctx_r1", "RegisterComponent_small_28_span_1_Template", "RegisterComponent_small_28_span_2_Template", "RegisterComponent_small_28_span_3_Template", "ctx_r2", "RegisterComponent_small_33_span_1_Template", "RegisterComponent_small_33_span_2_Template", "ctx_r3", "RegisterComponent_small_38_span_1_Template", "RegisterComponent_small_38_span_2_Template", "RegisterComponent_small_38_span_3_Template", "ctx_r4", "RegisterComponent_small_43_span_1_Template", "RegisterComponent_small_43_span_2_Template", "ctx_r5", "ɵɵelement", "ɵɵtextInterpolate", "ctx_r7", "errorMessage", "RegisterComponent", "constructor", "fb", "authService", "router", "messageService", "errorService", "enhancedMessage", "isLoading", "destroy$", "ngOnInit", "initializeForm", "ngOnDestroy", "next", "complete", "group", "firstName", "required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "lastName", "username", "pattern", "email", "emailDomainValidator", "password", "strongPasswordValidator", "confirmPassword", "acceptTerms", "requiredTrue", "validators", "passwordMatchValidator", "valueChanges", "pipe", "subscribe", "control", "value", "hasUpperCase", "test", "hasLowerCase", "hasNumeric", "hasSpecialChar", "valid", "blockedDomains", "domain", "split", "includes", "blockedDomain", "form", "passwordMismatch", "register", "invalid", "markFormGroupTouched", "showSuccess", "setTimeout", "navigate", "error", "handleRegistrationError", "status", "addError", "showError", "Object", "keys", "controls", "for<PERSON>ach", "key", "<PERSON><PERSON><PERSON><PERSON>ched", "redirectLogin", "showTerms", "showInfo", "showPrivacy", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "i4", "MessageService", "i5", "ErrorService", "i6", "EnhancedMessageService", "_2", "selectors", "decls", "vars", "consts", "template", "RegisterComponent_Template", "rf", "ctx", "ɵɵlistener", "RegisterComponent_Template_form_ngSubmit_13_listener", "RegisterComponent_small_18_Template", "RegisterComponent_small_23_Template", "RegisterComponent_small_28_Template", "RegisterComponent_small_33_Template", "RegisterComponent_small_38_Template", "RegisterComponent_small_43_Template", "RegisterComponent_Template_a_click_49_listener", "RegisterComponent_Template_a_click_52_listener", "RegisterComponent_small_54_Template", "RegisterComponent_div_55_Template", "RegisterComponent_Template_a_click_60_listener", "ɵɵclassProp", "touched", "tmp_3_0", "tmp_4_0", "tmp_5_0", "tmp_6_0", "tmp_7_0", "tmp_8_0", "tmp_9_0", "tmp_12_0", "tmp_13_0", "tmp_16_0", "tmp_18_0"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\auth\\register\\register.component.ts", "D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\auth\\register\\register.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { MessageService } from 'primeng/api';\r\nimport { AuthService } from '../../../service/auth.service';\r\nimport { ErrorService } from '../../../service/error.service';\r\nimport { EnhancedMessageService } from '../../../service/enhanced-message.service';\r\nimport { Subject } from 'rxjs';\r\nimport { takeUntil } from 'rxjs/operators';\r\n\r\n@Component({\r\n  selector: 'app-register',\r\n  templateUrl: './register.component.html',\r\n  styleUrls: ['./register.component.scss']\r\n})\r\nexport class RegisterComponent implements OnInit, OnDestroy {\r\n  registerForm!: FormGroup;\r\n  errorMessage = '';\r\n  isLoading = false;\r\n  private destroy$ = new Subject<void>();\r\n\r\n  constructor(\r\n    private fb: FormBuilder,\r\n    private authService: AuthService,\r\n    private router: Router,\r\n    private messageService: MessageService,\r\n    private errorService: ErrorService,\r\n    private enhancedMessage: EnhancedMessageService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.initializeForm();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.destroy$.next();\r\n    this.destroy$.complete();\r\n  }\r\n\r\n  private initializeForm() {\r\n    this.registerForm = this.fb.group({\r\n      firstName: ['', [\r\n        Validators.required,\r\n        Validators.minLength(2),\r\n        Validators.maxLength(50)\r\n      ]],\r\n      lastName: ['', [\r\n        Validators.required,\r\n        Validators.minLength(2),\r\n        Validators.maxLength(50)\r\n      ]],\r\n      username: ['', [\r\n        Validators.required,\r\n        Validators.minLength(3),\r\n        Validators.maxLength(20),\r\n        Validators.pattern(/^[a-zA-Z0-9_]+$/) // Only alphanumeric and underscore\r\n      ]],\r\n      email: ['', [\r\n        Validators.required,\r\n        Validators.email,\r\n        this.emailDomainValidator\r\n      ]],\r\n      password: ['', [\r\n        Validators.required,\r\n        Validators.minLength(8),\r\n        this.strongPasswordValidator\r\n      ]],\r\n      confirmPassword: ['', [\r\n        Validators.required\r\n      ]],\r\n      acceptTerms: [false, [\r\n        Validators.requiredTrue\r\n      ]]\r\n    }, {\r\n      validators: this.passwordMatchValidator\r\n    });\r\n\r\n    // Clear error message when form values change\r\n    this.registerForm.valueChanges\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe(() => {\r\n        if (this.errorMessage) {\r\n          this.errorMessage = '';\r\n        }\r\n      });\r\n  }\r\n\r\n  // Custom validator for strong password\r\n  private strongPasswordValidator(control: AbstractControl): ValidationErrors | null {\r\n    const value = control.value;\r\n    if (!value) return null;\r\n\r\n    const hasUpperCase = /[A-Z]/.test(value);\r\n    const hasLowerCase = /[a-z]/.test(value);\r\n    const hasNumeric = /[0-9]/.test(value);\r\n    const hasSpecialChar = /[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(value);\r\n\r\n    const valid = hasUpperCase && hasLowerCase && hasNumeric && hasSpecialChar;\r\n    return valid ? null : { pattern: true };\r\n  }\r\n\r\n  // Custom validator for email domain\r\n  private emailDomainValidator(control: AbstractControl): ValidationErrors | null {\r\n    const value = control.value;\r\n    if (!value) return null;\r\n\r\n    const blockedDomains = ['tempmail.com', '10minutemail.com', 'guerrillamail.com'];\r\n    const domain = value.split('@')[1];\r\n\r\n    return blockedDomains.includes(domain) ? { blockedDomain: true } : null;\r\n  }\r\n\r\n  // Custom validator for password match\r\n  private passwordMatchValidator(form: AbstractControl): ValidationErrors | null {\r\n    const password = form.get('password')?.value;\r\n    const confirmPassword = form.get('confirmPassword')?.value;\r\n\r\n    if (!password || !confirmPassword) return null;\r\n\r\n    return password === confirmPassword ? null : { passwordMismatch: true };\r\n  }\r\n\r\n  register() {\r\n    if (this.registerForm.invalid) {\r\n      this.markFormGroupTouched();\r\n      return;\r\n    }\r\n\r\n    this.isLoading = true;\r\n    this.errorMessage = '';\r\n\r\n    const { firstName, lastName, username, email, password } = this.registerForm.value;\r\n\r\n    this.authService.register(username, password, email, firstName, lastName)\r\n      .pipe(takeUntil(this.destroy$))\r\n      .subscribe({\r\n        next: () => {\r\n          this.isLoading = false;\r\n          this.enhancedMessage.showSuccess(\r\n            'Account Created!',\r\n            'Your account has been created successfully. Please sign in.'\r\n          );\r\n\r\n          // Small delay for better UX\r\n          setTimeout(() => {\r\n            this.router.navigate(['/auth/login']);\r\n          }, 1500);\r\n        },\r\n        error: (error) => {\r\n          this.isLoading = false;\r\n          this.handleRegistrationError(error);\r\n        }\r\n      });\r\n  }\r\n\r\n  private handleRegistrationError(error: any) {\r\n    let errorMessage = 'Registration failed. Please try again.';\r\n\r\n    if (error.status === 409) {\r\n      errorMessage = 'Username or email already exists. Please choose different credentials.';\r\n    } else if (error.status === 400) {\r\n      errorMessage = 'Invalid registration data. Please check your information and try again.';\r\n    } else if (error.status === 422) {\r\n      errorMessage = 'Validation failed. Please ensure all fields are filled correctly.';\r\n    } else if (error.status === 0) {\r\n      errorMessage = 'Unable to connect to server. Please check your internet connection.';\r\n    } else if (error.status >= 500) {\r\n      errorMessage = 'Server error. Please try again later.';\r\n    }\r\n\r\n    this.errorMessage = errorMessage;\r\n\r\n    // Also add to global error service\r\n    this.errorService.addError(error);\r\n\r\n    // Show enhanced toast message\r\n    this.enhancedMessage.showError('Registration Failed', errorMessage);\r\n  }\r\n\r\n  private markFormGroupTouched() {\r\n    Object.keys(this.registerForm.controls).forEach(key => {\r\n      const control = this.registerForm.get(key);\r\n      control?.markAsTouched();\r\n    });\r\n  }\r\n\r\n  redirectLogin() {\r\n    this.router.navigate(['/auth/login']);\r\n  }\r\n\r\n  showTerms() {\r\n    this.enhancedMessage.showInfo(\r\n      'Terms of Service',\r\n      'Terms of Service page will be available soon.'\r\n    );\r\n  }\r\n\r\n  showPrivacy() {\r\n    this.enhancedMessage.showInfo(\r\n      'Privacy Policy',\r\n      'Privacy Policy page will be available soon.'\r\n    );\r\n  }\r\n}\r\n", "<div class=\"auth-container\">\r\n    <div class=\"auth-background\">\r\n        <div class=\"bg-element bg-element-1\"></div>\r\n        <div class=\"bg-element bg-element-2\"></div>\r\n        <div class=\"bg-element bg-element-3\"></div>\r\n    </div>\r\n\r\n    <div class=\"auth-content\">\r\n        <div class=\"auth-card\">\r\n            <!-- Header -->\r\n            <div class=\"text-center mb-4\">\r\n                <img src=\"assets/layout/images/logo-dark.png\" alt=\"SolarKapital\" height=\"50\" class=\"mb-3\">\r\n                <h2 class=\"text-900 text-2xl font-semibold mb-2\">Create Account</h2>\r\n                <p class=\"text-600\">Join us to manage your solar energy systems</p>\r\n            </div>\r\n\r\n                <!-- Register Form -->\r\n                <form [formGroup]=\"registerForm\" (ngSubmit)=\"register()\">\r\n                    <!-- First Name Field -->\r\n                    <div class=\"mb-4\">\r\n                        <label for=\"firstName\" class=\"block text-900 font-medium mb-2\">First Name</label>\r\n                        <input id=\"firstName\"\r\n                               type=\"text\"\r\n                               formControlName=\"firstName\"\r\n                               placeholder=\"Enter your first name\"\r\n                               pInputText\r\n                               class=\"w-full p-3\"\r\n                               [class.p-invalid]=\"registerForm.get('firstName')?.invalid && registerForm.get('firstName')?.touched\">\r\n                        <small class=\"p-error\" *ngIf=\"registerForm.get('firstName')?.invalid && registerForm.get('firstName')?.touched\">\r\n                            <span *ngIf=\"registerForm.get('firstName')?.errors?.['required']\">First name is required</span>\r\n                            <span *ngIf=\"registerForm.get('firstName')?.errors?.['minlength']\">First name must be at least 2 characters</span>\r\n                            <span *ngIf=\"registerForm.get('firstName')?.errors?.['maxlength']\">First name cannot exceed 50 characters</span>\r\n                        </small>\r\n                    </div>\r\n\r\n                    <!-- Last Name Field -->\r\n                    <div class=\"mb-4\">\r\n                        <label for=\"lastName\" class=\"block text-900 font-medium mb-2\">Last Name</label>\r\n                        <input id=\"lastName\"\r\n                               type=\"text\"\r\n                               formControlName=\"lastName\"\r\n                               placeholder=\"Enter your last name\"\r\n                               pInputText\r\n                               class=\"w-full p-3\"\r\n                               [class.p-invalid]=\"registerForm.get('lastName')?.invalid && registerForm.get('lastName')?.touched\">\r\n                        <small class=\"p-error\" *ngIf=\"registerForm.get('lastName')?.invalid && registerForm.get('lastName')?.touched\">\r\n                            <span *ngIf=\"registerForm.get('lastName')?.errors?.['required']\">Last name is required</span>\r\n                            <span *ngIf=\"registerForm.get('lastName')?.errors?.['minlength']\">Last name must be at least 2 characters</span>\r\n                            <span *ngIf=\"registerForm.get('lastName')?.errors?.['maxlength']\">Last name cannot exceed 50 characters</span>\r\n                        </small>\r\n                    </div>\r\n\r\n                    <!-- Username Field -->\r\n                    <div class=\"mb-4\">\r\n                        <label for=\"username\" class=\"block text-900 font-medium mb-2\">Username</label>\r\n                        <input id=\"username\"\r\n                               type=\"text\"\r\n                               formControlName=\"username\"\r\n                               placeholder=\"Choose a username\"\r\n                               pInputText\r\n                               class=\"w-full p-3\"\r\n                               [class.p-invalid]=\"registerForm.get('username')?.invalid && registerForm.get('username')?.touched\">\r\n                        <small class=\"p-error\" *ngIf=\"registerForm.get('username')?.invalid && registerForm.get('username')?.touched\">\r\n                            <span *ngIf=\"registerForm.get('username')?.errors?.['required']\">Username is required</span>\r\n                            <span *ngIf=\"registerForm.get('username')?.errors?.['minlength']\">Username must be at least 3 characters</span>\r\n                            <span *ngIf=\"registerForm.get('username')?.errors?.['pattern']\">Username can only contain letters, numbers, and underscores</span>\r\n                        </small>\r\n                    </div>\r\n\r\n                    <!-- Email Field -->\r\n                    <div class=\"mb-4\">\r\n                        <label for=\"email\" class=\"block text-900 font-medium mb-2\">Email Address</label>\r\n                        <input id=\"email\"\r\n                               type=\"email\"\r\n                               formControlName=\"email\"\r\n                               placeholder=\"Enter your email address\"\r\n                               pInputText\r\n                               class=\"w-full p-3\"\r\n                               [class.p-invalid]=\"registerForm.get('email')?.invalid && registerForm.get('email')?.touched\">\r\n                        <small class=\"p-error\" *ngIf=\"registerForm.get('email')?.invalid && registerForm.get('email')?.touched\">\r\n                            <span *ngIf=\"registerForm.get('email')?.errors?.['required']\">Email is required</span>\r\n                            <span *ngIf=\"registerForm.get('email')?.errors?.['email']\">Please enter a valid email address</span>\r\n                        </small>\r\n                    </div>\r\n\r\n                    <!-- Password Field -->\r\n                    <div class=\"mb-4\">\r\n                        <label for=\"password\" class=\"block text-900 font-medium mb-2\">Password</label>\r\n                        <p-password id=\"password\"\r\n                                   formControlName=\"password\"\r\n                                   placeholder=\"Create a strong password\"\r\n                                   [toggleMask]=\"true\"\r\n                                   [feedback]=\"true\"\r\n                                   styleClass=\"w-full\"\r\n                                   inputStyleClass=\"w-full p-3\"\r\n                                   [class.p-invalid]=\"registerForm.get('password')?.invalid && registerForm.get('password')?.touched\">\r\n                        </p-password>\r\n                        <small class=\"p-error\" *ngIf=\"registerForm.get('password')?.invalid && registerForm.get('password')?.touched\">\r\n                            <span *ngIf=\"registerForm.get('password')?.errors?.['required']\">Password is required</span>\r\n                            <span *ngIf=\"registerForm.get('password')?.errors?.['minlength']\">Password must be at least 8 characters</span>\r\n                            <span *ngIf=\"registerForm.get('password')?.errors?.['pattern']\">Password must contain uppercase, lowercase, number and special character</span>\r\n                        </small>\r\n                    </div>\r\n\r\n                    <!-- Confirm Password Field -->\r\n                    <div class=\"mb-4\">\r\n                        <label for=\"confirmPassword\" class=\"block text-900 font-medium mb-2\">Confirm Password</label>\r\n                        <p-password id=\"confirmPassword\"\r\n                                   formControlName=\"confirmPassword\"\r\n                                   placeholder=\"Confirm your password\"\r\n                                   [toggleMask]=\"true\"\r\n                                   [feedback]=\"false\"\r\n                                   styleClass=\"w-full\"\r\n                                   inputStyleClass=\"w-full p-3\"\r\n                                   [class.p-invalid]=\"registerForm.get('confirmPassword')?.invalid && registerForm.get('confirmPassword')?.touched\">\r\n                        </p-password>\r\n                        <small class=\"p-error\" *ngIf=\"(registerForm.get('confirmPassword')?.invalid && registerForm.get('confirmPassword')?.touched) || (registerForm.errors?.['passwordMismatch'] && registerForm.get('confirmPassword')?.touched)\">\r\n                            <span *ngIf=\"registerForm.get('confirmPassword')?.errors?.['required']\">Please confirm your password</span>\r\n                            <span *ngIf=\"registerForm.errors?.['passwordMismatch']\">Passwords do not match</span>\r\n                        </small>\r\n                    </div>\r\n\r\n                    <!-- Terms and Conditions -->\r\n                    <div class=\"mb-4\">\r\n                        <div class=\"flex align-items-center\">\r\n                            <p-checkbox id=\"acceptTerms\"\r\n                                       formControlName=\"acceptTerms\"\r\n                                       [binary]=\"true\"\r\n                                       styleClass=\"mr-2\">\r\n                            </p-checkbox>\r\n                            <label for=\"acceptTerms\" class=\"text-900\">\r\n                                I agree to the\r\n                                <a class=\"font-medium no-underline cursor-pointer\"\r\n                                   style=\"color: var(--primary-color)\"\r\n                                   (click)=\"showTerms()\">Terms of Service</a>\r\n                                and\r\n                                <a class=\"font-medium no-underline cursor-pointer\"\r\n                                   style=\"color: var(--primary-color)\"\r\n                                   (click)=\"showPrivacy()\">Privacy Policy</a>\r\n                            </label>\r\n                        </div>\r\n                        <small class=\"p-error\" *ngIf=\"registerForm.get('acceptTerms')?.invalid && registerForm.get('acceptTerms')?.touched\">\r\n                            You must accept the terms and conditions\r\n                        </small>\r\n                    </div>\r\n\r\n                    <!-- Error Message -->\r\n                    <div class=\"p-message p-message-error mb-4\" *ngIf=\"errorMessage\">\r\n                        <div class=\"p-message-wrapper\">\r\n                            <span class=\"p-message-icon pi pi-exclamation-triangle\"></span>\r\n                            <span class=\"p-message-text\">{{ errorMessage }}</span>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- Submit Button -->\r\n                    <button type=\"submit\"\r\n                            pButton\r\n                            pRipple\r\n                            label=\"Create Account\"\r\n                            class=\"w-full p-3 text-xl mb-4\"\r\n                            [disabled]=\"registerForm.invalid || isLoading\"\r\n                            [loading]=\"isLoading\">\r\n                    </button>\r\n\r\n                    <!-- Login Link -->\r\n                    <div class=\"text-center\">\r\n                        <span class=\"text-600\">Already have an account? </span>\r\n                        <a class=\"font-medium no-underline cursor-pointer\"\r\n                           style=\"color: var(--primary-color)\"\r\n                           (click)=\"redirectLogin()\">\r\n                            Sign in here\r\n                        </a>\r\n                    </div>\r\n                </form>\r\n        </div>\r\n    </div>\r\n</div>\r\n"], "mappings": "AACA,SAAiCA,UAAU,QAA2C,gBAAgB;AAMtG,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;;;;;;;;;;;;;;;ICqBdC,EAAA,CAAAC,cAAA,WAAkE;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC/FH,EAAA,CAAAC,cAAA,WAAmE;IAAAD,EAAA,CAAAE,MAAA,+CAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAClHH,EAAA,CAAAC,cAAA,WAAmE;IAAAD,EAAA,CAAAE,MAAA,6CAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAHpHH,EAAA,CAAAC,cAAA,gBAAgH;IAC5GD,EAAA,CAAAI,UAAA,IAAAC,0CAAA,mBAA+F,IAAAC,0CAAA,uBAAAC,0CAAA;IAGnGP,EAAA,CAAAG,YAAA,EAAQ;;;;;;;IAHGH,EAAA,CAAAQ,SAAA,GAAyD;IAAzDR,EAAA,CAAAS,UAAA,UAAAC,OAAA,GAAAC,MAAA,CAAAC,YAAA,CAAAC,GAAA,gCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAyD;IACzDd,EAAA,CAAAQ,SAAA,GAA0D;IAA1DR,EAAA,CAAAS,UAAA,UAAAM,OAAA,GAAAJ,MAAA,CAAAC,YAAA,CAAAC,GAAA,gCAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,cAA0D;IAC1Dd,EAAA,CAAAQ,SAAA,GAA0D;IAA1DR,EAAA,CAAAS,UAAA,UAAAO,OAAA,GAAAL,MAAA,CAAAC,YAAA,CAAAC,GAAA,gCAAAG,OAAA,CAAAF,MAAA,kBAAAE,OAAA,CAAAF,MAAA,cAA0D;;;;;IAejEd,EAAA,CAAAC,cAAA,WAAiE;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC7FH,EAAA,CAAAC,cAAA,WAAkE;IAAAD,EAAA,CAAAE,MAAA,8CAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAChHH,EAAA,CAAAC,cAAA,WAAkE;IAAAD,EAAA,CAAAE,MAAA,4CAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAHlHH,EAAA,CAAAC,cAAA,gBAA8G;IAC1GD,EAAA,CAAAI,UAAA,IAAAa,0CAAA,mBAA6F,IAAAC,0CAAA,uBAAAC,0CAAA;IAGjGnB,EAAA,CAAAG,YAAA,EAAQ;;;;;;;IAHGH,EAAA,CAAAQ,SAAA,GAAwD;IAAxDR,EAAA,CAAAS,UAAA,UAAAC,OAAA,GAAAU,MAAA,CAAAR,YAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAwD;IACxDd,EAAA,CAAAQ,SAAA,GAAyD;IAAzDR,EAAA,CAAAS,UAAA,UAAAM,OAAA,GAAAK,MAAA,CAAAR,YAAA,CAAAC,GAAA,+BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,cAAyD;IACzDd,EAAA,CAAAQ,SAAA,GAAyD;IAAzDR,EAAA,CAAAS,UAAA,UAAAO,OAAA,GAAAI,MAAA,CAAAR,YAAA,CAAAC,GAAA,+BAAAG,OAAA,CAAAF,MAAA,kBAAAE,OAAA,CAAAF,MAAA,cAAyD;;;;;IAehEd,EAAA,CAAAC,cAAA,WAAiE;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC5FH,EAAA,CAAAC,cAAA,WAAkE;IAAAD,EAAA,CAAAE,MAAA,6CAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC/GH,EAAA,CAAAC,cAAA,WAAgE;IAAAD,EAAA,CAAAE,MAAA,kEAA2D;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAHtIH,EAAA,CAAAC,cAAA,gBAA8G;IAC1GD,EAAA,CAAAI,UAAA,IAAAiB,0CAAA,mBAA4F,IAAAC,0CAAA,uBAAAC,0CAAA;IAGhGvB,EAAA,CAAAG,YAAA,EAAQ;;;;;;;IAHGH,EAAA,CAAAQ,SAAA,GAAwD;IAAxDR,EAAA,CAAAS,UAAA,UAAAC,OAAA,GAAAc,MAAA,CAAAZ,YAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAwD;IACxDd,EAAA,CAAAQ,SAAA,GAAyD;IAAzDR,EAAA,CAAAS,UAAA,UAAAM,OAAA,GAAAS,MAAA,CAAAZ,YAAA,CAAAC,GAAA,+BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,cAAyD;IACzDd,EAAA,CAAAQ,SAAA,GAAuD;IAAvDR,EAAA,CAAAS,UAAA,UAAAO,OAAA,GAAAQ,MAAA,CAAAZ,YAAA,CAAAC,GAAA,+BAAAG,OAAA,CAAAF,MAAA,kBAAAE,OAAA,CAAAF,MAAA,YAAuD;;;;;IAe9Dd,EAAA,CAAAC,cAAA,WAA8D;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACtFH,EAAA,CAAAC,cAAA,WAA2D;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFxGH,EAAA,CAAAC,cAAA,gBAAwG;IACpGD,EAAA,CAAAI,UAAA,IAAAqB,0CAAA,mBAAsF,IAAAC,0CAAA;IAE1F1B,EAAA,CAAAG,YAAA,EAAQ;;;;;;IAFGH,EAAA,CAAAQ,SAAA,GAAqD;IAArDR,EAAA,CAAAS,UAAA,UAAAC,OAAA,GAAAiB,MAAA,CAAAf,YAAA,CAAAC,GAAA,4BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAqD;IACrDd,EAAA,CAAAQ,SAAA,GAAkD;IAAlDR,EAAA,CAAAS,UAAA,UAAAM,OAAA,GAAAY,MAAA,CAAAf,YAAA,CAAAC,GAAA,4BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,UAAkD;;;;;IAiBzDd,EAAA,CAAAC,cAAA,WAAiE;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC5FH,EAAA,CAAAC,cAAA,WAAkE;IAAAD,EAAA,CAAAE,MAAA,6CAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC/GH,EAAA,CAAAC,cAAA,WAAgE;IAAAD,EAAA,CAAAE,MAAA,+EAAwE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAHnJH,EAAA,CAAAC,cAAA,gBAA8G;IAC1GD,EAAA,CAAAI,UAAA,IAAAwB,0CAAA,mBAA4F,IAAAC,0CAAA,uBAAAC,0CAAA;IAGhG9B,EAAA,CAAAG,YAAA,EAAQ;;;;;;;IAHGH,EAAA,CAAAQ,SAAA,GAAwD;IAAxDR,EAAA,CAAAS,UAAA,UAAAC,OAAA,GAAAqB,MAAA,CAAAnB,YAAA,CAAAC,GAAA,+BAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAAwD;IACxDd,EAAA,CAAAQ,SAAA,GAAyD;IAAzDR,EAAA,CAAAS,UAAA,UAAAM,OAAA,GAAAgB,MAAA,CAAAnB,YAAA,CAAAC,GAAA,+BAAAE,OAAA,CAAAD,MAAA,kBAAAC,OAAA,CAAAD,MAAA,cAAyD;IACzDd,EAAA,CAAAQ,SAAA,GAAuD;IAAvDR,EAAA,CAAAS,UAAA,UAAAO,OAAA,GAAAe,MAAA,CAAAnB,YAAA,CAAAC,GAAA,+BAAAG,OAAA,CAAAF,MAAA,kBAAAE,OAAA,CAAAF,MAAA,YAAuD;;;;;IAiB9Dd,EAAA,CAAAC,cAAA,WAAwE;IAAAD,EAAA,CAAAE,MAAA,mCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC3GH,EAAA,CAAAC,cAAA,WAAwD;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFzFH,EAAA,CAAAC,cAAA,gBAA6N;IACzND,EAAA,CAAAI,UAAA,IAAA4B,0CAAA,mBAA2G,IAAAC,0CAAA;IAE/GjC,EAAA,CAAAG,YAAA,EAAQ;;;;;IAFGH,EAAA,CAAAQ,SAAA,GAA+D;IAA/DR,EAAA,CAAAS,UAAA,UAAAC,OAAA,GAAAwB,MAAA,CAAAtB,YAAA,CAAAC,GAAA,sCAAAH,OAAA,CAAAI,MAAA,kBAAAJ,OAAA,CAAAI,MAAA,aAA+D;IAC/Dd,EAAA,CAAAQ,SAAA,GAA+C;IAA/CR,EAAA,CAAAS,UAAA,SAAAyB,MAAA,CAAAtB,YAAA,CAAAE,MAAA,kBAAAoB,MAAA,CAAAtB,YAAA,CAAAE,MAAA,qBAA+C;;;;;IAuB1Dd,EAAA,CAAAC,cAAA,gBAAoH;IAChHD,EAAA,CAAAE,MAAA,iDACJ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAIZH,EAAA,CAAAC,cAAA,cAAiE;IAEzDD,EAAA,CAAAmC,SAAA,eAA+D;IAC/DnC,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAzBH,EAAA,CAAAQ,SAAA,GAAkB;IAAlBR,EAAA,CAAAoC,iBAAA,CAAAC,MAAA,CAAAC,YAAA,CAAkB;;;ADvI3E,OAAM,MAAOC,iBAAiB;EAM5BC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc,EACdC,cAA8B,EAC9BC,YAA0B,EAC1BC,eAAuC;IALvC,KAAAL,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,eAAe,GAAfA,eAAe;IAVzB,KAAAR,YAAY,GAAG,EAAE;IACjB,KAAAS,SAAS,GAAG,KAAK;IACT,KAAAC,QAAQ,GAAG,IAAIlD,OAAO,EAAQ;EASnC;EAEHmD,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACH,QAAQ,CAACI,IAAI,EAAE;IACpB,IAAI,CAACJ,QAAQ,CAACK,QAAQ,EAAE;EAC1B;EAEQH,cAAcA,CAAA;IACpB,IAAI,CAACtC,YAAY,GAAG,IAAI,CAAC6B,EAAE,CAACa,KAAK,CAAC;MAChCC,SAAS,EAAE,CAAC,EAAE,EAAE,CACd1D,UAAU,CAAC2D,QAAQ,EACnB3D,UAAU,CAAC4D,SAAS,CAAC,CAAC,CAAC,EACvB5D,UAAU,CAAC6D,SAAS,CAAC,EAAE,CAAC,CACzB,CAAC;MACFC,QAAQ,EAAE,CAAC,EAAE,EAAE,CACb9D,UAAU,CAAC2D,QAAQ,EACnB3D,UAAU,CAAC4D,SAAS,CAAC,CAAC,CAAC,EACvB5D,UAAU,CAAC6D,SAAS,CAAC,EAAE,CAAC,CACzB,CAAC;MACFE,QAAQ,EAAE,CAAC,EAAE,EAAE,CACb/D,UAAU,CAAC2D,QAAQ,EACnB3D,UAAU,CAAC4D,SAAS,CAAC,CAAC,CAAC,EACvB5D,UAAU,CAAC6D,SAAS,CAAC,EAAE,CAAC,EACxB7D,UAAU,CAACgE,OAAO,CAAC,iBAAiB,CAAC,CAAC;MAAA,CACvC,CAAC;;MACFC,KAAK,EAAE,CAAC,EAAE,EAAE,CACVjE,UAAU,CAAC2D,QAAQ,EACnB3D,UAAU,CAACiE,KAAK,EAChB,IAAI,CAACC,oBAAoB,CAC1B,CAAC;MACFC,QAAQ,EAAE,CAAC,EAAE,EAAE,CACbnE,UAAU,CAAC2D,QAAQ,EACnB3D,UAAU,CAAC4D,SAAS,CAAC,CAAC,CAAC,EACvB,IAAI,CAACQ,uBAAuB,CAC7B,CAAC;MACFC,eAAe,EAAE,CAAC,EAAE,EAAE,CACpBrE,UAAU,CAAC2D,QAAQ,CACpB,CAAC;MACFW,WAAW,EAAE,CAAC,KAAK,EAAE,CACnBtE,UAAU,CAACuE,YAAY,CACxB;KACF,EAAE;MACDC,UAAU,EAAE,IAAI,CAACC;KAClB,CAAC;IAEF;IACA,IAAI,CAAC1D,YAAY,CAAC2D,YAAY,CAC3BC,IAAI,CAACzE,SAAS,CAAC,IAAI,CAACiD,QAAQ,CAAC,CAAC,CAC9ByB,SAAS,CAAC,MAAK;MACd,IAAI,IAAI,CAACnC,YAAY,EAAE;QACrB,IAAI,CAACA,YAAY,GAAG,EAAE;;IAE1B,CAAC,CAAC;EACN;EAEA;EACQ2B,uBAAuBA,CAACS,OAAwB;IACtD,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK;IAC3B,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;IAEvB,MAAMC,YAAY,GAAG,OAAO,CAACC,IAAI,CAACF,KAAK,CAAC;IACxC,MAAMG,YAAY,GAAG,OAAO,CAACD,IAAI,CAACF,KAAK,CAAC;IACxC,MAAMI,UAAU,GAAG,OAAO,CAACF,IAAI,CAACF,KAAK,CAAC;IACtC,MAAMK,cAAc,GAAG,uCAAuC,CAACH,IAAI,CAACF,KAAK,CAAC;IAE1E,MAAMM,KAAK,GAAGL,YAAY,IAAIE,YAAY,IAAIC,UAAU,IAAIC,cAAc;IAC1E,OAAOC,KAAK,GAAG,IAAI,GAAG;MAAEpB,OAAO,EAAE;IAAI,CAAE;EACzC;EAEA;EACQE,oBAAoBA,CAACW,OAAwB;IACnD,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK;IAC3B,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;IAEvB,MAAMO,cAAc,GAAG,CAAC,cAAc,EAAE,kBAAkB,EAAE,mBAAmB,CAAC;IAChF,MAAMC,MAAM,GAAGR,KAAK,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAElC,OAAOF,cAAc,CAACG,QAAQ,CAACF,MAAM,CAAC,GAAG;MAAEG,aAAa,EAAE;IAAI,CAAE,GAAG,IAAI;EACzE;EAEA;EACQhB,sBAAsBA,CAACiB,IAAqB;IAClD,MAAMvB,QAAQ,GAAGuB,IAAI,CAAC1E,GAAG,CAAC,UAAU,CAAC,EAAE8D,KAAK;IAC5C,MAAMT,eAAe,GAAGqB,IAAI,CAAC1E,GAAG,CAAC,iBAAiB,CAAC,EAAE8D,KAAK;IAE1D,IAAI,CAACX,QAAQ,IAAI,CAACE,eAAe,EAAE,OAAO,IAAI;IAE9C,OAAOF,QAAQ,KAAKE,eAAe,GAAG,IAAI,GAAG;MAAEsB,gBAAgB,EAAE;IAAI,CAAE;EACzE;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC7E,YAAY,CAAC8E,OAAO,EAAE;MAC7B,IAAI,CAACC,oBAAoB,EAAE;MAC3B;;IAGF,IAAI,CAAC5C,SAAS,GAAG,IAAI;IACrB,IAAI,CAACT,YAAY,GAAG,EAAE;IAEtB,MAAM;MAAEiB,SAAS;MAAEI,QAAQ;MAAEC,QAAQ;MAAEE,KAAK;MAAEE;IAAQ,CAAE,GAAG,IAAI,CAACpD,YAAY,CAAC+D,KAAK;IAElF,IAAI,CAACjC,WAAW,CAAC+C,QAAQ,CAAC7B,QAAQ,EAAEI,QAAQ,EAAEF,KAAK,EAAEP,SAAS,EAAEI,QAAQ,CAAC,CACtEa,IAAI,CAACzE,SAAS,CAAC,IAAI,CAACiD,QAAQ,CAAC,CAAC,CAC9ByB,SAAS,CAAC;MACTrB,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACL,SAAS,GAAG,KAAK;QACtB,IAAI,CAACD,eAAe,CAAC8C,WAAW,CAC9B,kBAAkB,EAClB,6DAA6D,CAC9D;QAED;QACAC,UAAU,CAAC,MAAK;UACd,IAAI,CAAClD,MAAM,CAACmD,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;QACvC,CAAC,EAAE,IAAI,CAAC;MACV,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAChD,SAAS,GAAG,KAAK;QACtB,IAAI,CAACiD,uBAAuB,CAACD,KAAK,CAAC;MACrC;KACD,CAAC;EACN;EAEQC,uBAAuBA,CAACD,KAAU;IACxC,IAAIzD,YAAY,GAAG,wCAAwC;IAE3D,IAAIyD,KAAK,CAACE,MAAM,KAAK,GAAG,EAAE;MACxB3D,YAAY,GAAG,wEAAwE;KACxF,MAAM,IAAIyD,KAAK,CAACE,MAAM,KAAK,GAAG,EAAE;MAC/B3D,YAAY,GAAG,yEAAyE;KACzF,MAAM,IAAIyD,KAAK,CAACE,MAAM,KAAK,GAAG,EAAE;MAC/B3D,YAAY,GAAG,mEAAmE;KACnF,MAAM,IAAIyD,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;MAC7B3D,YAAY,GAAG,qEAAqE;KACrF,MAAM,IAAIyD,KAAK,CAACE,MAAM,IAAI,GAAG,EAAE;MAC9B3D,YAAY,GAAG,uCAAuC;;IAGxD,IAAI,CAACA,YAAY,GAAGA,YAAY;IAEhC;IACA,IAAI,CAACO,YAAY,CAACqD,QAAQ,CAACH,KAAK,CAAC;IAEjC;IACA,IAAI,CAACjD,eAAe,CAACqD,SAAS,CAAC,qBAAqB,EAAE7D,YAAY,CAAC;EACrE;EAEQqD,oBAAoBA,CAAA;IAC1BS,MAAM,CAACC,IAAI,CAAC,IAAI,CAACzF,YAAY,CAAC0F,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACpD,MAAM9B,OAAO,GAAG,IAAI,CAAC9D,YAAY,CAACC,GAAG,CAAC2F,GAAG,CAAC;MAC1C9B,OAAO,EAAE+B,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAC,aAAaA,CAAA;IACX,IAAI,CAAC/D,MAAM,CAACmD,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAEAa,SAASA,CAAA;IACP,IAAI,CAAC7D,eAAe,CAAC8D,QAAQ,CAC3B,kBAAkB,EAClB,+CAA+C,CAChD;EACH;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC/D,eAAe,CAAC8D,QAAQ,CAC3B,gBAAgB,EAChB,6CAA6C,CAC9C;EACH;EAAC,QAAAE,CAAA,G;qBA3LUvE,iBAAiB,EAAAvC,EAAA,CAAA+G,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjH,EAAA,CAAA+G,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAnH,EAAA,CAAA+G,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAArH,EAAA,CAAA+G,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAvH,EAAA,CAAA+G,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAAzH,EAAA,CAAA+G,iBAAA,CAAAW,EAAA,CAAAC,sBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAjBrF,iBAAiB;IAAAsF,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCf9BnI,EAAA,CAAAC,cAAA,aAA4B;QAEpBD,EAAA,CAAAmC,SAAA,aAA2C;QAG/CnC,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,aAA0B;QAIdD,EAAA,CAAAmC,SAAA,aAA0F;QAC1FnC,EAAA,CAAAC,cAAA,YAAiD;QAAAD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACpEH,EAAA,CAAAC,cAAA,aAAoB;QAAAD,EAAA,CAAAE,MAAA,mDAA2C;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAInEH,EAAA,CAAAC,cAAA,gBAAyD;QAAxBD,EAAA,CAAAqI,UAAA,sBAAAC,qDAAA;UAAA,OAAYF,GAAA,CAAA3C,QAAA,EAAU;QAAA,EAAC;QAEpDzF,EAAA,CAAAC,cAAA,eAAkB;QACiDD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACjFH,EAAA,CAAAmC,SAAA,iBAM4G;QAC5GnC,EAAA,CAAAI,UAAA,KAAAmI,mCAAA,oBAIQ;QACZvI,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAC,cAAA,eAAkB;QACgDD,EAAA,CAAAE,MAAA,iBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC/EH,EAAA,CAAAmC,SAAA,iBAM0G;QAC1GnC,EAAA,CAAAI,UAAA,KAAAoI,mCAAA,oBAIQ;QACZxI,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAC,cAAA,eAAkB;QACgDD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC9EH,EAAA,CAAAmC,SAAA,iBAM0G;QAC1GnC,EAAA,CAAAI,UAAA,KAAAqI,mCAAA,oBAIQ;QACZzI,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAC,cAAA,eAAkB;QAC6CD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAChFH,EAAA,CAAAmC,SAAA,iBAMoG;QACpGnC,EAAA,CAAAI,UAAA,KAAAsI,mCAAA,oBAGQ;QACZ1I,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAC,cAAA,eAAkB;QACgDD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC9EH,EAAA,CAAAmC,SAAA,sBAQa;QACbnC,EAAA,CAAAI,UAAA,KAAAuI,mCAAA,oBAIQ;QACZ3I,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAC,cAAA,eAAkB;QACuDD,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC7FH,EAAA,CAAAmC,SAAA,sBAQa;QACbnC,EAAA,CAAAI,UAAA,KAAAwI,mCAAA,oBAGQ;QACZ5I,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAC,cAAA,eAAkB;QAEVD,EAAA,CAAAmC,SAAA,sBAIa;QACbnC,EAAA,CAAAC,cAAA,iBAA0C;QACtCD,EAAA,CAAAE,MAAA,wBACA;QAAAF,EAAA,CAAAC,cAAA,aAEyB;QAAtBD,EAAA,CAAAqI,UAAA,mBAAAQ,+CAAA;UAAA,OAAST,GAAA,CAAAzB,SAAA,EAAW;QAAA,EAAC;QAAC3G,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAC7CH,EAAA,CAAAE,MAAA,aACA;QAAAF,EAAA,CAAAC,cAAA,aAE2B;QAAxBD,EAAA,CAAAqI,UAAA,mBAAAS,+CAAA;UAAA,OAASV,GAAA,CAAAvB,WAAA,EAAa;QAAA,EAAC;QAAC7G,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAGrDH,EAAA,CAAAI,UAAA,KAAA2I,mCAAA,oBAEQ;QACZ/I,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAI,UAAA,KAAA4I,iCAAA,kBAKM;QAGNhJ,EAAA,CAAAmC,SAAA,kBAOS;QAGTnC,EAAA,CAAAC,cAAA,eAAyB;QACED,EAAA,CAAAE,MAAA,iCAAyB;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACvDH,EAAA,CAAAC,cAAA,aAE6B;QAA1BD,EAAA,CAAAqI,UAAA,mBAAAY,+CAAA;UAAA,OAASb,GAAA,CAAA1B,aAAA,EAAe;QAAA,EAAC;QACxB1G,EAAA,CAAAE,MAAA,sBACJ;QAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;;;;;;;;;;;QA1JNH,EAAA,CAAAQ,SAAA,IAA0B;QAA1BR,EAAA,CAAAS,UAAA,cAAA2H,GAAA,CAAAxH,YAAA,CAA0B;QAUjBZ,EAAA,CAAAQ,SAAA,GAAoG;QAApGR,EAAA,CAAAkJ,WAAA,gBAAAnI,OAAA,GAAAqH,GAAA,CAAAxH,YAAA,CAAAC,GAAA,gCAAAE,OAAA,CAAA2E,OAAA,OAAA3E,OAAA,GAAAqH,GAAA,CAAAxH,YAAA,CAAAC,GAAA,gCAAAE,OAAA,CAAAoI,OAAA,EAAoG;QACnFnJ,EAAA,CAAAQ,SAAA,GAAsF;QAAtFR,EAAA,CAAAS,UAAA,WAAAO,OAAA,GAAAoH,GAAA,CAAAxH,YAAA,CAAAC,GAAA,gCAAAG,OAAA,CAAA0E,OAAA,OAAA1E,OAAA,GAAAoH,GAAA,CAAAxH,YAAA,CAAAC,GAAA,gCAAAG,OAAA,CAAAmI,OAAA,EAAsF;QAgBvGnJ,EAAA,CAAAQ,SAAA,GAAkG;QAAlGR,EAAA,CAAAkJ,WAAA,gBAAAE,OAAA,GAAAhB,GAAA,CAAAxH,YAAA,CAAAC,GAAA,+BAAAuI,OAAA,CAAA1D,OAAA,OAAA0D,OAAA,GAAAhB,GAAA,CAAAxH,YAAA,CAAAC,GAAA,+BAAAuI,OAAA,CAAAD,OAAA,EAAkG;QACjFnJ,EAAA,CAAAQ,SAAA,GAAoF;QAApFR,EAAA,CAAAS,UAAA,WAAA4I,OAAA,GAAAjB,GAAA,CAAAxH,YAAA,CAAAC,GAAA,+BAAAwI,OAAA,CAAA3D,OAAA,OAAA2D,OAAA,GAAAjB,GAAA,CAAAxH,YAAA,CAAAC,GAAA,+BAAAwI,OAAA,CAAAF,OAAA,EAAoF;QAgBrGnJ,EAAA,CAAAQ,SAAA,GAAkG;QAAlGR,EAAA,CAAAkJ,WAAA,gBAAAI,OAAA,GAAAlB,GAAA,CAAAxH,YAAA,CAAAC,GAAA,+BAAAyI,OAAA,CAAA5D,OAAA,OAAA4D,OAAA,GAAAlB,GAAA,CAAAxH,YAAA,CAAAC,GAAA,+BAAAyI,OAAA,CAAAH,OAAA,EAAkG;QACjFnJ,EAAA,CAAAQ,SAAA,GAAoF;QAApFR,EAAA,CAAAS,UAAA,WAAA8I,OAAA,GAAAnB,GAAA,CAAAxH,YAAA,CAAAC,GAAA,+BAAA0I,OAAA,CAAA7D,OAAA,OAAA6D,OAAA,GAAAnB,GAAA,CAAAxH,YAAA,CAAAC,GAAA,+BAAA0I,OAAA,CAAAJ,OAAA,EAAoF;QAgBrGnJ,EAAA,CAAAQ,SAAA,GAA4F;QAA5FR,EAAA,CAAAkJ,WAAA,gBAAAM,OAAA,GAAApB,GAAA,CAAAxH,YAAA,CAAAC,GAAA,4BAAA2I,OAAA,CAAA9D,OAAA,OAAA8D,OAAA,GAAApB,GAAA,CAAAxH,YAAA,CAAAC,GAAA,4BAAA2I,OAAA,CAAAL,OAAA,EAA4F;QAC3EnJ,EAAA,CAAAQ,SAAA,GAA8E;QAA9ER,EAAA,CAAAS,UAAA,WAAAgJ,OAAA,GAAArB,GAAA,CAAAxH,YAAA,CAAAC,GAAA,4BAAA4I,OAAA,CAAA/D,OAAA,OAAA+D,OAAA,GAAArB,GAAA,CAAAxH,YAAA,CAAAC,GAAA,4BAAA4I,OAAA,CAAAN,OAAA,EAA8E;QAgB3FnJ,EAAA,CAAAQ,SAAA,GAAkG;QAAlGR,EAAA,CAAAkJ,WAAA,gBAAAQ,OAAA,GAAAtB,GAAA,CAAAxH,YAAA,CAAAC,GAAA,+BAAA6I,OAAA,CAAAhE,OAAA,OAAAgE,OAAA,GAAAtB,GAAA,CAAAxH,YAAA,CAAAC,GAAA,+BAAA6I,OAAA,CAAAP,OAAA,EAAkG;QAJlGnJ,EAAA,CAAAS,UAAA,oBAAmB;QAMNT,EAAA,CAAAQ,SAAA,GAAoF;QAApFR,EAAA,CAAAS,UAAA,WAAAkJ,QAAA,GAAAvB,GAAA,CAAAxH,YAAA,CAAAC,GAAA,+BAAA8I,QAAA,CAAAjE,OAAA,OAAAiE,QAAA,GAAAvB,GAAA,CAAAxH,YAAA,CAAAC,GAAA,+BAAA8I,QAAA,CAAAR,OAAA,EAAoF;QAiBjGnJ,EAAA,CAAAQ,SAAA,GAAgH;QAAhHR,EAAA,CAAAkJ,WAAA,gBAAAU,QAAA,GAAAxB,GAAA,CAAAxH,YAAA,CAAAC,GAAA,sCAAA+I,QAAA,CAAAlE,OAAA,OAAAkE,QAAA,GAAAxB,GAAA,CAAAxH,YAAA,CAAAC,GAAA,sCAAA+I,QAAA,CAAAT,OAAA,EAAgH;QAJhHnJ,EAAA,CAAAS,UAAA,oBAAmB;QAMNT,EAAA,CAAAQ,SAAA,GAAmM;QAAnMR,EAAA,CAAAS,UAAA,WAAAoJ,QAAA,GAAAzB,GAAA,CAAAxH,YAAA,CAAAC,GAAA,sCAAAgJ,QAAA,CAAAnE,OAAA,OAAAmE,QAAA,GAAAzB,GAAA,CAAAxH,YAAA,CAAAC,GAAA,sCAAAgJ,QAAA,CAAAV,OAAA,MAAAf,GAAA,CAAAxH,YAAA,CAAAE,MAAA,kBAAAsH,GAAA,CAAAxH,YAAA,CAAAE,MAAA,2BAAA+I,QAAA,GAAAzB,GAAA,CAAAxH,YAAA,CAAAC,GAAA,sCAAAgJ,QAAA,CAAAV,OAAA,EAAmM;QAW5MnJ,EAAA,CAAAQ,SAAA,GAAe;QAAfR,EAAA,CAAAS,UAAA,gBAAe;QAcNT,EAAA,CAAAQ,SAAA,GAA0F;QAA1FR,EAAA,CAAAS,UAAA,WAAAqJ,QAAA,GAAA1B,GAAA,CAAAxH,YAAA,CAAAC,GAAA,kCAAAiJ,QAAA,CAAApE,OAAA,OAAAoE,QAAA,GAAA1B,GAAA,CAAAxH,YAAA,CAAAC,GAAA,kCAAAiJ,QAAA,CAAAX,OAAA,EAA0F;QAMzEnJ,EAAA,CAAAQ,SAAA,GAAkB;QAAlBR,EAAA,CAAAS,UAAA,SAAA2H,GAAA,CAAA9F,YAAA,CAAkB;QAavDtC,EAAA,CAAAQ,SAAA,GAA8C;QAA9CR,EAAA,CAAAS,UAAA,aAAA2H,GAAA,CAAAxH,YAAA,CAAA8E,OAAA,IAAA0C,GAAA,CAAArF,SAAA,CAA8C,YAAAqF,GAAA,CAAArF,SAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}