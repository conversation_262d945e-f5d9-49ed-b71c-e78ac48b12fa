{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../service/stations.service\";\nexport class AddStationComponent {\n  constructor(stationsService) {\n    this.stationsService = stationsService;\n  }\n  ngOnInit() {}\n  ngOnDestroy() {}\n  static #_ = this.ɵfac = function AddStationComponent_Factory(t) {\n    return new (t || AddStationComponent)(i0.ɵɵdirectiveInject(i1.StationsService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AddStationComponent,\n    selectors: [[\"ng-component\"]],\n    decls: 1,\n    vars: 0,\n    template: function AddStationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtext(0, \"adsads\");\n      }\n    },\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["AddStationComponent", "constructor", "stationsService", "ngOnInit", "ngOnDestroy", "_", "i0", "ɵɵdirectiveInject", "i1", "StationsService", "_2", "selectors", "decls", "vars", "template", "AddStationComponent_Template", "rf", "ctx", "ɵɵtext"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\stations\\add.component.ts", "D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\stations\\add.component.html"], "sourcesContent": ["import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { Product } from '../../api/product';\r\nimport { ProductService } from '../../service/product.service';\r\nimport { Subscription, debounceTime } from 'rxjs';\r\n//import { LayoutService } from 'src/app/layout/service/app.layout.service';\r\nimport { SelectItem } from 'primeng/api';\r\nimport { DataView } from 'primeng/dataview';\r\nimport { StationsService } from '../../service/stations.service';\r\nimport { Station } from '../../api/station';\r\nimport { InvertPower } from '../../api/invertpower';\r\n\r\n@Component({\r\n    templateUrl: './add.component.html',\r\n})\r\nexport class AddStationComponent implements OnInit, OnDestroy {\r\n\r\n    \r\n\r\n\r\n    constructor(private stationsService: StationsService\r\n        ) {\r\n        \r\n    }\r\n\r\n    ngOnInit() {\r\n        \r\n    }\r\n\r\n    ngOnDestroy() {\r\n        \r\n    }\r\n}\r\n", "adsads"], "mappings": ";;AAeA,OAAM,MAAOA,mBAAmB;EAK5BC,YAAoBC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;EAGnC;EAEAC,QAAQA,CAAA,GAER;EAEAC,WAAWA,CAAA,GAEX;EAAC,QAAAC,CAAA,G;qBAhBQL,mBAAmB,EAAAM,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,eAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAnBV,mBAAmB;IAAAW,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCfhCV,EAAA,CAAAY,MAAA,aAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}