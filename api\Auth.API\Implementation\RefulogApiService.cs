﻿using Auth.API.AuroraModels;
using Auth.API.HuaweiModels;
using Auth.API.Models;
using Auth.API.RefulogModels;
using Auth.API.Repository.Models;
using Auth.API.Types;
using AutoMapper;
using OpenQA.Selenium.BiDi.Modules.Network;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using System.Xml.Serialization;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace Auth.API.Implementation
{
    public class RefulogApiService
    {
        private readonly HttpClient _httpClient;
        private readonly RefulogTokenService _refulogTokenService;
        private readonly IMapper _mapper;

        private readonly Dictionary<string, string> refulogSearchTypeMapping = new Dictionary<string, string>
        {
            {"monthly", "month"},
            {"yearly", "year" },
            {"lifetime","year" }
        };

        public RefulogApiService(HttpClient httpClient, RefulogTokenService refulogTokenService, IMapper mapper)
        {
            _httpClient = httpClient;
            _refulogTokenService = refulogTokenService;
            _mapper = mapper;
        }

        public async Task<List<Station>> GetStationsAsync(string username, string userPassword, string baseUrl)
        {
            var password = await _refulogTokenService.ProduceUserCredentials(username, userPassword);
            // Εκτελούμε το GET request
            HttpResponseMessage response = await _httpClient.GetAsync(baseUrl + $"/user/{username}/plants/search/paged?password={password}&page=0&pagesize=10");

            if (response.IsSuccessStatusCode)
            {
                string xmlResponse = await response.Content.ReadAsStringAsync();

                // Κάνουμε deserialize το XML σε RefulogGetStationsResponse object
                var serializer = new XmlSerializer(typeof(RefulogGetStationsResponse));
                using var reader = new StringReader(xmlResponse);
                var apiResponse = (RefulogGetStationsResponse)serializer.Deserialize(reader);

                var stations = _mapper.Map<List<RefulogStation>, List<Station>>(apiResponse.Plants);

                return stations;
            }
            else
            {
                throw new Exception($"Failed to get data: {response.StatusCode}");
            }
        }

        public async Task<List<Device>> GetStationDevicesAsync(string stationId, string username, string userPassword, string baseUrl)
        {
            var password = await _refulogTokenService.ProduceUserCredentials(username, userPassword);
            // Εκτελούμε το POST request
            HttpResponseMessage response = await _httpClient.GetAsync(baseUrl + $"/plant/{stationId}/inverters/search/paged?username={username}&password={password}&page=0&pagesize=10");

            if (response.IsSuccessStatusCode)
            {
                string xmlResponse = await response.Content.ReadAsStringAsync();

                // Κάνουμε deserialize το XML σε RefulogGetDevicesResponse object
                var serializer = new XmlSerializer(typeof(RefulogGetDevicesResponse));
                using var reader = new StringReader(xmlResponse);
                var apiResponse = (RefulogGetDevicesResponse)serializer.Deserialize(reader);
                //TODO filter devices only inverters??
                var stations = _mapper.Map<List<RefulogDevice>, List<Device>>(apiResponse.Inverters);

                return stations;
            }
            else
            {
                throw new Exception($"Failed to get data: {response.StatusCode}");
            }

        }


        public async Task<SumData> GetSumData(string stationId, string username, string userPassword, string baseUrl)
        {
            var password = await _refulogTokenService.ProduceUserCredentials(username, userPassword);
            // Εκτελούμε το GET request
            HttpResponseMessage response = await _httpClient.GetAsync(baseUrl + $"/user/{username}/plants/search/paged?password={password}&page=0&pagesize=10");

            if (response.IsSuccessStatusCode)
            {
                string xmlResponse = await response.Content.ReadAsStringAsync();

                // Κάνουμε deserialize το XML σε RefulogGetStationsResponse object
                var serializer = new XmlSerializer(typeof(RefulogGetStationsResponse));
                using var reader = new StringReader(xmlResponse);
                var apiResponse = (RefulogGetStationsResponse)serializer.Deserialize(reader);

                var stationData = apiResponse.Plants.FirstOrDefault(p => p.ID.ToString() == stationId);


                return new SumData()
                {
                    TotalPower = stationData.TotalYield,
                    DayPower = stationData.DailyYield,
                };
            }
            else
            {
                throw new Exception($"Failed to get data: {response.StatusCode}");
            }
        }


        //public async Task<RealTimeData> GetRealTimeData(GetDataRequest req, string username, string password, string baseUrl)
        //{
        //    //var todayMidnightUtc = DateTime.UtcNow.Date; // Σήμερα στις 00:00 UTC
        //    //var startTime = new DateTimeOffset(todayMidnightUtc).ToUnixTimeSeconds();

        //    //var tomorrowMidnightUtc = DateTime.UtcNow.Date.AddDays(1);
        //    //var endTime = new DateTimeOffset(tomorrowMidnightUtc).ToUnixTimeSeconds();

        //    //// Εκτελούμε το GET request
        //    //HttpResponseMessage response = await _httpClient.GetAsync(baseUrl + $"plant/{req.StationId}/data/day/ACPower?username={username}&password={password}&start={startTime}&end={endTime}");

        //    //if (response.IsSuccessStatusCode)
        //    //{
        //    //    string xmlResponse = await response.Content.ReadAsStringAsync();

        //    //    // Κάνουμε deserialize το XML σε RefulogGetStationsResponse object
        //    //    var serializer = new XmlSerializer(typeof(RefulogRealTimeDataResponse));
        //    //    using var reader = new StringReader(xmlResponse);
        //    //    var apiResponse = (RefulogRealTimeDataResponse)serializer.Deserialize(reader);

        //    //    var data = _mapper.Map<List<RefulogDevice>, List<Device>>(apiResponse.ChartDataList);

        //    //}
        //    //else
        //    //{
        //    //    throw new Exception($"Failed to get data: {response.StatusCode}");
        //    //}
        //}


        public async Task<HistoricTimeData> GetHistoricTimeData(GetDataWithTimestampRequest req, string username, string userPassword, string baseUrl)
        {
            var password = await _refulogTokenService.ProduceUserCredentials(username, userPassword);

            // Ensure we use UTC for Unix timestamp conversion (server timezone independent)
            var startTime = new DateTimeOffset(req.StartDateTime.ToUniversalTime(), TimeSpan.Zero).ToUnixTimeSeconds();
            var endTime = new DateTimeOffset(req.EndDateTime.ToUniversalTime(), TimeSpan.Zero).ToUnixTimeSeconds();

            var type = req.SearchType != null ? refulogSearchTypeMapping[req.SearchType] : "day";
            var dataResponseType = req.SearchType != null ? "DailyYield" : "ACPower";
            List<RealTimeData> responsedeviceData = new List<RealTimeData>();

            if (req.Separated)
            {
                List<RealTimeData> deviceData = new List<RealTimeData>();
                foreach (string deviceId in req.DevIds.Split(","))
                {
                    var stationDevices = await GetStationDevicesAsync(req.StationId, username, userPassword, baseUrl);

                    HttpResponseMessage response = await _httpClient.GetAsync(baseUrl + $"/inverter/{deviceId}/data/{type}/{dataResponseType}?username={username}&password={password}&start={startTime}&end={endTime}");

                    if (response.IsSuccessStatusCode)
                    {
                        string xmlResponse = await response.Content.ReadAsStringAsync();

                        // Κάνουμε deserialize το XML σε RefulogGetStationsResponse object
                        var serializer = new XmlSerializer(typeof(RefulogRealTimeDataResponse));
                        using var reader = new StringReader(xmlResponse);
                        var apiResponse = (RefulogRealTimeDataResponse)serializer.Deserialize(reader);

                        var data = _mapper.Map<List<RefulogChartData>, List<RealTimeData>>(apiResponse.ChartDataList);
                        data.ForEach(d => d.name = stationDevices.FirstOrDefault(s => s.Id == deviceId).Name);

                        
                        deviceData.AddRange(data);
                    }
                    else
                    {
                        throw new Exception($"Failed to get data: {response.StatusCode}");
                    }
                }


                if (req.SearchType is null)
                {
                    responsedeviceData = deviceData;
                }
                else
                {
                    switch (req.SearchType.ToLower())
                    {
                        case "monthly":
                            responsedeviceData = deviceData
                                .GroupBy(d => d.dateTime.Date) // Πρώτο grouping ανά ημερομηνία
                                .SelectMany(dateGroup => dateGroup
                                    .GroupBy(d => d.name) // Δεύτερο grouping ανά name
                                    .Select(nameGroup => new RealTimeData
                                    {
                                        name = $"{nameGroup.Key}",
                                        activePower = nameGroup.Sum(d => d.activePower),
                                        totalInputPower = nameGroup.Sum(d => d.totalInputPower),
                                        dateTime = dateGroup.Key, // Η κοινή ημερομηνία της πρώτης ομάδας
                                        dateDescription = dateGroup.Key.ToString("dd/MM")
                                    })
                                )
                                .ToList();
                            break;
                        case "yearly":
                            responsedeviceData = deviceData
                                .GroupBy(d => new { d.dateTime.Year, d.dateTime.Month }) // Πρώτο GroupBy (Έτος, Μήνας)
                                .SelectMany(monthGroup => monthGroup
                                    .GroupBy(d => d.name) // Δεύτερο GroupBy (Name)
                                    .Select(nameGroup => new RealTimeData
                                    {
                                        name = $"{nameGroup.Key}",
                                        activePower = nameGroup.Sum(d => d.activePower),
                                        totalInputPower = nameGroup.Sum(d => d.totalInputPower),
                                        dateTime = new DateTime(monthGroup.Key.Year, monthGroup.Key.Month, 1),
                                        dateDescription = new DateTime(monthGroup.Key.Year, monthGroup.Key.Month, 1).ToString("MMMM", CultureInfo.InvariantCulture)
                                    })
                                )
                                .ToList();

                            break;
                        case "lifetime":
                            responsedeviceData = deviceData
                                .GroupBy(d => d.dateTime.Year)  // Πρώτο grouping με το Year
                                .Select(g => new
                                {
                                    Year = g.Key,
                                    GroupedByName = g
                                        .GroupBy(d => d.name)  // Δεύτερο grouping με το name
                                        .Select(gg => new RealTimeData
                                        {
                                            name = $"{gg.Key}",
                                            activePower = gg.Sum(d => d.activePower),
                                            totalInputPower = gg.Sum(d => d.totalInputPower),
                                            dateTime = new DateTime(g.Key, 1, 1),
                                            dateDescription = new DateTime(g.Key, 1, 1).ToString("MMMM yyyy", CultureInfo.InvariantCulture)
                                        })
                                        .ToList()
                                })
                                .SelectMany(yearGroup => yearGroup.GroupedByName)  // Εξέταση όλων των grouped δεδομένων για να επιστρέψουν ένα επίπεδο
                                .ToList();
                            break;
                        default:
                            throw new ArgumentException("Invalid search type");

                    }
                }


                //responsedeviceData = deviceData.ToList();

            }
            else {
                // Εκτελούμε το GET request
                HttpResponseMessage response = await _httpClient.GetAsync(baseUrl + $"/plant/{req.StationId}/data/{type}/{dataResponseType}?username={username}&password={password}&start={startTime}&end={endTime}");

                if (response.IsSuccessStatusCode)
                {
                    string xmlResponse = await response.Content.ReadAsStringAsync();

                    // Κάνουμε deserialize το XML σε RefulogGetStationsResponse object
                    var serializer = new XmlSerializer(typeof(RefulogRealTimeDataResponse));
                    using var reader = new StringReader(xmlResponse);
                    var apiResponse = (RefulogRealTimeDataResponse)serializer.Deserialize(reader);

                    var r = _mapper.Map<RefulogChartData>(apiResponse.ChartDataList.First());

                    responsedeviceData = _mapper.Map<List<RefulogChartData>, List<RealTimeData>>(apiResponse.ChartDataList);
                    responsedeviceData.ForEach(d =>
                    {
                        if (req.SearchType != null)
                        {
                            switch (req.SearchType)
                            {
                                case "monthly":
                                    d.name = d.dateTime.ToString("dd/MM");
                                    d.dateDescription = d.dateTime.ToString("dd/MM");
                                    break;
                                case "yearly":
                                    d.name = CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(d.dateTime.Month);
                                    d.dateDescription = CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(d.dateTime.Month);
                                    break;
                                case "lifetime":
                                    d.name = d.dateTime.Year.ToString();
                                    d.dateDescription = d.dateTime.Year.ToString();
                                    break;
                            }
                        }
                        else
                        {
                            d.name = d.dateTime.Date.ToString();
                        }
                    });
                }
                else
                {
                    throw new Exception($"Failed to get data: {response.StatusCode}");
                }
            }

            var allData = new HistoricTimeData()
            {
                Data = responsedeviceData,
                Sum = responsedeviceData.Sum(s => s.activePower)
            };

            return allData;
        }
    }
}
