{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { StyleClassModule } from 'primeng/styleclass';\nimport { DividerModule } from 'primeng/divider';\nimport { ChartModule } from 'primeng/chart';\nimport { PanelModule } from 'primeng/panel';\nimport { ButtonModule } from 'primeng/button';\nimport { ProvidersRoutingModule } from './providers-routing.module';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { FontAwesomeModule } from '@fortawesome/angular-fontawesome';\nimport { fas } from '@fortawesome/free-solid-svg-icons';\nimport { CardModule } from 'primeng/card';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { PasswordModule } from 'primeng/password';\nimport { BadgeModule } from 'primeng/badge';\nimport { TagModule } from 'primeng/tag';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { MessageModule } from 'primeng/message';\nimport { MessagesModule } from 'primeng/messages';\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\nimport { SkeletonModule } from 'primeng/skeleton';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@fortawesome/angular-fontawesome\";\nexport let ProvidersModule = /*#__PURE__*/(() => {\n  class ProvidersModule {\n    constructor(library) {\n      //library.addIcons(faSmile);\n      library.addIconPacks(fas);\n    }\n    static #_ = this.ɵfac = function ProvidersModule_Factory(t) {\n      return new (t || ProvidersModule)(i0.ɵɵinject(i1.FaIconLibrary));\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ProvidersModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ProvidersRoutingModule, DividerModule, StyleClassModule, ChartModule, PanelModule, ButtonModule, ReactiveFormsModule, BreadcrumbModule, FontAwesomeModule, CardModule, InputTextModule, DropdownModule, PasswordModule, BadgeModule, TagModule, TooltipModule, ConfirmDialogModule, MessageModule, MessagesModule, ProgressSpinnerModule, SkeletonModule, RippleModule]\n    });\n  }\n  return ProvidersModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}