{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./service/app.layout.service\";\nexport class AppFooterComponent {\n  constructor(layoutService) {\n    this.layoutService = layoutService;\n  }\n  static #_ = this.ɵfac = function AppFooterComponent_Factory(t) {\n    return new (t || AppFooterComponent)(i0.ɵɵdirectiveInject(i1.LayoutService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppFooterComponent,\n    selectors: [[\"app-footer\"]],\n    decls: 5,\n    vars: 1,\n    consts: [[1, \"layout-footer\"], [\"alt\", \"Logo\", \"height\", \"20\", 1, \"mr-2\", 3, \"src\"], [1, \"font-medium\", \"ml-2\"]],\n    template: function AppFooterComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵelement(1, \"img\", 1);\n        i0.ɵɵtext(2, \" by \");\n        i0.ɵɵelementStart(3, \"span\", 2);\n        i0.ɵɵtext(4, \"PrimeNG\");\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵpropertyInterpolate1(\"src\", \"assets/layout/images/\", ctx.layoutService.config().colorScheme === \"light\" ? \"logo-dark\" : \"logo-white\", \".svg\", i0.ɵɵsanitizeUrl);\n      }\n    },\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["AppFooterComponent", "constructor", "layoutService", "_", "i0", "ɵɵdirectiveInject", "i1", "LayoutService", "_2", "selectors", "decls", "vars", "consts", "template", "AppFooterComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵpropertyInterpolate1", "config", "colorScheme", "ɵɵsanitizeUrl"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\layout\\app.footer.component.ts", "D:\\Greg\\solarkapital\\ui\\src\\app\\layout\\app.footer.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { LayoutService } from \"./service/app.layout.service\";\n\n@Component({\n    selector: 'app-footer',\n    templateUrl: './app.footer.component.html'\n})\nexport class AppFooterComponent {\n    constructor(public layoutService: LayoutService) { }\n}\n", "<div class=\"layout-footer\">\n    <img src=\"assets/layout/images/{{layoutService.config().colorScheme === 'light' ? 'logo-dark' : 'logo-white'}}.svg\" alt=\"Logo\" height=\"20\" class=\"mr-2\"/>\n    by\n    <span class=\"font-medium ml-2\">PrimeNG</span>\n</div>\n"], "mappings": ";;AAOA,OAAM,MAAOA,kBAAkB;EAC3BC,YAAmBC,aAA4B;IAA5B,KAAAA,aAAa,GAAbA,aAAa;EAAmB;EAAC,QAAAC,CAAA,G;qBAD3CH,kBAAkB,EAAAI,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlBR,kBAAkB;IAAAS,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCP/BX,EAAA,CAAAa,cAAA,aAA2B;QACvBb,EAAA,CAAAc,SAAA,aAAyJ;QACzJd,EAAA,CAAAe,MAAA,WACA;QAAAf,EAAA,CAAAa,cAAA,cAA+B;QAAAb,EAAA,CAAAe,MAAA,cAAO;QAAAf,EAAA,CAAAgB,YAAA,EAAO;;;QAFxChB,EAAA,CAAAiB,SAAA,GAA8G;QAA9GjB,EAAA,CAAAkB,sBAAA,iCAAAN,GAAA,CAAAd,aAAA,CAAAqB,MAAA,GAAAC,WAAA,mDAAApB,EAAA,CAAAqB,aAAA,CAA8G"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}