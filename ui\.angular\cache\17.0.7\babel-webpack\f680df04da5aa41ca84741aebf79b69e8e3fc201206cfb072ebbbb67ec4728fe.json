{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MediaDemoComponent } from './mediademo.component';\nimport { MediaDemoRoutingModule } from './mediademo-routing.module';\nimport { ButtonModule } from 'primeng/button';\nimport { ImageModule } from 'primeng/image';\nimport { GalleriaModule } from 'primeng/galleria';\nimport { CarouselModule } from 'primeng/carousel';\nimport * as i0 from \"@angular/core\";\nexport class MediaDemoModule {\n  static #_ = this.ɵfac = function MediaDemoModule_Factory(t) {\n    return new (t || MediaDemoModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: MediaDemoModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, MediaDemoRoutingModule, ButtonModule, ImageModule, GalleriaModule, CarouselModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(MediaDemoModule, {\n    declarations: [MediaDemoComponent],\n    imports: [CommonModule, MediaDemoRoutingModule, ButtonModule, ImageModule, GalleriaModule, CarouselModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "MediaDemoComponent", "MediaDemoRoutingModule", "ButtonModule", "ImageModule", "GalleriaModule", "CarouselModule", "MediaDemoModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["D:\\Greg\\sakai-ng-master\\sakai-ng-master\\src\\app\\demo\\components\\uikit\\media\\mediademo.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MediaDemoComponent } from './mediademo.component';\nimport { MediaDemoRoutingModule } from './mediademo-routing.module';\nimport { ButtonModule } from 'primeng/button';\nimport { ImageModule } from 'primeng/image';\nimport { GalleriaModule } from 'primeng/galleria';\nimport { CarouselModule } from 'primeng/carousel';\n\n@NgModule({\n\timports: [\n\t\tCommonModule,\n\t\tMediaDemoRoutingModule,\n\t\tButtonModule,\n\t\tImageModule,\n\t\tGalleriaModule,\n\t\tCarouselModule\n\t],\n\tdeclarations: [MediaDemoComponent]\n})\nexport class MediaDemoModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;;AAajD,OAAM,MAAOC,eAAe;EAAA,QAAAC,CAAA,G;qBAAfD,eAAe;EAAA;EAAA,QAAAE,EAAA,G;UAAfF;EAAe;EAAA,QAAAG,EAAA,G;cAT1BV,YAAY,EACZE,sBAAsB,EACtBC,YAAY,EACZC,WAAW,EACXC,cAAc,EACdC,cAAc;EAAA;;;2EAIHC,eAAe;IAAAI,YAAA,GAFZV,kBAAkB;IAAAW,OAAA,GAPhCZ,YAAY,EACZE,sBAAsB,EACtBC,YAAY,EACZC,WAAW,EACXC,cAAc,EACdC,cAAc;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}