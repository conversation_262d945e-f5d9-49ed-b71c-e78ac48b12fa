{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let PersonalComponent = /*#__PURE__*/(() => {\n  class PersonalComponent {\n    constructor() {}\n    static #_ = this.ɵfac = function PersonalComponent_Factory(t) {\n      return new (t || PersonalComponent)();\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PersonalComponent,\n      selectors: [[\"ng-component\"]],\n      decls: 4,\n      vars: 0,\n      consts: [[1, \"flex\", \"flex-column\", \"align-items-center\", \"justify-content-center\", \"h-full\", \"py-5\", \"px-3\"], [1, \"pi\", \"pi-fw\", \"pi-user\", \"mr-2\", \"text-2xl\"], [1, \"mt-5\", \"text-center\", \"text-lg\"]],\n      template: function PersonalComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"i\", 1);\n          i0.ɵɵelementStart(2, \"p\", 2);\n          i0.ɵɵtext(3, \"Personal Component Content via Child Route\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      encapsulation: 2\n    });\n  }\n  return PersonalComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}