import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { User, UpdateUserRequest, UpdateUserResponse } from '../api/user';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private apiUrl = environment.solarApi + 'api/user';

  constructor(private http: HttpClient) {}

  private getAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('auth_token');
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }

  getUser(): Observable<User> {
    return this.http.get<User>(this.apiUrl, { 
      headers: this.getAuthHeaders() 
    });
  }

  updateUser(updateRequest: UpdateUserRequest): Observable<UpdateUserResponse> {
    return this.http.put<UpdateUserResponse>(this.apiUrl, updateRequest, {
      headers: this.getAuthHeaders()
    });
  }
}
