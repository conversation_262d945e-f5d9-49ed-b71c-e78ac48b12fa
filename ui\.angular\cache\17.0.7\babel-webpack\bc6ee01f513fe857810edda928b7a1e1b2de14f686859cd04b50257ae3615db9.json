{"ast": null, "code": "import { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../service/error.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/dialog\";\nimport * as i4 from \"primeng/api\";\nfunction ErrorModalComponent_div_0_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"i\", 17);\n    i0.ɵɵelementStart(2, \"span\", 18);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const error_r1 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.getErrorTitle(error_r1));\n  }\n}\nfunction ErrorModalComponent_div_0_ng_template_19_p_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 22);\n    i0.ɵɵlistener(\"click\", function ErrorModalComponent_div_0_ng_template_19_p_button_2_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const error_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.retry(error_r1));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ErrorModalComponent_div_0_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"p-button\", 20);\n    i0.ɵɵlistener(\"click\", function ErrorModalComponent_div_0_ng_template_19_Template_p_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const error_r1 = i0.ɵɵnextContext().$implicit;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.close(error_r1));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, ErrorModalComponent_div_0_ng_template_19_p_button_2_Template, 1, 0, \"p-button\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const error_r1 = i0.ɵɵnextContext().$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.canRetry(error_r1));\n  }\n}\nconst _c0 = () => ({\n  width: \"90vw\",\n  maxWidth: \"500px\"\n});\nconst _c1 = () => ({\n  \"960px\": \"95vw\"\n});\nfunction ErrorModalComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"p-dialog\", 2);\n    i0.ɵɵlistener(\"visibleChange\", function ErrorModalComponent_div_0_Template_p_dialog_visibleChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.visible = $event);\n    })(\"onHide\", function ErrorModalComponent_div_0_Template_p_dialog_onHide_1_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.onDialogHide());\n    });\n    i0.ɵɵtemplate(2, ErrorModalComponent_div_0_ng_template_2_Template, 4, 1, \"ng-template\", 3);\n    i0.ɵɵelementStart(3, \"div\", 4)(4, \"div\", 5)(5, \"div\", 6);\n    i0.ɵɵelement(6, \"i\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 8)(8, \"h4\", 9);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 10);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 11)(13, \"div\", 12);\n    i0.ɵɵelement(14, \"i\", 13);\n    i0.ɵɵelementStart(15, \"span\", 14);\n    i0.ɵɵtext(16, \"Error Details\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"p\", 15);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(19, ErrorModalComponent_div_0_ng_template_19_Template, 3, 1, \"ng-template\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const error_r1 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(12, _c0));\n    i0.ɵɵproperty(\"header\", ctx_r0.getErrorTitle(error_r1))(\"visible\", ctx_r0.visible)(\"modal\", true)(\"closable\", true)(\"draggable\", false)(\"resizable\", false)(\"breakpoints\", i0.ɵɵpureFunction0(13, _c1));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.getErrorType(error_r1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getErrorMessage(error_r1));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(error_r1);\n  }\n}\nexport let ErrorModalComponent = /*#__PURE__*/(() => {\n  class ErrorModalComponent {\n    constructor(errorService) {\n      this.errorService = errorService;\n      this.visible = false;\n      this.errors = [];\n      this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n      this.errorService.errors$.pipe(takeUntil(this.destroy$)).subscribe(messages => {\n        this.errors = messages;\n        this.visible = messages.length > 0;\n      });\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    close(error) {\n      this.errorService.removeError(error);\n    }\n    onDialogHide() {\n      // Όταν κλείνει το dialog, καθαρίζουμε όλα τα errors\n      this.errorService.clearErrors();\n    }\n    // Helper method to convert error to string\n    getErrorString(error) {\n      if (typeof error === 'string') {\n        return error;\n      } else if (error?.message) {\n        return error.message;\n      } else if (error?.error?.message) {\n        return error.error.message;\n      } else if (error?.statusText) {\n        return error.statusText;\n      } else if (error?.status) {\n        return `HTTP Error ${error.status}`;\n      } else {\n        return JSON.stringify(error) || 'Unknown error';\n      }\n    }\n    getErrorTitle(error) {\n      const errorStr = this.getErrorString(error);\n      const status = error?.status || 0;\n      if (status === 400 || errorStr.includes('400') || errorStr.includes('Bad Request')) {\n        return 'Invalid Request';\n      } else if (status === 401 || errorStr.includes('401') || errorStr.includes('Unauthorized')) {\n        return 'Authentication Required';\n      } else if (status === 403 || errorStr.includes('403') || errorStr.includes('Forbidden')) {\n        return 'Access Denied';\n      } else if (status === 404 || errorStr.includes('404') || errorStr.includes('Not Found')) {\n        return 'Resource Not Found';\n      } else if (status === 500 || errorStr.includes('500') || errorStr.includes('Internal Server Error')) {\n        return 'Server Error';\n      } else if (errorStr.includes('Network') || errorStr.includes('Connection') || status === 0) {\n        return 'Connection Error';\n      }\n      return 'Application Error';\n    }\n    getErrorType(error) {\n      const errorStr = this.getErrorString(error);\n      const status = error?.status || 0;\n      if (status === 400 || errorStr.includes('400')) {\n        return 'Validation Error';\n      } else if (status === 401 || errorStr.includes('401')) {\n        return 'Authentication Error';\n      } else if (status === 403 || errorStr.includes('403')) {\n        return 'Permission Error';\n      } else if (status === 404 || errorStr.includes('404')) {\n        return 'Not Found Error';\n      } else if (status === 500 || errorStr.includes('500')) {\n        return 'Server Error';\n      } else if (errorStr.includes('Network') || status === 0) {\n        return 'Network Error';\n      }\n      return 'Unknown Error';\n    }\n    getErrorMessage(error) {\n      const errorStr = this.getErrorString(error);\n      const status = error?.status || 0;\n      if (status === 400 || errorStr.includes('400')) {\n        return 'The request contains invalid data. Please check your input and try again.';\n      } else if (status === 401 || errorStr.includes('401')) {\n        return 'Your session has expired. Please log in again to continue.';\n      } else if (status === 403 || errorStr.includes('403')) {\n        return 'You do not have permission to perform this action.';\n      } else if (status === 404 || errorStr.includes('404')) {\n        return 'The requested resource could not be found.';\n      } else if (status === 500 || errorStr.includes('500')) {\n        return 'An internal server error occurred. Please try again later.';\n      } else if (errorStr.includes('Network') || status === 0) {\n        return 'Unable to connect to the server. Please check your internet connection.';\n      }\n      return 'An unexpected error occurred. Please try again or contact support if the problem persists.';\n    }\n    canRetry(error) {\n      const errorStr = this.getErrorString(error);\n      const status = error?.status || 0;\n      // Δείχνουμε retry button για network errors και server errors\n      return status === 500 || status === 0 || errorStr.includes('500') || errorStr.includes('Network') || errorStr.includes('Connection');\n    }\n    retry(error) {\n      // Κλείνουμε το error και μπορούμε να προσθέσουμε retry logic\n      this.close(error);\n      // Εδώ μπορείς να προσθέσεις retry logic αν χρειάζεται\n      window.location.reload(); // Simple retry - reload page\n    }\n    static #_ = this.ɵfac = function ErrorModalComponent_Factory(t) {\n      return new (t || ErrorModalComponent)(i0.ɵɵdirectiveInject(i1.ErrorService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ErrorModalComponent,\n      selectors: [[\"app-error-modal\"]],\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"error-container\", 4, \"ngFor\", \"ngForOf\"], [1, \"error-container\"], [\"styleClass\", \"error-dialog\", 3, \"header\", \"visible\", \"modal\", \"closable\", \"draggable\", \"resizable\", \"breakpoints\", \"visibleChange\", \"onHide\"], [\"pTemplate\", \"header\"], [1, \"error-content\"], [1, \"flex\", \"align-items-start\", \"gap-3\", \"mb-4\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-red-100\", \"border-circle\", 2, \"width\", \"3rem\", \"height\", \"3rem\", \"flex-shrink\", \"0\"], [1, \"pi\", \"pi-times\", \"text-red-600\", \"text-xl\"], [1, \"flex-1\"], [1, \"text-900\", \"font-semibold\", \"mb-2\", \"mt-0\"], [1, \"text-600\", \"line-height-3\", \"m-0\"], [1, \"bg-red-50\", \"border-left-3\", \"border-red-500\", \"p-3\", \"border-round\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"pi\", \"pi-info-circle\", \"text-red-600\"], [1, \"text-red-800\", \"text-sm\", \"font-medium\"], [1, \"text-red-700\", \"text-sm\", \"mt-2\", \"mb-0\", \"word-break\"], [\"pTemplate\", \"footer\"], [1, \"pi\", \"pi-exclamation-triangle\", \"text-red-500\", \"text-xl\"], [1, \"font-semibold\", \"text-red-800\"], [1, \"flex\", \"gap-2\", \"justify-content-end\"], [\"label\", \"Dismiss\", \"icon\", \"pi pi-times\", \"severity\", \"secondary\", \"size\", \"small\", 3, \"click\"], [\"label\", \"Retry\", \"icon\", \"pi pi-refresh\", \"severity\", \"info\", \"size\", \"small\", 3, \"click\", 4, \"ngIf\"], [\"label\", \"Retry\", \"icon\", \"pi pi-refresh\", \"severity\", \"info\", \"size\", \"small\", 3, \"click\"]],\n      template: function ErrorModalComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ErrorModalComponent_div_0_Template, 20, 14, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngForOf\", ctx.errors);\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf, i3.Dialog, i4.PrimeTemplate],\n      styles: [\"[_nghost-%COMP%]     .error-dialog .p-dialog-header{background:linear-gradient(135deg,#fee2e2 0%,#fecaca 100%);border-bottom:1px solid #fca5a5;border-radius:6px 6px 0 0;padding:1rem 1.5rem}[_nghost-%COMP%]     .error-dialog .p-dialog-content{padding:1.5rem;background:#ffffff}[_nghost-%COMP%]     .error-dialog .p-dialog-footer{background:#f9fafb;border-top:1px solid #e5e7eb;padding:1rem 1.5rem;border-radius:0 0 6px 6px}[_nghost-%COMP%]     .error-dialog .p-dialog-mask{background:rgba(0,0,0,.6);-webkit-backdrop-filter:blur(2px);backdrop-filter:blur(2px)}.error-container[_ngcontent-%COMP%]   .error-content[_ngcontent-%COMP%]   .word-break[_ngcontent-%COMP%]{word-break:break-word;overflow-wrap:break-word}[_nghost-%COMP%]     .error-dialog .p-dialog{animation:_ngcontent-%COMP%_errorSlideIn .3s ease-out}@keyframes _ngcontent-%COMP%_errorSlideIn{0%{opacity:0;transform:translateY(-20px) scale(.95)}to{opacity:1;transform:translateY(0) scale(1)}}[_nghost-%COMP%]     .error-dialog.network-error .p-dialog-header{background:linear-gradient(135deg,#dbeafe 0%,#bfdbfe 100%);border-bottom-color:#93c5fd}[_nghost-%COMP%]     .error-dialog.network-error .p-dialog-header .text-red-800{color:#1e40af!important}[_nghost-%COMP%]     .error-dialog.network-error .p-dialog-header .text-red-500{color:#3b82f6!important}[_nghost-%COMP%]     .error-dialog.server-error .p-dialog-header{background:linear-gradient(135deg,#fef3c7 0%,#fde68a 100%);border-bottom-color:#f59e0b}[_nghost-%COMP%]     .error-dialog.server-error .p-dialog-header .text-red-800{color:#92400e!important}[_nghost-%COMP%]     .error-dialog.server-error .p-dialog-header .text-red-500{color:#f59e0b!important}.dark[_nghost-%COMP%]     .error-dialog .p-dialog-header, .dark   [_nghost-%COMP%]     .error-dialog .p-dialog-header{background:linear-gradient(135deg,#7f1d1d 0%,#991b1b 100%);border-bottom-color:#dc2626}.dark[_nghost-%COMP%]     .error-dialog .p-dialog-content, .dark   [_nghost-%COMP%]     .error-dialog .p-dialog-content{background:#1f2937;color:#f9fafb}.dark[_nghost-%COMP%]     .error-dialog .p-dialog-footer, .dark   [_nghost-%COMP%]     .error-dialog .p-dialog-footer{background:#111827;border-top-color:#374151}.dark[_nghost-%COMP%]     .error-dialog .bg-red-50, .dark   [_nghost-%COMP%]     .error-dialog .bg-red-50{background:rgba(127,29,29,.2)!important}.dark[_nghost-%COMP%]     .error-dialog .text-red-700, .dark   [_nghost-%COMP%]     .error-dialog .text-red-700{color:#fca5a5!important}.dark[_nghost-%COMP%]     .error-dialog .text-red-800, .dark   [_nghost-%COMP%]     .error-dialog .text-red-800{color:#f87171!important}@media (max-width: 768px){[_nghost-%COMP%]     .error-dialog .p-dialog-header, [_nghost-%COMP%]     .error-dialog .p-dialog-content, [_nghost-%COMP%]     .error-dialog .p-dialog-footer{padding:1rem}.error-content[_ngcontent-%COMP%]   .flex.align-items-start[_ngcontent-%COMP%]{flex-direction:column;text-align:center}.error-content[_ngcontent-%COMP%]   .flex.align-items-start[_ngcontent-%COMP%]   .bg-red-100[_ngcontent-%COMP%]{margin-bottom:1rem}}\"]\n    });\n  }\n  return ErrorModalComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}