import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { MenuItem, Message, MessageService } from 'primeng/api';
import { Product } from '../../api/product';
import { ProductService } from '../../service/product.service';
import { Subscription, debounceTime, throwIfEmpty } from 'rxjs';
//import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { SelectItem } from 'primeng/api';
import { DataView } from 'primeng/dataview';
import { StationsService } from '../../service/stations.service';
import { Station } from '../../api/station';
import { InvertPower } from '../../api/invertpower';
import { ActivatedRoute } from '@angular/router';
import { Device } from '../../api/device';
import { GetHistoricDataResponse, GetStationSumDataResponse, GetWeatherResponse } from '../../api/responses';
import { GetHistoricDataRequest, GetStationDevicesRequest, GetWeatherRequest } from '../../api/requests';
import { CacheService } from '../../service/cache.service';
import { WeatherService } from '../../service/weather.service';
import { DateUtilsService } from '../../service/date-utils.service';

@Component({
    templateUrl: './view.component.html',
})
export class ViewStationComponent implements OnInit, OnDestroy {

    items!: MenuItem[];
    stations: Station[] = [];
    devices: Device[] =[];
    inverters: Device[]= [];
    selectedStation: Station;
    sumData: GetStationSumDataResponse;
    energyData: GetHistoricDataResponse;
    weatherData: GetWeatherResponse;
    inverterIds: string;
    timeFrames: any[];
    selectedTimeFrame: string;
    searchType:string = null;
    selectedDate: Date = new Date(); // default σημερινή ημερομηνία

    chartData: any;

    chartOptions: any;

    subscription!: Subscription;

    sortOptionsCountry: SelectItem[] = [];

    sortOptionsStatus: SelectItem[] = [];

    sortOrder: number = 0;

    sortField: string = '';

    sourceCities: any[] = [];

    targetCities: any[] = [];

    orderCities: any[] = [];

    mapSrc:string;

    lineData: any;

    barData: any;

    pieData: any;

    polarData: any;

    radarData: any;

    lineOptions: any;

    barOptions: any;

    pieOptions: any;

    polarOptions: any;

    radarOptions: any;

    days:any[];
    showGeneral: boolean = true;
    showInvert: boolean = false;
    showString: boolean = false;
    invertpower: InvertPower[] =[];
    chartTableData: any[] = []; // Data for the table based on chart
    invert: InvertPower = {};
    rowsPerPageOptions = [5, 10, 20];
    cols: any[] = [];


    constructor(private stationsService: StationsService,
        private route: ActivatedRoute,
        private cacheService: CacheService,
        private weatherService: WeatherService,
        private messageService: MessageService,
        private dateUtils: DateUtilsService
        ) {
        
    }
 
    ngOnInit() {
        let stations = this.cacheService.getStations();
        console.log(stations)
        
        //console.log(this.stations)
        this.route.paramMap.subscribe(params => {
            this.selectedStation = stations.find(s => s.id == params.get('id'));
            //this.selectedStation = params.get('id');
            console.log('Station ID:', this.selectedStation);
        });

        this.getStationDevices();
        this.getStationSumData();
        this.getWeather();

        //this.initCharts();
        
        ///duummyy
        // this.initDays();

        //dummy - will be populated with real data from charts
        // this.stationsService.getInvertPower().then(data => this.invertpower = data);

        this.cols = [
           
        ];

        this.timeFrames = [
            { id: 'day', label: 'Day' },
            { id: 'month', label: 'Month' },
            { id: 'year', label: 'Year' },
            { id: 'last30days', label: 'Last 30 days' },
            { id: 'last365days', label: 'Last 365 days' },
            { id: 'thisYear', label: 'This year (1/1 - today)' },
            { id: 'fromBeginning', label: 'From the beginning' }
        ];
          
        this.selectedTimeFrame = "day";

    }

    // initDays(){
    //     this.days = [
    //         {
    //             "day":"Tuesday",
    //             "temp":23,
    //             "kati":3.20,
    //             "image":"https://cdn-icons-png.flaticon.com/512/979/979585.png",
    //             "alert":"No alerts"
    //         },
    //         {
    //             "day":"Today",
    //             "temp":23,
    //             "kati":3.20,
    //             "image":"https://cdn-icons-png.flaticon.com/512/5213/5213385.png",
    //             "alert":"No alerts"
    //         },
    //         {
    //             "day":"Thursday",
    //             "temp":22,
    //             "kati":3.20,
    //             "image":"https://cdn-icons-png.flaticon.com/512/5213/5213385.png",
    //             "alert":"No alerts"
    //         },
    //         {
    //             "day":"Friday",
    //             "temp":23,
    //             "kati":3.20,
    //             "image":"https://cdn-icons-png.flaticon.com/512/5213/5213385.png",
    //             "alert":"No alerts"
    //         },
    //         {
    //             "day":"Saturday",
    //             "temp":23,
    //             "kati":3.20,
    //             "image":"https://cdn-icons-png.flaticon.com/512/1163/1163657.png",
    //             "alert":"Light Hail Probability"
    //         },
    //         {
    //             "day":"Sunday",
    //             "temp":21,
    //             "kati":3.20,
    //             "image":"https://cdn-icons-png.flaticon.com/512/979/979585.png",
    //             "alert":"No alerts"
    //         },
    //         {
    //             "day":"Monday",
    //             "temp":23,
    //             "kati":3.20,
    //             "image":"https://cdn-icons-png.flaticon.com/512/12607/12607703.png",
    //             "alert":"No alerts"
    //         }
    //     ]
    // }

    initCharts(separated: boolean, searchType: string) {
        const documentStyle = getComputedStyle(document.documentElement);
        const textColor = documentStyle.getPropertyValue('--text-color');
        const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');
        const surfaceBorder = documentStyle.getPropertyValue('--surface-border');
        
        if (separated){
            
            // Βρίσκουμε τις μοναδικές ημερομηνίες με σειρά
            const uniqueDates = Array.from(new Set(this.energyData.data.map(d => d.dateTime)));
            const uniqueDateDescriptions = Array.from(new Set(this.energyData.data.map(d => d.dateDescription)));
            // Βρίσκουμε τους μοναδικούς inverters
            const uniqueInverters = Array.from(new Set(this.energyData.data.map(d => d.name)));

            // Δημιουργούμε datasets, ένα για κάθε inverter
            const barDatasets = uniqueInverters.map(inv => {
                const color = this.getRandomColor();
                const data = uniqueDates.map(date => {
                    const entry = this.energyData.data.find(d => d.name === inv && d.dateTime === date);
                    return entry ? entry.activePower : 0;
                });

                return {
                    label: inv,
                    data: data,
                    backgroundColor: color,
                };
            });

            this.barData = {
                labels: uniqueDateDescriptions.map(dt => dt), // ημερομηνίες στον Χ-άξονα
                datasets: barDatasets
            };

            // const uniqueLineDates = Array.from(new Set(this.energyData.data.map(d => d.dateTime)));
            // const uniqueLineDateDescriptions = Array.from(new Set(this.energyData.data.map(d => d.dateDescription)));
            // const uniqueLineInverters = Array.from(new Set(this.energyData.data.map(d => d.name)));

            // const lineDatasets = uniqueLineInverters.map(inv => {
            //     const color = this.getRandomColor();
            //     const data = uniqueLineDates.map(date => {
            //         const entry = this.energyData.data.find(d => d.name === inv && d.dateTime === date);
            //         return entry ? entry.activePower : 0;
            //     });

            //     return {
            //         label: inv,
            //         data: data,
            //         borderColor: color,
            //         fill: false,
            //         tension: 0.3, // προαιρετικά για πιο ομαλές καμπύλες
            //     };
            // });

            // this.lineData = {
            //     labels: uniqueLineDateDescriptions, // ετικέτες στον X-άξονα
            //     datasets: lineDatasets
            // };
            
            var datasets:any[] = [];
            // const uniqueInverters = Array.from(new Set(this.energyData.data.map(d => d.name)));
             const activePowerByName: Record<string, number[]> = {};

            this.energyData.data.forEach(d => {
                if (!activePowerByName[d.name]) {
                    activePowerByName[d.name] = [];
                }
                activePowerByName[d.name].push(d.activePower);
            });
            
            uniqueInverters.forEach(inv => {
                var color = this.getRandomColor();
                datasets.push({
                    label: inv,
                    data: activePowerByName[inv],
                    fill: false,
                    backgroundColor: color,
                    borderColor: color,
                    tension: .4
                });

            });
            
            const firstName = this.energyData.data[0].name; // Παίρνουμε το πρώτο name
            const dateTimesForFirstName = this.energyData.data
            .filter(d => d.name === firstName) // Φιλτράρουμε μόνο τα αντικείμενα με το ίδιο name
            .map(d => d.dateTime); // Παίρνουμε μόνο τα dateTime

            this.lineData = {
                labels: dateTimesForFirstName.map((dt, index) =>
                    index % 2 === 0 ? this.dateUtils.formatTimeForChart(dt) : ''
                ),
                datasets: datasets
            };

            // this.barData = {
            //     labels: this.energyData.data.map(item => item.name), // Το property "name" για τον X-άξονα
            //     datasets: datasets
            // };

        }else{
            this.lineData = {
                labels: this.energyData.data.map((e, index) =>
                    index % 2 === 0 ? this.dateUtils.formatTimeForChart(e.dateTime) : ''
                ),
                datasets: [
                    {
                        label: 'Active Power',
                        data: this.energyData.data.map(e => e.activePower),
                        fill: false,
                        backgroundColor: documentStyle.getPropertyValue('--primary-500'),
                        borderColor: documentStyle.getPropertyValue('--primary-500'),
                        tension: .4
                    },
                    {
                        label: 'Total Input Power',
                        data: this.energyData.data.map(e => e.totalInputPower),
                        fill: false,
                        backgroundColor: documentStyle.getPropertyValue('--primary-200'),
                        borderColor: documentStyle.getPropertyValue('--primary-200'),
                        tension: .4
                    }
                ]
            };

            this.barData = {
                labels: this.energyData.data.map(item => item.name), // Το property "name" για τον X-άξονα
                datasets: [
                    {
                        label: 'Active Power',
                        data: this.energyData.data.map(e => e.activePower),
                        fill: false,
                        backgroundColor: documentStyle.getPropertyValue('--primary-500'),
                        borderColor: documentStyle.getPropertyValue('--primary-500'),
                        tension: .4
                    },
                    {
                        label: 'Total Input Power',
                        data: this.energyData.data.map(e => e.totalInputPower),
                        fill: false,
                        backgroundColor: documentStyle.getPropertyValue('--primary-200'),
                        borderColor: documentStyle.getPropertyValue('--primary-200'),
                        tension: .4
                    }
                ]
            };
        }



        

        this.lineOptions = {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            elements: {
                point: {
                    radius: 3,
                    hoverRadius: 6,
                    hitRadius: 15
                }
            },
            plugins: {
                legend: {
                    labels: {
                        fontColor: textColor
                    }
                },
                tooltip: {
                    enabled: true,
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: 'rgba(255, 255, 255, 0.1)',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: true,
                    padding: 12,
                    titleFont: {
                        size: 14,
                        weight: 'bold'
                    },
                    bodyFont: {
                        size: 13
                    },
                    callbacks: {
                        title: (context: any) => {
                            if (context && context.length > 0) {
                                const dataIndex = context[0].dataIndex;
                                // Get the original datetime from energyData
                                if (this.energyData && this.energyData.data && this.energyData.data[dataIndex]) {
                                    const dateTime = this.energyData.data[dataIndex].dateTime;
                                    return `Time: ${this.dateUtils.formatDateTimeForTooltip(dateTime)}`;
                                }
                                return `Time: ${context[0].label}`;
                            }
                            return 'Time: --:--';
                        },
                        label: (context: any) => {
                            const label = context.dataset.label || '';
                            const value = context.parsed.y;
                            return `${label}: ${value.toFixed(2)} kW`;
                        },
                        afterBody: (context: any) => {
                            if (context && context.length > 0) {
                                const dataIndex = context[0].dataIndex;
                                if (this.energyData && this.energyData.data && this.energyData.data[dataIndex]) {
                                    const data = this.energyData.data[dataIndex];
                                    const date = new Date(data.dateTime);
                                    return [
                                        '',
                                        `Date: ${date.toLocaleDateString()}`,
                                        `Total Input Power: ${data.totalInputPower?.toFixed(2) || 'N/A'} kW`
                                    ];
                                }
                            }
                            return [];
                        }
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: textColorSecondary,
                        maxRotation: 0,
                        minRotation: 0
                    },
                    grid: {
                        color: surfaceBorder,
                        drawBorder: false
                    }
                },
                y: {
                    ticks: {
                        color: textColorSecondary
                    },
                    grid: {
                        color: surfaceBorder,
                        drawBorder: false
                    }
                },
            }
        };

        // Enhanced Bar Chart Options with Tooltips
        this.barOptions = {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            plugins: {
                legend: {
                    labels: {
                        fontColor: textColor
                    }
                },
                tooltip: {
                    enabled: true,
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: 'rgba(255, 255, 255, 0.1)',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: true,
                    padding: 12,
                    titleFont: {
                        size: 14,
                        weight: 'bold'
                    },
                    bodyFont: {
                        size: 13
                    },
                    callbacks: {
                        title: (context: any) => {
                            if (context && context.length > 0) {
                                const dataIndex = context[0].dataIndex;
                                // For bar charts, show the label (date/time or inverter name)
                                if (this.energyData && this.energyData.data && this.energyData.data[dataIndex]) {
                                    const data = this.energyData.data[dataIndex];
                                    if (data.dateTime) {
                                        return `Time: ${this.dateUtils.formatDateTimeForTooltip(data.dateTime)}`;
                                    }
                                    return `Inverter: ${data.name || context[0].label}`;
                                }
                                return context[0].label;
                            }
                            return 'Data Point';
                        },
                        label: (context: any) => {
                            const label = context.dataset.label || '';
                            const value = context.parsed.y;
                            return `${label}: ${value.toFixed(2)} kW`;
                        },
                        afterBody: (context: any) => {
                            if (context && context.length > 0) {
                                const dataIndex = context[0].dataIndex;
                                if (this.energyData && this.energyData.data && this.energyData.data[dataIndex]) {
                                    const data = this.energyData.data[dataIndex];
                                    const additionalInfo = [];

                                    if (data.dateTime) {
                                        const date = new Date(data.dateTime);
                                        additionalInfo.push(`Date: ${date.toLocaleDateString()}`);
                                    }

                                    if (data.totalInputPower !== undefined) {
                                        additionalInfo.push(`Total Input Power: ${data.totalInputPower.toFixed(2)} kW`);
                                    }

                                    if (data.name) {
                                        additionalInfo.push(`Device: ${data.name}`);
                                    }

                                    return additionalInfo.length > 0 ? ['', ...additionalInfo] : [];
                                }
                            }
                            return [];
                        }
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: textColorSecondary,
                        maxRotation: 45,
                        minRotation: 0
                    },
                    grid: {
                        color: surfaceBorder,
                        drawBorder: false
                    }
                },
                y: {
                    ticks: {
                        color: textColorSecondary
                    },
                    grid: {
                        color: surfaceBorder,
                        drawBorder: false
                    }
                },
            }
        };
    }

    getStationDevices(){
        let request: GetStationDevicesRequest = {
            stationId : this.selectedStation.id
        }
        this.stationsService.getStationDevices(request).then(data => {
            console.log(data)
            this.devices = data;
            this.inverters = this.devices
                .filter(device => device.type === "StringInverter")
            console.log("DEBV" + this.devices);
            this.inverterIds = this.devices
                .filter(device => device.type === "StringInverter") // Φιλτράρει μόνο τα StringInverter
                .map(device => device.id) // Παίρνει μόνο τα id
                .join(","); // Τα ενώνει με κόμματα

            this.getHistoryEnergy(this.inverterIds);
        });
        
        
    }

    getStationSumData(){
        let request: GetStationDevicesRequest = {
            stationId : this.selectedStation.id
        }
        this.stationsService.getStationSumData(request).then(data => this.sumData = data);
    }


    getWeather(){
        let request: GetWeatherRequest = {
            coordinates : this.selectedStation.longitude + "," + this.selectedStation.latitude
        }
        this.weatherService.getWeather(request).then(data => this.weatherData = data);
    }

    getHistoryEnergy(inverterIds: string, separated: boolean = false, dateTimeFrom: Date = null, dateTimeTo: Date = null, searchType: string = null){
        var now = new Date();

        if (dateTimeFrom != null)
            var formattedStartDate = dateTimeFrom.toISOString();
        else
            var formattedStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0).toISOString();

        if (dateTimeTo != null)
            var formattedEndDate = dateTimeTo.toISOString();
        else
            var formattedEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0).toISOString();


        // var todayMidnightUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 0, 0, 0));
        // console.log(dateTimeFrom)
        // if (dateTimeFrom != null) {
        //     now = new Date(dateTimeFrom);
        //     todayMidnightUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 0, 0, 0));
        // }

        // var formattedStartDate = dateTimeFrom.toISOString();
        // console.log(formattedStartDate);

        // var tomorrowMidnightUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate() + 1, 0, 0, 0));
        // if (dateTimeTo != null) {
        //     now = new Date(dateTimeTo);
        //     tomorrowMidnightUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()+1, 0, 0, 0));
        // }
        
        


        let request: GetHistoricDataRequest = {
            devIds : inverterIds != "" ? inverterIds : null,
            devTypeId: 1,
            startDateTime: formattedStartDate,
            endDateTime: formattedEndDate,
            separated: separated,
            searchType:searchType,
            stationId: this.selectedStation.id
        }
        this.stationsService.getStationHistoricData(request).then(data => {
            this.energyData = data
            console.log(this.energyData)
            if (this.energyData.data.length > 0){
                // Filter data to not show beyond current time
                this.energyData.data = this.filterDataByCurrentTime(this.energyData.data);
                this.initCharts(separated, searchType);
                this.updateChartTableData(); // Update table data when chart data changes
            }else{
                this.messageService.add({ severity: 'warn', summary: 'No Data', detail: 'No Data to display!' });
            }
        });
    }

    showGeneralOverview(){
        var reload = true;
        if (this.showGeneral)
            reload = false;

        this.showGeneral = true;
        this.showInvert = false;
        this.showString = false;

        if (reload) {
            // Reload the original general overview data (non-separated)
            this.getHistoryEnergy(this.inverterIds, false);
        }
    }

    showInvertMonitoring(){
        var reload = true;
        if (this.showInvert)
            reload = false;

        this.showGeneral = false;
        this.showInvert = true;
        this.showString = false;

        if (reload)
            this.getHistoryEnergy(this.inverterIds, true)
    }

    showStringMonitoring(){
        this.showGeneral = false;
        this.showInvert = false;
        this.showString = true;
    }


    getRandomColor(){
        const rootStyles = getComputedStyle(document.documentElement);
        const cssVariables = Object.keys(rootStyles)
        .map(key => rootStyles[key])
        .filter(value => value.startsWith('--')) // Φιλτράρουμε μόνο τις CSS variables
        .map(varName => rootStyles.getPropertyValue(varName).trim()) // Παίρνουμε τις τιμές τους
        .filter(value => {
            // Ελέγχουμε αν το όνομα της μεταβλητής περιέχει έναν αριθμό μεγαλύτερο του 200
            const match = value.match(/(\d+)/); // Αντιστοιχεί στον αριθμό που υπάρχει στο όνομα
            return match && parseInt(match[0], 10) > 200; // Φιλτράρει μόνο χρώματα με αριθμό > 200
        });
        
        // Συνάρτηση που επιστρέφει τυχαίο χρώμα από τη λίστα
        return cssVariables[Math.floor(Math.random() * cssVariables.length)];

    }

    onDateChange(event: Date) {
        console.log('Ημερομηνία επιλέχθηκε:', event);

        // Create Athens timezone dates
        const athensDate = this.dateUtils.toAthensTime(event);
        var datefrom = new Date(athensDate.setHours(0,0,0,0));
        const pastDate = new Date(datefrom);
        pastDate.setDate(pastDate.getDate() + 1);

        this.getHistoryEnergy(this.inverterIds, !this.showGeneral, datefrom, pastDate, this.searchType);
    }


    onTimeFrameChange(event: any): void {
        console.log(event)
        let currentDate = new Date();
        console.log(currentDate)
        let datefrom: Date;
        let dateto: Date = new Date(currentDate); 
        
        switch (event.value) {
           
          case 'day':
            // Για την περίπτωση του "Day", το datefrom είναι σήμερα
            datefrom = new Date(currentDate.setHours(0, 0, 0, 0)); // Ορίζουμε το time στα 00:00:00
            this.searchType = null;
            if (event.value === 'day') {
                this.selectedDate = new Date(); // reset σε σημερινή ημερομηνία
            }
            break;
      
          case 'month':
            // Για την περίπτωση του "Month", το datefrom είναι η 1η μέρα του τρέχοντος μήνα
            datefrom = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
            datefrom.setHours(0,0,0,0);
            this.searchType = "monthly";
            break;
      
          case 'year':
            // Για την περίπτωση του "Year", το datefrom είναι η 1η Ιανουαρίου του τρέχοντος έτους
            datefrom = new Date(currentDate.getFullYear(), 0, 1);
            datefrom.setHours(0,0,0,0);
            this.searchType = "yearly";
            break;
      
          case 'last30days':
            // Για την περίπτωση του "Last 30 days", το datefrom είναι 30 μέρες πριν
            const pastDate = new Date(currentDate);
            pastDate.setDate(pastDate.getDate() - 30);
            pastDate.setHours(0, 0, 0, 0);
            datefrom = pastDate;
            this.searchType = "monthly";
            break;
      
          case 'last365days':
            // Για την περίπτωση του "Last 365 days", το datefrom είναι 365 μέρες πριν
            const pastDate2 = new Date(currentDate);
            pastDate2.setDate(pastDate2.getDate() - 365);
            pastDate2.setHours(0, 0, 0, 0);
            datefrom = pastDate2;
            this.searchType = "yearly";
            break;
      
          case 'thisYear':
            // Για την περίπτωση του "This year (1/1 - today)", το datefrom είναι η 1η Ιανουαρίου του τρέχοντος έτους
            datefrom = new Date(currentDate.getFullYear(), 0, 1);
            datefrom.setHours(0,0,0,0);
            this.searchType = "yearly";
            break;
      
          case 'fromBeginning':
            // Για την περίπτωση του "From the beginning", το datefrom μπορεί να είναι η αρχή της εφαρμογής ή άλλη προεπιλεγμένη ημερομηνία
            // Εδώ χρησιμοποιούμε την αρχική ημερομηνία της εφαρμογής ή άλλη ημερομηνία
            datefrom = new Date(2000, 0, 1); // Ή οποιαδήποτε άλλη ημερομηνία αρχής
            this.searchType = "lifetime";
            break;
      
          default:
              
            // Αν δεν είναι καμία από τις παραπάνω επιλογές, ορίζουμε μια προεπιλεγμένη τιμή
            datefrom = new Date(currentDate.setHours(0, 0, 0, 0));
            break;
        }
      
        // Καλούμε τη συνάρτηση getHistoryEnergy με τα ορίσματα
        this.getHistoryEnergy(this.inverterIds, !this.showGeneral, datefrom, dateto, this.searchType);
      }



    onGlobalFilter(table: any, event: Event) {
        table.filterGlobal((event.target as HTMLInputElement).value, 'contains');
    }

    private filterDataByCurrentTime(data: any[]): any[] {
        const now = new Date();
        const currentTime = now.getTime();

        return data.filter(item => {
            const itemDateTime = new Date(item.dateTime);
            const itemTime = itemDateTime.getTime();

            // Only include data points that are not in the future
            return itemTime <= currentTime;
        });
    }

    private updateChartTableData() {
        if (this.energyData && this.energyData.data) {
            this.chartTableData = this.energyData.data.map((item, index) => {
                return {
                    id: index + 1,
                    name: item.name || `Device-${index + 1}`, // Include device name
                    time: this.dateUtils.formatTimeForTable(item.dateTime),
                    date: this.dateUtils.formatDateForTable(item.dateTime),
                    dateTime: item.dateTime,
                    activePower: item.activePower || 0,
                    totalInputPower: item.totalInputPower || 0,
                    efficiency: item.activePower && item.totalInputPower ?
                        ((item.activePower / item.totalInputPower) * 100).toFixed(1) : 'N/A',
                    status: this.getStatusFromPower(item.activePower || 0)
                };
            });

            // Also update invertpower array with latest data from each device
            this.updateInvertPowerFromChartData();
        }
    }

    private updateInvertPowerFromChartData() {
        if (this.energyData && this.energyData.data) {
            // Group data by device name and get the latest entry for each
            const deviceMap = new Map();

            this.energyData.data.forEach(item => {
                const deviceName = item.name || 'Unknown Device';
                const itemTime = new Date(item.dateTime).getTime();

                if (!deviceMap.has(deviceName) ||
                    new Date(deviceMap.get(deviceName).dateTime).getTime() < itemTime) {
                    deviceMap.set(deviceName, item);
                }
            });

            // Convert to invertpower format
            this.invertpower = Array.from(deviceMap.values()).map((item, index) => ({
                id: (index + 1).toString().padStart(4, '0'),
                name: item.name || `Device-${index + 1}`,
                nominaloutput: Math.round(item.totalInputPower || 0),
                capacity: Math.round((item.totalInputPower || 0) * 0.95), // Assume 95% capacity
                acpower: item.activePower || 0,
                totalenergy: Math.round((item.activePower || 0) * 24), // Estimate daily energy
                performance: item.activePower && item.totalInputPower ?
                    Math.round((item.activePower / item.totalInputPower) * 100) : 0
            }));
        }
    }

    private getStatusFromPower(power: number): string {
        if (power > 80) return 'Optimal';
        if (power > 50) return 'Good';
        if (power > 20) return 'Low';
        if (power > 0) return 'Minimal';
        return 'Offline';
    }

    private getStatusSeverity(status: string): string {
        switch (status) {
            case 'Optimal': return 'success';
            case 'Good': return 'info';
            case 'Low': return 'warning';
            case 'Minimal': return 'warning';
            case 'Offline': return 'danger';
            default: return 'secondary';
        }
    }

    exportChartData() {
        if (!this.chartTableData || this.chartTableData.length === 0) {
            this.messageService.add({
                severity: 'warn',
                summary: 'No Data',
                detail: 'No chart data available to export.'
            });
            return;
        }

        // Prepare CSV data
        const headers = ['Device Name', 'Time', 'Date', 'Active Power (kW)', 'Total Input Power (kW)', 'Efficiency (%)', 'Status'];
        const csvData = this.chartTableData.map(row => [
            row.name,
            row.time,
            row.date,
            row.activePower.toFixed(2),
            row.totalInputPower.toFixed(2),
            row.efficiency,
            row.status
        ]);

        // Create CSV content
        const csvContent = [
            headers.join(','),
            ...csvData.map(row => row.join(','))
        ].join('\n');

        // Create and download file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `inverter-data-${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        this.messageService.add({
            severity: 'success',
            summary: 'Export Complete',
            detail: 'Chart data has been exported to CSV successfully.'
        });
    }

    ngOnDestroy() {
        if (this.subscription) {
            this.subscription.unsubscribe();
        }
    }
}
