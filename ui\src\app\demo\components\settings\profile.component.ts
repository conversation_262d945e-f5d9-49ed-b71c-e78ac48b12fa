import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MessageService } from 'primeng/api';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { User } from '../../api/user';
import { AuthService } from '../../service/auth.service';
import { UserService } from '../../service/user.service';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss']
})
export class ProfileComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  user: User = {};
  profileForm: FormGroup;
  isEditing = false;
  isLoading = false;
  
  breadcrumbItems = [
    { label: 'Dashboard', routerLink: '/app/index' },
    { label: 'Settings', routerLink: '/app/settings' },
    { label: 'Profile' }
  ];

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private userService: UserService,
    private messageService: MessageService
  ) {
    this.initializeForm();
  }

  ngOnInit() {
    this.loadUserData();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm() {
    this.profileForm = this.fb.group({
      username: [{ value: '', disabled: true }], // Username is not editable
      email: ['', [Validators.required, Validators.email]],
      firstName: [''],
      lastName: ['']
    });
  }

  private loadUserData() {
    this.isLoading = true;

    this.userService.getUser()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (user) => {
          this.user = {
            id: user.userId?.toString() || '',
            username: user.username || '',
            email: user.email || '',
            role: 'User', // Default role since API doesn't return this yet
            registrationDate: new Date().toISOString(), // Default since API doesn't return this yet
            firstName: user.firstName || '',
            lastName: user.lastName || '',
            isActive: true, // Default since API doesn't return this yet
            lastLogin: new Date().toISOString() // Default since API doesn't return this yet
          };

          // Update form with user data
          this.profileForm.patchValue({
            username: this.user.username,
            email: this.user.email,
            firstName: this.user.firstName,
            lastName: this.user.lastName
          });

          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading user data:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to load user data. Please try again.'
          });
          this.isLoading = false;
        }
      });
  }

  toggleEdit() {
    this.isEditing = !this.isEditing;
    
    if (!this.isEditing) {
      // Cancel editing - reset form
      this.profileForm.patchValue({
        email: this.user.email,
        firstName: this.user.firstName,
        lastName: this.user.lastName
      });
    }
  }

  saveProfile() {
    if (this.profileForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.isLoading = true;
    
    // Simulate API call
    setTimeout(() => {
      const formValue = this.profileForm.value;
      
      // Update user object
      this.user = {
        ...this.user,
        email: formValue.email,
        firstName: formValue.firstName,
        lastName: formValue.lastName
      };

      this.isLoading = false;
      this.isEditing = false;
      
      this.messageService.add({
        severity: 'success',
        summary: 'Profile Updated',
        detail: 'Your profile has been updated successfully.'
      });
    }, 1000);
  }

  private markFormGroupTouched() {
    Object.keys(this.profileForm.controls).forEach(key => {
      const control = this.profileForm.get(key);
      control?.markAsTouched();
    });
  }

  getFieldError(fieldName: string): string {
    const control = this.profileForm.get(fieldName);
    if (control?.errors && control.touched) {
      if (control.errors['required']) {
        return `${fieldName} is required`;
      }
      if (control.errors['email']) {
        return 'Please enter a valid email address';
      }
    }
    return '';
  }

  formatDate(dateString: string): string {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-GB', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  formatDateTime(dateString: string): string {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString('en-GB', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getRoleColor(role: string): string {
    switch (role?.toLowerCase()) {
      case 'admin': return 'danger';
      case 'manager': return 'warning';
      case 'user': return 'info';
      default: return 'secondary';
    }
  }

  getStatusColor(isActive: boolean): string {
    return isActive ? 'success' : 'danger';
  }

  getStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive';
  }
}
