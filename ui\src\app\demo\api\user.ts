export interface User {
    id?: string;
    userId?: string; // API returns userId instead of id
    username?: string;
    email?: string;
    role?: string;
    registrationDate?: string;
    firstName?: string;
    lastName?: string;
    avatar?: string;
    isActive?: boolean;
    lastLogin?: string;
    name?: string; // API also returns name field
    createdAt?: string; // API returns createdAt for registration date
    status?: string; // API returns status (Active, Inactive, etc.)
}

export interface UpdateUserRequest {
    email?: string;
    firstName?: string;
    lastName?: string;
}

export interface UpdateUserResponse {
    success: boolean;
    message?: string;
    user?: User;
}
