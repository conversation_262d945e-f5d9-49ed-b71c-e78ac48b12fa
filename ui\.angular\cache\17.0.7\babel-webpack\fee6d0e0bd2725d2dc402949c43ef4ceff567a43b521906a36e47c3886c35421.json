{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../layout/service/app.layout.service\";\nimport * as i2 from \"../../service/stations.service\";\nimport * as i3 from \"../../service/providers.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"../../service/cache.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"primeng/breadcrumb\";\nimport * as i9 from \"@fortawesome/angular-fontawesome\";\nfunction ProvidersComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtext(1, \" You are not registered to any provider. You can start adding providers now! \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProvidersComponent_div_9_div_1_div_17_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const provider_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", provider_r6.configuration.Stations[0].StationName, \" \");\n  }\n}\nfunction ProvidersComponent_div_9_div_1_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"label\", 29);\n    i0.ɵɵtext(2, \"Station\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ProvidersComponent_div_9_div_1_div_17_div_3_Template, 2, 1, \"div\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const provider_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", provider_r6.configuration.Stations.length > 0);\n  }\n}\nfunction ProvidersComponent_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28)(2, \"div\")(3, \"label\", 29);\n    i0.ɵɵtext(4, \"Provider\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 30);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 31)(8, \"label\", 29);\n    i0.ɵɵtext(9, \"Username\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\")(13, \"label\", 29);\n    i0.ɵɵtext(14, \"Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(17, ProvidersComponent_div_9_div_1_div_17_Template, 4, 1, \"div\", 8);\n    i0.ɵɵelementStart(18, \"div\", 32)(19, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function ProvidersComponent_div_9_div_1_Template_button_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const i_r7 = restoredCtx.index;\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.editProvider(i_r7));\n    });\n    i0.ɵɵtext(20, \" Edit \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function ProvidersComponent_div_9_div_1_Template_button_click_21_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const i_r7 = restoredCtx.index;\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.removeProvider(i_r7));\n    });\n    i0.ɵɵtext(22, \" Remove \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const provider_r6 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r5.getSelectedProviderName(provider_r6.providerId));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(provider_r6.configuration.Username);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", provider_r6.configuration.Password, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", provider_r6.configuration.Stations);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", true);\n  }\n}\nfunction ProvidersComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, ProvidersComponent_div_9_div_1_Template, 23, 5, \"div\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.userProviders);\n  }\n}\nfunction ProvidersComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"p\", 36);\n    i0.ɵɵtext(2, \" You are not registered to any provider. You can start adding providers now! \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProvidersComponent_div_33_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const p_r22 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", p_r22.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(p_r22.name);\n  }\n}\nfunction ProvidersComponent_div_33_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"label\", 29);\n    i0.ɵɵtext(2, \"Portfolio Id*\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 45);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProvidersComponent_div_33_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"label\", 29);\n    i0.ɵɵtext(2, \"FTPUrl* (must begin with ftp://)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 46);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProvidersComponent_div_33_div_17_option_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const station_r24 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", station_r24.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(station_r24.name);\n  }\n}\nfunction ProvidersComponent_div_33_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"label\", 47);\n    i0.ɵɵtext(2, \"Select Station*\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"select\", 48);\n    i0.ɵɵtemplate(4, ProvidersComponent_div_33_div_17_option_4_Template, 2, 2, \"option\", 39);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const provider_r15 = i0.ɵɵnextContext().$implicit;\n    let tmp_0_0;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", (tmp_0_0 = provider_r15.get(\"stations\")) == null ? null : tmp_0_0.value);\n  }\n}\nfunction ProvidersComponent_div_33_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function ProvidersComponent_div_33_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const i_r16 = i0.ɵɵnextContext().index;\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.getStations(i_r16));\n    });\n    i0.ɵɵtext(1, \" Get Stations \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProvidersComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 28)(2, \"div\")(3, \"label\", 29);\n    i0.ɵɵtext(4, \"Provider*\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"select\", 38);\n    i0.ɵɵtemplate(6, ProvidersComponent_div_33_option_6_Template, 2, 2, \"option\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\")(8, \"label\", 29);\n    i0.ɵɵtext(9, \"Username*\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"input\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\")(12, \"label\", 29);\n    i0.ɵɵtext(13, \"Password*\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, ProvidersComponent_div_33_div_15_Template, 4, 0, \"div\", 8)(16, ProvidersComponent_div_33_div_16_Template, 4, 0, \"div\", 8)(17, ProvidersComponent_div_33_div_17_Template, 5, 1, \"div\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function ProvidersComponent_div_33_Template_button_click_18_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r30);\n      const i_r16 = restoredCtx.index;\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.removeProvider(i_r16));\n    });\n    i0.ɵɵtext(19, \" Remove \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, ProvidersComponent_div_33_button_20_Template, 2, 0, \"button\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const provider_r15 = ctx.$implicit;\n    const i_r16 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_5_0;\n    i0.ɵɵproperty(\"formGroupName\", i_r16);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.availableProviders);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = provider_r15.get(\"providerId\")) == null ? null : tmp_2_0.value) == 4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = provider_r15.get(\"providerId\")) == null ? null : tmp_3_0.value) == 6);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = provider_r15.get(\"stations\")) == null ? null : tmp_4_0.value.length) > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = provider_r15.get(\"providerId\")) == null ? null : tmp_5_0.value) && ctx_r3.userProvidersForm.valid);\n  }\n}\nfunction ProvidersComponent_button_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 50);\n    i0.ɵɵtext(1, \" Save Providers \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = () => ({\n  label: \"Providers\"\n});\nconst _c1 = a0 => [a0];\nconst _c2 = () => ({\n  icon: \"pi pi-home\"\n});\nexport class ProvidersComponent {\n  constructor(layoutService, stationsService, providersService, messageService, fb, cacheService) {\n    this.layoutService = layoutService;\n    this.stationsService = stationsService;\n    this.providersService = providersService;\n    this.messageService = messageService;\n    this.fb = fb;\n    this.cacheService = cacheService;\n    this.availableProviders = [];\n    this.userProviders = [];\n    this.stations = [];\n    this.tooltipVisible = false;\n    this.userProvidersForm = this.fb.group({\n      providers: this.fb.array([])\n    });\n  }\n  ngOnInit() {\n    this.providersService.getProviders().then(data => {\n      this.availableProviders = data;\n    });\n    this.getUserProviders();\n    this.addProvider(); // Ξεκινάμε με μία προεπιλεγμένη σειρά\n  }\n\n  addProvider() {\n    const providerGroup = this.fb.group({\n      providerId: ['', Validators.required],\n      username: ['', Validators.required],\n      password: ['', Validators.required],\n      portfolioId: [''],\n      ftpUrl: [''],\n      stations: [[]],\n      selectedStation: ['']\n    });\n    this.providers.push(providerGroup);\n    // Παρακολούθηση του providerId\n    providerGroup.get('providerId')?.valueChanges.subscribe(value => {\n      const portfolioIdControl = providerGroup.get('portfolioId');\n      if (value === '4') {\n        portfolioIdControl?.setValidators(Validators.required);\n      } else {\n        portfolioIdControl?.clearValidators();\n      }\n      portfolioIdControl?.updateValueAndValidity();\n    });\n  }\n  get providers() {\n    return this.userProvidersForm.get('providers');\n  }\n  getSelectedProviderName(id) {\n    return this.availableProviders.find(p => p.id == id).name;\n  }\n  removeProvider(index) {\n    this.providers.removeAt(index);\n  }\n  getStations(index) {\n    const providerId = this.providers.at(index).get('providerId')?.value;\n    if (!providerId) return;\n    let request = {\n      providerId: this.providers.at(index).get('providerId')?.value,\n      username: this.providers.at(index).get('username')?.value,\n      password: this.providers.at(index).get('password')?.value,\n      portfolioId: this.providers.at(index).get('portfolioId')?.value,\n      ftpUrl: this.providers.at(index).get('ftpUrl')?.value\n    };\n    console.log('Form Data:', request);\n    this.stationsService.getStations(request).then(data => {\n      this.providers.at(index).patchValue({\n        stations: data\n      });\n    });\n    const providerGroup = this.providers.at(index);\n    if (providerId === 4) {\n      providerGroup.get('portfolioId')?.setValidators(Validators.required);\n    }\n    if (providerId === 6) {\n      providerGroup.get('ftpUrl')?.setValidators(Validators.required);\n    } else {\n      providerGroup.get('portfolioId')?.clearValidators();\n      providerGroup.get('portfolioId')?.setValue('');\n    }\n    providerGroup.get('portfolioId')?.updateValueAndValidity();\n    //providerGroup.get('ftpUrl')?.updateValueAndValidity();\n  }\n\n  onSubmit() {\n    if (this.userProvidersForm.invalid) {\n      this.userProvidersForm.markAllAsTouched();\n      return;\n    }\n    if (this.userProvidersForm.valid) {\n      console.log('Form Data:', this.userProvidersForm.value.providers);\n      // Εδώ μπορείς να κάνεις POST τα δεδομένα στο API\n      let request = {\n        providers: this.userProvidersForm.value.providers\n      };\n      this.providersService.saveUserProviders(request).then(data => {\n        console.log(data);\n        this.getUserProviders();\n      });\n    }\n  }\n  needsPortfolio(index) {\n    return this.providers && this.providers.length > index && this.providers.at(index).get('providerId')?.value === 4;\n  }\n  getUserProviders() {\n    this.providersService.getUserProviders().then(data => {\n      this.userProviders = data;\n      console.log(this.userProviders);\n      this.userProviders.forEach(up => {\n        up.configuration = JSON.parse(up.configuration);\n      });\n      if (data.length > 0) {\n        this.stationsService.getUserStations().then(data => {\n          this.stations = data;\n          this.cacheService.setStations(this.stations);\n        });\n      }\n    });\n  }\n  static #_ = this.ɵfac = function ProvidersComponent_Factory(t) {\n    return new (t || ProvidersComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.StationsService), i0.ɵɵdirectiveInject(i3.ProvidersService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.FormBuilder), i0.ɵɵdirectiveInject(i6.CacheService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProvidersComponent,\n    selectors: [[\"app-providers\"]],\n    decls: 37,\n    vars: 12,\n    consts: [[1, \"grid\"], [1, \"col-12\"], [3, \"model\", \"home\"], [1, \"col-12\", \"lg:col-12\", \"xl:col-12\"], [1, \"card\", \"p-4\"], [1, \"container\"], [1, \"text-xl\", \"font-semibold\", \"mb-4\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"text-center text-lg\", 4, \"ngIf\"], [1, \"bg-blue-50\", \"border-l-4\", \"border-blue-400\", \"text-blue-700\", \"p-4\", \"rounded\", \"mb-4\", \"text-sm\", \"relative\"], [1, \"flex\", \"items-start\"], [\"icon\", \"circle-info\", \"size\", \"8x\"], [1, \"font-semibold\", \"mb-1\"], [1, \"list-disc\", \"list-inside\", \"space-y-1\"], [1, \"relative\", \"flex\", \"items-center\", \"gap-2\"], [1, \"icon-wrapper\", \"relative\", \"cursor-pointer\", \"text-blue-500\"], [\"icon\", \"circle-question\"], [1, \"tooltip\", \"absolute\", \"top-6\", \"left-0\", \"hidden\", \"bg-white\", \"border\", \"border-gray-300\", \"p-2\", \"shadow-lg\", \"rounded\", \"w-48\", \"h-48\"], [\"src\", \"assets/layout/images/logo-dark.png\", \"alt\", \"Tooltip image\", 1, \"w-full\", \"h-full\", \"object-contain\", \"rounded\"], [3, \"formGroup\", \"ngSubmit\"], [\"formArrayName\", \"providers\"], [\"class\", \"mb-4 border p-3 rounded shadow\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 1, \"mt-4\", \"bg-blue-500\", \"text-white\", \"p-2\", \"rounded\", 3, \"click\"], [\"type\", \"submit\", \"class\", \"mt-4 bg-green-500 text-white p-2 rounded\", 4, \"ngIf\"], [1, \"alert\", \"alert-info\"], [\"class\", \"mb-4 border p-4 rounded shadow\", 4, \"ngFor\", \"ngForOf\"], [1, \"mb-4\", \"border\", \"p-4\", \"rounded\", \"shadow\"], [1, \"grid\", \"grid-cols-3\", \"gap-4\"], [1, \"block\", \"font-medium\", \"mb-1\"], [1, \"font-medium\"], [\"ngId\", \"provider.configuration\"], [1, \"mt-3\"], [\"type\", \"button\", \"disabled\", \"\", 1, \"bg-yellow-500\", \"text-white\", \"py-2\", \"px-4\", \"rounded\", \"mr-2\", 3, \"click\"], [\"type\", \"button\", 1, \"bg-red-500\", \"text-white\", \"py-2\", \"px-4\", \"rounded\", 3, \"disabled\", \"click\"], [1, \"text-center\", \"text-lg\"], [1, \"text-red-500\", \"font-semibold\"], [1, \"mb-4\", \"border\", \"p-3\", \"rounded\", \"shadow\", 3, \"formGroupName\"], [\"formControlName\", \"providerId\", 1, \"w-full\", \"p-2\", \"border\", \"rounded\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"text\", \"formControlName\", \"username\", \"placeholder\", \"Enter username\", 1, \"w-full\", \"p-2\", \"border\", \"rounded\"], [\"type\", \"text\", \"formControlName\", \"password\", \"placeholder\", \"Enter Password\", 1, \"w-full\", \"p-2\", \"border\", \"rounded\"], [\"type\", \"button\", 1, \"mt-2\", \"text-red-500\", 3, \"click\"], [\"type\", \"button\", \"class\", \"mt-2 text-blue-500\", 3, \"click\", 4, \"ngIf\"], [3, \"value\"], [\"type\", \"text\", \"formControlName\", \"portfolioId\", \"placeholder\", \"Enter Your Portfolio Id\", 1, \"w-full\", \"p-2\", \"border\", \"rounded\"], [\"type\", \"text\", \"formControlName\", \"ftpUrl\", \"placeholder\", \"Enter the FTP Url\", 1, \"w-full\", \"p-2\", \"border\", \"rounded\"], [1, \"block\", \"font-medium\", \"mt-2\"], [\"formControlName\", \"selectedStation\", 1, \"w-full\", \"p-2\", \"border\", \"rounded\"], [\"type\", \"button\", 1, \"mt-2\", \"text-blue-500\", 3, \"click\"], [\"type\", \"submit\", 1, \"mt-4\", \"bg-green-500\", \"text-white\", \"p-2\", \"rounded\"]],\n    template: function ProvidersComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"p-breadcrumb\", 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"h2\", 6);\n        i0.ɵɵtext(7, \"Your Providers\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(8, ProvidersComponent_div_8_Template, 2, 0, \"div\", 7)(9, ProvidersComponent_div_9_Template, 2, 1, \"div\", 8);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 4);\n        i0.ɵɵtemplate(11, ProvidersComponent_div_11_Template, 3, 0, \"div\", 9);\n        i0.ɵɵelementStart(12, \"h2\", 6);\n        i0.ɵɵtext(13, \"Add Providers\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"div\", 10)(15, \"div\", 11);\n        i0.ɵɵelement(16, \"fa-icon\", 12);\n        i0.ɵɵelementStart(17, \"div\")(18, \"p\", 13);\n        i0.ɵɵtext(19, \"Form Instructions\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"ul\", 14)(21, \"li\");\n        i0.ɵɵtext(22, \"Fill in all required fields marked with an asterisk (*).\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"li\", 15);\n        i0.ɵɵtext(24, \" If you select Provider with ID 4, the Portfolio ID field becomes required. \");\n        i0.ɵɵelementStart(25, \"div\", 16);\n        i0.ɵɵelement(26, \"fa-icon\", 17);\n        i0.ɵɵelementStart(27, \"div\", 18);\n        i0.ɵɵelement(28, \"img\", 19);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(29, \"li\");\n        i0.ɵɵtext(30, \"Make sure to select one or more stations from the available list.\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(31, \"form\", 20);\n        i0.ɵɵlistener(\"ngSubmit\", function ProvidersComponent_Template_form_ngSubmit_31_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(32, \"div\", 21);\n        i0.ɵɵtemplate(33, ProvidersComponent_div_33_Template, 21, 6, \"div\", 22);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(34, \"button\", 23);\n        i0.ɵɵlistener(\"click\", function ProvidersComponent_Template_button_click_34_listener() {\n          return ctx.addProvider();\n        });\n        i0.ɵɵtext(35, \" + Add Provider \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(36, ProvidersComponent_button_36_Template, 2, 0, \"button\", 24);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"model\", i0.ɵɵpureFunction1(9, _c1, i0.ɵɵpureFunction0(8, _c0)))(\"home\", i0.ɵɵpureFunction0(11, _c2));\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngIf\", ctx.userProviders.length === 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.userProviders.length > 0);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.userProviders.length == 0);\n        i0.ɵɵadvance(20);\n        i0.ɵɵproperty(\"formGroup\", ctx.userProvidersForm);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.providers.controls);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.userProvidersForm.valid);\n      }\n    },\n    dependencies: [i7.NgForOf, i7.NgIf, i5.ɵNgNoValidate, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.FormGroupDirective, i5.FormControlName, i5.FormGroupName, i5.FormArrayName, i8.Breadcrumb, i9.FaIconComponent],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "provider_r6", "configuration", "Stations", "StationName", "ɵɵtemplate", "ProvidersComponent_div_9_div_1_div_17_div_3_Template", "ɵɵproperty", "length", "ProvidersComponent_div_9_div_1_div_17_Template", "ɵɵlistener", "ProvidersComponent_div_9_div_1_Template_button_click_19_listener", "restoredCtx", "ɵɵrestoreView", "_r13", "i_r7", "index", "ctx_r12", "ɵɵnextContext", "ɵɵresetView", "edit<PERSON><PERSON><PERSON>", "ProvidersComponent_div_9_div_1_Template_button_click_21_listener", "ctx_r14", "removeProvider", "ɵɵtextInterpolate", "ctx_r5", "getSelectedProviderName", "providerId", "Username", "Password", "ProvidersComponent_div_9_div_1_Template", "ctx_r1", "userProviders", "p_r22", "id", "name", "ɵɵelement", "station_r24", "ProvidersComponent_div_33_div_17_option_4_Template", "tmp_0_0", "provider_r15", "get", "value", "ProvidersComponent_div_33_button_20_Template_button_click_0_listener", "_r28", "i_r16", "ctx_r26", "getStations", "ProvidersComponent_div_33_option_6_Template", "ProvidersComponent_div_33_div_15_Template", "ProvidersComponent_div_33_div_16_Template", "ProvidersComponent_div_33_div_17_Template", "ProvidersComponent_div_33_Template_button_click_18_listener", "_r30", "ctx_r29", "ProvidersComponent_div_33_button_20_Template", "ctx_r3", "availableProviders", "tmp_2_0", "tmp_3_0", "tmp_4_0", "tmp_5_0", "userProvidersForm", "valid", "ProvidersComponent", "constructor", "layoutService", "stationsService", "providersService", "messageService", "fb", "cacheService", "stations", "tooltipVisible", "group", "providers", "array", "ngOnInit", "getProviders", "then", "data", "getUserProviders", "addProvider", "providerGroup", "required", "username", "password", "portfolioId", "ftpUrl", "selectedStation", "push", "valueChanges", "subscribe", "portfolioIdControl", "setValidators", "clearValidators", "updateValueAndValidity", "find", "p", "removeAt", "at", "request", "console", "log", "patchValue", "setValue", "onSubmit", "invalid", "mark<PERSON>llAsTouched", "saveUserProviders", "needsPortfolio", "for<PERSON>ach", "up", "JSON", "parse", "getUserStations", "setStations", "_", "ɵɵdirectiveInject", "i1", "LayoutService", "i2", "StationsService", "i3", "ProvidersService", "i4", "MessageService", "i5", "FormBuilder", "i6", "CacheService", "_2", "selectors", "decls", "vars", "consts", "template", "ProvidersComponent_Template", "rf", "ctx", "ProvidersComponent_div_8_Template", "ProvidersComponent_div_9_Template", "ProvidersComponent_div_11_Template", "ProvidersComponent_Template_form_ngSubmit_31_listener", "ProvidersComponent_div_33_Template", "ProvidersComponent_Template_button_click_34_listener", "ProvidersComponent_button_36_Template", "ɵɵpureFunction1", "_c1", "ɵɵpureFunction0", "_c0", "_c2", "controls"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\providers\\providers.component.ts", "D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\providers\\providers.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { MessageService } from 'primeng/api';\r\nimport { LayoutService } from '../../../layout/service/app.layout.service';\r\n\r\nimport { GetStationsRequest, SaveUserProvidersRequest } from '../../api/requests';\r\nimport { IProvider, IUserProvider, IUserProviderConfiguration } from '../../api/responses';\r\nimport { Station } from '../../api/station';\r\nimport { CacheService } from '../../service/cache.service';\r\nimport { ProvidersService } from '../../service/providers.service';\r\nimport { StationsService } from '../../service/stations.service';\r\nimport { FaIconLibrary, FontAwesomeModule } from '@fortawesome/angular-fontawesome';\r\n\r\n@Component({\r\n    selector: 'app-providers',\r\n    templateUrl: './providers.component.html'\r\n})\r\nexport class ProvidersComponent {\r\n\r\n    availableProviders:IProvider[] = [];\r\n    userProviders:IUserProviderConfiguration[] = [];\r\n    userProvidersForm: FormGroup;\r\n    stations: Station[] =[];\r\n    tooltipVisible = false\r\n\r\n    constructor(public layoutService: LayoutService, \r\n        private stationsService: StationsService,\r\n        private providersService: ProvidersService,\r\n        private messageService: MessageService,\r\n        private fb: FormBuilder,\r\n        private cacheService: CacheService) {\r\n        \r\n        this.userProvidersForm = this.fb.group({\r\n          providers: this.fb.array([])\r\n        });\r\n\r\n    }\r\n\r\n    ngOnInit(){\r\n        this.providersService.getProviders().then(data => {\r\n            this.availableProviders = data;\r\n          });\r\n\r\n          this.getUserProviders();\r\n\r\n          this.addProvider(); // Ξεκινάμε με μία προεπιλεγμένη σειρά\r\n    }\r\n\r\n\r\n    addProvider() {\r\n        const providerGroup = this.fb.group({\r\n          providerId: ['', Validators.required],\r\n          username: ['', Validators.required],\r\n          password: ['', Validators.required],\r\n          portfolioId: [''],\r\n          ftpUrl: [''],\r\n          stations: [[]], // Αρχικά empty array για το multiSelect,\r\n          selectedStation:['']\r\n        });\r\n        this.providers.push(providerGroup);\r\n\r\n        // Παρακολούθηση του providerId\r\n        providerGroup.get('providerId')?.valueChanges.subscribe((value) => {\r\n          const portfolioIdControl = providerGroup.get('portfolioId');\r\n\r\n          if (value === '4') {\r\n            portfolioIdControl?.setValidators(Validators.required);\r\n          } else {\r\n            portfolioIdControl?.clearValidators();\r\n          }\r\n\r\n          portfolioIdControl?.updateValueAndValidity();\r\n        });\r\n      }\r\n  \r\n      get providers(): FormArray {\r\n        return this.userProvidersForm.get('providers') as FormArray;\r\n      }\r\n\r\n      getSelectedProviderName(id:number): string | undefined {\r\n        return this.availableProviders.find(p => p.id == id).name;\r\n      }\r\n\r\n      \r\n    \r\n  \r\n  \r\n      removeProvider(index: number) {\r\n        this.providers.removeAt(index);\r\n      }\r\n  \r\n      getStations(index: number) {\r\n        const providerId = this.providers.at(index).get('providerId')?.value;\r\n    \r\n        if (!providerId) return;\r\n\r\n        let request: GetStationsRequest = {\r\n          providerId : this.providers.at(index).get('providerId')?.value,\r\n          username: this.providers.at(index).get('username')?.value, \r\n          password: this.providers.at(index).get('password')?.value,\r\n          portfolioId: this.providers.at(index).get('portfolioId')?.value,\r\n          ftpUrl: this.providers.at(index).get('ftpUrl')?.value\r\n        }\r\n        console.log('Form Data:', request);\r\n  \r\n        this.stationsService.getStations(request).then(data => {\r\n          this.providers.at(index).patchValue({ stations: data });\r\n        });\r\n\r\n        const providerGroup = this.providers.at(index);\r\n        \r\n      \r\n        if (providerId === 4) {\r\n          providerGroup.get('portfolioId')?.setValidators(Validators.required);\r\n        }if (providerId === 6) {\r\n          providerGroup.get('ftpUrl')?.setValidators(Validators.required);\r\n        } else {\r\n          providerGroup.get('portfolioId')?.clearValidators();\r\n          providerGroup.get('portfolioId')?.setValue('');\r\n        }\r\n        providerGroup.get('portfolioId')?.updateValueAndValidity();\r\n        //providerGroup.get('ftpUrl')?.updateValueAndValidity();\r\n\r\n      }\r\n    \r\n      onSubmit() {\r\n        if (this.userProvidersForm.invalid) {\r\n          this.userProvidersForm.markAllAsTouched();\r\n          return;\r\n        }\r\n        if (this.userProvidersForm.valid) {\r\n          console.log('Form Data:', this.userProvidersForm.value.providers);\r\n          // Εδώ μπορείς να κάνεις POST τα δεδομένα στο API\r\n          let request:SaveUserProvidersRequest = {\r\n            providers : this.userProvidersForm.value.providers\r\n          };\r\n  \r\n          this.providersService.saveUserProviders(request).then(data => {\r\n            console.log(data);  \r\n            this.getUserProviders();\r\n          });\r\n  \r\n        }\r\n\r\n        \r\n      }\r\n\r\n      needsPortfolio(index:number): boolean{\r\n        return this.providers && this.providers.length > index && this.providers.at(index).get('providerId')?.value === 4;\r\n      }\r\n  \r\n      getUserProviders(){\r\n        this.providersService.getUserProviders().then(data => {\r\n          this.userProviders = data;\r\n          console.log(this.userProviders)\r\n          this.userProviders.forEach(up => {\r\n            up.configuration = JSON.parse(up.configuration);\r\n\r\n          })\r\n          if (data.length > 0){\r\n            this.stationsService.getUserStations().then(data => {\r\n              this.stations = data;\r\n              this.cacheService.setStations(this.stations);\r\n            });\r\n          }\r\n        });\r\n      }\r\n    \r\n}\r\n", "<div class=\"grid\">\r\n    <div class=\"col-12\" >\r\n        <p-breadcrumb [model]=\"[{ label: 'Providers' }]\" [home]=\"{icon: 'pi pi-home'}\"></p-breadcrumb>\r\n    </div>\r\n    <div class=\"col-12 lg:col-12 xl:col-12\">\r\n\t\t<div class=\"card p-4\">\r\n            <div class=\"container\">\r\n                <h2 class=\"text-xl font-semibold mb-4\">Your Providers</h2>\r\n                \r\n                <div *ngIf=\"userProviders.length === 0\" class=\"alert alert-info\">\r\n                  You are not registered to any provider. You can start adding providers now!\r\n                </div>\r\n              \r\n                <div *ngIf=\"userProviders.length > 0\">\r\n                  <div *ngFor=\"let provider of userProviders; let i = index\" class=\"mb-4 border p-4 rounded shadow\">\r\n                    <div class=\"grid grid-cols-3 gap-4\">\r\n                      <!-- Provider Info -->\r\n                      <div>\r\n                        <label class=\"block font-medium mb-1\">Provider</label>\r\n                        <div class=\"font-medium\">{{ getSelectedProviderName(provider.providerId) }}</div>\r\n                      </div>\r\n              \r\n                      <!-- Username Info -->\r\n                      <div ngId=\"provider.configuration\">\r\n                        <label class=\"block font-medium mb-1\">Username</label>\r\n                        <div>{{ provider.configuration.Username }}</div>\r\n                      </div>\r\n              \r\n                      <!-- Password Info -->\r\n                      <div>\r\n                        <label class=\"block font-medium mb-1\">Password</label>\r\n                        <div> {{ provider.configuration.Password }}</div>\r\n                      </div>\r\n\r\n\t\t\t\t\t  <div *ngIf=\"provider.configuration.Stations\">\r\n                        <label class=\"block font-medium mb-1\">Station</label>\r\n                        <div *ngIf=\"provider.configuration.Stations.length > 0\"> {{provider.configuration.Stations[0].StationName}} </div>\r\n                      </div>\r\n\t\t\t\t\t  <div class=\"mt-3\">\r\n                      <button type=\"button\" disabled class=\"bg-yellow-500 text-white py-2 px-4 rounded mr-2\"\r\n                              (click)=\"editProvider(i)\">\r\n                        Edit\r\n                      </button>\r\n                      <button type=\"button\" [disabled]=\"true\" class=\"bg-red-500 text-white py-2 px-4 rounded\"\r\n                              (click)=\"removeProvider(i)\">\r\n                        Remove\r\n                      </button>\r\n                    </div>\r\n                    </div>\r\n              \r\n                    \r\n                  </div>\r\n                </div>\r\n              </div>\r\n              \r\n        </div>\r\n\r\n        <div class=\"card p-4\">\r\n\t\t  <div *ngIf=\"userProviders.length == 0\" class=\"text-center text-lg\">\r\n\t\t\t<p class=\"text-red-500 font-semibold\">\r\n\t\t\t  You are not registered to any provider. You can start adding providers now!\r\n\t\t\t</p>\r\n\t\t  </div>\r\n          <h2 class=\"text-xl font-semibold mb-4\">Add Providers</h2>\r\n\t\t  <div class=\"bg-blue-50 border-l-4 border-blue-400 text-blue-700 p-4 rounded mb-4 text-sm relative\">\r\n\t\t\t<div class=\"flex items-start\">\r\n\t\t\t\t<fa-icon icon=\"circle-info\" size=\"8x\" ></fa-icon>\r\n\t\t\t\t<div>\r\n\t\t\t\t<p class=\"font-semibold mb-1\">Form Instructions</p>\r\n\t\t\t\t<ul class=\"list-disc list-inside space-y-1\">\r\n\t\t\t\t\t<li>Fill in all required fields marked with an asterisk (*).</li>\r\n<li class=\"relative flex items-center gap-2\">\r\n  If you select Provider with ID 4, the Portfolio ID field becomes required.\r\n  <div class=\"icon-wrapper relative cursor-pointer text-blue-500\">\r\n  <fa-icon icon=\"circle-question\"></fa-icon>\r\n  <div class=\"tooltip absolute top-6 left-0 hidden bg-white border border-gray-300 p-2 shadow-lg rounded w-48 h-48\">\r\n    <img src=\"assets/layout/images/logo-dark.png\" alt=\"Tooltip image\" class=\"w-full h-full object-contain rounded\" />\r\n  </div>\r\n</div>\r\n</li>\r\n\r\n\r\n\r\n\t\t\t\t\t<li>Make sure to select one or more stations from the available list.</li>\r\n\t\t\t\t</ul>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t   </div>\r\n\r\n\r\n\r\n          <!-- Dynamic Form -->\r\n\t\t  <form [formGroup]=\"userProvidersForm\" (ngSubmit)=\"onSubmit()\">\r\n\t\t\t<div formArrayName=\"providers\">\r\n\t\t\t  <div *ngFor=\"let provider of providers.controls; let i = index\" [formGroupName]=\"i\" class=\"mb-4 border p-3 rounded shadow\">\r\n\t\t\t\t<div class=\"grid grid-cols-3 gap-4\">\r\n\t\t\t\t  <!-- Dropdown για επιλογή provider -->\r\n\t\t\t\t  <div>\r\n\t\t\t\t\t<label class=\"block font-medium mb-1\">Provider*</label>\r\n\t\t\t\t\t<select formControlName=\"providerId\" class=\"w-full p-2 border rounded\">\r\n\t\t\t\t\t  <option *ngFor=\"let p of availableProviders\" [value]=\"p.id\">{{ p.name }}</option>\r\n\t\t\t\t\t</select>\r\n\t\t\t\t  </div>\r\n\t  \r\n\t\t\t\t  <!-- Input για username -->\r\n\t\t\t\t  <div>\r\n\t\t\t\t\t<label class=\"block font-medium mb-1\">Username*</label>\r\n\t\t\t\t\t<input type=\"text\" formControlName=\"username\" class=\"w-full p-2 border rounded\" placeholder=\"Enter username\">\r\n\t\t\t\t  </div>\r\n\t  \r\n\t\t\t\t  <!-- Input για API Key -->\r\n\t\t\t\t  <div>\r\n\t\t\t\t\t<label class=\"block font-medium mb-1\">Password*</label>\r\n\t\t\t\t\t<input type=\"text\" formControlName=\"password\" class=\"w-full p-2 border rounded\" placeholder=\"Enter Password\">\r\n\t\t\t\t  </div>\r\n\t\t\t\t  <div *ngIf=\"provider.get('providerId')?.value == 4\">\r\n\t\t\t\t\t<label class=\"block font-medium mb-1\">Portfolio Id*</label>\r\n\t\t\t\t\t<input type=\"text\" formControlName=\"portfolioId\" class=\"w-full p-2 border rounded\" placeholder=\"Enter Your Portfolio Id\">\r\n\t\t\t\t  </div>\r\n\t\t\t\t  <div *ngIf=\"provider.get('providerId')?.value == 6\">\r\n\t\t\t\t\t<label class=\"block font-medium mb-1\">FTPUrl* (must begin with ftp://)</label>\r\n\t\t\t\t\t<input type=\"text\" formControlName=\"ftpUrl\" class=\"w-full p-2 border rounded\" placeholder=\"Enter the FTP Url\">\r\n\t\t\t\t  </div>\r\n\t\t\t\t  <!-- Dropdown για επιλογή σταθμού (εμφανίζεται μόνο αν υπάρχουν stations) -->\r\n\t\t\t\t\t<div *ngIf=\"provider.get('stations')?.value.length > 0\">\r\n\t\t\t\t\t\t<label class=\"block font-medium mt-2\">Select Station*</label>\r\n\t\t\t\t\t\t<select formControlName=\"selectedStation\" class=\"w-full p-2 border rounded\">\r\n\t\t\t\t\t\t<option *ngFor=\"let station of provider.get('stations')?.value\" [value]=\"station.id\">{{ station.name }}</option>\r\n\t\t\t\t\t\t</select>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t  \r\n\t\t\t\t</div>\r\n\t  \r\n\t\t\t\t<!-- Remove Button -->\r\n\t\t\t\t<button type=\"button\" class=\"mt-2 text-red-500\" (click)=\"removeProvider(i)\">\r\n\t\t\t\t  Remove\r\n\t\t\t\t</button>\r\n\t\t\t\t<button type=\"button\" class=\"mt-2 text-blue-500\" \r\n\t\t\t\t\t\t*ngIf=\"provider.get('providerId')?.value && userProvidersForm.valid\" \r\n\t\t\t\t\t\t(click)=\"getStations(i)\">\r\n\t\t\t\t\t\tGet Stations\r\n\t\t\t\t</button>\r\n\r\n\t\t\t\t\r\n\t\t\t  </div>\r\n\t\t\t</div>\r\n\t  \r\n\t\t\t<!-- Add Provider Button -->\r\n\t\t\t<button type=\"button\" class=\"mt-4 bg-blue-500 text-white p-2 rounded\" (click)=\"addProvider()\">\r\n\t\t\t  + Add Provider\r\n\t\t\t</button>\r\n\t  \r\n\t\t\t<!-- Submit Button -->\r\n\t\t\t<button *ngIf=\"userProvidersForm.valid\" type=\"submit\" class=\"mt-4 bg-green-500 text-white p-2 rounded\"\r\n        \t\t>\r\n\t\t\t\tSave Providers\r\n\t\t\t</button>\r\n\t\t  </form>\r\n\t\t</div>\r\n\t  </div>\r\n</div>"], "mappings": "AACA,SAA4CA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;ICQ9DC,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAE,MAAA,oFACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAyBEH,EAAA,CAAAC,cAAA,UAAwD;IAACD,EAAA,CAAAE,MAAA,GAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAzDH,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAK,kBAAA,MAAAC,WAAA,CAAAC,aAAA,CAAAC,QAAA,IAAAC,WAAA,MAAmD;;;;;IAF7HT,EAAA,CAAAC,cAAA,UAA6C;IACUD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrDH,EAAA,CAAAU,UAAA,IAAAC,oDAAA,iBAAkH;IACpHX,EAAA,CAAAG,YAAA,EAAM;;;;IADEH,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAY,UAAA,SAAAN,WAAA,CAAAC,aAAA,CAAAC,QAAA,CAAAK,MAAA,KAAgD;;;;;;IAtB5Db,EAAA,CAAAC,cAAA,cAAkG;IAItDD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtDH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAkD;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAInFH,EAAA,CAAAC,cAAA,cAAmC;IACKD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtDH,EAAA,CAAAC,cAAA,WAAK;IAAAD,EAAA,CAAAE,MAAA,IAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAIlDH,EAAA,CAAAC,cAAA,WAAK;IACmCD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtDH,EAAA,CAAAC,cAAA,WAAK;IAACD,EAAA,CAAAE,MAAA,IAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGlEH,EAAA,CAAAU,UAAA,KAAAI,8CAAA,iBAGqB;IACrBd,EAAA,CAAAC,cAAA,eAAkB;IAEKD,EAAA,CAAAe,UAAA,mBAAAC,iEAAA;MAAA,MAAAC,WAAA,GAAAjB,EAAA,CAAAkB,aAAA,CAAAC,IAAA;MAAA,MAAAC,IAAA,GAAAH,WAAA,CAAAI,KAAA;MAAA,MAAAC,OAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAF,OAAA,CAAAG,YAAA,CAAAL,IAAA,CAAe;IAAA,EAAC;IAC/BpB,EAAA,CAAAE,MAAA,cACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACoC;IAA5BD,EAAA,CAAAe,UAAA,mBAAAW,iEAAA;MAAA,MAAAT,WAAA,GAAAjB,EAAA,CAAAkB,aAAA,CAAAC,IAAA;MAAA,MAAAC,IAAA,GAAAH,WAAA,CAAAI,KAAA;MAAA,MAAAM,OAAA,GAAA3B,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAG,OAAA,CAAAC,cAAA,CAAAR,IAAA,CAAiB;IAAA,EAAC;IACjCpB,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IA3BkBH,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAA6B,iBAAA,CAAAC,MAAA,CAAAC,uBAAA,CAAAzB,WAAA,CAAA0B,UAAA,EAAkD;IAMtEhC,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAA6B,iBAAA,CAAAvB,WAAA,CAAAC,aAAA,CAAA0B,QAAA,CAAqC;IAMpCjC,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAK,kBAAA,MAAAC,WAAA,CAAAC,aAAA,CAAA2B,QAAA,KAAqC;IAGtDlC,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAY,UAAA,SAAAN,WAAA,CAAAC,aAAA,CAAAC,QAAA,CAAqC;IASNR,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAY,UAAA,kBAAiB;;;;;IA9B7CZ,EAAA,CAAAC,cAAA,UAAsC;IACpCD,EAAA,CAAAU,UAAA,IAAAyB,uCAAA,mBAqCM;IACRnC,EAAA,CAAAG,YAAA,EAAM;;;;IAtCsBH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAY,UAAA,YAAAwB,MAAA,CAAAC,aAAA,CAAkB;;;;;IA4C1DrC,EAAA,CAAAC,cAAA,cAAmE;IAElED,EAAA,CAAAE,MAAA,oFACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAuCAH,EAAA,CAAAC,cAAA,iBAA4D;IAAAD,EAAA,CAAAE,MAAA,GAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAApCH,EAAA,CAAAY,UAAA,UAAA0B,KAAA,CAAAC,EAAA,CAAc;IAACvC,EAAA,CAAAI,SAAA,GAAY;IAAZJ,EAAA,CAAA6B,iBAAA,CAAAS,KAAA,CAAAE,IAAA,CAAY;;;;;IAezExC,EAAA,CAAAC,cAAA,UAAoD;IACfD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC3DH,EAAA,CAAAyC,SAAA,gBAAyH;IACxHzC,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,UAAoD;IACfD,EAAA,CAAAE,MAAA,uCAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9EH,EAAA,CAAAyC,SAAA,gBAA8G;IAC7GzC,EAAA,CAAAG,YAAA,EAAM;;;;;IAKNH,EAAA,CAAAC,cAAA,iBAAqF;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAhDH,EAAA,CAAAY,UAAA,UAAA8B,WAAA,CAAAH,EAAA,CAAoB;IAACvC,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAA6B,iBAAA,CAAAa,WAAA,CAAAF,IAAA,CAAkB;;;;;IAHxGxC,EAAA,CAAAC,cAAA,UAAwD;IACjBD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7DH,EAAA,CAAAC,cAAA,iBAA4E;IAC5ED,EAAA,CAAAU,UAAA,IAAAiC,kDAAA,qBAAgH;IAChH3C,EAAA,CAAAG,YAAA,EAAS;;;;;IADmBH,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAY,UAAA,aAAAgC,OAAA,GAAAC,YAAA,CAAAC,GAAA,+BAAAF,OAAA,CAAAG,KAAA,CAAkC;;;;;;IAUhE/C,EAAA,CAAAC,cAAA,iBAE2B;IAAzBD,EAAA,CAAAe,UAAA,mBAAAiC,qEAAA;MAAAhD,EAAA,CAAAkB,aAAA,CAAA+B,IAAA;MAAA,MAAAC,KAAA,GAAAlD,EAAA,CAAAuB,aAAA,GAAAF,KAAA;MAAA,MAAA8B,OAAA,GAAAnD,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAA2B,OAAA,CAAAC,WAAA,CAAAF,KAAA,CAAc;IAAA,EAAC;IACxBlD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA/CRH,EAAA,CAAAC,cAAA,cAA2H;IAIrFD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvDH,EAAA,CAAAC,cAAA,iBAAuE;IACrED,EAAA,CAAAU,UAAA,IAAA2C,2CAAA,qBAAiF;IACnFrD,EAAA,CAAAG,YAAA,EAAS;IAIRH,EAAA,CAAAC,cAAA,UAAK;IACgCD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvDH,EAAA,CAAAyC,SAAA,iBAA6G;IAC5GzC,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IACgCD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvDH,EAAA,CAAAyC,SAAA,iBAA6G;IAC5GzC,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAU,UAAA,KAAA4C,yCAAA,iBAGM,KAAAC,yCAAA,sBAAAC,yCAAA;IAaRxD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,kBAA4E;IAA5BD,EAAA,CAAAe,UAAA,mBAAA0C,4DAAA;MAAA,MAAAxC,WAAA,GAAAjB,EAAA,CAAAkB,aAAA,CAAAwC,IAAA;MAAA,MAAAR,KAAA,GAAAjC,WAAA,CAAAI,KAAA;MAAA,MAAAsC,OAAA,GAAA3D,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAmC,OAAA,CAAA/B,cAAA,CAAAsB,KAAA,CAAiB;IAAA,EAAC;IACzElD,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAU,UAAA,KAAAkD,4CAAA,qBAIS;IAGR5D,EAAA,CAAAG,YAAA,EAAM;;;;;;;;;;IAlD0DH,EAAA,CAAAY,UAAA,kBAAAsC,KAAA,CAAmB;IAM3DlD,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAY,UAAA,YAAAiD,MAAA,CAAAC,kBAAA,CAAqB;IAetC9D,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAY,UAAA,WAAAmD,OAAA,GAAAlB,YAAA,CAAAC,GAAA,iCAAAiB,OAAA,CAAAhB,KAAA,OAA4C;IAI5C/C,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAY,UAAA,WAAAoD,OAAA,GAAAnB,YAAA,CAAAC,GAAA,iCAAAkB,OAAA,CAAAjB,KAAA,OAA4C;IAK7C/C,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAY,UAAA,WAAAqD,OAAA,GAAApB,YAAA,CAAAC,GAAA,+BAAAmB,OAAA,CAAAlB,KAAA,CAAAlC,MAAA,MAAgD;IAcpDb,EAAA,CAAAI,SAAA,GAAkE;IAAlEJ,EAAA,CAAAY,UAAA,WAAAsD,OAAA,GAAArB,YAAA,CAAAC,GAAA,iCAAAoB,OAAA,CAAAnB,KAAA,KAAAc,MAAA,CAAAM,iBAAA,CAAAC,KAAA,CAAkE;;;;;IAetEpE,EAAA,CAAAC,cAAA,iBACQ;IACPD,EAAA,CAAAE,MAAA,uBACD;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;;AD1IZ,OAAM,MAAOkE,kBAAkB;EAQ3BC,YAAmBC,aAA4B,EACnCC,eAAgC,EAChCC,gBAAkC,EAClCC,cAA8B,EAC9BC,EAAe,EACfC,YAA0B;IALnB,KAAAL,aAAa,GAAbA,aAAa;IACpB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,YAAY,GAAZA,YAAY;IAXxB,KAAAd,kBAAkB,GAAe,EAAE;IACnC,KAAAzB,aAAa,GAAgC,EAAE;IAE/C,KAAAwC,QAAQ,GAAa,EAAE;IACvB,KAAAC,cAAc,GAAG,KAAK;IASlB,IAAI,CAACX,iBAAiB,GAAG,IAAI,CAACQ,EAAE,CAACI,KAAK,CAAC;MACrCC,SAAS,EAAE,IAAI,CAACL,EAAE,CAACM,KAAK,CAAC,EAAE;KAC5B,CAAC;EAEN;EAEAC,QAAQA,CAAA;IACJ,IAAI,CAACT,gBAAgB,CAACU,YAAY,EAAE,CAACC,IAAI,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACvB,kBAAkB,GAAGuB,IAAI;IAChC,CAAC,CAAC;IAEF,IAAI,CAACC,gBAAgB,EAAE;IAEvB,IAAI,CAACC,WAAW,EAAE,CAAC,CAAC;EAC1B;;EAGAA,WAAWA,CAAA;IACP,MAAMC,aAAa,GAAG,IAAI,CAACb,EAAE,CAACI,KAAK,CAAC;MAClC/C,UAAU,EAAE,CAAC,EAAE,EAAEjC,UAAU,CAAC0F,QAAQ,CAAC;MACrCC,QAAQ,EAAE,CAAC,EAAE,EAAE3F,UAAU,CAAC0F,QAAQ,CAAC;MACnCE,QAAQ,EAAE,CAAC,EAAE,EAAE5F,UAAU,CAAC0F,QAAQ,CAAC;MACnCG,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,MAAM,EAAE,CAAC,EAAE,CAAC;MACZhB,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdiB,eAAe,EAAC,CAAC,EAAE;KACpB,CAAC;IACF,IAAI,CAACd,SAAS,CAACe,IAAI,CAACP,aAAa,CAAC;IAElC;IACAA,aAAa,CAAC1C,GAAG,CAAC,YAAY,CAAC,EAAEkD,YAAY,CAACC,SAAS,CAAElD,KAAK,IAAI;MAChE,MAAMmD,kBAAkB,GAAGV,aAAa,CAAC1C,GAAG,CAAC,aAAa,CAAC;MAE3D,IAAIC,KAAK,KAAK,GAAG,EAAE;QACjBmD,kBAAkB,EAAEC,aAAa,CAACpG,UAAU,CAAC0F,QAAQ,CAAC;OACvD,MAAM;QACLS,kBAAkB,EAAEE,eAAe,EAAE;;MAGvCF,kBAAkB,EAAEG,sBAAsB,EAAE;IAC9C,CAAC,CAAC;EACJ;EAEA,IAAIrB,SAASA,CAAA;IACX,OAAO,IAAI,CAACb,iBAAiB,CAACrB,GAAG,CAAC,WAAW,CAAc;EAC7D;EAEAf,uBAAuBA,CAACQ,EAAS;IAC/B,OAAO,IAAI,CAACuB,kBAAkB,CAACwC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChE,EAAE,IAAIA,EAAE,CAAC,CAACC,IAAI;EAC3D;EAMAZ,cAAcA,CAACP,KAAa;IAC1B,IAAI,CAAC2D,SAAS,CAACwB,QAAQ,CAACnF,KAAK,CAAC;EAChC;EAEA+B,WAAWA,CAAC/B,KAAa;IACvB,MAAMW,UAAU,GAAG,IAAI,CAACgD,SAAS,CAACyB,EAAE,CAACpF,KAAK,CAAC,CAACyB,GAAG,CAAC,YAAY,CAAC,EAAEC,KAAK;IAEpE,IAAI,CAACf,UAAU,EAAE;IAEjB,IAAI0E,OAAO,GAAuB;MAChC1E,UAAU,EAAG,IAAI,CAACgD,SAAS,CAACyB,EAAE,CAACpF,KAAK,CAAC,CAACyB,GAAG,CAAC,YAAY,CAAC,EAAEC,KAAK;MAC9D2C,QAAQ,EAAE,IAAI,CAACV,SAAS,CAACyB,EAAE,CAACpF,KAAK,CAAC,CAACyB,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK;MACzD4C,QAAQ,EAAE,IAAI,CAACX,SAAS,CAACyB,EAAE,CAACpF,KAAK,CAAC,CAACyB,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK;MACzD6C,WAAW,EAAE,IAAI,CAACZ,SAAS,CAACyB,EAAE,CAACpF,KAAK,CAAC,CAACyB,GAAG,CAAC,aAAa,CAAC,EAAEC,KAAK;MAC/D8C,MAAM,EAAE,IAAI,CAACb,SAAS,CAACyB,EAAE,CAACpF,KAAK,CAAC,CAACyB,GAAG,CAAC,QAAQ,CAAC,EAAEC;KACjD;IACD4D,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEF,OAAO,CAAC;IAElC,IAAI,CAAClC,eAAe,CAACpB,WAAW,CAACsD,OAAO,CAAC,CAACtB,IAAI,CAACC,IAAI,IAAG;MACpD,IAAI,CAACL,SAAS,CAACyB,EAAE,CAACpF,KAAK,CAAC,CAACwF,UAAU,CAAC;QAAEhC,QAAQ,EAAEQ;MAAI,CAAE,CAAC;IACzD,CAAC,CAAC;IAEF,MAAMG,aAAa,GAAG,IAAI,CAACR,SAAS,CAACyB,EAAE,CAACpF,KAAK,CAAC;IAG9C,IAAIW,UAAU,KAAK,CAAC,EAAE;MACpBwD,aAAa,CAAC1C,GAAG,CAAC,aAAa,CAAC,EAAEqD,aAAa,CAACpG,UAAU,CAAC0F,QAAQ,CAAC;;IACrE,IAAIzD,UAAU,KAAK,CAAC,EAAE;MACrBwD,aAAa,CAAC1C,GAAG,CAAC,QAAQ,CAAC,EAAEqD,aAAa,CAACpG,UAAU,CAAC0F,QAAQ,CAAC;KAChE,MAAM;MACLD,aAAa,CAAC1C,GAAG,CAAC,aAAa,CAAC,EAAEsD,eAAe,EAAE;MACnDZ,aAAa,CAAC1C,GAAG,CAAC,aAAa,CAAC,EAAEgE,QAAQ,CAAC,EAAE,CAAC;;IAEhDtB,aAAa,CAAC1C,GAAG,CAAC,aAAa,CAAC,EAAEuD,sBAAsB,EAAE;IAC1D;EAEF;;EAEAU,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC5C,iBAAiB,CAAC6C,OAAO,EAAE;MAClC,IAAI,CAAC7C,iBAAiB,CAAC8C,gBAAgB,EAAE;MACzC;;IAEF,IAAI,IAAI,CAAC9C,iBAAiB,CAACC,KAAK,EAAE;MAChCuC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACzC,iBAAiB,CAACpB,KAAK,CAACiC,SAAS,CAAC;MACjE;MACA,IAAI0B,OAAO,GAA4B;QACrC1B,SAAS,EAAG,IAAI,CAACb,iBAAiB,CAACpB,KAAK,CAACiC;OAC1C;MAED,IAAI,CAACP,gBAAgB,CAACyC,iBAAiB,CAACR,OAAO,CAAC,CAACtB,IAAI,CAACC,IAAI,IAAG;QAC3DsB,OAAO,CAACC,GAAG,CAACvB,IAAI,CAAC;QACjB,IAAI,CAACC,gBAAgB,EAAE;MACzB,CAAC,CAAC;;EAKN;EAEA6B,cAAcA,CAAC9F,KAAY;IACzB,OAAO,IAAI,CAAC2D,SAAS,IAAI,IAAI,CAACA,SAAS,CAACnE,MAAM,GAAGQ,KAAK,IAAI,IAAI,CAAC2D,SAAS,CAACyB,EAAE,CAACpF,KAAK,CAAC,CAACyB,GAAG,CAAC,YAAY,CAAC,EAAEC,KAAK,KAAK,CAAC;EACnH;EAEAuC,gBAAgBA,CAAA;IACd,IAAI,CAACb,gBAAgB,CAACa,gBAAgB,EAAE,CAACF,IAAI,CAACC,IAAI,IAAG;MACnD,IAAI,CAAChD,aAAa,GAAGgD,IAAI;MACzBsB,OAAO,CAACC,GAAG,CAAC,IAAI,CAACvE,aAAa,CAAC;MAC/B,IAAI,CAACA,aAAa,CAAC+E,OAAO,CAACC,EAAE,IAAG;QAC9BA,EAAE,CAAC9G,aAAa,GAAG+G,IAAI,CAACC,KAAK,CAACF,EAAE,CAAC9G,aAAa,CAAC;MAEjD,CAAC,CAAC;MACF,IAAI8E,IAAI,CAACxE,MAAM,GAAG,CAAC,EAAC;QAClB,IAAI,CAAC2D,eAAe,CAACgD,eAAe,EAAE,CAACpC,IAAI,CAACC,IAAI,IAAG;UACjD,IAAI,CAACR,QAAQ,GAAGQ,IAAI;UACpB,IAAI,CAACT,YAAY,CAAC6C,WAAW,CAAC,IAAI,CAAC5C,QAAQ,CAAC;QAC9C,CAAC,CAAC;;IAEN,CAAC,CAAC;EACJ;EAAC,QAAA6C,CAAA,G;qBArJMrD,kBAAkB,EAAArE,EAAA,CAAA2H,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA7H,EAAA,CAAA2H,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA/H,EAAA,CAAA2H,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAjI,EAAA,CAAA2H,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAnI,EAAA,CAAA2H,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAArI,EAAA,CAAA2H,iBAAA,CAAAW,EAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlBnE,kBAAkB;IAAAoE,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QClB/B/I,EAAA,CAAAC,cAAA,aAAkB;QAEVD,EAAA,CAAAyC,SAAA,sBAA8F;QAClGzC,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,aAAwC;QAGWD,EAAA,CAAAE,MAAA,qBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAE1DH,EAAA,CAAAU,UAAA,IAAAuI,iCAAA,iBAEM,IAAAC,iCAAA;QA0CRlJ,EAAA,CAAAG,YAAA,EAAM;QAIZH,EAAA,CAAAC,cAAA,cAAsB;QAC1BD,EAAA,CAAAU,UAAA,KAAAyI,kCAAA,iBAIM;QACAnJ,EAAA,CAAAC,cAAA,aAAuC;QAAAD,EAAA,CAAAE,MAAA,qBAAa;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC/DH,EAAA,CAAAC,cAAA,eAAmG;QAEnGD,EAAA,CAAAyC,SAAA,mBAAiD;QACjDzC,EAAA,CAAAC,cAAA,WAAK;QACyBD,EAAA,CAAAE,MAAA,yBAAiB;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACnDH,EAAA,CAAAC,cAAA,cAA4C;QACvCD,EAAA,CAAAE,MAAA,gEAAwD;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACtEH,EAAA,CAAAC,cAAA,cAA6C;QAC3CD,EAAA,CAAAE,MAAA,oFACA;QAAAF,EAAA,CAAAC,cAAA,eAAgE;QAChED,EAAA,CAAAyC,SAAA,mBAA0C;QAC1CzC,EAAA,CAAAC,cAAA,eAAkH;QAChHD,EAAA,CAAAyC,SAAA,eAAiH;QACnHzC,EAAA,CAAAG,YAAA,EAAM;QAMHH,EAAA,CAAAC,cAAA,UAAI;QAAAD,EAAA,CAAAE,MAAA,yEAAiE;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAS3EH,EAAA,CAAAC,cAAA,gBAA8D;QAAxBD,EAAA,CAAAe,UAAA,sBAAAqI,sDAAA;UAAA,OAAYJ,GAAA,CAAAjC,QAAA,EAAU;QAAA,EAAC;QAC9D/G,EAAA,CAAAC,cAAA,eAA+B;QAC7BD,EAAA,CAAAU,UAAA,KAAA2I,kCAAA,mBAkDM;QACRrJ,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAC,cAAA,kBAA8F;QAAxBD,EAAA,CAAAe,UAAA,mBAAAuI,qDAAA;UAAA,OAASN,GAAA,CAAAzD,WAAA,EAAa;QAAA,EAAC;QAC3FvF,EAAA,CAAAE,MAAA,wBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAGTH,EAAA,CAAAU,UAAA,KAAA6I,qCAAA,qBAGS;QACRvJ,EAAA,CAAAG,YAAA,EAAO;;;QA3JWH,EAAA,CAAAI,SAAA,GAAkC;QAAlCJ,EAAA,CAAAY,UAAA,UAAAZ,EAAA,CAAAwJ,eAAA,IAAAC,GAAA,EAAAzJ,EAAA,CAAA0J,eAAA,IAAAC,GAAA,GAAkC,SAAA3J,EAAA,CAAA0J,eAAA,KAAAE,GAAA;QAOlC5J,EAAA,CAAAI,SAAA,GAAgC;QAAhCJ,EAAA,CAAAY,UAAA,SAAAoI,GAAA,CAAA3G,aAAA,CAAAxB,MAAA,OAAgC;QAIhCb,EAAA,CAAAI,SAAA,GAA8B;QAA9BJ,EAAA,CAAAY,UAAA,SAAAoI,GAAA,CAAA3G,aAAA,CAAAxB,MAAA,KAA8B;QA6C1Cb,EAAA,CAAAI,SAAA,GAA+B;QAA/BJ,EAAA,CAAAY,UAAA,SAAAoI,GAAA,CAAA3G,aAAA,CAAAxB,MAAA,MAA+B;QAkC/Bb,EAAA,CAAAI,SAAA,IAA+B;QAA/BJ,EAAA,CAAAY,UAAA,cAAAoI,GAAA,CAAA7E,iBAAA,CAA+B;QAEVnE,EAAA,CAAAI,SAAA,GAAuB;QAAvBJ,EAAA,CAAAY,UAAA,YAAAoI,GAAA,CAAAhE,SAAA,CAAA6E,QAAA,CAAuB;QA2D1C7J,EAAA,CAAAI,SAAA,GAA6B;QAA7BJ,EAAA,CAAAY,UAAA,SAAAoI,GAAA,CAAA7E,iBAAA,CAAAC,KAAA,CAA6B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}