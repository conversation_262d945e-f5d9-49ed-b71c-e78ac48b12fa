﻿using Auth.API.HuaweiModels;
using Auth.API.Interfaces;
using Auth.API.Models;
using Auth.API.Repositories.Interfaces;
using Auth.API.Repository;
using Auth.API.Repository.Models;
using Auth.API.SMAModels;
using Auth.API.Types;
using Auth.Demo.Models;
using OpenQA.Selenium;
using OpenQA.Selenium.DevTools.V132.Network;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace Auth.API.Implementation
{
    public class ApiService : IApiService
    {

        private readonly IUserService _userService;
        private readonly IProviderRepositoryService _providerRepositoryService;
        private readonly HuaweiApiService _huaweiApiService;
        private readonly AuroraApiService _auroraApiService;
        private readonly RefulogApiService _refulogApiService;
        private readonly IDataRepositoryService _dataRepositoryService;

        public ApiService(IUserService userService, IProviderRepositoryService providerRepositoryService, 
            HuaweiApiService huaweiApiService, 
            AuroraApiService auroraApiService,
            RefulogApiService refulogApiService,
            IDataRepositoryService dataRepositoryService) {
            _userService = userService;
            _providerRepositoryService = providerRepositoryService;
            _huaweiApiService = huaweiApiService;
            _auroraApiService = auroraApiService;
            _refulogApiService = refulogApiService;
            _dataRepositoryService = dataRepositoryService;
        }

        //get stations from specific provider
        public async Task<List<Station>> GetStationsAsync(string userId, GetStationsRequest request)
        {
            var userProviders = await _userService.GetUserProvidersAsync(Guid.Parse(userId));
            var providers = await _providerRepositoryService.GetAllProvidersAsync();

            List<Station> stationsResponse = new List<Station>();

            
                
            switch (request.ProviderId)
            {
                //Huawei
                case 1:
                    var userHuaweiStations = await _huaweiApiService.GetStationsAsync(request.Username, request.Password, providers.FirstOrDefault(p=> p.Id == request.ProviderId).BaseUrl);
                    stationsResponse.AddRange(userHuaweiStations);
                    break;
                case 4:
                    // aurora api call
                    var userAuroraStations = await _auroraApiService.GetStationsAsync(request.Username, request.Password, request.PortfolioId, providers.FirstOrDefault(p => p.Id == request.ProviderId).BaseUrl);
                    stationsResponse.AddRange(userAuroraStations);
                    break;

                case 5:
                    //Refulog api call
                    var userRefulogStations = await _refulogApiService.GetStationsAsync(request.Username, request.Password, providers.FirstOrDefault(p => p.Id == request.ProviderId).BaseUrl);
                    stationsResponse.AddRange(userRefulogStations);
                    break;
                case 6:
                    //SMA(Ftp) api call
                    var userSMAStations = GetSMAStations(request.ftpUrl, request.Username, request.Password);
                    stationsResponse.AddRange(userSMAStations);
                    break;
                default:

                    break;
            }
            

            return stationsResponse;
        }

        //only needed for Huawei
        public async Task<List<Device>> GetStationDevicesWithCredsAsync(string providerId, string userId, string stationId, string username, string password) {
            var providers = await _providerRepositoryService.GetAllProvidersAsync();
            List<Device> devicesResponse = new List<Device>();

            switch (providerId)
            {
                //Huawei
                case "1":
                    var husaweiStationDevices = await _huaweiApiService.GetStationDevicesAsync(stationId, username, password, providers.FirstOrDefault(p => p.Id == Int32.Parse(providerId)).BaseUrl);
                    devicesResponse.AddRange(husaweiStationDevices);
                    break;
                case "4":
                    // aurora api call
                    var auroraStationDevices = await _auroraApiService.GetStationDevicesAsync(stationId, username, password, providers.FirstOrDefault(p => p.Id == Int32.Parse(providerId)).BaseUrl);
                    devicesResponse.AddRange(auroraStationDevices);
                    break;

                case "5":
                    //Refulog api call
                    var refulogStationDevices = await _refulogApiService.GetStationDevicesAsync(stationId, username, password, providers.FirstOrDefault(p => p.Id == Int32.Parse(providerId)).BaseUrl);
                    devicesResponse.AddRange(refulogStationDevices);
                    break;
                default:
                    break;
            }


            
            return devicesResponse;
        }

        public async Task<List<Device>> GetStationDevicesAsync(string userId, string stationId)
        {
            var userProviders = await _userService.GetUserProvidersAsync(Guid.Parse(userId));
            var providers = await _providerRepositoryService.GetAllProvidersAsync();

            List<Device> devicesResponse = new List<Device>();
            
            var userProvider = userProviders.FirstOrDefault(up => JsonSerializer.Deserialize<UserProviderConfiguration>(up.Configuration).Stations.Any(s => s.StationId == stationId));

            if (userProvider is null)
                throw new NotFoundException("Station not found");

            var userConfiguration = JsonSerializer.Deserialize<UserProviderConfiguration>(userProvider.Configuration);

            switch (userProvider.ProviderId)
            {
                //Huawei
                case 1:
                    var husaweiStationDevices = await _huaweiApiService.GetStationDevicesAsync(stationId, userConfiguration.Username, userConfiguration.Password, providers.FirstOrDefault(p => p.Id == userProvider.ProviderId).BaseUrl);
                    devicesResponse.AddRange(husaweiStationDevices);
                    break;
                case 4:
                    var auroraStationDevices = await _auroraApiService.GetStationDevicesAsync(stationId, userConfiguration.Username, userConfiguration.Password, providers.FirstOrDefault(p => p.Id == userProvider.ProviderId).BaseUrl);
                    devicesResponse.AddRange(auroraStationDevices);
                    break;

                case 5:
                    var refulogStationDevices = await _refulogApiService.GetStationDevicesAsync(stationId, userConfiguration.Username, userConfiguration.Password, providers.FirstOrDefault(p => p.Id == userProvider.ProviderId).BaseUrl);
                    devicesResponse.AddRange(refulogStationDevices);
                    break;
                default:

                    break;
            }


            return devicesResponse;
        }

        public async Task<SumData> GetSumData(string userId, string stationId)
        {
            var userProviders = await _userService.GetUserProvidersAsync(Guid.Parse(userId));
            var providers = await _providerRepositoryService.GetAllProvidersAsync();

            var userProvider = userProviders.FirstOrDefault(up => JsonSerializer.Deserialize<UserProviderConfiguration>(up.Configuration).Stations.Any(s => s.StationId == stationId));

            if (userProvider is null)
                throw new NotFoundException("Station not found");

            var userConfiguration = JsonSerializer.Deserialize<UserProviderConfiguration>(userProvider.Configuration);

            switch (userProvider.ProviderId)
            {
                //Huawei
                case 1:
                    var stationsSumData = await _huaweiApiService.GetSumData(stationId, userConfiguration.Username, userConfiguration.Password, providers.FirstOrDefault(p => p.Id == userProvider.ProviderId).BaseUrl);
                    return stationsSumData;
                    break;
                case 4:
                    //TODO aurora api call
                    return null;
                    break;

                case 5:
                    //TODO refulog api call
                    return null;
                    break;
                default:
                    return null;
                    break;
            }
        }

        public async Task<HistoricTimeData> GetHistoricTimeData(string userId, GetDataWithTimestampRequest request)
        {
            var userProviders = await _userService.GetUserProvidersAsync(Guid.Parse(userId));
            var providers = await _providerRepositoryService.GetAllProvidersAsync();


            var userProvider = userProviders.FirstOrDefault(up => JsonSerializer.Deserialize<UserProviderConfiguration>(up.Configuration).Stations.Any(s => s.StationId == request.StationId));

            if (userProvider is null)
                throw new NotFoundException("Devices not found");

            

            switch (userProvider.ProviderId)
            {
                //Huawei
                case 1:
                    var huaweiHistoricData = await GetHuaweiHistoricTimeData(request, userProvider, providers);
                    return huaweiHistoricData;
                    break;
                //case 4:
                //    //aurora api call
                //    var historicData = await GetHuaweiHistoricTimeData(request, userProvider, providers);
                //    return historicData;
                //    break;

                //case 5:
                //    //TODO refulog api call
                //    return null;
                //    break;
                default:
                    var historicData = await GetHistoricTimeData(request, userProvider, providers);
                    return historicData;
                    break;
            }

           
        }


        private async Task<HistoricTimeData> GetHuaweiHistoricTimeData(GetDataWithTimestampRequest request, UserProvider userProvider, List<RepoProvider> providers)
        {
            var userConfiguration = JsonSerializer.Deserialize<UserProviderConfiguration>(userProvider.Configuration);

            try
            {
                //if (request.StartDateTime > DateTime.Now.AddDays(-3) && request.EndDateTime > DateTime.Now.AddDays(-3))
                //{
                //    List<RealTimeData> deviceData = new List<RealTimeData>();
                //    foreach (string deviceId in request.DevIds.Split(","))
                //    {
                //        var datas = await _dataRepositoryService.GetByDeviceIdAndDatesAsync(deviceId, request.StartDateTime, request.EndDateTime);
                //        foreach (RepoData repoData in datas)
                //        {
                //            var realtimedata = JsonSerializer.Deserialize<HistoricTimeData>(repoData.Data).Data;
                //            deviceData.AddRange(realtimedata);
                //        }
                //    }
                //    if (deviceData.Count > 0) {
                //        List<RealTimeData> responsedeviceData = new List<RealTimeData>();
                //        switch (request.SearchType.ToLower())
                //        {
                //            case "monthly":
                //                responsedeviceData = deviceData
                //                    .GroupBy(d => d.dateTime.Date)
                //                    .Select(g => new RealTimeData
                //                    {
                //                        name = g.Key.Day.ToString(),
                //                        activePower = g.Sum(d => d.activePower),
                //                        totalInputPower = g.Sum(d => d.totalInputPower),
                //                        dateTime = g.Key
                //                    })
                //                    .ToList();
                //                break;
                //            case "yearly":
                //                responsedeviceData = deviceData
                //                    .GroupBy(d => new { d.dateTime.Year, d.dateTime.Month })
                //                    .Select(g => new RealTimeData
                //                    {
                //                        name = CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(g.Key.Month),
                //                        activePower = g.Sum(d => d.activePower),
                //                        totalInputPower = g.Sum(d => d.totalInputPower),
                //                        dateTime = new DateTime(g.Key.Year, g.Key.Month, 1)
                //                    })
                //                    .ToList();
                //                break;
                //            case "lifetime":
                //                responsedeviceData = deviceData
                //                    .GroupBy(d => d.dateTime.Year)
                //                    .Select(g => new RealTimeData
                //                    {
                //                        name = g.Key.ToString(),
                //                        activePower = g.Sum(d => d.activePower),
                //                        totalInputPower = g.Sum(d => d.totalInputPower),
                //                        dateTime = new DateTime(g.Key, 1, 1)
                //                    })
                //                    .ToList();
                //                break;
                //            default:
                //                throw new ArgumentException("Invalid search type");

                //        }

                //        HistoricTimeData response = new HistoricTimeData()
                //        {
                //            Data = responsedeviceData,
                //            Sum = deviceData.Sum(g => g.totalInputPower)
                //        };

                //        return response;
                //    }
                    
                //    var data = await _huaweiApiService.GetHistoricTimeData(request, userConfiguration.Username, userConfiguration.Password, providers.FirstOrDefault(p => p.Id == userProvider.ProviderId).BaseUrl); // Παίρνουμε τα saved δεδομένα
                //    return data;
                //}
                //else 
                if (request.SearchType != null && !request.Separated)
                {
                    List<RealTimeData> deviceData = new List<RealTimeData>();
                    foreach (string deviceId in request.DevIds.Split(","))
                    {
                        var datas = await _dataRepositoryService.GetByDeviceIdAndDatesAsync(deviceId, request.StartDateTime, request.EndDateTime, userProvider.Id.ToString());
                        foreach (RepoData data in datas)
                        {
                            var realtimedata = JsonSerializer.Deserialize<HistoricTimeData>(data.Data).Data;
                            deviceData.AddRange(realtimedata);
                        }
                    }

                    //var filteredData = deviceData.Where(d => d.dateTime >= request.StartDateTime && d.dateTime <= request.EndDateTime);
                    List<RealTimeData> responsedeviceData = new List<RealTimeData>();
                    switch (request.SearchType.ToLower())
                    {
                        case "monthly":
                            responsedeviceData = deviceData
                                .GroupBy(d => d.dateTime.Date)
                                .Select(g => new RealTimeData
                                {
                                    name = g.Key.Day.ToString(),
                                    activePower = g.Sum(d => d.activePower),
                                    totalInputPower = g.Sum(d => d.totalInputPower),
                                    dateTime = g.Key
                                })
                                .ToList();
                            break;
                        case "yearly":
                            responsedeviceData = deviceData
                                .GroupBy(d => new { d.dateTime.Year, d.dateTime.Month })
                                .Select(g => new RealTimeData
                                {
                                    name = CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(g.Key.Month),
                                    activePower = g.Sum(d => d.activePower),
                                    totalInputPower = g.Sum(d => d.totalInputPower),
                                    dateTime = new DateTime(g.Key.Year, g.Key.Month, 1)
                                })
                                .ToList();
                            break;
                        case "lifetime":
                            responsedeviceData = deviceData
                                .GroupBy(d => d.dateTime.Year)
                                .Select(g => new RealTimeData
                                {
                                    name = g.Key.ToString(),
                                    activePower = g.Sum(d => d.activePower),
                                    totalInputPower = g.Sum(d => d.totalInputPower),
                                    dateTime = new DateTime(g.Key, 1, 1)
                                })
                                .ToList();
                            break;
                        default:
                            throw new ArgumentException("Invalid search type");

                    }

                    HistoricTimeData response = new HistoricTimeData()
                    {
                        Data = responsedeviceData,
                        Sum = deviceData.Sum(g => g.totalInputPower)
                    };

                    return response;
                }
                else if (request.SearchType != null && request.Separated)
                {
                    List<RealTimeData> deviceData = new List<RealTimeData>();
                    foreach (string deviceId in request.DevIds.Split(","))
                    {
                        var datas = await _dataRepositoryService.GetByDeviceIdAndDatesAsync(deviceId, request.StartDateTime, request.EndDateTime, userProvider.Id.ToString());
                        foreach (RepoData data in datas)
                        {
                            var realtimedata = JsonSerializer.Deserialize<HistoricTimeData>(data.Data).Data;
                            deviceData.AddRange(realtimedata);
                        }
                    }

                    //var filteredData = deviceData.Where(d => d.dateTime >= request.StartDateTime && d.dateTime <= request.EndDateTime);
                    List<RealTimeData> responsedeviceData = new List<RealTimeData>();
                    switch (request.SearchType.ToLower())
                    {
                        case "monthly":
                            responsedeviceData = deviceData
                                .GroupBy(d => d.dateTime.Date) // Πρώτο grouping ανά ημερομηνία
                                .SelectMany(dateGroup => dateGroup
                                    .GroupBy(d => d.name) // Δεύτερο grouping ανά name
                                    .Select(nameGroup => new RealTimeData
                                    {
                                        name = $"{dateGroup.Key.Day} - {nameGroup.Key}",
                                        activePower = nameGroup.Sum(d => d.activePower),
                                        totalInputPower = nameGroup.Sum(d => d.totalInputPower),
                                        dateTime = dateGroup.Key, // Η κοινή ημερομηνία της πρώτης ομάδας
                                        dateDescription = dateGroup.Key.ToString("dd/MM")
                                    })
                                )
                                .ToList();
                            break;
                        case "yearly":
                            responsedeviceData = deviceData
                                .GroupBy(d => new { d.dateTime.Year, d.dateTime.Month }) // Πρώτο GroupBy (Έτος, Μήνας)
                                .SelectMany(monthGroup => monthGroup
                                    .GroupBy(d => d.name) // Δεύτερο GroupBy (Name)
                                    .Select(nameGroup => new RealTimeData
                                    {
                                        name = $"{CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(monthGroup.Key.Month)} - {nameGroup.Key}",
                                        activePower = nameGroup.Sum(d => d.activePower),
                                        totalInputPower = nameGroup.Sum(d => d.totalInputPower),
                                        dateTime = new DateTime(monthGroup.Key.Year, monthGroup.Key.Month, 1),
                                        dateDescription = new DateTime(monthGroup.Key.Year, monthGroup.Key.Month, 1).ToString("MMMM", CultureInfo.InvariantCulture)
                                    })
                                )
                                .ToList();

                            break;
                        case "lifetime":
                            responsedeviceData = deviceData
                                .GroupBy(d => d.dateTime.Year)  // Πρώτο grouping με το Year
                                .Select(g => new
                                {
                                    Year = g.Key,
                                    GroupedByName = g
                                        .GroupBy(d => d.name)  // Δεύτερο grouping με το name
                                        .Select(gg => new RealTimeData
                                        {
                                            name = $"{g.Key} - {gg.Key}",
                                            activePower = gg.Sum(d => d.activePower),
                                            totalInputPower = gg.Sum(d => d.totalInputPower),
                                            dateTime = new DateTime(g.Key, 1, 1),
                                            dateDescription = new DateTime(g.Key, 1, 1).ToString("MMMM yyyy", CultureInfo.InvariantCulture)
                                        })
                                        .ToList()
                                })
                                .SelectMany(yearGroup => yearGroup.GroupedByName)  // Εξέταση όλων των grouped δεδομένων για να επιστρέψουν ένα επίπεδο
                                .ToList();
                            break;
                        default:
                            throw new ArgumentException("Invalid search type");

                    }

                    HistoricTimeData response = new HistoricTimeData()
                    {
                        Data = responsedeviceData,
                        Sum = deviceData.Sum(g => g.totalInputPower)
                    };

                    return response;
                }
                else
                {
                    List<RealTimeData> deviceData = new List<RealTimeData>();
                    foreach (string deviceId in request.DevIds.Split(","))
                    {
                        var datas = await _dataRepositoryService.GetByDeviceIdAndDatesAsync(deviceId, request.StartDateTime, request.EndDateTime, userProvider.Id.ToString());
                        foreach (RepoData data in datas)
                        {
                            var realtimedata = JsonSerializer.Deserialize<HistoricTimeData>(data.Data).Data;
                            deviceData.AddRange(realtimedata);
                        }
                    }

                    if (deviceData.Count == 0) {
                        var data = await _huaweiApiService.GetHistoricTimeData(request, userConfiguration.Username, userConfiguration.Password, providers.FirstOrDefault(p => p.Id == userProvider.ProviderId).BaseUrl); // Παίρνουμε τα saved δεδομένα
                        return data;
                    }


                    if (!request.Separated)
                    {
                        var groupedData = deviceData
                        .GroupBy(d => d.timestamp)
                        .Select(g => new RealTimeData
                        {
                            timestamp = g.Key,
                            totalInputPower = g.Sum(d => d.totalInputPower),
                            activePower = g.Sum(d => d.activePower),
                            dateTime = ConvertUnixTimestampToAthensDateTime(g.Key),
                            name = "sum"
                            //CollectTime = g.Key,
                            //TotalActivePower = g.Sum(d => d.dataItemMap.ActivePower ?? 0)
                        })
                        .ToList();

                        HistoricTimeData response = new HistoricTimeData()
                        {
                            Data = groupedData,
                            Sum = deviceData.Sum(g => g.totalInputPower)
                        };

                        return response;
                    }
                    else
                    {
                        HistoricTimeData response = new HistoricTimeData()
                        {
                            Data = deviceData,
                            Sum = deviceData.Sum(g => g.totalInputPower)
                        };

                        return response;
                    }


                }

            }
            catch (Exception ex)
            {
                throw ex;
            }

        }


        private async Task<HistoricTimeData> GetHistoricTimeData(GetDataWithTimestampRequest request, UserProvider userProvider, List<RepoProvider> providers)
        {
            var userConfiguration = JsonSerializer.Deserialize<UserProviderConfiguration>(userProvider.Configuration);

            try
            {
                List<RealTimeData> deviceData = new List<RealTimeData>();
                switch (userProvider.ProviderId)
                {

                    case 4:
                        //aurora api call
                        var auroraHistoricData = await _auroraApiService.GetHistoricTimeData(request, userConfiguration.Username, userConfiguration.Password, providers.FirstOrDefault(p => p.Id == userProvider.ProviderId).BaseUrl);
                        deviceData = auroraHistoricData.Data;
                        break;

                    case 5:
                        var refulogHistoricData = await _refulogApiService.GetHistoricTimeData(request, userConfiguration.Username, userConfiguration.Password, providers.FirstOrDefault(p => p.Id == userProvider.ProviderId).BaseUrl);
                        deviceData = refulogHistoricData.Data;
                        break;
                    case 6:
                        var smaHistoricData = await GetSMAHistoricTimeData(request, userConfiguration.Username, userConfiguration.Password, userConfiguration.FtpUrl, userProvider);
                        deviceData = smaHistoricData.Data;
                        break;
                    default:
                        
                        break;
                }

                HistoricTimeData response = new HistoricTimeData()
                {
                    Data = deviceData,
                    Sum = deviceData.Sum(g => g.totalInputPower)
                };

                return response;


                //if (request.SearchType != null && !request.Separated)
                //{
                //    //var filteredData = deviceData.Where(d => d.dateTime >= request.StartDateTime && d.dateTime <= request.EndDateTime);
                //    List<RealTimeData> responsedeviceData = new List<RealTimeData>();
                //    switch (request.SearchType.ToLower())
                //    {
                //        case "monthly":
                //            responsedeviceData = deviceData
                //                .GroupBy(d => d.dateTime.Date)
                //                .Select(g => new RealTimeData
                //                {
                //                    name = g.Key.Day.ToString(),
                //                    activePower = g.Sum(d => d.activePower),
                //                    totalInputPower = g.Sum(d => d.totalInputPower),
                //                    dateTime = g.Key
                //                })
                //                .ToList();
                //            break;
                //        case "yearly":
                //            responsedeviceData = deviceData
                //                .GroupBy(d => new { d.dateTime.Year, d.dateTime.Month })
                //                .Select(g => new RealTimeData
                //                {
                //                    name = CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(g.Key.Month),
                //                    activePower = g.Sum(d => d.activePower),
                //                    totalInputPower = g.Sum(d => d.totalInputPower),
                //                    dateTime = new DateTime(g.Key.Year, g.Key.Month, 1)
                //                })
                //                .ToList();
                //            break;
                //        case "lifetime":
                //            responsedeviceData = deviceData
                //                .GroupBy(d => d.dateTime.Year)
                //                .Select(g => new RealTimeData
                //                {
                //                    name = g.Key.ToString(),
                //                    activePower = g.Sum(d => d.activePower),
                //                    totalInputPower = g.Sum(d => d.totalInputPower),
                //                    dateTime = new DateTime(g.Key, 1, 1)
                //                })
                //                .ToList();
                //            break;
                //        default:
                //            throw new ArgumentException("Invalid search type");

                //    }

                //    HistoricTimeData response = new HistoricTimeData()
                //    {
                //        Data = responsedeviceData,
                //        Sum = deviceData.Sum(g => g.totalInputPower)
                //    };

                //    return response;
                //}
                //else if (request.SearchType != null && request.Separated)
                //{
                    
                //    //var filteredData = deviceData.Where(d => d.dateTime >= request.StartDateTime && d.dateTime <= request.EndDateTime);
                //    List<RealTimeData> responsedeviceData = new List<RealTimeData>();
                //    switch (request.SearchType.ToLower())
                //    {
                //        case "monthly":
                //            responsedeviceData = deviceData
                //                .GroupBy(d => d.dateTime.Date) // Πρώτο grouping ανά ημερομηνία
                //                .SelectMany(dateGroup => dateGroup
                //                    .GroupBy(d => d.name) // Δεύτερο grouping ανά name
                //                    .Select(nameGroup => new RealTimeData
                //                    {
                //                        name = $"{dateGroup.Key.Day} - {nameGroup.Key}",
                //                        activePower = nameGroup.Sum(d => d.activePower),
                //                        totalInputPower = nameGroup.Sum(d => d.totalInputPower),
                //                        dateTime = dateGroup.Key // Η κοινή ημερομηνία της πρώτης ομάδας
                //                    })
                //                )
                //                .ToList();
                //            break;
                //        case "yearly":
                //            responsedeviceData = deviceData
                //                .GroupBy(d => new { d.dateTime.Year, d.dateTime.Month }) // Πρώτο GroupBy (Έτος, Μήνας)
                //                .SelectMany(monthGroup => monthGroup
                //                    .GroupBy(d => d.name) // Δεύτερο GroupBy (Name)
                //                    .Select(nameGroup => new RealTimeData
                //                    {
                //                        name = $"{CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(monthGroup.Key.Month)} - {nameGroup.Key}",
                //                        activePower = nameGroup.Sum(d => d.activePower),
                //                        totalInputPower = nameGroup.Sum(d => d.totalInputPower),
                //                        dateTime = new DateTime(monthGroup.Key.Year, monthGroup.Key.Month, 1)
                //                    })
                //                )
                //                .ToList();

                //            break;
                //        case "lifetime":
                //            responsedeviceData = deviceData
                //                .GroupBy(d => d.dateTime.Year)  // Πρώτο grouping με το Year
                //                .Select(g => new
                //                {
                //                    Year = g.Key,
                //                    GroupedByName = g
                //                        .GroupBy(d => d.name)  // Δεύτερο grouping με το name
                //                        .Select(gg => new RealTimeData
                //                        {
                //                            name = $"{g.Key} - {gg.Key}",
                //                            activePower = gg.Sum(d => d.activePower),
                //                            totalInputPower = gg.Sum(d => d.totalInputPower),
                //                            dateTime = new DateTime(g.Key, 1, 1)
                //                        })
                //                        .ToList()
                //                })
                //                .SelectMany(yearGroup => yearGroup.GroupedByName)  // Εξέταση όλων των grouped δεδομένων για να επιστρέψουν ένα επίπεδο
                //                .ToList();
                //            break;
                //        default:
                //            throw new ArgumentException("Invalid search type");

                //    }

                //    HistoricTimeData response = new HistoricTimeData()
                //    {
                //        Data = responsedeviceData,
                //        Sum = deviceData.Sum(g => g.totalInputPower)
                //    };

                //    return response;
                //}
                //else
                //{
                //    if (!request.Separated)
                //    {
                //        var groupedData = deviceData
                //        .GroupBy(d => d.timestamp)
                //        .Select(g => new RealTimeData
                //        {
                //            timestamp = g.Key,
                //            totalInputPower = g.Sum(d => d.totalInputPower),
                //            activePower = g.Sum(d => d.activePower),
                //            dateTime = DateTimeOffset.FromUnixTimeMilliseconds(g.Key).UtcDateTime,
                //            name = "sum"
                //            //CollectTime = g.Key,
                //            //TotalActivePower = g.Sum(d => d.dataItemMap.ActivePower ?? 0)
                //        })
                //        .ToList();

                //        HistoricTimeData response = new HistoricTimeData()
                //        {
                //            Data = groupedData,
                //            Sum = deviceData.Sum(g => g.totalInputPower)
                //        };

                //        return response;
                //    }
                //    else
                //    {
                //        HistoricTimeData response = new HistoricTimeData()
                //        {
                //            Data = deviceData,
                //            Sum = deviceData.Sum(g => g.totalInputPower)
                //        };

                //        return response;
                //    }


                //}

            }
            catch (Exception ex)
            {
                throw ex;
            }

        }

        public List<Station> GetSMAStations(string ftpUrl, string user, string pass) {
            var stationsList = ListFolders(ftpUrl, user, pass);
            List<Station> stations = new List<Station>();
            foreach(string station in stationsList)
            {
                stations.Add(new Station
                {
                    Name = station,
                    Id = station,
                    Provider = "SMA"
                });
            }

            return stations;
        }

        public async Task<HistoricTimeData> GetSMAHistoricTimeData(GetDataWithTimestampRequest req, string username, string userPassword, string baseUrl, UserProvider userProvider)  {
            //var data = await GetFtpData(req.StationId, username, userPassword, baseUrl, "2025-06-09");
            List<RealTimeData> stationData = new List<RealTimeData>();
            var datas = await _dataRepositoryService.GetByDeviceIdAndDatesAsync(req.StationId, req.StartDateTime, req.EndDateTime, userProvider.Id.ToString() );
            foreach (RepoData data in datas)
            {
                var realtimedata = JsonSerializer.Deserialize<HistoricTimeData>(data.Data).Data;
                stationData.AddRange(realtimedata);
            }
            List<RealTimeData> responsedeviceData = new List<RealTimeData>();

            if (req.Separated)
            {
                if (req.SearchType is null)
                {
                    responsedeviceData = stationData;
                }
                else
                {
                    switch (req.SearchType.ToLower())
                    {
                        case "monthly":
                            responsedeviceData = stationData
                                .GroupBy(d => d.dateTime.Date) // Πρώτο grouping ανά ημερομηνία
                                .SelectMany(dateGroup => dateGroup
                                    .GroupBy(d => d.name) // Δεύτερο grouping ανά name
                                    .Select(nameGroup => new RealTimeData
                                    {
                                        name = $"{dateGroup.Key.Day} - {nameGroup.Key}",
                                        activePower = nameGroup.Sum(d => d.activePower),
                                        totalInputPower = nameGroup.Sum(d => d.totalInputPower),
                                        dateTime = dateGroup.Key, // Η κοινή ημερομηνία της πρώτης ομάδας
                                        dateDescription = dateGroup.Key.ToString("dd/MM")
                                    })
                                )
                                .ToList();
                            break;
                        case "yearly":
                            responsedeviceData = stationData
                                .GroupBy(d => new { d.dateTime.Year, d.dateTime.Month }) // Πρώτο GroupBy (Έτος, Μήνας)
                                .SelectMany(monthGroup => monthGroup
                                    .GroupBy(d => d.name) // Δεύτερο GroupBy (Name)
                                    .Select(nameGroup => new RealTimeData
                                    {
                                        name = $"{CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(monthGroup.Key.Month)} - {nameGroup.Key}",
                                        activePower = nameGroup.Sum(d => d.activePower),
                                        totalInputPower = nameGroup.Sum(d => d.totalInputPower),
                                        dateTime = new DateTime(monthGroup.Key.Year, monthGroup.Key.Month, 1),
                                        dateDescription = new DateTime(monthGroup.Key.Year, monthGroup.Key.Month, 1).ToString("MMMM", CultureInfo.InvariantCulture)
                                    })
                                )
                                .ToList();

                            break;
                        case "lifetime":
                            responsedeviceData = stationData
                                .GroupBy(d => d.dateTime.Year)  // Πρώτο grouping με το Year
                                .Select(g => new
                                {
                                    Year = g.Key,
                                    GroupedByName = g
                                        .GroupBy(d => d.name)  // Δεύτερο grouping με το name
                                        .Select(gg => new RealTimeData
                                        {
                                            name = $"{g.Key} - {gg.Key}",
                                            activePower = gg.Sum(d => d.activePower),
                                            totalInputPower = gg.Sum(d => d.totalInputPower),
                                            dateTime = new DateTime(g.Key, 1, 1),
                                            dateDescription = new DateTime(g.Key, 1, 1).ToString("MMMM yyyy", CultureInfo.InvariantCulture)
                                        })
                                        .ToList()
                                })
                                .SelectMany(yearGroup => yearGroup.GroupedByName)  // Εξέταση όλων των grouped δεδομένων για να επιστρέψουν ένα επίπεδο
                                .ToList();
                            break;
                        default:
                            throw new ArgumentException("Invalid search type");

                    }
                }

                
            }
            else {

                if (req.SearchType == null) {
                    responsedeviceData = stationData
                        .GroupBy(d => d.timestamp)
                        .Select(g => new RealTimeData
                        {
                            timestamp = g.Key,
                            totalInputPower = g.Sum(d => d.totalInputPower),
                            activePower = g.Sum(d => d.activePower),
                            dateTime = DateTimeOffset.FromUnixTimeMilliseconds(g.Key).UtcDateTime,
                            name = "sum"
                            //CollectTime = g.Key,
                            //TotalActivePower = g.Sum(d => d.dataItemMap.ActivePower ?? 0)
                        })
                        .ToList();
                }
                else
                {
                    switch (req.SearchType.ToLower())
                    {
                        case "monthly":
                            responsedeviceData = stationData
                                .GroupBy(d => d.dateTime.Date)
                                .Select(g => new RealTimeData
                                {
                                    name = g.Key.Day.ToString(),
                                    activePower = g.Sum(d => d.activePower),
                                    totalInputPower = g.Sum(d => d.totalInputPower),
                                    dateTime = g.Key
                                })
                                .ToList();
                            break;
                        case "yearly":
                            responsedeviceData = stationData
                                .GroupBy(d => new { d.dateTime.Year, d.dateTime.Month })
                                .Select(g => new RealTimeData
                                {
                                    name = CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(g.Key.Month),
                                    activePower = g.Sum(d => d.activePower),
                                    totalInputPower = g.Sum(d => d.totalInputPower),
                                    dateTime = new DateTime(g.Key.Year, g.Key.Month, 1)
                                })
                                .ToList();
                            break;
                        case "lifetime":
                            responsedeviceData = stationData
                                .GroupBy(d => d.dateTime.Year)
                                .Select(g => new RealTimeData
                                {
                                    name = g.Key.ToString(),
                                    activePower = g.Sum(d => d.activePower),
                                    totalInputPower = g.Sum(d => d.totalInputPower),
                                    dateTime = new DateTime(g.Key, 1, 1)
                                })
                                .ToList();
                            break;
                        default:
                            throw new ArgumentException("Invalid search type");

                    }
                
                }

                

            }


            var allData = new HistoricTimeData()
            {
                Data = responsedeviceData,
                Sum = responsedeviceData.Sum(s => s.activePower)
            };

            return allData;
        }

        public async Task<HistoricTimeData> GetFtpData(string stationName, string username, string password, string ftpBaseUrl, string date)
        {
            //string ftpBaseUrl = "ftp://178.62.7.136/"; // Πρέπει να τελειώνει με /
            //string username = "solarkapital-ftp";
            //string password = "Fru65SI9";


            //string todayString = DateTime.Now.ToString("yyyyMMdd");
            string todayString = date != null ? DateTime.Parse(date).ToString("yyyyMMdd") : DateTime.Now.ToString("yyyyMMdd");

            var allData = new List<RealTimeData>();

            var stationsList = ListFolders(ftpBaseUrl, username, password);

            // Βήμα 1: Πάρε όλα τα folders
            // foreach (string folder in ListFolders(ftpBaseUrl, username, password))
            // {
                string folderUrl = ftpBaseUrl + stationName + "/";

                // Βήμα 2: Πάρε όλα τα zip αρχεία μέσα στο folder
                foreach (string zipFile in ListFiles(folderUrl, username, password))
                {
                    if (!zipFile.EndsWith(".zip")) continue;

                    // Φίλτρο με βάση ημερομηνία μέσα στο όνομα
                    if (!zipFile.Contains(todayString)) continue;

                    //if (!zipFile.Contains("wb150057332")) continue;

                   // DownloadFtpFile(folderUrl + zipFile, zipFile, username, password);

                    string zipUrl = folderUrl + zipFile;

                    using (var zipStream = DownloadFileToMemory(zipUrl, username, password))
                    using (var outerZip = new ZipArchive(zipStream, ZipArchiveMode.Read))
                    {
                        foreach (var innerZipEntry in outerZip.Entries)
                        {
                            if (!innerZipEntry.FullName.EndsWith(".zip")) continue;

                            // Φίλτρο με βάση ημερομηνία μέσα στο όνομα
                            if (!innerZipEntry.FullName.Contains(todayString)) continue;

                            using (var innerStream = innerZipEntry.Open())
                                using (var innerMem = new MemoryStream())
                                {
                                    innerStream.CopyTo(innerMem);
                                    innerMem.Position = 0;

                                    using (var innerZip = new ZipArchive(innerMem, ZipArchiveMode.Read))
                                    {
                                        foreach (var xmlEntry in innerZip.Entries)
                                        {
                                            if (!xmlEntry.FullName.EndsWith(".xml")) continue;

                                            using (var xmlStream = xmlEntry.Open())
                                            {
                                                var parsed = ParseMeanPublicFromXml(xmlStream, innerZipEntry.FullName + "/" + zipFile);
                                                allData.AddRange(parsed);
                                            }
                                        }
                                    }
                                }
                        }
                    }
                }
            //}

            // Τύπωσε συνοπτικά τα δεδομένα
            //foreach (var item in allData)
            //{
            //    Console.WriteLine($"{item.TimeStamp:yyyy-MM-dd HH:mm} | {item.Key} | Mean: {item.Mean}");
            //}

            return new HistoricTimeData() { 
                Data = allData.OrderBy(a => a.dateTime).ToList(),
                Sum = allData.Sum(a=> a.activePower)
            };
        }

        // Βοηθητικά
        static List<string> ListFolders(string ftpUrl, string user, string pass)
        {
            var folders = new List<string>();

            var request = (FtpWebRequest)WebRequest.Create(ftpUrl);
            request.Method = WebRequestMethods.Ftp.ListDirectoryDetails;
            request.Credentials = new NetworkCredential(user, pass);

            using (var response = (FtpWebResponse)request.GetResponse())
            using (var reader = new StreamReader(response.GetResponseStream()))
            {
                while (!reader.EndOfStream)
                {
                    var line = reader.ReadLine();

                    // UNIX-style: drwxrwxr-x   2 <USER> <GROUP> 4096 Jun 16 15:35 Agioi Deka FS
                    if (line.StartsWith("d")) // Είναι directory
                    {
                        // Τα πρώτα 8 πεδία είναι permission, links, user, group, size, month, day, time/year
                        var parts = line.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);

                        if (parts.Length >= 9)
                        {
                            // Πάρε όλα τα κομμάτια από το 9ο και μετά (index 8)
                            var folderNameParts = parts.Skip(8);
                            var folderName = string.Join(" ", folderNameParts);

                            folders.Add(folderName);
                        }
                    }
                }
            }

            return folders;
        }


        static List<string> ListDevices(string url, string username, string password)
        {
            var result = new List<string>();

            var request = (HttpWebRequest)WebRequest.Create(url);
            request.Credentials = new NetworkCredential(username, password);
            request.Method = "GET";

            using (var response = (HttpWebResponse)request.GetResponse())
            using (var reader = new StreamReader(response.GetResponseStream()))
            {
                string raw = reader.ReadToEnd();

                string[] entries = raw.Split('|');

                foreach (var entry in entries)
                {
                    // Παράδειγμα: img_WRTP4Q8C_colon_2110012349~...
                    var match = Regex.Match(entry, @"img_(.+?)_colon_(\d+)");
                    if (match.Success)
                    {
                        string sensorId = match.Groups[1].Value;
                        string number = match.Groups[2].Value;
                        result.Add($"{sensorId}:{number}");
                    }
                }
            }

            return result;
        }

        static List<string> ListFiles(string ftpUrl, string user, string pass)
        {
            var files = new List<string>();

            var request = (FtpWebRequest)WebRequest.Create(ftpUrl);
            request.Method = WebRequestMethods.Ftp.ListDirectory;
            request.Credentials = new NetworkCredential(user, pass);

            using (var response = (FtpWebResponse)request.GetResponse())
            using (var reader = new StreamReader(response.GetResponseStream()))
            {
                while (!reader.EndOfStream)
                {
                    var line = reader.ReadLine();
                    files.Add(line);
                }
            }

            return files;
        }

        static MemoryStream DownloadFileToMemory(string fileUrl, string user, string pass)
        {
            var request = (FtpWebRequest)WebRequest.Create(fileUrl);
            request.Method = WebRequestMethods.Ftp.DownloadFile;
            request.Credentials = new NetworkCredential(user, pass);

            using (var response = (FtpWebResponse)request.GetResponse())
            using (var stream = response.GetResponseStream())
            {
                var memory = new MemoryStream();
                stream.CopyTo(memory);
                memory.Position = 0;
                return memory;
            }
        }

        static bool DownloadFtpFile(string ftpFileUrl, string fileName, string username, string password) {

            string localFilePath = @"C:\Users\<USER>\Downloads\" + fileName;

            // Δημιουργία request
            FtpWebRequest request = (FtpWebRequest)WebRequest.Create(ftpFileUrl);
            request.Method = WebRequestMethods.Ftp.DownloadFile;
            request.Credentials = new NetworkCredential(username, password);

            // Ανάγνωση από FTP και αποθήκευση τοπικά
            using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
            using (Stream ftpStream = response.GetResponseStream())
            using (FileStream fileStream = new FileStream(localFilePath, FileMode.Create))
            {
                ftpStream.CopyTo(fileStream);
            }

            return true;

        }

        List<RealTimeData> ParseMeanPublicFromXml(Stream xmlStream, string fileName)
        {
            var result = new List<RealTimeData>();
            var doc = XDocument.Load(xmlStream);

            foreach (var el in doc.Descendants("MeanPublic"))
            {
                var data = new SMAMeanPublicData
                {
                    Key = (string)el.Element("Key"),
                    First = (double)el.Element("First"),
                    Last = (double)el.Element("Last"),
                    Min = (double)el.Element("Min"),
                    Max = (double)el.Element("Max"),
                    Mean = (double)el.Element("Mean"),
                    Base = (int)el.Element("Base"),
                    Period = (int)el.Element("Period"),
                    TimeStamp = DateTime.Parse((string)el.Element("TimeStamp"))
                };

                if (data.Key.Split(":")[2] == "Pac") {
                    // Convert timestamp to Athens timezone consistently
                    // Assume data.TimeStamp is in local time, convert to Athens timezone
                    TimeZoneInfo athensTimeZone = TimeZoneInfo.FindSystemTimeZoneById("GTB Standard Time");

                    // First, treat the timestamp as UTC (since it comes from the data source)
                    // Then convert to Athens timezone
                    var utcDateTime = DateTime.SpecifyKind(data.TimeStamp, DateTimeKind.Utc);
                    var athensDateTime = TimeZoneInfo.ConvertTimeFromUtc(utcDateTime, athensTimeZone);

                    result.Add(new RealTimeData() {
                        activePower = (decimal)data.Mean,
                        dateTime = athensDateTime, // Store as Athens time
                        name = data.Key.Split(":")[0] + ":" + data.Key.Split(":")[1],
                        timestamp = ((DateTimeOffset)utcDateTime).ToUnixTimeMilliseconds(), // Use UTC for timestamp
                        dateDescription = fileName
                    });

                }

            }

            return result;
        }

        //NOT USED
        public Task<HistoricTimeData> GetFtpData(string userId, GetFtpDataRequest request)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// Converts Unix timestamp (milliseconds) to Athens DateTime
        /// This ensures consistent timezone handling regardless of server location
        /// </summary>
        private DateTime ConvertUnixTimestampToAthensDateTime(long unixTimestampMs)
        {
            // Convert Unix timestamp to UTC DateTime
            var utcDateTime = DateTimeOffset.FromUnixTimeMilliseconds(unixTimestampMs).UtcDateTime;

            // Convert to Athens timezone
            TimeZoneInfo athensTimeZone = TimeZoneInfo.FindSystemTimeZoneById("GTB Standard Time");
            var athensDateTime = TimeZoneInfo.ConvertTimeFromUtc(utcDateTime, athensTimeZone);

            return athensDateTime;
        }
    }
}
