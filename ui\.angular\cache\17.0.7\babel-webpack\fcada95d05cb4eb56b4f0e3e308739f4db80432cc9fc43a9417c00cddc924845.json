{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { TreeDemoRoutingModule } from './treedemo-routing.module';\nimport { TreeModule } from 'primeng/tree';\nimport { TreeTableModule } from 'primeng/treetable';\nimport * as i0 from \"@angular/core\";\nexport let TreeDemoModule = /*#__PURE__*/(() => {\n  class TreeDemoModule {\n    static #_ = this.ɵfac = function TreeDemoModule_Factory(t) {\n      return new (t || TreeDemoModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: TreeDemoModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, TreeDemoRoutingModule, FormsModule, TreeModule, TreeTableModule]\n    });\n  }\n  return TreeDemoModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}