{"ast": null, "code": "import { debounceTime } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/layout/service/app.layout.service\";\nimport * as i2 from \"../../service/stations.service\";\nimport * as i3 from \"../../service/providers.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"../../service/cache.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"primeng/dataview\";\nimport * as i10 from \"primeng/inputtext\";\nimport * as i11 from \"primeng/dropdown\";\nimport * as i12 from \"@coreui/angular-chartjs\";\nimport * as i13 from \"primeng/badge\";\nimport * as i14 from \"@fortawesome/angular-fontawesome\";\nfunction IndexComponent_ng_template_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"p-dropdown\", 16);\n    i0.ɵɵlistener(\"onChange\", function IndexComponent_ng_template_25_Template_p_dropdown_onChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onSortChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"p-dropdown\", 17);\n    i0.ɵɵlistener(\"onChange\", function IndexComponent_ng_template_25_Template_p_dropdown_onChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onSortChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 18);\n    i0.ɵɵelement(4, \"i\", 19);\n    i0.ɵɵelementStart(5, \"input\", 20);\n    i0.ɵɵlistener(\"input\", function IndexComponent_ng_template_25_Template_input_input_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r6 = i0.ɵɵnextContext();\n      const _r0 = i0.ɵɵreference(24);\n      return i0.ɵɵresetView(ctx_r6.onFilter(_r0, $event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"options\", ctx_r1.sortOptionsCountry);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"options\", ctx_r1.sortOptionsStatus);\n  }\n}\nfunction IndexComponent_ng_template_26_div_0_c_chart_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"c-chart\", 44);\n  }\n  if (rf & 2) {\n    const station_r9 = i0.ɵɵnextContext().$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"data\", ctx_r10.getStationsRealTimeData(station_r9))(\"options\", ctx_r10.lineOptions);\n  }\n}\nfunction IndexComponent_ng_template_26_div_0_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵelement(1, \"i\", 46);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading data...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction IndexComponent_ng_template_26_div_0_p_badge_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-badge\", 47);\n  }\n  if (rf & 2) {\n    const station_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵpropertyInterpolate(\"value\", station_r9.devicesIds.split(\",\").length);\n  }\n}\nconst _c0 = a1 => [\"/app/station\", a1];\nfunction IndexComponent_ng_template_26_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 22)(2, \"div\", 0)(3, \"div\", 23)(4, \"div\", 24);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 25)(7, \"a\", 26);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 27);\n    i0.ɵɵelement(10, \"fa-icon\", 28);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 29)(13, \"div\", 30);\n    i0.ɵɵelement(14, \"fa-icon\", 8);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 29)(17, \"div\", 30);\n    i0.ɵɵelement(18, \"fa-icon\", 31);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 32);\n    i0.ɵɵtemplate(21, IndexComponent_ng_template_26_div_0_c_chart_21_Template, 1, 2, \"c-chart\", 33)(22, IndexComponent_ng_template_26_div_0_div_22_Template, 4, 0, \"div\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 35);\n    i0.ɵɵtemplate(24, IndexComponent_ng_template_26_div_0_p_badge_24_Template, 1, 1, \"p-badge\", 36);\n    i0.ɵɵelement(25, \"p-badge\", 37)(26, \"p-badge\", 38)(27, \"p-badge\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 40)(29, \"p\", 41);\n    i0.ɵɵelement(30, \"i\", 42);\n    i0.ɵɵelementStart(31, \"span\", 43);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const station_r9 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(\"station-box-status-\" + station_r9.status.toLowerCase());\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", station_r9.provider, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(15, _c0, station_r9.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(station_r9.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.getStationLastUpdate(station_r9.id), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.getStationsSumData(station_r9.id), \"kW\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", station_r9.irradiance, \"kWh/m2\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.getStationsRealTimeData(station_r9));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.getStationsRealTimeData(station_r9));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", station_r9.devicesIds);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"value\", station_r9.mmpt);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"value\", station_r9.string);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"value\", station_r9.pvn);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", station_r9.temperature, \" \\u2103\");\n  }\n}\nfunction IndexComponent_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, IndexComponent_ng_template_26_div_0_Template, 33, 17, \"div\", 21);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.stations);\n  }\n}\nconst _c1 = () => ({\n  width: \"2.5rem\",\n  height: \"2.5rem\"\n});\nexport class IndexComponent {\n  constructor(layoutService, stationsService, providersService, messageService, fb, router, cacheService) {\n    this.layoutService = layoutService;\n    this.stationsService = stationsService;\n    this.providersService = providersService;\n    this.messageService = messageService;\n    this.fb = fb;\n    this.router = router;\n    this.cacheService = cacheService;\n    this.stations = [];\n    this.sortOptionsCountry = [];\n    this.sortOptionsStatus = [];\n    this.sortOrder = 0;\n    this.sortField = '';\n    this.sourceCities = [];\n    this.targetCities = [];\n    this.orderCities = [];\n    this.stationsData = new Map();\n    this.stationsRawData = new Map();\n    this.stationsSumData = new Map();\n    this.subscription = this.layoutService.configUpdate$.pipe(debounceTime(25)).subscribe(config => {\n      //   this.initChart();\n    });\n  }\n  ngOnInit() {\n    this.getUserProviders();\n    this.barOptions = {\n      maintainAspectRatio: false,\n      plugins: {\n        legend: {\n          display: false\n        }\n      },\n      scales: {\n        x: {\n          display: false\n        },\n        y: {\n          display: false\n        }\n      }\n    };\n    this.lineOptions = {\n      maintainAspectRatio: false,\n      elements: {\n        line: {\n          tension: 0.4\n        },\n        point: {\n          radius: 0\n        }\n      },\n      plugins: {\n        legend: {\n          display: false\n        }\n      },\n      scales: {\n        x: {\n          display: false\n        },\n        y: {\n          display: false\n        }\n      }\n    };\n  }\n  getUserProviders() {\n    this.providersService.getUserProviders().then(providersData => {\n      if (providersData.length > 0) {\n        this.stationsService.getUserStations().then(stationsData => {\n          this.cacheService.setStations(stationsData);\n          this.stations = stationsData;\n          console.log(\"stations set\");\n          console.log(stationsData);\n          // Φορτώνουμε τα δεδομένα για κάθε σταθμό\n          this.loadAllStationsData();\n        });\n      } else {\n        this.router.navigate(['/app/providers']);\n      }\n    });\n  }\n  // Νέα μέθοδος για φόρτωση δεδομένων όλων των σταθμών\n  loadAllStationsData() {\n    if (!this.stations || this.stations.length === 0) return;\n    // Φορτώνουμε τα δεδομένα για κάθε σταθμό\n    this.stations.forEach(station => {\n      this.loadStationData(station);\n    });\n  }\n  // Νέα μέθοδος για φόρτωση δεδομένων ενός σταθμού\n  loadStationData(station) {\n    if (!station) return;\n    const now = new Date();\n    const formattedStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0).toISOString();\n    const formattedEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0).toISOString();\n    let request = {\n      devIds: station.deviceIds,\n      devTypeId: 1,\n      startDateTime: formattedStartDate,\n      endDateTime: formattedEndDate,\n      separated: false,\n      searchType: null,\n      stationId: station.id\n    };\n    this.stationsService.getStationHistoricData(request).then(data => {\n      if (data && data.data) {\n        const documentStyle = getComputedStyle(document.documentElement);\n        const lineData = {\n          labels: data.data.map((e, index) => index % 2 === 0 ? new Date(e.dateTime).toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n          }) : ''),\n          datasets: [{\n            label: 'Active Power',\n            data: data.data.map(e => e.activePower),\n            fill: false,\n            backgroundColor: documentStyle.getPropertyValue('--primary-500'),\n            borderColor: documentStyle.getPropertyValue('--primary-500'),\n            tension: .4\n          }, {\n            label: 'Total Input Power',\n            data: data.data.map(e => e.totalInputPower),\n            fill: false,\n            backgroundColor: documentStyle.getPropertyValue('--primary-200'),\n            borderColor: documentStyle.getPropertyValue('--primary-200'),\n            tension: .4\n          }]\n        };\n        // Αποθηκεύουμε τα δεδομένα στο Map με κλειδί το ID του σταθμού\n        this.stationsData.set(station.id, lineData);\n        this.stationsRawData.set(station.id, data.data);\n        this.stationsSumData.set(station.id, data.sum);\n      }\n    }).catch(error => {\n      console.error(`Error loading data for station ${station.id}:`, error);\n    });\n  }\n  // Τροποποιημένη μέθοδος που επιστρέφει τα προ-φορτωμένα δεδομένα\n  getStationsRealTimeData(station) {\n    // Επιστρέφουμε τα δεδομένα από το Map αν υπάρχουν\n    if (station && station.id && this.stationsData.has(station.id)) {\n      return this.stationsData.get(station.id);\n    }\n    // Αν δεν έχουν φορτωθεί ακόμα, επιστρέφουμε null\n    return null;\n  }\n  getStationsSumData(stationId) {\n    return this.stationsSumData.get(stationId);\n  }\n  getStationLastUpdate(stationId) {\n    var data = this.stationsRawData.get(stationId);\n    if (!data || data.length === 0) return;\n    const latest = data.reduce((latestSoFar, current) => {\n      return new Date(current.dateTime) > new Date(latestSoFar.dateTime) ? current : latestSoFar;\n    });\n    return latest.dateTime;\n  }\n  onSortChange(event) {\n    const value = event.value;\n    if (value.indexOf('!') === 0) {\n      this.sortOrder = -1;\n      this.sortField = value.substring(1, value.length);\n    } else {\n      this.sortOrder = 1;\n      this.sortField = value;\n    }\n  }\n  onFilter(dv, event) {\n    dv.filter(event.target.value);\n  }\n  initMap() {\n    this.mapSrc = \"https://www.digitaltrends.com/wp-content/uploads/2020/02/custom-map.jpg?fit=720%2C479&p=1\";\n  }\n  // initChart() {\n  //     const documentStyle = getComputedStyle(document.documentElement);\n  //     const textColor = documentStyle.getPropertyValue('--text-color');\n  //     const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n  //     const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\n  //     this.chartData = {\n  //         labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\n  //         datasets: [\n  //             {\n  //                 label: 'First Dataset',\n  //                 data: [65, 59, 80, 81, 56, 55, 40],\n  //                 fill: false,\n  //                 backgroundColor: documentStyle.getPropertyValue('--bluegray-700'),\n  //                 borderColor: documentStyle.getPropertyValue('--bluegray-700'),\n  //                 tension: .4\n  //             },\n  //             {\n  //                 label: 'Second Dataset',\n  //                 data: [28, 48, 40, 19, 86, 27, 90],\n  //                 fill: false,\n  //                 backgroundColor: documentStyle.getPropertyValue('--green-600'),\n  //                 borderColor: documentStyle.getPropertyValue('--green-600'),\n  //                 tension: .4\n  //             }\n  //         ]\n  //     };\n  //     this.chartOptions = {\n  //         plugins: {\n  //             legend: {\n  //                 labels: {\n  //                     color: textColor\n  //                 }\n  //             }\n  //         },\n  //         scales: {\n  //             x: {\n  //                 ticks: {\n  //                     color: textColorSecondary\n  //                 },\n  //                 grid: {\n  //                     color: surfaceBorder,\n  //                     drawBorder: false\n  //                 }\n  //             },\n  //             y: {\n  //                 ticks: {\n  //                     color: textColorSecondary\n  //                 },\n  //                 grid: {\n  //                     color: surfaceBorder,\n  //                     drawBorder: false\n  //                 }\n  //             }\n  //         }\n  //     };\n  // }\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n  static #_ = this.ɵfac = function IndexComponent_Factory(t) {\n    return new (t || IndexComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.StationsService), i0.ɵɵdirectiveInject(i3.ProvidersService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.FormBuilder), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.CacheService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: IndexComponent,\n    selectors: [[\"ng-component\"]],\n    decls: 27,\n    vars: 10,\n    consts: [[1, \"grid\"], [1, \"col-12\", \"lg:col-6\", \"xl:col-6\"], [1, \"card\", \"mb-0\"], [1, \"flex\", \"justify-content-between\", \"mb-3\"], [1, \"block\", \"text-500\", \"font-medium\", \"font-bold\", \"mb-3\"], [1, \"text-900\", \"font-medium\", \"text-xl\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-blue-100\", \"border-round\", 3, \"ngStyle\"], [\"icon\", \"solar-panel\"], [\"icon\", \"plug\"], [1, \"col-12\", \"lg:col-12\", \"xl:col-12\"], [1, \"card\"], [\"filterBy\", \"name\", \"layout\", \"list\", 3, \"value\", \"paginator\", \"rows\", \"sortField\", \"sortOrder\"], [\"dv\", \"\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"listItem\"], [1, \"flex\", \"flex-column\", \"md:flex-row\", \"md:justify-content-between\", \"gap-2\"], [\"placeholder\", \"Country\", 3, \"options\", \"onChange\"], [\"placeholder\", \"Status\", 3, \"options\", \"onChange\"], [1, \"p-input-icon-left\"], [1, \"pi\", \"pi-search\"], [\"type\", \"search\", \"pInputText\", \"\", \"placeholder\", \"Search\", 3, \"input\"], [3, \"class\", 4, \"ngFor\", \"ngForOf\"], [1, \"\"], [1, \"col-12\", \"md:col-6\", \"lg:col-3\"], [1, \"provider-text\"], [1, \"font-bold\", \"text-xl\"], [3, \"routerLink\"], [1, \"font-italic\"], [\"icon\", \"clock\"], [1, \"col-12\", \"md:col-6\", \"lg:col-1\"], [1, \"text-center\", \"font-bold\"], [\"icon\", \"sun\"], [1, \"col-12\", \"md:col-6\", \"lg:col-2\"], [\"type\", \"line\", \"class\", \"mx-auto\", \"height\", \"40\", \"width\", \"80\", 3, \"data\", \"options\", 4, \"ngIf\"], [\"class\", \"text-center p-2\", 4, \"ngIf\"], [1, \"col-12\", \"md:col-6\", \"lg:col-3\", \"text-center\"], [\"class\", \"pad-2 \", \"size\", \"large\", 3, \"value\", 4, \"ngIf\"], [\"size\", \"large\", \"severity\", \"success\", 1, \"pad-2\", 3, \"value\"], [\"size\", \"large\", \"severity\", \"info\", 1, \"pad-2\", 3, \"value\"], [\"size\", \"large\", \"severity\", \"warning\", 1, \"pad-2\", 3, \"value\"], [1, \"col-12\", \"md:col-6\", \"lg:col-2\", \"text-center\"], [1, \"text-center\"], [1, \"pi\", \"pi-cloud\", \"text-2xl\"], [1, \"text-2xl\", \"mb-2\", \"align-self-center\", \"md:align-self-end\"], [\"type\", \"line\", \"height\", \"40\", \"width\", \"80\", 1, \"mx-auto\", 3, \"data\", \"options\"], [1, \"text-center\", \"p-2\"], [1, \"pi\", \"pi-spin\", \"pi-spinner\", 2, \"font-size\", \"1.5rem\"], [\"size\", \"large\", 1, \"pad-2\", 3, \"value\"]],\n    template: function IndexComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\")(5, \"span\", 4);\n        i0.ɵɵtext(6, \"Active Energy Systems\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"div\", 5);\n        i0.ɵɵtext(8);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(9, \"div\", 6);\n        i0.ɵɵelement(10, \"fa-icon\", 7);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(11, \"div\", 1)(12, \"div\", 2)(13, \"div\", 3)(14, \"div\")(15, \"span\", 4);\n        i0.ɵɵtext(16, \"Combined Power Permormance\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"div\", 5);\n        i0.ɵɵtext(18, \"33% (4.124kW) \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(19, \"div\", 6);\n        i0.ɵɵelement(20, \"fa-icon\", 8);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(21, \"div\", 9)(22, \"div\", 10)(23, \"p-dataView\", 11, 12);\n        i0.ɵɵtemplate(25, IndexComponent_ng_template_25_Template, 6, 2, \"ng-template\", 13)(26, IndexComponent_ng_template_26_Template, 1, 1, \"ng-template\", 14);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(8);\n        i0.ɵɵtextInterpolate(ctx.stations.length);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(8, _c1));\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(9, _c1));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"value\", ctx.stations)(\"paginator\", true)(\"rows\", 9)(\"sortField\", ctx.sortField)(\"sortOrder\", ctx.sortOrder);\n      }\n    },\n    dependencies: [i8.NgForOf, i8.NgIf, i8.NgStyle, i6.RouterLink, i4.PrimeTemplate, i9.DataView, i10.InputText, i11.Dropdown, i12.ChartjsComponent, i13.Badge, i14.FaIconComponent],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["debounceTime", "i0", "ɵɵelementStart", "ɵɵlistener", "IndexComponent_ng_template_25_Template_p_dropdown_onChange_1_listener", "$event", "ɵɵrestoreView", "_r4", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "onSortChange", "ɵɵelementEnd", "IndexComponent_ng_template_25_Template_p_dropdown_onChange_2_listener", "ctx_r5", "ɵɵelement", "IndexComponent_ng_template_25_Template_input_input_5_listener", "ctx_r6", "_r0", "ɵɵreference", "onFilter", "ɵɵadvance", "ɵɵproperty", "ctx_r1", "sortOptionsCountry", "sortOptionsStatus", "ctx_r10", "getStationsRealTimeData", "station_r9", "lineOptions", "ɵɵtext", "ɵɵpropertyInterpolate", "devicesIds", "split", "length", "ɵɵtemplate", "IndexComponent_ng_template_26_div_0_c_chart_21_Template", "IndexComponent_ng_template_26_div_0_div_22_Template", "IndexComponent_ng_template_26_div_0_p_badge_24_Template", "ɵɵclassMap", "status", "toLowerCase", "ɵɵtextInterpolate1", "provider", "ɵɵpureFunction1", "_c0", "id", "ɵɵtextInterpolate", "name", "ctx_r8", "getStationLastUpdate", "getStationsSumData", "irradiance", "mmpt", "string", "pvn", "temperature", "IndexComponent_ng_template_26_div_0_Template", "ctx_r2", "stations", "IndexComponent", "constructor", "layoutService", "stationsService", "providersService", "messageService", "fb", "router", "cacheService", "sortOrder", "sortField", "sourceCities", "targetCities", "orderCities", "stationsData", "Map", "stationsRawData", "stationsSumData", "subscription", "configUpdate$", "pipe", "subscribe", "config", "ngOnInit", "getUserProviders", "barOptions", "maintainAspectRatio", "plugins", "legend", "display", "scales", "x", "y", "elements", "line", "tension", "point", "radius", "then", "providersData", "getUserStations", "setStations", "console", "log", "loadAllStationsData", "navigate", "for<PERSON>ach", "station", "loadStationData", "now", "Date", "formattedStartDate", "getFullYear", "getMonth", "getDate", "toISOString", "formattedEndDate", "request", "devIds", "deviceIds", "devTypeId", "startDateTime", "endDateTime", "separated", "searchType", "stationId", "getStationHistoricData", "data", "documentStyle", "getComputedStyle", "document", "documentElement", "lineData", "labels", "map", "e", "index", "dateTime", "toLocaleTimeString", "hour", "minute", "datasets", "label", "activePower", "fill", "backgroundColor", "getPropertyValue", "borderColor", "totalInputPower", "set", "sum", "catch", "error", "has", "get", "latest", "reduce", "latestSoFar", "current", "event", "value", "indexOf", "substring", "dv", "filter", "target", "initMap", "mapSrc", "ngOnDestroy", "unsubscribe", "_", "ɵɵdirectiveInject", "i1", "LayoutService", "i2", "StationsService", "i3", "ProvidersService", "i4", "MessageService", "i5", "FormBuilder", "i6", "Router", "i7", "CacheService", "_2", "selectors", "decls", "vars", "consts", "template", "IndexComponent_Template", "rf", "ctx", "IndexComponent_ng_template_25_Template", "IndexComponent_ng_template_26_Template", "ɵɵpureFunction0", "_c1"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\index\\index.component.ts", "D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\index\\index.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { MenuItem, MessageService } from 'primeng/api';\r\nimport { Product } from '../../api/product';\r\nimport { ProductService } from '../../service/product.service';\r\nimport { Subscription, debounceTime } from 'rxjs';\r\nimport { LayoutService } from 'src/app/layout/service/app.layout.service';\r\nimport { SelectItem } from 'primeng/api';\r\nimport { DataView } from 'primeng/dataview';\r\nimport { StationsService } from '../../service/stations.service';\r\nimport { Station } from '../../api/station';\r\nimport { CacheService } from '../../service/cache.service';\r\nimport { ProvidersService } from '../../service/providers.service';\r\nimport { IProvider, IUserProvider } from '../../api/responses';\r\nimport { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { GetHistoricDataRequest, SaveUserProvidersRequest } from '../../api/requests';\r\nimport { Router } from '@angular/router';\r\n\r\n\r\n@Component({\r\n    templateUrl: './index.component.html',\r\n})\r\nexport class IndexComponent implements OnInit, OnDestroy {\r\n\r\n    items!: MenuItem[];\r\n\r\n    stations: Station[] =[];\r\n\r\n    chartData: any;\r\n\r\n    chartOptions: any;\r\n\r\n    subscription!: Subscription;\r\n\r\n    sortOptionsCountry: SelectItem[] = [];\r\n\r\n    sortOptionsStatus: SelectItem[] = [];\r\n\r\n    sortOrder: number = 0;\r\n\r\n    sortField: string = '';\r\n\r\n    sourceCities: any[] = [];\r\n\r\n    targetCities: any[] = [];\r\n\r\n    orderCities: any[] = [];\r\n\r\n    mapSrc:string;\r\n\r\n    barOptions:any;\r\n    lineOptions:any;\r\n    stationsData:Map<string,any> = new Map();\r\n    stationsRawData:Map<string,any> = new Map();\r\n    stationsSumData:Map<string,number> = new Map();\r\n    \r\n\r\n    constructor(public layoutService: LayoutService, \r\n        private stationsService: StationsService,\r\n        private providersService: ProvidersService,\r\n        private messageService: MessageService,\r\n        private fb: FormBuilder,\r\n        private router: Router,\r\n        private cacheService: CacheService) {\r\n        this.subscription = this.layoutService.configUpdate$\r\n        .pipe(debounceTime(25))\r\n        .subscribe((config) => {\r\n         //   this.initChart();\r\n        });\r\n    }\r\n\r\n    ngOnInit() {\r\n        this.getUserProviders();\r\n\r\n        this.barOptions = {\r\n            maintainAspectRatio: false,\r\n            plugins: {\r\n              legend: {\r\n                display: false\r\n              }\r\n            },\r\n            scales: {\r\n              x: {\r\n                display: false\r\n              },\r\n              y: {\r\n                display: false\r\n              }\r\n            }\r\n          };\r\n        \r\n          this.lineOptions = {\r\n            maintainAspectRatio: false,\r\n            elements: {\r\n              line: {\r\n                tension: 0.4\r\n              },\r\n              point: {\r\n                radius: 0\r\n              }\r\n            },\r\n            plugins: {\r\n              legend: {\r\n                display: false\r\n              }\r\n            },\r\n            scales: {\r\n              x: {\r\n                display: false\r\n              },\r\n              y: {\r\n                display: false\r\n              }\r\n            }\r\n          };\r\n\r\n\r\n          \r\n        \r\n    }\r\n\r\n    getUserProviders(){\r\n      this.providersService.getUserProviders().then(providersData => {\r\n        if (providersData.length > 0){\r\n          this.stationsService.getUserStations().then(stationsData => {\r\n            this.cacheService.setStations(stationsData);\r\n            this.stations = stationsData;\r\n            console.log(\"stations set\")\r\n            console.log(stationsData)\r\n            // Φορτώνουμε τα δεδομένα για κάθε σταθμό\r\n            this.loadAllStationsData();\r\n          });\r\n        }else{\r\n          this.router.navigate(['/app/providers']); \r\n        }\r\n      });\r\n    }\r\n\r\n    // Νέα μέθοδος για φόρτωση δεδομένων όλων των σταθμών\r\n    loadAllStationsData() {\r\n        if (!this.stations || this.stations.length === 0) return;\r\n        \r\n        // Φορτώνουμε τα δεδομένα για κάθε σταθμό\r\n        this.stations.forEach(station => {\r\n            this.loadStationData(station);\r\n        });\r\n    }\r\n\r\n    // Νέα μέθοδος για φόρτωση δεδομένων ενός σταθμού\r\n    loadStationData(station: Station) {\r\n        if (!station) return;\r\n        \r\n        const now = new Date();\r\n        const formattedStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0).toISOString();\r\n        const formattedEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0).toISOString();\r\n        \r\n        let request: GetHistoricDataRequest = {\r\n            devIds: station.deviceIds,\r\n            devTypeId: 1,\r\n            startDateTime: formattedStartDate,\r\n            endDateTime: formattedEndDate,\r\n            separated: false,\r\n            searchType: null,\r\n            stationId: station.id\r\n        };\r\n        \r\n        this.stationsService.getStationHistoricData(request).then(data => {\r\n            if (data && data.data) {\r\n                const documentStyle = getComputedStyle(document.documentElement);\r\n                \r\n                const lineData = {\r\n                    labels: data.data.map((e, index) => \r\n                        index % 2 === 0 ? new Date(e.dateTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : ''\r\n                    ),\r\n                    datasets: [\r\n                        {\r\n                            label: 'Active Power',\r\n                            data: data.data.map(e => e.activePower),\r\n                            fill: false,\r\n                            backgroundColor: documentStyle.getPropertyValue('--primary-500'),\r\n                            borderColor: documentStyle.getPropertyValue('--primary-500'),\r\n                            tension: .4\r\n                        },\r\n                        {\r\n                            label: 'Total Input Power',\r\n                            data: data.data.map(e => e.totalInputPower),\r\n                            fill: false,\r\n                            backgroundColor: documentStyle.getPropertyValue('--primary-200'),\r\n                            borderColor: documentStyle.getPropertyValue('--primary-200'),\r\n                            tension: .4\r\n                        }\r\n                    ]\r\n                };\r\n                \r\n                // Αποθηκεύουμε τα δεδομένα στο Map με κλειδί το ID του σταθμού\r\n                this.stationsData.set(station.id, lineData);\r\n                this.stationsRawData.set(station.id, data.data);\r\n                this.stationsSumData.set(station.id, data.sum);\r\n            }\r\n        }).catch(error => {\r\n            console.error(`Error loading data for station ${station.id}:`, error);\r\n        });\r\n    }\r\n    \r\n\r\n    // Τροποποιημένη μέθοδος που επιστρέφει τα προ-φορτωμένα δεδομένα\r\n    getStationsRealTimeData(station: Station) {\r\n        // Επιστρέφουμε τα δεδομένα από το Map αν υπάρχουν\r\n        if (station && station.id && this.stationsData.has(station.id)) {\r\n            return this.stationsData.get(station.id);\r\n        }\r\n        \r\n        // Αν δεν έχουν φορτωθεί ακόμα, επιστρέφουμε null\r\n        return null;\r\n    }\r\n\r\n    getStationsSumData(stationId:string){\r\n      return this.stationsSumData.get(stationId);\r\n    }\r\n\r\n    getStationLastUpdate(stationId:string){\r\n      var data = this.stationsRawData.get(stationId);\r\n      if (!data || data.length === 0) return;\r\n\r\n      const latest = data.reduce((latestSoFar, current) => {\r\n        return new Date(current.dateTime) > new Date(latestSoFar.dateTime)\r\n          ? current\r\n          : latestSoFar;\r\n      });\r\n\r\n      return latest.dateTime;\r\n    }\r\n    \r\n\r\n    onSortChange(event: any) {\r\n        const value = event.value;\r\n\r\n        if (value.indexOf('!') === 0) {\r\n            this.sortOrder = -1;\r\n            this.sortField = value.substring(1, value.length);\r\n        } else {\r\n            this.sortOrder = 1;\r\n            this.sortField = value;\r\n        }\r\n    }\r\n\r\n    onFilter(dv: DataView, event: Event) {\r\n        dv.filter((event.target as HTMLInputElement).value);\r\n    }\r\n\r\n\r\n    initMap(){\r\n        this.mapSrc = \"https://www.digitaltrends.com/wp-content/uploads/2020/02/custom-map.jpg?fit=720%2C479&p=1\"\r\n    }\r\n\r\n    // initChart() {\r\n    //     const documentStyle = getComputedStyle(document.documentElement);\r\n    //     const textColor = documentStyle.getPropertyValue('--text-color');\r\n    //     const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\r\n    //     const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\r\n\r\n    //     this.chartData = {\r\n    //         labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\r\n    //         datasets: [\r\n    //             {\r\n    //                 label: 'First Dataset',\r\n    //                 data: [65, 59, 80, 81, 56, 55, 40],\r\n    //                 fill: false,\r\n    //                 backgroundColor: documentStyle.getPropertyValue('--bluegray-700'),\r\n    //                 borderColor: documentStyle.getPropertyValue('--bluegray-700'),\r\n    //                 tension: .4\r\n    //             },\r\n    //             {\r\n    //                 label: 'Second Dataset',\r\n    //                 data: [28, 48, 40, 19, 86, 27, 90],\r\n    //                 fill: false,\r\n    //                 backgroundColor: documentStyle.getPropertyValue('--green-600'),\r\n    //                 borderColor: documentStyle.getPropertyValue('--green-600'),\r\n    //                 tension: .4\r\n    //             }\r\n    //         ]\r\n    //     };\r\n\r\n    //     this.chartOptions = {\r\n    //         plugins: {\r\n    //             legend: {\r\n    //                 labels: {\r\n    //                     color: textColor\r\n    //                 }\r\n    //             }\r\n    //         },\r\n    //         scales: {\r\n    //             x: {\r\n    //                 ticks: {\r\n    //                     color: textColorSecondary\r\n    //                 },\r\n    //                 grid: {\r\n    //                     color: surfaceBorder,\r\n    //                     drawBorder: false\r\n    //                 }\r\n    //             },\r\n    //             y: {\r\n    //                 ticks: {\r\n    //                     color: textColorSecondary\r\n    //                 },\r\n    //                 grid: {\r\n    //                     color: surfaceBorder,\r\n    //                     drawBorder: false\r\n    //                 }\r\n    //             }\r\n    //         }\r\n    //     };\r\n    // }\r\n\r\n    ngOnDestroy() {\r\n        if (this.subscription) {\r\n            this.subscription.unsubscribe();\r\n        }\r\n    }\r\n}\r\n", "<div class=\"grid\">\r\n    <div class=\"col-12 lg:col-6 xl:col-6\">\r\n        <div class=\"card mb-0\">\r\n            <div class=\"flex justify-content-between mb-3\">\r\n                <div>\r\n                    <span class=\"block text-500 font-medium font-bold mb-3\">Active Energy Systems</span>\r\n                    <div class=\"text-900 font-medium text-xl\">{{stations.length}}</div>\r\n                </div>\r\n                <div class=\"flex align-items-center justify-content-center bg-blue-100 border-round\" [ngStyle]=\"{width: '2.5rem', height: '2.5rem'}\">\r\n                    <fa-icon icon=\"solar-panel\"></fa-icon> \r\n                </div>\r\n            </div>\r\n            <!-- <span class=\"text-green-500 font-medium\">24 new </span>\r\n            <span class=\"text-500\">since last visit</span> -->\r\n        </div>\r\n    </div>\r\n    <div class=\"col-12 lg:col-6 xl:col-6\">\r\n        <div class=\"card mb-0\">\r\n            <div class=\"flex justify-content-between mb-3\">\r\n                <div>\r\n                    <span class=\"block text-500 font-medium font-bold mb-3\">Combined Power Permormance</span>\r\n                    <div class=\"text-900 font-medium text-xl\">33% (4.124kW) </div>\r\n                </div>\r\n                <div class=\"flex align-items-center justify-content-center bg-blue-100 border-round\" [ngStyle]=\"{width: '2.5rem', height: '2.5rem'}\">\r\n                    <fa-icon icon=\"plug\"></fa-icon> \r\n                </div>\r\n            </div>\r\n            <!-- <span class=\"text-green-500 font-medium\">24 new </span>\r\n            <span class=\"text-500\">since last visit</span> -->\r\n        </div>\r\n    </div>\r\n\t \r\n\r\n    <div class=\"col-12 lg:col-12 xl:col-12\">\r\n\t\t<div class=\"card\">\r\n\t\t\t<p-dataView #dv [value]=\"stations\" [paginator]=\"true\" [rows]=\"9\" filterBy=\"name\" [sortField]=\"sortField\" [sortOrder]=\"sortOrder\" layout=\"list\">\r\n\t\t\t\t<ng-template pTemplate=\"header\">\r\n\t\t\t\t\t<div class=\"flex flex-column md:flex-row md:justify-content-between gap-2\">\r\n\t\t\t\t\t\t<p-dropdown [options]=\"sortOptionsCountry\" placeholder=\"Country\" (onChange)=\"onSortChange($event)\"></p-dropdown>\r\n\t\t\t\t\t\t<p-dropdown [options]=\"sortOptionsStatus\" placeholder=\"Status\" (onChange)=\"onSortChange($event)\"></p-dropdown>\r\n\t\t\t\t\t\t<span class=\"p-input-icon-left\">\r\n                            <i class=\"pi pi-search\"></i>\r\n                            <input type=\"search\" pInputText placeholder=\"Search\" (input)=\"onFilter(dv, $event)\">\r\n                        </span>\t\r\n\t\t\t\t\t\t<!-- <p-dataViewLayoutOptions></p-dataViewLayoutOptions> -->\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</ng-template>\r\n\r\n\t\t\t\t<ng-template let-products pTemplate=\"listItem\">\r\n\t\t\t\t\t<div *ngFor=\"let station of stations\" [class]=\"'station-box-status-' + station.status.toLowerCase()\">\r\n\t\t\t\t\t\t<div class=\"\">\r\n\t\t\t\t\t\t\t<div class=\"grid\">\r\n\t\t\t\t\t\t\t\t<div class=\"col-12 md:col-6 lg:col-3\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"provider-text\">\r\n\t\t\t\t\t\t\t\t\t\t<!-- <fa-icon icon=\"truck\"></fa-icon> -->\r\n\t\t\t\t\t\t\t\t\t\t{{station.provider}}\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"font-bold text-xl\"><a [routerLink]=\"['/app/station',station.id]\">{{station.name}}</a></div>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<div class=\"font-italic\"><fa-icon icon=\"clock\"></fa-icon> {{getStationLastUpdate(station.id)}} \r\n\t\t\t\t\t\t\t\t\t\t<!-- <span [class]=\"'station-badge status-' + station.status.toLowerCase()\">{{station.status.toLowerCase()}}</span> -->\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"col-12 md:col-6 lg:col-1\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"text-center font-bold\"><fa-icon icon=\"plug\"></fa-icon> {{getStationsSumData(station.id)}}kW</div>\t\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"col-12 md:col-6 lg:col-1\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"text-center font-bold\"><fa-icon icon=\"sun\"></fa-icon>  {{station.irradiance}}kWh/m2</div>\t\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"col-12 md:col-6 lg:col-2\">\r\n\t\t\t\t\t\t\t\t\t<c-chart *ngIf=\"getStationsRealTimeData(station)\" [data]=\"getStationsRealTimeData(station)\" [options]=\"lineOptions\" type=\"line\" class=\"mx-auto\" height=\"40\" width=\"80\" />\r\n\t\t\t\t\t\t\t\t\t<div *ngIf=\"!getStationsRealTimeData(station)\" class=\"text-center p-2\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"pi pi-spin pi-spinner\" style=\"font-size: 1.5rem\"></i>\r\n\t\t\t\t\t\t\t\t\t\t<p>Loading data...</p>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<!-- <div class=\"\">Inverter: {{station.inverter}} | MMPT: {{station.mmpt}} | String: {{station.string}} | PVN: {{station.pvn}}</div> -->\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"col-12 md:col-6 lg:col-3 text-center\">\r\n\t\t\t\t\t\t\t\t\t<p-badge *ngIf=\"station.devicesIds\" class=\"pad-2 \" value=\"{{station.devicesIds.split(',').length}}\" size=\"large\"></p-badge>\r\n\t\t\t\t\t\t\t\t\t<p-badge class=\"pad-2\" value=\"{{station.mmpt}}\" size=\"large\"  severity=\"success\"></p-badge>\r\n\t\t\t\t\t\t\t\t\t<p-badge class=\"pad-2\" value=\"{{station.string}}\" size=\"large\" severity=\"info\"></p-badge>\r\n\t\t\t\t\t\t\t\t\t<p-badge class=\"pad-2\" value=\"{{station.pvn}}\" size=\"large\" severity=\"warning\"></p-badge>\r\n\t\t\t\t\t\t\t\t\t<!-- <div class=\"grid\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"col-3 md:col-3 lg:col-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\">Inverter</p>\r\n\t\t\t\t\t\t\t\t\t\t\t<p-badge [value]=\"2\" severity=\"success\" />\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"col-3 md:col-3 lg:col-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\">MMPT</p>\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\"><p-knob [(ngModel)]=\"station.mmpt\" valueTemplate=\"{{station.mmpt}}\" [step]=\"1\" [min]=\"0\" [max]=\"5\" [size]=\"50\"></p-knob></p>\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"col-3 md:col-3 lg:col-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\">String</p>\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\"><p-knob [(ngModel)]=\"station.string\" valueTemplate=\"{{station.string}}\" [step]=\"1\" [min]=\"0\" [max]=\"5\" [size]=\"50\"></p-knob></p>\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"col-3 md:col-3 lg:col-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\">PVN</p>\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\"><p-knob [(ngModel)]=\"station.pvn\" valueTemplate=\"{{station.pvn}}\" [step]=\"1\" [min]=\"0\" [max]=\"5\" [size]=\"50\"></p-knob></p>\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\t -->\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"col-12 md:col-6 lg:col-2 text-center\">\r\n\t\t\t\t\t\t\t\t\t<p class=\"text-center\"><i class=\"pi pi-cloud text-2xl\"></i>\r\n\t\t\t\t\t\t\t\t\t<span class=\"text-2xl mb-2 align-self-center md:align-self-end\"> {{station.temperature}} &#x2103;</span></p>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<!-- <div class=\"card\">\r\n\t\t\t\t\t\t\t<div class=\"flex flex-column md:flex-row align-items-center p-3 w-full\">\r\n\t\t\t\t\t\t\t\t<div class=\"flex-1 flex flex-column md:text-left\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"flex align-items-center\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"font-bold text-xl\"><a [routerLink]=\"['/uikit/station',station.id]\">{{station.name}}</a></div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"font-bold flex-auto text-xl align-items-right text-right\">{{station.updateTime}} <span [class]=\"'station-badge status-' + station.status.toLowerCase()\">{{station.status.toLowerCase()}}</span></div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<hr>\r\n\t\t\t\t\t\t\t\t\t<div class=\"mb-2\">{{station.location}}</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"flex align-items-center mt-2\">\r\n\t\t\t\t\t\t\t\t\t\t<p-knob [(ngModel)]=\"station.percentage\" valueTemplate=\"{value}%\" [step]=\"10\" [min]=\"0\" [max]=\"100\" [size]=\"70\"></p-knob>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"align-items-right\">\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"mt-2 text-xl\">Active Performance</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-2 text-sm\">Power: {{station.power}}kW | Irradiance: {{station.irradiance}}kWh/m2</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"mt-2\">Inverter: {{station.inverter}} | MMPT: {{station.mmpt}} | String: {{station.string}} | PVN: {{station.pvn}}</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"flex flex-row md:flex-column justify-content-between w-full md:w-auto align-items-center md:align-items-end mt-5 md:mt-0\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"pi pi-cloud text-2xl\"></i>\r\n\t\t\t\t\t\t\t\t\t\t<span class=\"text-2xl font-semibold mb-2 align-self-center md:align-self-end\">{{station.temperature}} &#x2103;</span>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div> -->\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</ng-template>\r\n\r\n\t\t\t\t<!-- <ng-template let-products pTemplate=\"listItem\">\r\n\t\t\t\t\t<div class=\"col-12\" *ngFor=\"let station of stations\">\r\n\t\t\t\t\t\t<div class=\"card\">\r\n\t\t\t\t\t\t\t<div class=\"flex flex-column md:flex-row align-items-center p-3 w-full\">\r\n\t\t\t\t\t\t\t\t<div class=\"flex-1 flex flex-column md:text-left\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"flex align-items-center\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"font-bold text-xl\"><a [routerLink]=\"['/uikit/station',station.id]\">{{station.name}}</a></div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"font-bold flex-auto text-xl align-items-right text-right\">{{station.updateTime}} <span [class]=\"'station-badge status-' + station.status.toLowerCase()\">{{station.status.toLowerCase()}}</span></div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<hr>\r\n\t\t\t\t\t\t\t\t\t<div class=\"mb-2\">{{station.location}}</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"flex align-items-center mt-2\">\r\n\t\t\t\t\t\t\t\t\t\t<p-knob [(ngModel)]=\"station.percentage\" valueTemplate=\"{value}%\" [step]=\"10\" [min]=\"0\" [max]=\"100\" [size]=\"70\"></p-knob>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"align-items-right\">\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"mt-2 text-xl\">Active Performance</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-2 text-sm\">Power: {{station.power}}kW | Irradiance: {{station.irradiance}}kWh/m2</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"mt-2\">Inverter: {{station.inverter}} | MMPT: {{station.mmpt}} | String: {{station.string}} | PVN: {{station.pvn}}</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"flex flex-row md:flex-column justify-content-between w-full md:w-auto align-items-center md:align-items-end mt-5 md:mt-0\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"pi pi-cloud text-2xl\"></i>\r\n\t\t\t\t\t\t\t\t\t\t<span class=\"text-2xl font-semibold mb-2 align-self-center md:align-self-end\">{{station.temperature}} &#x2103;</span>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</ng-template> -->\r\n\r\n\t\t\t\t<!-- <ng-template let-products pTemplate=\"gridItem\">\r\n\t\t\t\t\t<div class=\"grid grid-nogutter\">\r\n\t\t\t\t\t<div class=\"col-12 md:col-4\" *ngFor=\"let station of stations\">\r\n\t\t\t\t\t\t<div class=\"card m-3 border-1 surface-border\">\r\n\t\t\t\t\t\t\t<div class=\"flex flex-wrap gap-2 align-items-center justify-content-between mb-2\">\r\n\t\t\t\t\t\t\t\t<div class=\"flex align-items-center\">\r\n\t\t\t\t\t\t\t\t\t<i class=\"pi pi-tag mr-2\"></i>\r\n\t\t\t\t\t\t\t\t\t<span class=\"font-semibold\">{{station.name}}</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"flex flex-column align-items-center text-center mb-3\">\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<div class=\"text-2xl font-bold\">{{station.name}}</div>\r\n\t\t\t\t\t\t\t\t<div class=\"mb-3\">{{station.location}}</div>\r\n\t\t\t\t\t\t\t\t<p-rating [ngModel]=\"station.temperature\" [readonly]=\"true\" [cancel]=\"false\"></p-rating>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"flex align-items-center justify-content-between\">\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</ng-template> -->\r\n\t\t\t</p-dataView>\r\n\t\t</div>\r\n\t</div>\r\n\r\n    <!-- <div class=\"col-12 lg:col-6 xl:col-6\">\r\n\t\t<div class=\"card\">\r\n\t\t\t<img src=\"https://www.digitaltrends.com/wp-content/uploads/2020/02/custom-map.jpg?fit=720%2C479&p=1\" >\r\n\t\t</div>\r\n\t</div> -->\r\n</div>"], "mappings": "AAIA,SAAuBA,YAAY,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;ICiC5CC,EAAA,CAAAC,cAAA,cAA2E;IACTD,EAAA,CAAAE,UAAA,sBAAAC,sEAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAYR,EAAA,CAAAS,WAAA,CAAAF,MAAA,CAAAG,YAAA,CAAAN,MAAA,CAAoB;IAAA,EAAC;IAACJ,EAAA,CAAAW,YAAA,EAAa;IAChHX,EAAA,CAAAC,cAAA,qBAAiG;IAAlCD,EAAA,CAAAE,UAAA,sBAAAU,sEAAAR,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAO,MAAA,GAAAb,EAAA,CAAAQ,aAAA;MAAA,OAAYR,EAAA,CAAAS,WAAA,CAAAI,MAAA,CAAAH,YAAA,CAAAN,MAAA,CAAoB;IAAA,EAAC;IAACJ,EAAA,CAAAW,YAAA,EAAa;IAC9GX,EAAA,CAAAC,cAAA,eAAgC;IACVD,EAAA,CAAAc,SAAA,YAA4B;IAC5Bd,EAAA,CAAAC,cAAA,gBAAoF;IAA/BD,EAAA,CAAAE,UAAA,mBAAAa,8DAAAX,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAU,MAAA,GAAAhB,EAAA,CAAAQ,aAAA;MAAA,MAAAS,GAAA,GAAAjB,EAAA,CAAAkB,WAAA;MAAA,OAASlB,EAAA,CAAAS,WAAA,CAAAO,MAAA,CAAAG,QAAA,CAAAF,GAAA,EAAAb,MAAA,CAAoB;IAAA,EAAC;IAAnFJ,EAAA,CAAAW,YAAA,EAAoF;;;;IAJ9FX,EAAA,CAAAoB,SAAA,GAA8B;IAA9BpB,EAAA,CAAAqB,UAAA,YAAAC,MAAA,CAAAC,kBAAA,CAA8B;IAC9BvB,EAAA,CAAAoB,SAAA,GAA6B;IAA7BpB,EAAA,CAAAqB,UAAA,YAAAC,MAAA,CAAAE,iBAAA,CAA6B;;;;;IA+BtCxB,EAAA,CAAAc,SAAA,kBAAyK;;;;;IAAvHd,EAAA,CAAAqB,UAAA,SAAAI,OAAA,CAAAC,uBAAA,CAAAC,UAAA,EAAyC,YAAAF,OAAA,CAAAG,WAAA;;;;;IAC3F5B,EAAA,CAAAC,cAAA,cAAuE;IACtED,EAAA,CAAAc,SAAA,YAA+D;IAC/Dd,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAA6B,MAAA,sBAAe;IAAA7B,EAAA,CAAAW,YAAA,EAAI;;;;;IAKvBX,EAAA,CAAAc,SAAA,kBAA2H;;;;IAAxEd,EAAA,CAAA8B,qBAAA,UAAAH,UAAA,CAAAI,UAAA,CAAAC,KAAA,MAAAC,MAAA,CAAgD;;;;;;IA7BvGjC,EAAA,CAAAC,cAAA,UAAqG;IAMhGD,EAAA,CAAA6B,MAAA,GACD;IAAA7B,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,cAA+B;IAA8CD,EAAA,CAAA6B,MAAA,GAAgB;IAAA7B,EAAA,CAAAW,YAAA,EAAI;IAEjGX,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAc,SAAA,mBAAgC;IAACd,EAAA,CAAA6B,MAAA,IACzD;IACD7B,EAAA,CAAAW,YAAA,EAAM;IAEPX,EAAA,CAAAC,cAAA,eAAsC;IACFD,EAAA,CAAAc,SAAA,kBAA+B;IAACd,EAAA,CAAA6B,MAAA,IAAoC;IAAA7B,EAAA,CAAAW,YAAA,EAAM;IAE9GX,EAAA,CAAAC,cAAA,eAAsC;IACFD,EAAA,CAAAc,SAAA,mBAA8B;IAAEd,EAAA,CAAA6B,MAAA,IAA4B;IAAA7B,EAAA,CAAAW,YAAA,EAAM;IAEtGX,EAAA,CAAAC,cAAA,eAAsC;IACrCD,EAAA,CAAAkC,UAAA,KAAAC,uDAAA,sBAAyK,KAAAC,mDAAA;IAM1KpC,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAAkD;IACjDD,EAAA,CAAAkC,UAAA,KAAAG,uDAAA,sBAA2H;IAC3HrC,EAAA,CAAAc,SAAA,mBAA2F;IAwB5Fd,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAAkD;IAC1BD,EAAA,CAAAc,SAAA,aAAoC;IAC3Dd,EAAA,CAAAC,cAAA,gBAAgE;IAACD,EAAA,CAAA6B,MAAA,IAAgC;IAAA7B,EAAA,CAAAW,YAAA,EAAO;;;;;IAzDtEX,EAAA,CAAAsC,UAAA,yBAAAX,UAAA,CAAAY,MAAA,CAAAC,WAAA,GAA8D;IAM/FxC,EAAA,CAAAoB,SAAA,GACD;IADCpB,EAAA,CAAAyC,kBAAA,MAAAd,UAAA,CAAAe,QAAA,MACD;IACkC1C,EAAA,CAAAoB,SAAA,GAA0C;IAA1CpB,EAAA,CAAAqB,UAAA,eAAArB,EAAA,CAAA2C,eAAA,KAAAC,GAAA,EAAAjB,UAAA,CAAAkB,EAAA,EAA0C;IAAC7C,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAA8C,iBAAA,CAAAnB,UAAA,CAAAoB,IAAA,CAAgB;IAEnC/C,EAAA,CAAAoB,SAAA,GACzD;IADyDpB,EAAA,CAAAyC,kBAAA,MAAAO,MAAA,CAAAC,oBAAA,CAAAtB,UAAA,CAAAkB,EAAA,OACzD;IAIkE7C,EAAA,CAAAoB,SAAA,GAAoC;IAApCpB,EAAA,CAAAyC,kBAAA,MAAAO,MAAA,CAAAE,kBAAA,CAAAvB,UAAA,CAAAkB,EAAA,QAAoC;IAGpC7C,EAAA,CAAAoB,SAAA,GAA4B;IAA5BpB,EAAA,CAAAyC,kBAAA,MAAAd,UAAA,CAAAwB,UAAA,WAA4B;IAGrFnD,EAAA,CAAAoB,SAAA,GAAsC;IAAtCpB,EAAA,CAAAqB,UAAA,SAAA2B,MAAA,CAAAtB,uBAAA,CAAAC,UAAA,EAAsC;IAC1C3B,EAAA,CAAAoB,SAAA,GAAuC;IAAvCpB,EAAA,CAAAqB,UAAA,UAAA2B,MAAA,CAAAtB,uBAAA,CAAAC,UAAA,EAAuC;IAOnC3B,EAAA,CAAAoB,SAAA,GAAwB;IAAxBpB,EAAA,CAAAqB,UAAA,SAAAM,UAAA,CAAAI,UAAA,CAAwB;IACX/B,EAAA,CAAAoB,SAAA,GAAwB;IAAxBpB,EAAA,CAAA8B,qBAAA,UAAAH,UAAA,CAAAyB,IAAA,CAAwB;IACxBpD,EAAA,CAAAoB,SAAA,GAA0B;IAA1BpB,EAAA,CAAA8B,qBAAA,UAAAH,UAAA,CAAA0B,MAAA,CAA0B;IAC1BrD,EAAA,CAAAoB,SAAA,GAAuB;IAAvBpB,EAAA,CAAA8B,qBAAA,UAAAH,UAAA,CAAA2B,GAAA,CAAuB;IAyBmBtD,EAAA,CAAAoB,SAAA,GAAgC;IAAhCpB,EAAA,CAAAyC,kBAAA,MAAAd,UAAA,CAAA4B,WAAA,YAAgC;;;;;IAzDrGvD,EAAA,CAAAkC,UAAA,IAAAsB,4CAAA,oBAsFM;;;;IAtFmBxD,EAAA,CAAAqB,UAAA,YAAAoC,MAAA,CAAAC,QAAA,CAAW;;;;;;;AD5BzC,OAAM,MAAOC,cAAc;EAmCvBC,YAAmBC,aAA4B,EACnCC,eAAgC,EAChCC,gBAAkC,EAClCC,cAA8B,EAC9BC,EAAe,EACfC,MAAc,EACdC,YAA0B;IANnB,KAAAN,aAAa,GAAbA,aAAa;IACpB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IArCxB,KAAAT,QAAQ,GAAa,EAAE;IAQvB,KAAAnC,kBAAkB,GAAiB,EAAE;IAErC,KAAAC,iBAAiB,GAAiB,EAAE;IAEpC,KAAA4C,SAAS,GAAW,CAAC;IAErB,KAAAC,SAAS,GAAW,EAAE;IAEtB,KAAAC,YAAY,GAAU,EAAE;IAExB,KAAAC,YAAY,GAAU,EAAE;IAExB,KAAAC,WAAW,GAAU,EAAE;IAMvB,KAAAC,YAAY,GAAmB,IAAIC,GAAG,EAAE;IACxC,KAAAC,eAAe,GAAmB,IAAID,GAAG,EAAE;IAC3C,KAAAE,eAAe,GAAsB,IAAIF,GAAG,EAAE;IAU1C,IAAI,CAACG,YAAY,GAAG,IAAI,CAAChB,aAAa,CAACiB,aAAa,CACnDC,IAAI,CAAChF,YAAY,CAAC,EAAE,CAAC,CAAC,CACtBiF,SAAS,CAAEC,MAAM,IAAI;MACrB;IAAA,CACA,CAAC;EACN;EAEAC,QAAQA,CAAA;IACJ,IAAI,CAACC,gBAAgB,EAAE;IAEvB,IAAI,CAACC,UAAU,GAAG;MACdC,mBAAmB,EAAE,KAAK;MAC1BC,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE;;OAEZ;MACDC,MAAM,EAAE;QACNC,CAAC,EAAE;UACDF,OAAO,EAAE;SACV;QACDG,CAAC,EAAE;UACDH,OAAO,EAAE;;;KAGd;IAED,IAAI,CAAC5D,WAAW,GAAG;MACjByD,mBAAmB,EAAE,KAAK;MAC1BO,QAAQ,EAAE;QACRC,IAAI,EAAE;UACJC,OAAO,EAAE;SACV;QACDC,KAAK,EAAE;UACLC,MAAM,EAAE;;OAEX;MACDV,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE;;OAEZ;MACDC,MAAM,EAAE;QACNC,CAAC,EAAE;UACDF,OAAO,EAAE;SACV;QACDG,CAAC,EAAE;UACDH,OAAO,EAAE;;;KAGd;EAKP;EAEAL,gBAAgBA,CAAA;IACd,IAAI,CAACpB,gBAAgB,CAACoB,gBAAgB,EAAE,CAACc,IAAI,CAACC,aAAa,IAAG;MAC5D,IAAIA,aAAa,CAACjE,MAAM,GAAG,CAAC,EAAC;QAC3B,IAAI,CAAC6B,eAAe,CAACqC,eAAe,EAAE,CAACF,IAAI,CAACxB,YAAY,IAAG;UACzD,IAAI,CAACN,YAAY,CAACiC,WAAW,CAAC3B,YAAY,CAAC;UAC3C,IAAI,CAACf,QAAQ,GAAGe,YAAY;UAC5B4B,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;UAC3BD,OAAO,CAACC,GAAG,CAAC7B,YAAY,CAAC;UACzB;UACA,IAAI,CAAC8B,mBAAmB,EAAE;QAC5B,CAAC,CAAC;OACH,MAAI;QACH,IAAI,CAACrC,MAAM,CAACsC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;;IAE5C,CAAC,CAAC;EACJ;EAEA;EACAD,mBAAmBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAC7C,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACzB,MAAM,KAAK,CAAC,EAAE;IAElD;IACA,IAAI,CAACyB,QAAQ,CAAC+C,OAAO,CAACC,OAAO,IAAG;MAC5B,IAAI,CAACC,eAAe,CAACD,OAAO,CAAC;IACjC,CAAC,CAAC;EACN;EAEA;EACAC,eAAeA,CAACD,OAAgB;IAC5B,IAAI,CAACA,OAAO,EAAE;IAEd,MAAME,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,kBAAkB,GAAG,IAAID,IAAI,CAACD,GAAG,CAACG,WAAW,EAAE,EAAEH,GAAG,CAACI,QAAQ,EAAE,EAAEJ,GAAG,CAACK,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,EAAE;IAC5G,MAAMC,gBAAgB,GAAG,IAAIN,IAAI,CAACD,GAAG,CAACG,WAAW,EAAE,EAAEH,GAAG,CAACI,QAAQ,EAAE,EAAEJ,GAAG,CAACK,OAAO,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,EAAE;IAE9G,IAAIE,OAAO,GAA2B;MAClCC,MAAM,EAAEX,OAAO,CAACY,SAAS;MACzBC,SAAS,EAAE,CAAC;MACZC,aAAa,EAAEV,kBAAkB;MACjCW,WAAW,EAAEN,gBAAgB;MAC7BO,SAAS,EAAE,KAAK;MAChBC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAElB,OAAO,CAAC7D;KACtB;IAED,IAAI,CAACiB,eAAe,CAAC+D,sBAAsB,CAACT,OAAO,CAAC,CAACnB,IAAI,CAAC6B,IAAI,IAAG;MAC7D,IAAIA,IAAI,IAAIA,IAAI,CAACA,IAAI,EAAE;QACnB,MAAMC,aAAa,GAAGC,gBAAgB,CAACC,QAAQ,CAACC,eAAe,CAAC;QAEhE,MAAMC,QAAQ,GAAG;UACbC,MAAM,EAAEN,IAAI,CAACA,IAAI,CAACO,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAC3BA,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI1B,IAAI,CAACyB,CAAC,CAACE,QAAQ,CAAC,CAACC,kBAAkB,CAAC,EAAE,EAAE;YAAEC,IAAI,EAAE,SAAS;YAAEC,MAAM,EAAE;UAAS,CAAE,CAAC,GAAG,EAAE,CAC7G;UACDC,QAAQ,EAAE,CACN;YACIC,KAAK,EAAE,cAAc;YACrBf,IAAI,EAAEA,IAAI,CAACA,IAAI,CAACO,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACQ,WAAW,CAAC;YACvCC,IAAI,EAAE,KAAK;YACXC,eAAe,EAAEjB,aAAa,CAACkB,gBAAgB,CAAC,eAAe,CAAC;YAChEC,WAAW,EAAEnB,aAAa,CAACkB,gBAAgB,CAAC,eAAe,CAAC;YAC5DnD,OAAO,EAAE;WACZ,EACD;YACI+C,KAAK,EAAE,mBAAmB;YAC1Bf,IAAI,EAAEA,IAAI,CAACA,IAAI,CAACO,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACa,eAAe,CAAC;YAC3CJ,IAAI,EAAE,KAAK;YACXC,eAAe,EAAEjB,aAAa,CAACkB,gBAAgB,CAAC,eAAe,CAAC;YAChEC,WAAW,EAAEnB,aAAa,CAACkB,gBAAgB,CAAC,eAAe,CAAC;YAC5DnD,OAAO,EAAE;WACZ;SAER;QAED;QACA,IAAI,CAACrB,YAAY,CAAC2E,GAAG,CAAC1C,OAAO,CAAC7D,EAAE,EAAEsF,QAAQ,CAAC;QAC3C,IAAI,CAACxD,eAAe,CAACyE,GAAG,CAAC1C,OAAO,CAAC7D,EAAE,EAAEiF,IAAI,CAACA,IAAI,CAAC;QAC/C,IAAI,CAAClD,eAAe,CAACwE,GAAG,CAAC1C,OAAO,CAAC7D,EAAE,EAAEiF,IAAI,CAACuB,GAAG,CAAC;;IAEtD,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAG;MACblD,OAAO,CAACkD,KAAK,CAAC,kCAAkC7C,OAAO,CAAC7D,EAAE,GAAG,EAAE0G,KAAK,CAAC;IACzE,CAAC,CAAC;EACN;EAGA;EACA7H,uBAAuBA,CAACgF,OAAgB;IACpC;IACA,IAAIA,OAAO,IAAIA,OAAO,CAAC7D,EAAE,IAAI,IAAI,CAAC4B,YAAY,CAAC+E,GAAG,CAAC9C,OAAO,CAAC7D,EAAE,CAAC,EAAE;MAC5D,OAAO,IAAI,CAAC4B,YAAY,CAACgF,GAAG,CAAC/C,OAAO,CAAC7D,EAAE,CAAC;;IAG5C;IACA,OAAO,IAAI;EACf;EAEAK,kBAAkBA,CAAC0E,SAAgB;IACjC,OAAO,IAAI,CAAChD,eAAe,CAAC6E,GAAG,CAAC7B,SAAS,CAAC;EAC5C;EAEA3E,oBAAoBA,CAAC2E,SAAgB;IACnC,IAAIE,IAAI,GAAG,IAAI,CAACnD,eAAe,CAAC8E,GAAG,CAAC7B,SAAS,CAAC;IAC9C,IAAI,CAACE,IAAI,IAAIA,IAAI,CAAC7F,MAAM,KAAK,CAAC,EAAE;IAEhC,MAAMyH,MAAM,GAAG5B,IAAI,CAAC6B,MAAM,CAAC,CAACC,WAAW,EAAEC,OAAO,KAAI;MAClD,OAAO,IAAIhD,IAAI,CAACgD,OAAO,CAACrB,QAAQ,CAAC,GAAG,IAAI3B,IAAI,CAAC+C,WAAW,CAACpB,QAAQ,CAAC,GAC9DqB,OAAO,GACPD,WAAW;IACjB,CAAC,CAAC;IAEF,OAAOF,MAAM,CAAClB,QAAQ;EACxB;EAGA9H,YAAYA,CAACoJ,KAAU;IACnB,MAAMC,KAAK,GAAGD,KAAK,CAACC,KAAK;IAEzB,IAAIA,KAAK,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;MAC1B,IAAI,CAAC5F,SAAS,GAAG,CAAC,CAAC;MACnB,IAAI,CAACC,SAAS,GAAG0F,KAAK,CAACE,SAAS,CAAC,CAAC,EAAEF,KAAK,CAAC9H,MAAM,CAAC;KACpD,MAAM;MACH,IAAI,CAACmC,SAAS,GAAG,CAAC;MAClB,IAAI,CAACC,SAAS,GAAG0F,KAAK;;EAE9B;EAEA5I,QAAQA,CAAC+I,EAAY,EAAEJ,KAAY;IAC/BI,EAAE,CAACC,MAAM,CAAEL,KAAK,CAACM,MAA2B,CAACL,KAAK,CAAC;EACvD;EAGAM,OAAOA,CAAA;IACH,IAAI,CAACC,MAAM,GAAG,2FAA2F;EAC7G;EAEA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEAC,WAAWA,CAAA;IACP,IAAI,IAAI,CAAC1F,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAAC2F,WAAW,EAAE;;EAEvC;EAAC,QAAAC,CAAA,G;qBAxSQ9G,cAAc,EAAA3D,EAAA,CAAA0K,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA5K,EAAA,CAAA0K,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA9K,EAAA,CAAA0K,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAhL,EAAA,CAAA0K,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAlL,EAAA,CAAA0K,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAApL,EAAA,CAAA0K,iBAAA,CAAAW,EAAA,CAAAC,MAAA,GAAAtL,EAAA,CAAA0K,iBAAA,CAAAa,EAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAd9H,cAAc;IAAA+H,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCrB3BhM,EAAA,CAAAC,cAAA,aAAkB;QAK0DD,EAAA,CAAA6B,MAAA,4BAAqB;QAAA7B,EAAA,CAAAW,YAAA,EAAO;QACpFX,EAAA,CAAAC,cAAA,aAA0C;QAAAD,EAAA,CAAA6B,MAAA,GAAmB;QAAA7B,EAAA,CAAAW,YAAA,EAAM;QAEvEX,EAAA,CAAAC,cAAA,aAAqI;QACjID,EAAA,CAAAc,SAAA,kBAAsC;QAC1Cd,EAAA,CAAAW,YAAA,EAAM;QAMlBX,EAAA,CAAAC,cAAA,cAAsC;QAIkCD,EAAA,CAAA6B,MAAA,kCAA0B;QAAA7B,EAAA,CAAAW,YAAA,EAAO;QACzFX,EAAA,CAAAC,cAAA,cAA0C;QAAAD,EAAA,CAAA6B,MAAA,sBAAc;QAAA7B,EAAA,CAAAW,YAAA,EAAM;QAElEX,EAAA,CAAAC,cAAA,cAAqI;QACjID,EAAA,CAAAc,SAAA,kBAA+B;QACnCd,EAAA,CAAAW,YAAA,EAAM;QAQlBX,EAAA,CAAAC,cAAA,cAAwC;QAGxCD,EAAA,CAAAkC,UAAA,KAAAgK,sCAAA,0BAUc,KAAAC,sCAAA;QAkJfnM,EAAA,CAAAW,YAAA,EAAa;;;QA1L8CX,EAAA,CAAAoB,SAAA,GAAmB;QAAnBpB,EAAA,CAAA8C,iBAAA,CAAAmJ,GAAA,CAAAvI,QAAA,CAAAzB,MAAA,CAAmB;QAEoBjC,EAAA,CAAAoB,SAAA,GAA+C;QAA/CpB,EAAA,CAAAqB,UAAA,YAAArB,EAAA,CAAAoM,eAAA,IAAAC,GAAA,EAA+C;QAe/CrM,EAAA,CAAAoB,SAAA,IAA+C;QAA/CpB,EAAA,CAAAqB,UAAA,YAAArB,EAAA,CAAAoM,eAAA,IAAAC,GAAA,EAA+C;QAYjIrM,EAAA,CAAAoB,SAAA,GAAkB;QAAlBpB,EAAA,CAAAqB,UAAA,UAAA4K,GAAA,CAAAvI,QAAA,CAAkB,4CAAAuI,GAAA,CAAA5H,SAAA,eAAA4H,GAAA,CAAA7H,SAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}