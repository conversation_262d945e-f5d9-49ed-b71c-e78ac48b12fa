{"ast": null, "code": "import { debounceTime } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/layout/service/app.layout.service\";\nimport * as i2 from \"../../service/stations.service\";\nimport * as i3 from \"../../service/providers.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"../../service/cache.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"primeng/dataview\";\nimport * as i10 from \"primeng/inputtext\";\nimport * as i11 from \"primeng/dropdown\";\nimport * as i12 from \"@coreui/angular-chartjs\";\nimport * as i13 from \"primeng/badge\";\nimport * as i14 from \"@fortawesome/angular-fontawesome\";\nfunction IndexComponent_ng_template_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"p-dropdown\", 16);\n    i0.ɵɵlistener(\"onChange\", function IndexComponent_ng_template_25_Template_p_dropdown_onChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onSortChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"p-dropdown\", 17);\n    i0.ɵɵlistener(\"onChange\", function IndexComponent_ng_template_25_Template_p_dropdown_onChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onSortChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 18);\n    i0.ɵɵelement(4, \"i\", 19);\n    i0.ɵɵelementStart(5, \"input\", 20);\n    i0.ɵɵlistener(\"input\", function IndexComponent_ng_template_25_Template_input_input_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r6 = i0.ɵɵnextContext();\n      const _r0 = i0.ɵɵreference(24);\n      return i0.ɵɵresetView(ctx_r6.onFilter(_r0, $event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"options\", ctx_r1.sortOptionsCountry);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"options\", ctx_r1.sortOptionsStatus);\n  }\n}\nconst _c0 = a1 => [\"/app/station\", a1];\nfunction IndexComponent_ng_template_26_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 22)(2, \"div\", 0)(3, \"div\", 23)(4, \"div\", 24);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 25)(7, \"a\", 26);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 27);\n    i0.ɵɵelement(10, \"fa-icon\", 28);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 29)(13, \"div\", 30);\n    i0.ɵɵelement(14, \"fa-icon\", 8);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 29)(17, \"div\", 30);\n    i0.ɵɵelement(18, \"fa-icon\", 31);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 32);\n    i0.ɵɵelement(21, \"c-chart\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 34);\n    i0.ɵɵelement(23, \"p-badge\", 35)(24, \"p-badge\", 36)(25, \"p-badge\", 37)(26, \"p-badge\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 39)(28, \"p\", 40);\n    i0.ɵɵelement(29, \"i\", 41);\n    i0.ɵɵelementStart(30, \"span\", 42);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const station_r9 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(\"station-box-status-\" + station_r9.status.toLowerCase());\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", station_r9.provider, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(15, _c0, station_r9.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(station_r9.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", station_r9.updateTime, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", station_r9.power, \"kW\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", station_r9.irradiance, \"kWh/m2\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"data\", ctx_r8.data[1])(\"options\", ctx_r8.lineOptions);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"value\", station_r9.inverter);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"value\", station_r9.mmpt);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"value\", station_r9.string);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"value\", station_r9.pvn);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", station_r9.temperature, \" \\u2103\");\n  }\n}\nfunction IndexComponent_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, IndexComponent_ng_template_26_div_0_Template, 32, 17, \"div\", 21);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.stations);\n  }\n}\nconst _c1 = () => ({\n  width: \"2.5rem\",\n  height: \"2.5rem\"\n});\nexport class IndexComponent {\n  constructor(layoutService, stationsService, providersService, messageService, fb, router, cacheService) {\n    this.layoutService = layoutService;\n    this.stationsService = stationsService;\n    this.providersService = providersService;\n    this.messageService = messageService;\n    this.fb = fb;\n    this.router = router;\n    this.cacheService = cacheService;\n    this.stations = [];\n    this.sortOptionsCountry = [];\n    this.sortOptionsStatus = [];\n    this.sortOrder = 0;\n    this.sortField = '';\n    this.sourceCities = [];\n    this.targetCities = [];\n    this.orderCities = [];\n    this.subscription = this.layoutService.configUpdate$.pipe(debounceTime(25)).subscribe(config => {\n      //   this.initChart();\n    });\n  }\n  ngOnInit() {\n    this.getUserProviders();\n    this.barOptions = {\n      maintainAspectRatio: false,\n      plugins: {\n        legend: {\n          display: false\n        }\n      },\n      scales: {\n        x: {\n          display: false\n        },\n        y: {\n          display: false\n        }\n      }\n    };\n    this.lineOptions = {\n      maintainAspectRatio: false,\n      elements: {\n        line: {\n          tension: 0.4\n        },\n        point: {\n          radius: 0\n        }\n      },\n      plugins: {\n        legend: {\n          display: false\n        }\n      },\n      scales: {\n        x: {\n          display: false\n        },\n        y: {\n          display: false\n        }\n      }\n    };\n    this.data = [{\n      labels: ['M', 'T', 'W', 'T', 'F', 'S', 'S', 'M', 'T', 'W', 'T', 'F', 'S', 'S', 'M'],\n      datasets: [{\n        backgroundColor: '#321fdb',\n        borderColor: 'transparent',\n        borderWidth: 1,\n        data: [41, 78, 51, 66, 74, 42, 89, 97, 87, 84, 78, 88, 67, 45, 47]\n      }]\n    }, {\n      labels: ['M', 'T', 'W', 'T', 'F', 'S', 'S', 'M', 'T', 'W', 'T', 'F', 'S', 'S', 'M'],\n      datasets: [{\n        backgroundColor: 'transparent',\n        borderColor: '#321fdb',\n        borderWidth: 2,\n        data: [41, 78, 51, 66, 74, 42, 89, 97, 87, 84, 78, 88, 67, 45, 47],\n        pointBackgroundColor: '#321fdb'\n      }]\n    }];\n  }\n  getUserProviders() {\n    this.providersService.getUserProviders().then(providersData => {\n      if (providersData.length > 0) {\n        this.stationsService.getUserStations().then(stationsData => {\n          this.cacheService.setStations(stationsData);\n          this.stations = stationsData;\n          this.getStationsRealTimeData();\n        });\n      } else {\n        this.router.navigate(['/app/providers']);\n      }\n    });\n  }\n  getStationsRealTimeData() {\n    this.stations.forEach(station => {\n      var now = new Date();\n      var formattedStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0).toISOString();\n      var formattedEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0).toISOString();\n      let request = {\n        devIds: inverterIds,\n        devTypeId: 1,\n        startDateTime: formattedStartDate,\n        endDateTime: formattedEndDate,\n        separated: false,\n        searchType: null,\n        stationId: station.id\n      };\n      this.stationsService.getStationHistoricData(request).then(data => {\n        this.energyData = data;\n        console.log(this.energyData);\n        if (this.energyData.data.length > 0) {\n          this.initCharts(separated, searchType);\n        } else {\n          this.messageService.add({\n            severity: 'warn',\n            summary: 'No Data',\n            detail: 'No Data to display!'\n          });\n        }\n      });\n    });\n    this.providersService.getUserProviders().then(providersData => {\n      if (providersData.length > 0) {\n        this.stationsService.getUserStations().then(stationsData => {\n          this.cacheService.setStations(stationsData);\n          this.stations = stationsData;\n          this.getStationsRealTimeData();\n        });\n      } else {\n        this.router.navigate(['/app/providers']);\n      }\n    });\n  }\n  onSortChange(event) {\n    const value = event.value;\n    if (value.indexOf('!') === 0) {\n      this.sortOrder = -1;\n      this.sortField = value.substring(1, value.length);\n    } else {\n      this.sortOrder = 1;\n      this.sortField = value;\n    }\n  }\n  onFilter(dv, event) {\n    dv.filter(event.target.value);\n  }\n  initMap() {\n    this.mapSrc = \"https://www.digitaltrends.com/wp-content/uploads/2020/02/custom-map.jpg?fit=720%2C479&p=1\";\n  }\n  // initChart() {\n  //     const documentStyle = getComputedStyle(document.documentElement);\n  //     const textColor = documentStyle.getPropertyValue('--text-color');\n  //     const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n  //     const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\n  //     this.chartData = {\n  //         labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\n  //         datasets: [\n  //             {\n  //                 label: 'First Dataset',\n  //                 data: [65, 59, 80, 81, 56, 55, 40],\n  //                 fill: false,\n  //                 backgroundColor: documentStyle.getPropertyValue('--bluegray-700'),\n  //                 borderColor: documentStyle.getPropertyValue('--bluegray-700'),\n  //                 tension: .4\n  //             },\n  //             {\n  //                 label: 'Second Dataset',\n  //                 data: [28, 48, 40, 19, 86, 27, 90],\n  //                 fill: false,\n  //                 backgroundColor: documentStyle.getPropertyValue('--green-600'),\n  //                 borderColor: documentStyle.getPropertyValue('--green-600'),\n  //                 tension: .4\n  //             }\n  //         ]\n  //     };\n  //     this.chartOptions = {\n  //         plugins: {\n  //             legend: {\n  //                 labels: {\n  //                     color: textColor\n  //                 }\n  //             }\n  //         },\n  //         scales: {\n  //             x: {\n  //                 ticks: {\n  //                     color: textColorSecondary\n  //                 },\n  //                 grid: {\n  //                     color: surfaceBorder,\n  //                     drawBorder: false\n  //                 }\n  //             },\n  //             y: {\n  //                 ticks: {\n  //                     color: textColorSecondary\n  //                 },\n  //                 grid: {\n  //                     color: surfaceBorder,\n  //                     drawBorder: false\n  //                 }\n  //             }\n  //         }\n  //     };\n  // }\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n  static #_ = this.ɵfac = function IndexComponent_Factory(t) {\n    return new (t || IndexComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.StationsService), i0.ɵɵdirectiveInject(i3.ProvidersService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.FormBuilder), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.CacheService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: IndexComponent,\n    selectors: [[\"ng-component\"]],\n    decls: 27,\n    vars: 10,\n    consts: [[1, \"grid\"], [1, \"col-12\", \"lg:col-6\", \"xl:col-6\"], [1, \"card\", \"mb-0\"], [1, \"flex\", \"justify-content-between\", \"mb-3\"], [1, \"block\", \"text-500\", \"font-medium\", \"font-bold\", \"mb-3\"], [1, \"text-900\", \"font-medium\", \"text-xl\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-blue-100\", \"border-round\", 3, \"ngStyle\"], [\"icon\", \"solar-panel\"], [\"icon\", \"plug\"], [1, \"col-12\", \"lg:col-12\", \"xl:col-12\"], [1, \"card\"], [\"filterBy\", \"name\", \"layout\", \"list\", 3, \"value\", \"paginator\", \"rows\", \"sortField\", \"sortOrder\"], [\"dv\", \"\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"listItem\"], [1, \"flex\", \"flex-column\", \"md:flex-row\", \"md:justify-content-between\", \"gap-2\"], [\"placeholder\", \"Country\", 3, \"options\", \"onChange\"], [\"placeholder\", \"Status\", 3, \"options\", \"onChange\"], [1, \"p-input-icon-left\"], [1, \"pi\", \"pi-search\"], [\"type\", \"search\", \"pInputText\", \"\", \"placeholder\", \"Search\", 3, \"input\"], [3, \"class\", 4, \"ngFor\", \"ngForOf\"], [1, \"\"], [1, \"col-12\", \"md:col-6\", \"lg:col-3\"], [1, \"provider-text\"], [1, \"font-bold\", \"text-xl\"], [3, \"routerLink\"], [1, \"font-italic\"], [\"icon\", \"clock\"], [1, \"col-12\", \"md:col-6\", \"lg:col-1\"], [1, \"text-center\", \"font-bold\"], [\"icon\", \"sun\"], [1, \"col-12\", \"md:col-6\", \"lg:col-2\"], [\"type\", \"line\", \"height\", \"40\", \"width\", \"80\", 1, \"mx-auto\", 3, \"data\", \"options\"], [1, \"col-12\", \"md:col-6\", \"lg:col-3\", \"text-center\"], [\"size\", \"large\", 1, \"pad-2\", 3, \"value\"], [\"size\", \"large\", \"severity\", \"success\", 1, \"pad-2\", 3, \"value\"], [\"size\", \"large\", \"severity\", \"info\", 1, \"pad-2\", 3, \"value\"], [\"size\", \"large\", \"severity\", \"warning\", 1, \"pad-2\", 3, \"value\"], [1, \"col-12\", \"md:col-6\", \"lg:col-2\", \"text-center\"], [1, \"text-center\"], [1, \"pi\", \"pi-cloud\", \"text-2xl\"], [1, \"text-2xl\", \"mb-2\", \"align-self-center\", \"md:align-self-end\"]],\n    template: function IndexComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\")(5, \"span\", 4);\n        i0.ɵɵtext(6, \"Active Energy Systems\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"div\", 5);\n        i0.ɵɵtext(8);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(9, \"div\", 6);\n        i0.ɵɵelement(10, \"fa-icon\", 7);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(11, \"div\", 1)(12, \"div\", 2)(13, \"div\", 3)(14, \"div\")(15, \"span\", 4);\n        i0.ɵɵtext(16, \"Combined Power Permormance\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"div\", 5);\n        i0.ɵɵtext(18, \"33% (4.124kW) \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(19, \"div\", 6);\n        i0.ɵɵelement(20, \"fa-icon\", 8);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(21, \"div\", 9)(22, \"div\", 10)(23, \"p-dataView\", 11, 12);\n        i0.ɵɵtemplate(25, IndexComponent_ng_template_25_Template, 6, 2, \"ng-template\", 13)(26, IndexComponent_ng_template_26_Template, 1, 1, \"ng-template\", 14);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(8);\n        i0.ɵɵtextInterpolate(ctx.stations.length);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(8, _c1));\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(9, _c1));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"value\", ctx.stations)(\"paginator\", true)(\"rows\", 9)(\"sortField\", ctx.sortField)(\"sortOrder\", ctx.sortOrder);\n      }\n    },\n    dependencies: [i8.NgForOf, i8.NgStyle, i6.RouterLink, i4.PrimeTemplate, i9.DataView, i10.InputText, i11.Dropdown, i12.ChartjsComponent, i13.Badge, i14.FaIconComponent],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["debounceTime", "i0", "ɵɵelementStart", "ɵɵlistener", "IndexComponent_ng_template_25_Template_p_dropdown_onChange_1_listener", "$event", "ɵɵrestoreView", "_r4", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "onSortChange", "ɵɵelementEnd", "IndexComponent_ng_template_25_Template_p_dropdown_onChange_2_listener", "ctx_r5", "ɵɵelement", "IndexComponent_ng_template_25_Template_input_input_5_listener", "ctx_r6", "_r0", "ɵɵreference", "onFilter", "ɵɵadvance", "ɵɵproperty", "ctx_r1", "sortOptionsCountry", "sortOptionsStatus", "ɵɵtext", "ɵɵclassMap", "station_r9", "status", "toLowerCase", "ɵɵtextInterpolate1", "provider", "ɵɵpureFunction1", "_c0", "id", "ɵɵtextInterpolate", "name", "updateTime", "power", "irradiance", "ctx_r8", "data", "lineOptions", "ɵɵpropertyInterpolate", "inverter", "mmpt", "string", "pvn", "temperature", "ɵɵtemplate", "IndexComponent_ng_template_26_div_0_Template", "ctx_r2", "stations", "IndexComponent", "constructor", "layoutService", "stationsService", "providersService", "messageService", "fb", "router", "cacheService", "sortOrder", "sortField", "sourceCities", "targetCities", "orderCities", "subscription", "configUpdate$", "pipe", "subscribe", "config", "ngOnInit", "getUserProviders", "barOptions", "maintainAspectRatio", "plugins", "legend", "display", "scales", "x", "y", "elements", "line", "tension", "point", "radius", "labels", "datasets", "backgroundColor", "borderColor", "borderWidth", "pointBackgroundColor", "then", "providersData", "length", "getUserStations", "stationsData", "setStations", "getStationsRealTimeData", "navigate", "for<PERSON>ach", "station", "now", "Date", "formattedStartDate", "getFullYear", "getMonth", "getDate", "toISOString", "formattedEndDate", "request", "devIds", "inverterIds", "devTypeId", "startDateTime", "endDateTime", "separated", "searchType", "stationId", "getStationHistoricData", "energyData", "console", "log", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "add", "severity", "summary", "detail", "event", "value", "indexOf", "substring", "dv", "filter", "target", "initMap", "mapSrc", "ngOnDestroy", "unsubscribe", "_", "ɵɵdirectiveInject", "i1", "LayoutService", "i2", "StationsService", "i3", "ProvidersService", "i4", "MessageService", "i5", "FormBuilder", "i6", "Router", "i7", "CacheService", "_2", "selectors", "decls", "vars", "consts", "template", "IndexComponent_Template", "rf", "ctx", "IndexComponent_ng_template_25_Template", "IndexComponent_ng_template_26_Template", "ɵɵpureFunction0", "_c1"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\index\\index.component.ts", "D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\index\\index.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { MenuItem, MessageService } from 'primeng/api';\r\nimport { Product } from '../../api/product';\r\nimport { ProductService } from '../../service/product.service';\r\nimport { Subscription, debounceTime } from 'rxjs';\r\nimport { LayoutService } from 'src/app/layout/service/app.layout.service';\r\nimport { SelectItem } from 'primeng/api';\r\nimport { DataView } from 'primeng/dataview';\r\nimport { StationsService } from '../../service/stations.service';\r\nimport { Station } from '../../api/station';\r\nimport { CacheService } from '../../service/cache.service';\r\nimport { ProvidersService } from '../../service/providers.service';\r\nimport { IProvider, IUserProvider } from '../../api/responses';\r\nimport { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { GetHistoricDataRequest, SaveUserProvidersRequest } from '../../api/requests';\r\nimport { Router } from '@angular/router';\r\n\r\n\r\n@Component({\r\n    templateUrl: './index.component.html',\r\n})\r\nexport class IndexComponent implements OnInit, OnDestroy {\r\n\r\n    items!: MenuItem[];\r\n\r\n    stations: Station[] =[];\r\n    energyData:any;\r\n\r\n    chartData: any;\r\n\r\n    chartOptions: any;\r\n\r\n    subscription!: Subscription;\r\n\r\n    sortOptionsCountry: SelectItem[] = [];\r\n\r\n    sortOptionsStatus: SelectItem[] = [];\r\n\r\n    sortOrder: number = 0;\r\n\r\n    sortField: string = '';\r\n\r\n    sourceCities: any[] = [];\r\n\r\n    targetCities: any[] = [];\r\n\r\n    orderCities: any[] = [];\r\n\r\n    mapSrc:string;\r\n\r\n    barOptions:any;\r\n    lineOptions:any;\r\n    data: any;\r\n    \r\n\r\n    constructor(public layoutService: LayoutService, \r\n        private stationsService: StationsService,\r\n        private providersService: ProvidersService,\r\n        private messageService: MessageService,\r\n        private fb: FormBuilder,\r\n        private router: Router,\r\n        private cacheService: CacheService) {\r\n        this.subscription = this.layoutService.configUpdate$\r\n        .pipe(debounceTime(25))\r\n        .subscribe((config) => {\r\n         //   this.initChart();\r\n        });\r\n    }\r\n\r\n    ngOnInit() {\r\n        this.getUserProviders();\r\n\r\n        this.barOptions = {\r\n            maintainAspectRatio: false,\r\n            plugins: {\r\n              legend: {\r\n                display: false\r\n              }\r\n            },\r\n            scales: {\r\n              x: {\r\n                display: false\r\n              },\r\n              y: {\r\n                display: false\r\n              }\r\n            }\r\n          };\r\n        \r\n          this.lineOptions = {\r\n            maintainAspectRatio: false,\r\n            elements: {\r\n              line: {\r\n                tension: 0.4\r\n              },\r\n              point: {\r\n                radius: 0\r\n              }\r\n            },\r\n            plugins: {\r\n              legend: {\r\n                display: false\r\n              }\r\n            },\r\n            scales: {\r\n              x: {\r\n                display: false\r\n              },\r\n              y: {\r\n                display: false\r\n              }\r\n            }\r\n          };\r\n        \r\n          this.data = [\r\n            {\r\n              labels: ['M', 'T', 'W', 'T', 'F', 'S', 'S', 'M', 'T', 'W', 'T', 'F', 'S', 'S', 'M'],\r\n              datasets: [\r\n                {\r\n                  backgroundColor: '#321fdb',\r\n                  borderColor: 'transparent',\r\n                  borderWidth: 1,\r\n                  data: [41, 78, 51, 66, 74, 42, 89, 97, 87, 84, 78, 88, 67, 45, 47]\r\n                }\r\n              ]\r\n            }, {\r\n              labels: ['M', 'T', 'W', 'T', 'F', 'S', 'S', 'M', 'T', 'W', 'T', 'F', 'S', 'S', 'M'],\r\n              datasets: [\r\n                {\r\n                  backgroundColor: 'transparent',\r\n                  borderColor: '#321fdb',\r\n                  borderWidth: 2,\r\n                  data: [41, 78, 51, 66, 74, 42, 89, 97, 87, 84, 78, 88, 67, 45, 47],\r\n                  pointBackgroundColor: '#321fdb'\r\n                }\r\n              ]\r\n            }\r\n          ];\r\n    }\r\n\r\n    getUserProviders(){\r\n      this.providersService.getUserProviders().then(providersData => {\r\n        if (providersData.length > 0){\r\n          this.stationsService.getUserStations().then(stationsData => {\r\n            this.cacheService.setStations(stationsData);\r\n            this.stations = stationsData;\r\n            this.getStationsRealTimeData();\r\n          });\r\n        }else{\r\n          this.router.navigate(['/app/providers']); \r\n        }\r\n      });\r\n    }\r\n\r\n    getStationsRealTimeData(){\r\n      this.stations.forEach(station => {\r\n        var now = new Date();\r\n        var formattedStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0).toISOString();\r\n        var formattedEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0).toISOString();\r\n         \r\n        let request: GetHistoricDataRequest = {\r\n            devIds : inverterIds,\r\n            devTypeId: 1,\r\n            startDateTime: formattedStartDate,\r\n            endDateTime: formattedEndDate,\r\n            separated: false,\r\n            searchType:null,\r\n            stationId: station.id\r\n        }\r\n        this.stationsService.getStationHistoricData(request).then(data => {\r\n            this.energyData = data\r\n            console.log(this.energyData)\r\n            if (this.energyData.data.length > 0){\r\n                this.initCharts(separated, searchType);\r\n            }else{\r\n                this.messageService.add({ severity: 'warn', summary: 'No Data', detail: 'No Data to display!' });\r\n            }\r\n        });\r\n\r\n      });\r\n\r\n\r\n\r\n      this.providersService.getUserProviders().then(providersData => {\r\n        if (providersData.length > 0){\r\n          this.stationsService.getUserStations().then(stationsData => {\r\n            this.cacheService.setStations(stationsData);\r\n            this.stations = stationsData;\r\n            this.getStationsRealTimeData();\r\n          });\r\n        }else{\r\n          this.router.navigate(['/app/providers']); \r\n        }\r\n      });\r\n    }\r\n    \r\n\r\n    onSortChange(event: any) {\r\n        const value = event.value;\r\n\r\n        if (value.indexOf('!') === 0) {\r\n            this.sortOrder = -1;\r\n            this.sortField = value.substring(1, value.length);\r\n        } else {\r\n            this.sortOrder = 1;\r\n            this.sortField = value;\r\n        }\r\n    }\r\n\r\n    onFilter(dv: DataView, event: Event) {\r\n        dv.filter((event.target as HTMLInputElement).value);\r\n    }\r\n\r\n\r\n    initMap(){\r\n        this.mapSrc = \"https://www.digitaltrends.com/wp-content/uploads/2020/02/custom-map.jpg?fit=720%2C479&p=1\"\r\n    }\r\n\r\n    // initChart() {\r\n    //     const documentStyle = getComputedStyle(document.documentElement);\r\n    //     const textColor = documentStyle.getPropertyValue('--text-color');\r\n    //     const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\r\n    //     const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\r\n\r\n    //     this.chartData = {\r\n    //         labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\r\n    //         datasets: [\r\n    //             {\r\n    //                 label: 'First Dataset',\r\n    //                 data: [65, 59, 80, 81, 56, 55, 40],\r\n    //                 fill: false,\r\n    //                 backgroundColor: documentStyle.getPropertyValue('--bluegray-700'),\r\n    //                 borderColor: documentStyle.getPropertyValue('--bluegray-700'),\r\n    //                 tension: .4\r\n    //             },\r\n    //             {\r\n    //                 label: 'Second Dataset',\r\n    //                 data: [28, 48, 40, 19, 86, 27, 90],\r\n    //                 fill: false,\r\n    //                 backgroundColor: documentStyle.getPropertyValue('--green-600'),\r\n    //                 borderColor: documentStyle.getPropertyValue('--green-600'),\r\n    //                 tension: .4\r\n    //             }\r\n    //         ]\r\n    //     };\r\n\r\n    //     this.chartOptions = {\r\n    //         plugins: {\r\n    //             legend: {\r\n    //                 labels: {\r\n    //                     color: textColor\r\n    //                 }\r\n    //             }\r\n    //         },\r\n    //         scales: {\r\n    //             x: {\r\n    //                 ticks: {\r\n    //                     color: textColorSecondary\r\n    //                 },\r\n    //                 grid: {\r\n    //                     color: surfaceBorder,\r\n    //                     drawBorder: false\r\n    //                 }\r\n    //             },\r\n    //             y: {\r\n    //                 ticks: {\r\n    //                     color: textColorSecondary\r\n    //                 },\r\n    //                 grid: {\r\n    //                     color: surfaceBorder,\r\n    //                     drawBorder: false\r\n    //                 }\r\n    //             }\r\n    //         }\r\n    //     };\r\n    // }\r\n\r\n    ngOnDestroy() {\r\n        if (this.subscription) {\r\n            this.subscription.unsubscribe();\r\n        }\r\n    }\r\n}\r\n", "<div class=\"grid\">\r\n    <div class=\"col-12 lg:col-6 xl:col-6\">\r\n        <div class=\"card mb-0\">\r\n            <div class=\"flex justify-content-between mb-3\">\r\n                <div>\r\n                    <span class=\"block text-500 font-medium font-bold mb-3\">Active Energy Systems</span>\r\n                    <div class=\"text-900 font-medium text-xl\">{{stations.length}}</div>\r\n                </div>\r\n                <div class=\"flex align-items-center justify-content-center bg-blue-100 border-round\" [ngStyle]=\"{width: '2.5rem', height: '2.5rem'}\">\r\n                    <fa-icon icon=\"solar-panel\"></fa-icon> \r\n                </div>\r\n            </div>\r\n            <!-- <span class=\"text-green-500 font-medium\">24 new </span>\r\n            <span class=\"text-500\">since last visit</span> -->\r\n        </div>\r\n    </div>\r\n    <div class=\"col-12 lg:col-6 xl:col-6\">\r\n        <div class=\"card mb-0\">\r\n            <div class=\"flex justify-content-between mb-3\">\r\n                <div>\r\n                    <span class=\"block text-500 font-medium font-bold mb-3\">Combined Power Permormance</span>\r\n                    <div class=\"text-900 font-medium text-xl\">33% (4.124kW) </div>\r\n                </div>\r\n                <div class=\"flex align-items-center justify-content-center bg-blue-100 border-round\" [ngStyle]=\"{width: '2.5rem', height: '2.5rem'}\">\r\n                    <fa-icon icon=\"plug\"></fa-icon> \r\n                </div>\r\n            </div>\r\n            <!-- <span class=\"text-green-500 font-medium\">24 new </span>\r\n            <span class=\"text-500\">since last visit</span> -->\r\n        </div>\r\n    </div>\r\n\t \r\n\r\n    <div class=\"col-12 lg:col-12 xl:col-12\">\r\n\t\t<div class=\"card\">\r\n\t\t\t<p-dataView #dv [value]=\"stations\" [paginator]=\"true\" [rows]=\"9\" filterBy=\"name\" [sortField]=\"sortField\" [sortOrder]=\"sortOrder\" layout=\"list\">\r\n\t\t\t\t<ng-template pTemplate=\"header\">\r\n\t\t\t\t\t<div class=\"flex flex-column md:flex-row md:justify-content-between gap-2\">\r\n\t\t\t\t\t\t<p-dropdown [options]=\"sortOptionsCountry\" placeholder=\"Country\" (onChange)=\"onSortChange($event)\"></p-dropdown>\r\n\t\t\t\t\t\t<p-dropdown [options]=\"sortOptionsStatus\" placeholder=\"Status\" (onChange)=\"onSortChange($event)\"></p-dropdown>\r\n\t\t\t\t\t\t<span class=\"p-input-icon-left\">\r\n                            <i class=\"pi pi-search\"></i>\r\n                            <input type=\"search\" pInputText placeholder=\"Search\" (input)=\"onFilter(dv, $event)\">\r\n                        </span>\t\r\n\t\t\t\t\t\t<!-- <p-dataViewLayoutOptions></p-dataViewLayoutOptions> -->\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</ng-template>\r\n\r\n\t\t\t\t<ng-template let-products pTemplate=\"listItem\">\r\n\t\t\t\t\t<div *ngFor=\"let station of stations\" [class]=\"'station-box-status-' + station.status.toLowerCase()\">\r\n\t\t\t\t\t\t<div class=\"\">\r\n\t\t\t\t\t\t\t<div class=\"grid\">\r\n\t\t\t\t\t\t\t\t<div class=\"col-12 md:col-6 lg:col-3\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"provider-text\">\r\n\t\t\t\t\t\t\t\t\t\t<!-- <fa-icon icon=\"truck\"></fa-icon> -->\r\n\t\t\t\t\t\t\t\t\t\t{{station.provider}}\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"font-bold text-xl\"><a [routerLink]=\"['/app/station',station.id]\">{{station.name}}</a></div>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<div class=\"font-italic\"><fa-icon icon=\"clock\"></fa-icon> {{station.updateTime}} \r\n\t\t\t\t\t\t\t\t\t\t<!-- <span [class]=\"'station-badge status-' + station.status.toLowerCase()\">{{station.status.toLowerCase()}}</span> -->\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"col-12 md:col-6 lg:col-1\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"text-center font-bold\"><fa-icon icon=\"plug\"></fa-icon> {{station.power}}kW</div>\t\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"col-12 md:col-6 lg:col-1\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"text-center font-bold\"><fa-icon icon=\"sun\"></fa-icon>  {{station.irradiance}}kWh/m2</div>\t\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"col-12 md:col-6 lg:col-2\">\r\n\t\t\t\t\t\t\t\t\t<c-chart [data]=\"data[1]\" [options]=\"lineOptions\" type=\"line\" class=\"mx-auto\" height=\"40\" width=\"80\" />\r\n\t\t\t\t\t\t\t\t\t<!-- <div class=\"\">Inverter: {{station.inverter}} | MMPT: {{station.mmpt}} | String: {{station.string}} | PVN: {{station.pvn}}</div> -->\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"col-12 md:col-6 lg:col-3 text-center\">\r\n\t\t\t\t\t\t\t\t\t<p-badge class=\"pad-2 \" value=\"{{station.inverter}}\" size=\"large\"></p-badge>\r\n\t\t\t\t\t\t\t\t\t<p-badge class=\"pad-2\" value=\"{{station.mmpt}}\" size=\"large\"  severity=\"success\"></p-badge>\r\n\t\t\t\t\t\t\t\t\t<p-badge class=\"pad-2\" value=\"{{station.string}}\" size=\"large\" severity=\"info\"></p-badge>\r\n\t\t\t\t\t\t\t\t\t<p-badge class=\"pad-2\" value=\"{{station.pvn}}\" size=\"large\" severity=\"warning\"></p-badge>\r\n\t\t\t\t\t\t\t\t\t<!-- <div class=\"grid\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"col-3 md:col-3 lg:col-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\">Inverter</p>\r\n\t\t\t\t\t\t\t\t\t\t\t<p-badge [value]=\"2\" severity=\"success\" />\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"col-3 md:col-3 lg:col-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\">MMPT</p>\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\"><p-knob [(ngModel)]=\"station.mmpt\" valueTemplate=\"{{station.mmpt}}\" [step]=\"1\" [min]=\"0\" [max]=\"5\" [size]=\"50\"></p-knob></p>\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"col-3 md:col-3 lg:col-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\">String</p>\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\"><p-knob [(ngModel)]=\"station.string\" valueTemplate=\"{{station.string}}\" [step]=\"1\" [min]=\"0\" [max]=\"5\" [size]=\"50\"></p-knob></p>\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"col-3 md:col-3 lg:col-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\">PVN</p>\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\"><p-knob [(ngModel)]=\"station.pvn\" valueTemplate=\"{{station.pvn}}\" [step]=\"1\" [min]=\"0\" [max]=\"5\" [size]=\"50\"></p-knob></p>\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\t -->\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"col-12 md:col-6 lg:col-2 text-center\">\r\n\t\t\t\t\t\t\t\t\t<p class=\"text-center\"><i class=\"pi pi-cloud text-2xl\"></i>\r\n\t\t\t\t\t\t\t\t\t<span class=\"text-2xl mb-2 align-self-center md:align-self-end\"> {{station.temperature}} &#x2103;</span></p>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<!-- <div class=\"card\">\r\n\t\t\t\t\t\t\t<div class=\"flex flex-column md:flex-row align-items-center p-3 w-full\">\r\n\t\t\t\t\t\t\t\t<div class=\"flex-1 flex flex-column md:text-left\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"flex align-items-center\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"font-bold text-xl\"><a [routerLink]=\"['/uikit/station',station.id]\">{{station.name}}</a></div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"font-bold flex-auto text-xl align-items-right text-right\">{{station.updateTime}} <span [class]=\"'station-badge status-' + station.status.toLowerCase()\">{{station.status.toLowerCase()}}</span></div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<hr>\r\n\t\t\t\t\t\t\t\t\t<div class=\"mb-2\">{{station.location}}</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"flex align-items-center mt-2\">\r\n\t\t\t\t\t\t\t\t\t\t<p-knob [(ngModel)]=\"station.percentage\" valueTemplate=\"{value}%\" [step]=\"10\" [min]=\"0\" [max]=\"100\" [size]=\"70\"></p-knob>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"align-items-right\">\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"mt-2 text-xl\">Active Performance</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-2 text-sm\">Power: {{station.power}}kW | Irradiance: {{station.irradiance}}kWh/m2</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"mt-2\">Inverter: {{station.inverter}} | MMPT: {{station.mmpt}} | String: {{station.string}} | PVN: {{station.pvn}}</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"flex flex-row md:flex-column justify-content-between w-full md:w-auto align-items-center md:align-items-end mt-5 md:mt-0\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"pi pi-cloud text-2xl\"></i>\r\n\t\t\t\t\t\t\t\t\t\t<span class=\"text-2xl font-semibold mb-2 align-self-center md:align-self-end\">{{station.temperature}} &#x2103;</span>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div> -->\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</ng-template>\r\n\r\n\t\t\t\t<!-- <ng-template let-products pTemplate=\"listItem\">\r\n\t\t\t\t\t<div class=\"col-12\" *ngFor=\"let station of stations\">\r\n\t\t\t\t\t\t<div class=\"card\">\r\n\t\t\t\t\t\t\t<div class=\"flex flex-column md:flex-row align-items-center p-3 w-full\">\r\n\t\t\t\t\t\t\t\t<div class=\"flex-1 flex flex-column md:text-left\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"flex align-items-center\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"font-bold text-xl\"><a [routerLink]=\"['/uikit/station',station.id]\">{{station.name}}</a></div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"font-bold flex-auto text-xl align-items-right text-right\">{{station.updateTime}} <span [class]=\"'station-badge status-' + station.status.toLowerCase()\">{{station.status.toLowerCase()}}</span></div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<hr>\r\n\t\t\t\t\t\t\t\t\t<div class=\"mb-2\">{{station.location}}</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"flex align-items-center mt-2\">\r\n\t\t\t\t\t\t\t\t\t\t<p-knob [(ngModel)]=\"station.percentage\" valueTemplate=\"{value}%\" [step]=\"10\" [min]=\"0\" [max]=\"100\" [size]=\"70\"></p-knob>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"align-items-right\">\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"mt-2 text-xl\">Active Performance</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-2 text-sm\">Power: {{station.power}}kW | Irradiance: {{station.irradiance}}kWh/m2</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"mt-2\">Inverter: {{station.inverter}} | MMPT: {{station.mmpt}} | String: {{station.string}} | PVN: {{station.pvn}}</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"flex flex-row md:flex-column justify-content-between w-full md:w-auto align-items-center md:align-items-end mt-5 md:mt-0\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"pi pi-cloud text-2xl\"></i>\r\n\t\t\t\t\t\t\t\t\t\t<span class=\"text-2xl font-semibold mb-2 align-self-center md:align-self-end\">{{station.temperature}} &#x2103;</span>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</ng-template> -->\r\n\r\n\t\t\t\t<!-- <ng-template let-products pTemplate=\"gridItem\">\r\n\t\t\t\t\t<div class=\"grid grid-nogutter\">\r\n\t\t\t\t\t<div class=\"col-12 md:col-4\" *ngFor=\"let station of stations\">\r\n\t\t\t\t\t\t<div class=\"card m-3 border-1 surface-border\">\r\n\t\t\t\t\t\t\t<div class=\"flex flex-wrap gap-2 align-items-center justify-content-between mb-2\">\r\n\t\t\t\t\t\t\t\t<div class=\"flex align-items-center\">\r\n\t\t\t\t\t\t\t\t\t<i class=\"pi pi-tag mr-2\"></i>\r\n\t\t\t\t\t\t\t\t\t<span class=\"font-semibold\">{{station.name}}</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"flex flex-column align-items-center text-center mb-3\">\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<div class=\"text-2xl font-bold\">{{station.name}}</div>\r\n\t\t\t\t\t\t\t\t<div class=\"mb-3\">{{station.location}}</div>\r\n\t\t\t\t\t\t\t\t<p-rating [ngModel]=\"station.temperature\" [readonly]=\"true\" [cancel]=\"false\"></p-rating>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"flex align-items-center justify-content-between\">\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</ng-template> -->\r\n\t\t\t</p-dataView>\r\n\t\t</div>\r\n\t</div>\r\n\r\n    <!-- <div class=\"col-12 lg:col-6 xl:col-6\">\r\n\t\t<div class=\"card\">\r\n\t\t\t<img src=\"https://www.digitaltrends.com/wp-content/uploads/2020/02/custom-map.jpg?fit=720%2C479&p=1\" >\r\n\t\t</div>\r\n\t</div> -->\r\n</div>"], "mappings": "AAIA,SAAuBA,YAAY,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;ICiC5CC,EAAA,CAAAC,cAAA,cAA2E;IACTD,EAAA,CAAAE,UAAA,sBAAAC,sEAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAYR,EAAA,CAAAS,WAAA,CAAAF,MAAA,CAAAG,YAAA,CAAAN,MAAA,CAAoB;IAAA,EAAC;IAACJ,EAAA,CAAAW,YAAA,EAAa;IAChHX,EAAA,CAAAC,cAAA,qBAAiG;IAAlCD,EAAA,CAAAE,UAAA,sBAAAU,sEAAAR,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAO,MAAA,GAAAb,EAAA,CAAAQ,aAAA;MAAA,OAAYR,EAAA,CAAAS,WAAA,CAAAI,MAAA,CAAAH,YAAA,CAAAN,MAAA,CAAoB;IAAA,EAAC;IAACJ,EAAA,CAAAW,YAAA,EAAa;IAC9GX,EAAA,CAAAC,cAAA,eAAgC;IACVD,EAAA,CAAAc,SAAA,YAA4B;IAC5Bd,EAAA,CAAAC,cAAA,gBAAoF;IAA/BD,EAAA,CAAAE,UAAA,mBAAAa,8DAAAX,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAU,MAAA,GAAAhB,EAAA,CAAAQ,aAAA;MAAA,MAAAS,GAAA,GAAAjB,EAAA,CAAAkB,WAAA;MAAA,OAASlB,EAAA,CAAAS,WAAA,CAAAO,MAAA,CAAAG,QAAA,CAAAF,GAAA,EAAAb,MAAA,CAAoB;IAAA,EAAC;IAAnFJ,EAAA,CAAAW,YAAA,EAAoF;;;;IAJ9FX,EAAA,CAAAoB,SAAA,GAA8B;IAA9BpB,EAAA,CAAAqB,UAAA,YAAAC,MAAA,CAAAC,kBAAA,CAA8B;IAC9BvB,EAAA,CAAAoB,SAAA,GAA6B;IAA7BpB,EAAA,CAAAqB,UAAA,YAAAC,MAAA,CAAAE,iBAAA,CAA6B;;;;;;IAU1CxB,EAAA,CAAAC,cAAA,UAAqG;IAMhGD,EAAA,CAAAyB,MAAA,GACD;IAAAzB,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,cAA+B;IAA8CD,EAAA,CAAAyB,MAAA,GAAgB;IAAAzB,EAAA,CAAAW,YAAA,EAAI;IAEjGX,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAc,SAAA,mBAAgC;IAACd,EAAA,CAAAyB,MAAA,IACzD;IACDzB,EAAA,CAAAW,YAAA,EAAM;IAEPX,EAAA,CAAAC,cAAA,eAAsC;IACFD,EAAA,CAAAc,SAAA,kBAA+B;IAACd,EAAA,CAAAyB,MAAA,IAAmB;IAAAzB,EAAA,CAAAW,YAAA,EAAM;IAE7FX,EAAA,CAAAC,cAAA,eAAsC;IACFD,EAAA,CAAAc,SAAA,mBAA8B;IAAEd,EAAA,CAAAyB,MAAA,IAA4B;IAAAzB,EAAA,CAAAW,YAAA,EAAM;IAEtGX,EAAA,CAAAC,cAAA,eAAsC;IACrCD,EAAA,CAAAc,SAAA,mBAAuG;IAExGd,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAAkD;IACjDD,EAAA,CAAAc,SAAA,mBAA4E;IAyB7Ed,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAAkD;IAC1BD,EAAA,CAAAc,SAAA,aAAoC;IAC3Dd,EAAA,CAAAC,cAAA,gBAAgE;IAACD,EAAA,CAAAyB,MAAA,IAAgC;IAAAzB,EAAA,CAAAW,YAAA,EAAO;;;;;IArDtEX,EAAA,CAAA0B,UAAA,yBAAAC,UAAA,CAAAC,MAAA,CAAAC,WAAA,GAA8D;IAM/F7B,EAAA,CAAAoB,SAAA,GACD;IADCpB,EAAA,CAAA8B,kBAAA,MAAAH,UAAA,CAAAI,QAAA,MACD;IACkC/B,EAAA,CAAAoB,SAAA,GAA0C;IAA1CpB,EAAA,CAAAqB,UAAA,eAAArB,EAAA,CAAAgC,eAAA,KAAAC,GAAA,EAAAN,UAAA,CAAAO,EAAA,EAA0C;IAAClC,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAAmC,iBAAA,CAAAR,UAAA,CAAAS,IAAA,CAAgB;IAEnCpC,EAAA,CAAAoB,SAAA,GACzD;IADyDpB,EAAA,CAAA8B,kBAAA,MAAAH,UAAA,CAAAU,UAAA,MACzD;IAIkErC,EAAA,CAAAoB,SAAA,GAAmB;IAAnBpB,EAAA,CAAA8B,kBAAA,MAAAH,UAAA,CAAAW,KAAA,OAAmB;IAGnBtC,EAAA,CAAAoB,SAAA,GAA4B;IAA5BpB,EAAA,CAAA8B,kBAAA,MAAAH,UAAA,CAAAY,UAAA,WAA4B;IAGtFvC,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAAqB,UAAA,SAAAmB,MAAA,CAAAC,IAAA,IAAgB,YAAAD,MAAA,CAAAE,WAAA;IAID1C,EAAA,CAAAoB,SAAA,GAA4B;IAA5BpB,EAAA,CAAA2C,qBAAA,UAAAhB,UAAA,CAAAiB,QAAA,CAA4B;IAC7B5C,EAAA,CAAAoB,SAAA,GAAwB;IAAxBpB,EAAA,CAAA2C,qBAAA,UAAAhB,UAAA,CAAAkB,IAAA,CAAwB;IACxB7C,EAAA,CAAAoB,SAAA,GAA0B;IAA1BpB,EAAA,CAAA2C,qBAAA,UAAAhB,UAAA,CAAAmB,MAAA,CAA0B;IAC1B9C,EAAA,CAAAoB,SAAA,GAAuB;IAAvBpB,EAAA,CAAA2C,qBAAA,UAAAhB,UAAA,CAAAoB,GAAA,CAAuB;IAyBmB/C,EAAA,CAAAoB,SAAA,GAAgC;IAAhCpB,EAAA,CAAA8B,kBAAA,MAAAH,UAAA,CAAAqB,WAAA,YAAgC;;;;;IArDrGhD,EAAA,CAAAiD,UAAA,IAAAC,4CAAA,oBAkFM;;;;IAlFmBlD,EAAA,CAAAqB,UAAA,YAAA8B,MAAA,CAAAC,QAAA,CAAW;;;;;;;AD5BzC,OAAM,MAAOC,cAAc;EAkCvBC,YAAmBC,aAA4B,EACnCC,eAAgC,EAChCC,gBAAkC,EAClCC,cAA8B,EAC9BC,EAAe,EACfC,MAAc,EACdC,YAA0B;IANnB,KAAAN,aAAa,GAAbA,aAAa;IACpB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IApCxB,KAAAT,QAAQ,GAAa,EAAE;IASvB,KAAA7B,kBAAkB,GAAiB,EAAE;IAErC,KAAAC,iBAAiB,GAAiB,EAAE;IAEpC,KAAAsC,SAAS,GAAW,CAAC;IAErB,KAAAC,SAAS,GAAW,EAAE;IAEtB,KAAAC,YAAY,GAAU,EAAE;IAExB,KAAAC,YAAY,GAAU,EAAE;IAExB,KAAAC,WAAW,GAAU,EAAE;IAgBnB,IAAI,CAACC,YAAY,GAAG,IAAI,CAACZ,aAAa,CAACa,aAAa,CACnDC,IAAI,CAACtE,YAAY,CAAC,EAAE,CAAC,CAAC,CACtBuE,SAAS,CAAEC,MAAM,IAAI;MACrB;IAAA,CACA,CAAC;EACN;EAEAC,QAAQA,CAAA;IACJ,IAAI,CAACC,gBAAgB,EAAE;IAEvB,IAAI,CAACC,UAAU,GAAG;MACdC,mBAAmB,EAAE,KAAK;MAC1BC,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE;;OAEZ;MACDC,MAAM,EAAE;QACNC,CAAC,EAAE;UACDF,OAAO,EAAE;SACV;QACDG,CAAC,EAAE;UACDH,OAAO,EAAE;;;KAGd;IAED,IAAI,CAACpC,WAAW,GAAG;MACjBiC,mBAAmB,EAAE,KAAK;MAC1BO,QAAQ,EAAE;QACRC,IAAI,EAAE;UACJC,OAAO,EAAE;SACV;QACDC,KAAK,EAAE;UACLC,MAAM,EAAE;;OAEX;MACDV,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE;;OAEZ;MACDC,MAAM,EAAE;QACNC,CAAC,EAAE;UACDF,OAAO,EAAE;SACV;QACDG,CAAC,EAAE;UACDH,OAAO,EAAE;;;KAGd;IAED,IAAI,CAACrC,IAAI,GAAG,CACV;MACE8C,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MACnFC,QAAQ,EAAE,CACR;QACEC,eAAe,EAAE,SAAS;QAC1BC,WAAW,EAAE,aAAa;QAC1BC,WAAW,EAAE,CAAC;QACdlD,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;OAClE;KAEJ,EAAE;MACD8C,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MACnFC,QAAQ,EAAE,CACR;QACEC,eAAe,EAAE,aAAa;QAC9BC,WAAW,EAAE,SAAS;QACtBC,WAAW,EAAE,CAAC;QACdlD,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QAClEmD,oBAAoB,EAAE;OACvB;KAEJ,CACF;EACP;EAEAnB,gBAAgBA,CAAA;IACd,IAAI,CAAChB,gBAAgB,CAACgB,gBAAgB,EAAE,CAACoB,IAAI,CAACC,aAAa,IAAG;MAC5D,IAAIA,aAAa,CAACC,MAAM,GAAG,CAAC,EAAC;QAC3B,IAAI,CAACvC,eAAe,CAACwC,eAAe,EAAE,CAACH,IAAI,CAACI,YAAY,IAAG;UACzD,IAAI,CAACpC,YAAY,CAACqC,WAAW,CAACD,YAAY,CAAC;UAC3C,IAAI,CAAC7C,QAAQ,GAAG6C,YAAY;UAC5B,IAAI,CAACE,uBAAuB,EAAE;QAChC,CAAC,CAAC;OACH,MAAI;QACH,IAAI,CAACvC,MAAM,CAACwC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;;IAE5C,CAAC,CAAC;EACJ;EAEAD,uBAAuBA,CAAA;IACrB,IAAI,CAAC/C,QAAQ,CAACiD,OAAO,CAACC,OAAO,IAAG;MAC9B,IAAIC,GAAG,GAAG,IAAIC,IAAI,EAAE;MACpB,IAAIC,kBAAkB,GAAG,IAAID,IAAI,CAACD,GAAG,CAACG,WAAW,EAAE,EAAEH,GAAG,CAACI,QAAQ,EAAE,EAAEJ,GAAG,CAACK,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,EAAE;MAC1G,IAAIC,gBAAgB,GAAG,IAAIN,IAAI,CAACD,GAAG,CAACG,WAAW,EAAE,EAAEH,GAAG,CAACI,QAAQ,EAAE,EAAEJ,GAAG,CAACK,OAAO,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,EAAE;MAE5G,IAAIE,OAAO,GAA2B;QAClCC,MAAM,EAAGC,WAAW;QACpBC,SAAS,EAAE,CAAC;QACZC,aAAa,EAAEV,kBAAkB;QACjCW,WAAW,EAAEN,gBAAgB;QAC7BO,SAAS,EAAE,KAAK;QAChBC,UAAU,EAAC,IAAI;QACfC,SAAS,EAAEjB,OAAO,CAACpE;OACtB;MACD,IAAI,CAACsB,eAAe,CAACgE,sBAAsB,CAACT,OAAO,CAAC,CAAClB,IAAI,CAACpD,IAAI,IAAG;QAC7D,IAAI,CAACgF,UAAU,GAAGhF,IAAI;QACtBiF,OAAO,CAACC,GAAG,CAAC,IAAI,CAACF,UAAU,CAAC;QAC5B,IAAI,IAAI,CAACA,UAAU,CAAChF,IAAI,CAACsD,MAAM,GAAG,CAAC,EAAC;UAChC,IAAI,CAAC6B,UAAU,CAACP,SAAS,EAAEC,UAAU,CAAC;SACzC,MAAI;UACD,IAAI,CAAC5D,cAAc,CAACmE,GAAG,CAAC;YAAEC,QAAQ,EAAE,MAAM;YAAEC,OAAO,EAAE,SAAS;YAAEC,MAAM,EAAE;UAAqB,CAAE,CAAC;;MAExG,CAAC,CAAC;IAEJ,CAAC,CAAC;IAIF,IAAI,CAACvE,gBAAgB,CAACgB,gBAAgB,EAAE,CAACoB,IAAI,CAACC,aAAa,IAAG;MAC5D,IAAIA,aAAa,CAACC,MAAM,GAAG,CAAC,EAAC;QAC3B,IAAI,CAACvC,eAAe,CAACwC,eAAe,EAAE,CAACH,IAAI,CAACI,YAAY,IAAG;UACzD,IAAI,CAACpC,YAAY,CAACqC,WAAW,CAACD,YAAY,CAAC;UAC3C,IAAI,CAAC7C,QAAQ,GAAG6C,YAAY;UAC5B,IAAI,CAACE,uBAAuB,EAAE;QAChC,CAAC,CAAC;OACH,MAAI;QACH,IAAI,CAACvC,MAAM,CAACwC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;;IAE5C,CAAC,CAAC;EACJ;EAGA1F,YAAYA,CAACuH,KAAU;IACnB,MAAMC,KAAK,GAAGD,KAAK,CAACC,KAAK;IAEzB,IAAIA,KAAK,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;MAC1B,IAAI,CAACrE,SAAS,GAAG,CAAC,CAAC;MACnB,IAAI,CAACC,SAAS,GAAGmE,KAAK,CAACE,SAAS,CAAC,CAAC,EAAEF,KAAK,CAACnC,MAAM,CAAC;KACpD,MAAM;MACH,IAAI,CAACjC,SAAS,GAAG,CAAC;MAClB,IAAI,CAACC,SAAS,GAAGmE,KAAK;;EAE9B;EAEA/G,QAAQA,CAACkH,EAAY,EAAEJ,KAAY;IAC/BI,EAAE,CAACC,MAAM,CAAEL,KAAK,CAACM,MAA2B,CAACL,KAAK,CAAC;EACvD;EAGAM,OAAOA,CAAA;IACH,IAAI,CAACC,MAAM,GAAG,2FAA2F;EAC7G;EAEA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEAC,WAAWA,CAAA;IACP,IAAI,IAAI,CAACvE,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAACwE,WAAW,EAAE;;EAEvC;EAAC,QAAAC,CAAA,G;qBApQQvF,cAAc,EAAArD,EAAA,CAAA6I,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA/I,EAAA,CAAA6I,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAjJ,EAAA,CAAA6I,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAnJ,EAAA,CAAA6I,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAArJ,EAAA,CAAA6I,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAAvJ,EAAA,CAAA6I,iBAAA,CAAAW,EAAA,CAAAC,MAAA,GAAAzJ,EAAA,CAAA6I,iBAAA,CAAAa,EAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAdvG,cAAc;IAAAwG,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCrB3BnK,EAAA,CAAAC,cAAA,aAAkB;QAK0DD,EAAA,CAAAyB,MAAA,4BAAqB;QAAAzB,EAAA,CAAAW,YAAA,EAAO;QACpFX,EAAA,CAAAC,cAAA,aAA0C;QAAAD,EAAA,CAAAyB,MAAA,GAAmB;QAAAzB,EAAA,CAAAW,YAAA,EAAM;QAEvEX,EAAA,CAAAC,cAAA,aAAqI;QACjID,EAAA,CAAAc,SAAA,kBAAsC;QAC1Cd,EAAA,CAAAW,YAAA,EAAM;QAMlBX,EAAA,CAAAC,cAAA,cAAsC;QAIkCD,EAAA,CAAAyB,MAAA,kCAA0B;QAAAzB,EAAA,CAAAW,YAAA,EAAO;QACzFX,EAAA,CAAAC,cAAA,cAA0C;QAAAD,EAAA,CAAAyB,MAAA,sBAAc;QAAAzB,EAAA,CAAAW,YAAA,EAAM;QAElEX,EAAA,CAAAC,cAAA,cAAqI;QACjID,EAAA,CAAAc,SAAA,kBAA+B;QACnCd,EAAA,CAAAW,YAAA,EAAM;QAQlBX,EAAA,CAAAC,cAAA,cAAwC;QAGxCD,EAAA,CAAAiD,UAAA,KAAAoH,sCAAA,0BAUc,KAAAC,sCAAA;QA8IftK,EAAA,CAAAW,YAAA,EAAa;;;QAtL8CX,EAAA,CAAAoB,SAAA,GAAmB;QAAnBpB,EAAA,CAAAmC,iBAAA,CAAAiI,GAAA,CAAAhH,QAAA,CAAA2C,MAAA,CAAmB;QAEoB/F,EAAA,CAAAoB,SAAA,GAA+C;QAA/CpB,EAAA,CAAAqB,UAAA,YAAArB,EAAA,CAAAuK,eAAA,IAAAC,GAAA,EAA+C;QAe/CxK,EAAA,CAAAoB,SAAA,IAA+C;QAA/CpB,EAAA,CAAAqB,UAAA,YAAArB,EAAA,CAAAuK,eAAA,IAAAC,GAAA,EAA+C;QAYjIxK,EAAA,CAAAoB,SAAA,GAAkB;QAAlBpB,EAAA,CAAAqB,UAAA,UAAA+I,GAAA,CAAAhH,QAAA,CAAkB,4CAAAgH,GAAA,CAAArG,SAAA,eAAAqG,GAAA,CAAAtG,SAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}