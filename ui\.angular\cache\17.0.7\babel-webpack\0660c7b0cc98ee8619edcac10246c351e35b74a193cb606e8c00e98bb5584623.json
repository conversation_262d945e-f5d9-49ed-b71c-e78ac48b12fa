{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"../../../shared/components/modern-breadcrumb/modern-breadcrumb.component\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"primeng/inputtext\";\nfunction HelpComponent_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function HelpComponent_button_19_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r5);\n      const category_r3 = restoredCtx.$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.filterByCategory(category_r3));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r3 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(\"category-btn \" + (ctx_r0.selectedCategory === category_r3 ? \"active\" : \"\"));\n    i0.ɵɵproperty(\"label\", category_r3);\n  }\n}\nfunction HelpComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 44);\n    i0.ɵɵelement(2, \"i\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No results found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Try adjusting your search terms or browse different categories.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HelpComponent_div_25_div_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵelement(1, \"img\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const faq_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", faq_r7.imageSrc, i0.ɵɵsanitizeUrl)(\"alt\", faq_r7.question);\n  }\n}\nfunction HelpComponent_div_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49);\n    i0.ɵɵlistener(\"click\", function HelpComponent_div_25_div_1_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const faq_r7 = restoredCtx.$implicit;\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.toggleFAQ(faq_r7));\n    });\n    i0.ɵɵelementStart(2, \"div\", 50)(3, \"span\", 51);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 52);\n    i0.ɵɵelement(8, \"i\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 54)(10, \"div\", 55);\n    i0.ɵɵelement(11, \"p\", 56);\n    i0.ɵɵtemplate(12, HelpComponent_div_25_div_1_div_12_Template, 2, 2, \"div\", 57);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const faq_r7 = ctx.$implicit;\n    i0.ɵɵclassProp(\"expanded\", faq_r7.expanded);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(faq_r7.category);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(faq_r7.question);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"pi-chevron-down\", !faq_r7.expanded)(\"pi-chevron-up\", faq_r7.expanded);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"show\", faq_r7.expanded);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", faq_r7.answer, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", faq_r7.imageSrc);\n  }\n}\nfunction HelpComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵtemplate(1, HelpComponent_div_25_div_1_Template, 13, 12, \"div\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.filteredFAQs);\n  }\n}\nexport let HelpComponent = /*#__PURE__*/(() => {\n  class HelpComponent {\n    constructor() {\n      this.faqItems = [\n      // Getting Started\n      {\n        id: 'getting-started-1',\n        question: 'How do I get started with SolarKapital?',\n        answer: 'Welcome to SolarKapital! To get started, you need to add your solar energy providers first. Navigate to the Providers section from the main menu and follow the setup wizard to connect your solar installations.',\n        category: 'Getting Started'\n      }, {\n        id: 'getting-started-2',\n        question: 'What information do I need to set up my account?',\n        answer: 'You will need your solar provider credentials (username and password), and depending on your provider, additional information like Portfolio ID or FTP URL. Make sure you have this information ready before starting the setup process.',\n        category: 'Getting Started'\n      },\n      // Providers Management\n      {\n        id: 'providers-1',\n        question: 'How do I add a new solar energy provider?',\n        answer: 'To add a new provider: 1) Go to the Providers page from the main menu, 2) Click the \"Add Provider\" button, 3) Select your provider from the dropdown list, 4) Enter your credentials (username and password), 5) For some providers like Aurora, you may need to enter a Portfolio ID,  6) For some providers like SMA, you may need to enter the Ftp Url, 7) Get the list of stations, 6) Select which stations you want to monitor, 7) Click \"Save Providers\" to complete the setup.',\n        category: 'Providers'\n      }, {\n        id: 'providers-2',\n        question: 'What providers are currently supported?',\n        answer: 'SolarKapital currently supports major solar energy providers including SolarEdge, Huawei, Fronius, SMA, and others. The list of supported providers is constantly expanding. If your provider is not listed, please contact our support team.',\n        category: 'Providers'\n      }, {\n        id: 'providers-3',\n        question: 'Why do I need to provide my provider credentials?',\n        answer: 'Your provider credentials are required to securely access your solar energy data from your provider\\'s platform. This allows SolarKapital to retrieve real-time and historical data about your solar installations. Your credentials are encrypted and stored securely.',\n        category: 'Providers'\n      }, {\n        id: 'providers-4',\n        question: 'What provider credentials are required for Huawei?',\n        answer: 'The username and password that should be used are not the ones that you use to enter Huawei portal. You should go to System->Company Management->Northbound Management and create a new user that will be able to retrieve portal data.',\n        category: 'Providers',\n        imageSrc: '/assets/layout/images/huawei_example.png'\n      }, {\n        id: 'providers-5',\n        question: 'What is a Portfolio ID and when do I need it?',\n        answer: 'A Portfolio ID is required for certain providers like Aurora. It\\'s a unique identifier that groups your solar installations under your account. You can find this ID in your provider\\'s web portal, usually in the account settings or dashboard overview.',\n        category: 'Providers',\n        imageSrc: '/assets/layout/images/aurora_example.png'\n      }, {\n        id: 'providers-6',\n        question: 'What is an FTP url and when do I need it?',\n        answer: 'An FTP url is required for certain providers like SMA. It\\'s the url of the FTP that is used to identify your solar installations under your account. The FTP url should be like <em>ftp://ftp.server.com/additionalfolder/</em>. Please use the full path where the stations folders are located.',\n        category: 'Providers'\n      }, {\n        id: 'providers-7',\n        question: 'How do I edit or remove a provider configuration?',\n        answer: 'To edit a provider: click the edit (pencil) icon on the provider card and update the information. To remove a provider: click the delete (trash) icon and confirm the removal. Note that removing a provider will also remove all associated station data.',\n        category: 'Providers'\n      }, {\n        id: 'providers-8',\n        question: 'Can I add multiple configurations for the same provider?',\n        answer: 'Yes, you can add multiple configurations for the same provider if you have multiple accounts or different access credentials. Each configuration will be treated separately and can monitor different sets of stations.',\n        category: 'Providers'\n      },\n      // Stations & Monitoring\n      {\n        id: 'stations-1',\n        question: 'How do I view my solar station data?',\n        answer: 'After setting up your providers, your stations will automatically appear in the Stations section. Click on any station to view detailed energy production data, charts, and performance metrics.',\n        category: 'Stations'\n      }, {\n        id: 'stations-2',\n        question: 'What data can I see for each station?',\n        answer: 'For each station, you can view: real-time energy production, historical energy data, performance charts, weather information, inverter details, and system health status. The data is updated regularly based on your provider\\'s refresh rate.',\n        category: 'Stations'\n      }, {\n        id: 'stations-3',\n        question: 'How often is the data updated?',\n        answer: 'Data update frequency depends on your solar provider. Most providers update data every 15-30 minutes. SolarKapital automatically syncs with your providers to ensure you have the latest information available.',\n        category: 'Stations'\n      },\n      // Troubleshooting\n      {\n        id: 'troubleshooting-1',\n        question: 'My provider configuration is not working. What should I do?',\n        answer: 'First, verify that your credentials are correct by logging into your provider\\'s website directly. If that works, check if your provider requires special permissions for API access. Some providers may need to enable third-party access in their settings.',\n        category: 'Troubleshooting'\n      }, {\n        id: 'troubleshooting-2',\n        question: 'I don\\'t see any data for my stations. Why?',\n        answer: 'This could be due to several reasons: 1) The provider configuration is incorrect, 2) Your stations are not properly selected in the provider setup, 3) There might be a temporary issue with the provider\\'s API, 4) Your stations might not have recent data available.',\n        category: 'Troubleshooting'\n      }, {\n        id: 'troubleshooting-3',\n        question: 'How do I reset my provider configuration?',\n        answer: 'To reset a provider configuration: go to the Providers page, click the delete icon for the problematic provider, confirm the removal, then add the provider again with the correct information. This will create a fresh configuration.',\n        category: 'Troubleshooting'\n      },\n      // Account & Security\n      {\n        id: 'security-1',\n        question: 'Is my data secure?',\n        answer: 'Yes, SolarKapital takes security seriously. All provider credentials are encrypted before storage, data transmission uses secure HTTPS connections, and we follow industry best practices for data protection. Your solar data is never shared with third parties.',\n        category: 'Security'\n      }, {\n        id: 'security-2',\n        question: 'Can I change my account password?',\n        answer: 'Yes, you can change your account password from the Profile Settings section. Click on your profile icon in the top-right corner and select \"Profile Settings\" to update your password and other account information.',\n        category: 'Security'\n      }];\n      this.categories = [];\n      this.selectedCategory = 'All';\n      this.filteredFAQs = [];\n      this.searchQuery = '';\n      this.breadcrumbItems = [{\n        label: 'Dashboard',\n        routerLink: '/app/index'\n      }, {\n        label: 'Help & FAQ'\n      }];\n    }\n    ngOnInit() {\n      // Extract unique categories\n      this.categories = ['All', ...Array.from(new Set(this.faqItems.map(item => item.category)))];\n      this.filteredFAQs = this.faqItems;\n    }\n    filterByCategory(category) {\n      this.selectedCategory = category;\n      this.applyFilters();\n    }\n    onSearch() {\n      this.applyFilters();\n    }\n    applyFilters() {\n      let filtered = this.faqItems;\n      // Filter by category\n      if (this.selectedCategory !== 'All') {\n        filtered = filtered.filter(item => item.category === this.selectedCategory);\n      }\n      // Filter by search query\n      if (this.searchQuery.trim()) {\n        const query = this.searchQuery.toLowerCase();\n        filtered = filtered.filter(item => item.question.toLowerCase().includes(query) || item.answer.toLowerCase().includes(query));\n      }\n      this.filteredFAQs = filtered;\n    }\n    toggleFAQ(faq) {\n      faq.expanded = !faq.expanded;\n    }\n    expandAll() {\n      this.filteredFAQs.forEach(faq => faq.expanded = true);\n    }\n    collapseAll() {\n      this.filteredFAQs.forEach(faq => faq.expanded = false);\n    }\n    static #_ = this.ɵfac = function HelpComponent_Factory(t) {\n      return new (t || HelpComponent)();\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HelpComponent,\n      selectors: [[\"app-help\"]],\n      decls: 80,\n      vars: 5,\n      consts: [[1, \"help-container\"], [1, \"col-12\", \"mb-4\"], [3, \"items\"], [1, \"help-header\"], [1, \"header-content\"], [1, \"header-icon\"], [1, \"pi\", \"pi-question-circle\"], [1, \"header-text\"], [1, \"help-controls\"], [1, \"search-section\"], [1, \"search-wrapper\"], [1, \"pi\", \"pi-search\", \"search-icon\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"Search for help topics...\", 1, \"search-input\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [1, \"filter-section\"], [1, \"category-filters\"], [\"pButton\", \"\", \"size\", \"small\", 3, \"label\", \"class\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"action-buttons\"], [\"pButton\", \"\", \"label\", \"Expand All\", \"icon\", \"pi pi-plus\", \"severity\", \"secondary\", \"size\", \"small\", 3, \"click\"], [\"pButton\", \"\", \"label\", \"Collapse All\", \"icon\", \"pi pi-minus\", \"severity\", \"secondary\", \"size\", \"small\", 3, \"click\"], [1, \"faq-content\"], [\"class\", \"no-results\", 4, \"ngIf\"], [\"class\", \"faq-list\", 4, \"ngIf\"], [1, \"quick-links-section\"], [1, \"quick-links-grid\"], [\"routerLink\", \"/app/providers\", 1, \"quick-link-card\"], [1, \"card-icon\"], [1, \"pi\", \"pi-server\"], [1, \"card-content\"], [1, \"card-arrow\"], [1, \"pi\", \"pi-arrow-right\"], [\"routerLink\", \"/app/stations\", 1, \"quick-link-card\"], [1, \"pi\", \"pi-chart-line\"], [\"routerLink\", \"/app/index\", 1, \"quick-link-card\"], [1, \"pi\", \"pi-home\"], [\"routerLink\", \"/app/communication\", 1, \"quick-link-card\"], [1, \"pi\", \"pi-comments\"], [1, \"support-section\"], [1, \"support-card\"], [1, \"support-icon\"], [1, \"pi\", \"pi-headphones\"], [1, \"support-content\"], [\"pButton\", \"\", \"label\", \"Contact Support\", \"icon\", \"pi pi-envelope\", 1, \"support-button\"], [\"pButton\", \"\", \"size\", \"small\", 3, \"label\", \"click\"], [1, \"no-results\"], [1, \"no-results-icon\"], [1, \"pi\", \"pi-search\"], [1, \"faq-list\"], [\"class\", \"faq-item\", 3, \"expanded\", 4, \"ngFor\", \"ngForOf\"], [1, \"faq-item\"], [1, \"faq-question\", 3, \"click\"], [1, \"question-content\"], [1, \"category-badge\"], [1, \"expand-icon\"], [1, \"pi\"], [1, \"faq-answer\"], [1, \"answer-content\"], [3, \"innerHTML\"], [\"class\", \"answer-image\", 4, \"ngIf\"], [1, \"answer-image\"], [3, \"src\", \"alt\"]],\n      template: function HelpComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"app-modern-breadcrumb\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelement(6, \"i\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 7)(8, \"h1\");\n          i0.ɵɵtext(9, \"Help & FAQ\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"p\");\n          i0.ɵɵtext(11, \"Find answers to common questions and learn how to make the most of SolarKapital\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"div\", 8)(13, \"div\", 9)(14, \"div\", 10);\n          i0.ɵɵelement(15, \"i\", 11);\n          i0.ɵɵelementStart(16, \"input\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function HelpComponent_Template_input_ngModelChange_16_listener($event) {\n            return ctx.searchQuery = $event;\n          })(\"input\", function HelpComponent_Template_input_input_16_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 13)(18, \"div\", 14);\n          i0.ɵɵtemplate(19, HelpComponent_button_19_Template, 1, 3, \"button\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 16)(21, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function HelpComponent_Template_button_click_21_listener() {\n            return ctx.expandAll();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function HelpComponent_Template_button_click_22_listener() {\n            return ctx.collapseAll();\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(23, \"div\", 19);\n          i0.ɵɵtemplate(24, HelpComponent_div_24_Template, 7, 0, \"div\", 20)(25, HelpComponent_div_25_Template, 2, 1, \"div\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 22)(27, \"h2\");\n          i0.ɵɵtext(28, \"Quick Links\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 23)(30, \"div\", 24)(31, \"div\", 25);\n          i0.ɵɵelement(32, \"i\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\", 27)(34, \"h3\");\n          i0.ɵɵtext(35, \"Manage Providers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"p\");\n          i0.ɵɵtext(37, \"Add, edit, or remove your solar energy providers\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 28);\n          i0.ɵɵelement(39, \"i\", 29);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"div\", 30)(41, \"div\", 25);\n          i0.ɵɵelement(42, \"i\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"div\", 27)(44, \"h3\");\n          i0.ɵɵtext(45, \"View Stations\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"p\");\n          i0.ɵɵtext(47, \"Monitor your solar installations and energy data\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"div\", 28);\n          i0.ɵɵelement(49, \"i\", 29);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"div\", 32)(51, \"div\", 25);\n          i0.ɵɵelement(52, \"i\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"div\", 27)(54, \"h3\");\n          i0.ɵɵtext(55, \"Dashboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"p\");\n          i0.ɵɵtext(57, \"Overview of all your solar energy systems\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(58, \"div\", 28);\n          i0.ɵɵelement(59, \"i\", 29);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(60, \"div\", 34)(61, \"div\", 25);\n          i0.ɵɵelement(62, \"i\", 35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"div\", 27)(64, \"h3\");\n          i0.ɵɵtext(65, \"Communication\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"p\");\n          i0.ɵɵtext(67, \"View system messages and notifications\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(68, \"div\", 28);\n          i0.ɵɵelement(69, \"i\", 29);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(70, \"div\", 36)(71, \"div\", 37)(72, \"div\", 38);\n          i0.ɵɵelement(73, \"i\", 39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"div\", 40)(75, \"h3\");\n          i0.ɵɵtext(76, \"Still need help?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"p\");\n          i0.ɵɵtext(78, \"Can't find what you're looking for? Our support team is here to help you get the most out of SolarKapital.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(79, \"button\", 41);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"items\", ctx.breadcrumbItems);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.filteredFAQs.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.filteredFAQs.length > 0);\n        }\n      },\n      dependencies: [i1.NgForOf, i1.NgIf, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgModel, i3.RouterLink, i4.ModernBreadcrumbComponent, i5.ButtonDirective, i6.InputText],\n      styles: [\".help-container[_ngcontent-%COMP%]{padding:2rem;max-width:1200px;margin:0 auto}.help-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--primary-color) 0%,var(--primary-600) 100%);border-radius:16px;padding:3rem 2rem;margin-bottom:2rem;color:#fff}.help-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{display:flex;align-items:center;gap:1.5rem}.help-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%]{font-size:3rem;opacity:.9}.help-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:700;margin:0 0 .5rem}.help-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.125rem;margin:0;opacity:.9}.help-controls[_ngcontent-%COMP%]{background:var(--surface-card);border-radius:12px;padding:1.5rem;margin-bottom:2rem;box-shadow:0 2px 8px #0000001a}.help-controls[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]{margin-bottom:1.5rem}.help-controls[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]{position:relative;max-width:500px}.help-controls[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-icon[_ngcontent-%COMP%]{position:absolute;left:1rem;top:50%;transform:translateY(-50%);color:var(--text-color-secondary);z-index:1}.help-controls[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]{width:100%;padding:.75rem 1rem .75rem 3rem;border:2px solid var(--surface-border);border-radius:8px;font-size:1rem;transition:all .3s ease}.help-controls[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-wrapper[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]:focus{border-color:var(--primary-color);box-shadow:0 0 0 3px rgba(var(--primary-color-rgb),.1)}.help-controls[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;flex-wrap:wrap;gap:1rem}.help-controls[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]   .category-filters[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:.5rem}.help-controls[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]   .category-filters[_ngcontent-%COMP%]   .category-btn[_ngcontent-%COMP%]{border-radius:20px;transition:all .3s ease}.help-controls[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]   .category-filters[_ngcontent-%COMP%]   .category-btn.active[_ngcontent-%COMP%]{background:var(--primary-color)!important;border-color:var(--primary-color)!important;color:#fff!important}.help-controls[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]   .category-filters[_ngcontent-%COMP%]   .category-btn[_ngcontent-%COMP%]:not(.active){background:var(--surface-100);border-color:var(--surface-300);color:var(--text-color)}.help-controls[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]   .category-filters[_ngcontent-%COMP%]   .category-btn[_ngcontent-%COMP%]:not(.active):hover{background:var(--surface-200)}.help-controls[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]{display:flex;gap:.5rem}.faq-content[_ngcontent-%COMP%]{margin-bottom:3rem}.faq-content[_ngcontent-%COMP%]   .no-results[_ngcontent-%COMP%]{text-align:center;padding:3rem 2rem;color:var(--text-color-secondary)}.faq-content[_ngcontent-%COMP%]   .no-results[_ngcontent-%COMP%]   .no-results-icon[_ngcontent-%COMP%]{font-size:3rem;margin-bottom:1rem;opacity:.5}.faq-content[_ngcontent-%COMP%]   .no-results[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.5rem;margin:0 0 .5rem}.faq-content[_ngcontent-%COMP%]   .no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1rem;margin:0}.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]{background:var(--surface-card);border-radius:12px;margin-bottom:1rem;box-shadow:0 2px 8px #0000001a;overflow:hidden;transition:all .3s ease}.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]:hover{box-shadow:0 4px 16px #00000026}.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-question[_ngcontent-%COMP%]{padding:1.5rem;cursor:pointer;display:flex;justify-content:space-between;align-items:flex-start;gap:1rem;transition:background-color .3s ease}.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-question[_ngcontent-%COMP%]:hover{background:var(--surface-50)}.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-question[_ngcontent-%COMP%]   .question-content[_ngcontent-%COMP%]{flex:1}.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-question[_ngcontent-%COMP%]   .question-content[_ngcontent-%COMP%]   .category-badge[_ngcontent-%COMP%]{display:inline-block;background:var(--primary-100);color:var(--primary-700);padding:.25rem .75rem;border-radius:12px;font-size:.75rem;font-weight:600;margin-bottom:.75rem}.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-question[_ngcontent-%COMP%]   .question-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.125rem;font-weight:600;margin:0;color:var(--text-color);line-height:1.4}.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-question[_ngcontent-%COMP%]   .expand-icon[_ngcontent-%COMP%]{color:var(--primary-color);font-size:1.25rem;transition:transform .3s ease}.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item.expanded[_ngcontent-%COMP%]   .faq-question[_ngcontent-%COMP%]   .expand-icon[_ngcontent-%COMP%]{transform:rotate(180deg)}.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-answer[_ngcontent-%COMP%]{max-height:0;overflow:hidden;transition:max-height .3s ease}.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-answer.show[_ngcontent-%COMP%]{max-height:500px}.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-answer[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]{padding:0 1.5rem 1.5rem;border-top:1px solid var(--surface-border)}.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-answer[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1rem;line-height:1.6;color:var(--text-color-secondary);margin:1rem 0 0}.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-answer[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   .answer-image[_ngcontent-%COMP%]{margin:1.5rem 0 0;border-radius:8px;overflow:hidden;box-shadow:0 4px 12px #0000001a;background:var(--surface-50);border:1px solid var(--surface-border)}.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-answer[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   .answer-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:auto;display:block;max-width:100%;object-fit:contain;border-radius:8px;transition:all .3s ease;cursor:pointer;max-height:400px;object-position:center}.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-answer[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   .answer-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover{transform:scale(1.02);box-shadow:0 6px 20px #00000026}.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-answer[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   .answer-image[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;width:100%;height:100%;background:var(--surface-100);display:none}.faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-answer[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   .answer-image[_ngcontent-%COMP%]   img[src=\\\"\\\"][_ngcontent-%COMP%], .faq-content[_ngcontent-%COMP%]   .faq-list[_ngcontent-%COMP%]   .faq-item[_ngcontent-%COMP%]   .faq-answer[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   .answer-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:not([src]){display:none}.quick-links-section[_ngcontent-%COMP%]{margin-bottom:3rem}.quick-links-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.75rem;font-weight:600;margin:0 0 1.5rem;color:var(--text-color)}.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:1.5rem}.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]{background:var(--surface-card);border-radius:12px;padding:1.5rem;display:flex;align-items:center;gap:1rem;cursor:pointer;transition:all .3s ease;box-shadow:0 2px 8px #0000001a;text-decoration:none;color:inherit}.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 24px #00000026}.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]:hover   .card-arrow[_ngcontent-%COMP%]{transform:translate(4px)}.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%]{width:3rem;height:3rem;background:var(--primary-100);color:var(--primary-700);border-radius:12px;display:flex;align-items:center;justify-content:center;font-size:1.5rem;flex-shrink:0}.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]{flex:1}.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.125rem;font-weight:600;margin:0 0 .25rem;color:var(--text-color)}.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.875rem;color:var(--text-color-secondary);margin:0;line-height:1.4}.quick-links-section[_ngcontent-%COMP%]   .quick-links-grid[_ngcontent-%COMP%]   .quick-link-card[_ngcontent-%COMP%]   .card-arrow[_ngcontent-%COMP%]{color:var(--primary-color);font-size:1.25rem;transition:transform .3s ease}.support-section[_ngcontent-%COMP%]   .support-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--surface-100) 0%,var(--surface-200) 100%);border-radius:16px;padding:2rem;display:flex;align-items:center;gap:1.5rem}.support-section[_ngcontent-%COMP%]   .support-card[_ngcontent-%COMP%]   .support-icon[_ngcontent-%COMP%]{width:4rem;height:4rem;background:var(--primary-color);color:#fff;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:2rem;flex-shrink:0}.support-section[_ngcontent-%COMP%]   .support-card[_ngcontent-%COMP%]   .support-content[_ngcontent-%COMP%]{flex:1}.support-section[_ngcontent-%COMP%]   .support-card[_ngcontent-%COMP%]   .support-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:600;margin:0 0 .5rem;color:var(--text-color)}.support-section[_ngcontent-%COMP%]   .support-card[_ngcontent-%COMP%]   .support-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1rem;color:var(--text-color-secondary);margin:0 0 1.5rem;line-height:1.5}.support-section[_ngcontent-%COMP%]   .support-card[_ngcontent-%COMP%]   .support-content[_ngcontent-%COMP%]   .support-button[_ngcontent-%COMP%]{background:var(--primary-color);border-color:var(--primary-color)}@media (max-width: 768px){.help-container[_ngcontent-%COMP%]{padding:1rem}.help-header[_ngcontent-%COMP%]{padding:2rem 1.5rem}.help-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{flex-direction:column;text-align:center;gap:1rem}.help-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem}.help-controls[_ngcontent-%COMP%]{padding:1rem}.help-controls[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.help-controls[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]   .category-filters[_ngcontent-%COMP%], .help-controls[_ngcontent-%COMP%]   .filter-section[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]{justify-content:center}.quick-links-grid[_ngcontent-%COMP%]{grid-template-columns:1fr}.support-card[_ngcontent-%COMP%]{flex-direction:column;text-align:center;padding:1.5rem}.faq-item[_ngcontent-%COMP%]   .faq-question[_ngcontent-%COMP%]{padding:1rem}.faq-item[_ngcontent-%COMP%]   .faq-question[_ngcontent-%COMP%]   .question-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1rem}.faq-item[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]{padding:0 1rem 1rem}.faq-item[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   .answer-image[_ngcontent-%COMP%]{margin:1rem 0 0;border-radius:6px}.faq-item[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   .answer-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{border-radius:6px}}.dark[_nghost-%COMP%]   .help-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .help-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--primary-600) 0%,var(--primary-700) 100%)}.dark[_nghost-%COMP%]   .category-btn[_ngcontent-%COMP%]:not(.active), .dark   [_nghost-%COMP%]   .category-btn[_ngcontent-%COMP%]:not(.active){background:var(--surface-700);border-color:var(--surface-600);color:var(--text-color)}.dark[_nghost-%COMP%]   .category-btn[_ngcontent-%COMP%]:not(.active):hover, .dark   [_nghost-%COMP%]   .category-btn[_ngcontent-%COMP%]:not(.active):hover{background:var(--surface-600)}.dark[_nghost-%COMP%]   .support-card[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .support-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,var(--surface-700) 0%,var(--surface-800) 100%)}.dark[_nghost-%COMP%]   .faq-item[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   .answer-image[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .faq-item[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   .answer-image[_ngcontent-%COMP%]{background:var(--surface-800);border-color:var(--surface-600)}.dark[_nghost-%COMP%]   .faq-item[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   .answer-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .faq-item[_ngcontent-%COMP%]   .answer-content[_ngcontent-%COMP%]   .answer-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover{box-shadow:0 6px 20px #0000004d}\"]\n    });\n  }\n  return HelpComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}