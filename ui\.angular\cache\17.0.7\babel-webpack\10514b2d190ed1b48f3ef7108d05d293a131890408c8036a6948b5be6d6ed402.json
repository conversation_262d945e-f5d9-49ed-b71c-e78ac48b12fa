{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../service/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"../../../service/error.service\";\nimport * as i6 from \"../../../service/enhanced-message.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/checkbox\";\nimport * as i10 from \"primeng/inputtext\";\nimport * as i11 from \"primeng/password\";\nfunction LoginComponent_small_18_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Username is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_small_18_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Username must be at least 3 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_small_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 26);\n    i0.ɵɵtemplate(1, LoginComponent_small_18_span_1_Template, 2, 0, \"span\", 27)(2, LoginComponent_small_18_span_2_Template, 2, 0, \"span\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r0.loginForm.get(\"username\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r0.loginForm.get(\"username\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n  }\n}\nfunction LoginComponent_small_23_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_small_23_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Password must be at least 6 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_small_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 26);\n    i0.ɵɵtemplate(1, LoginComponent_small_23_span_1_Template, 2, 0, \"span\", 27)(2, LoginComponent_small_23_span_2_Template, 2, 0, \"span\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r1.loginForm.get(\"password\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r1.loginForm.get(\"password\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n  }\n}\nfunction LoginComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29);\n    i0.ɵɵelement(2, \"span\", 30);\n    i0.ɵɵelementStart(3, \"span\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.errorMessage);\n  }\n}\nexport let LoginComponent = /*#__PURE__*/(() => {\n  class LoginComponent {\n    constructor(fb, authService, router, messageService, errorService, enhancedMessage) {\n      this.fb = fb;\n      this.authService = authService;\n      this.router = router;\n      this.messageService = messageService;\n      this.errorService = errorService;\n      this.enhancedMessage = enhancedMessage;\n      this.errorMessage = '';\n      this.isLoading = false;\n      this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n      this.initializeForm();\n      this.loadRememberedCredentials();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    initializeForm() {\n      this.loginForm = this.fb.group({\n        username: ['', [Validators.required, Validators.minLength(3), Validators.pattern(/^[a-zA-Z0-9_]+$/) // Only alphanumeric and underscore\n        ]],\n\n        password: ['', [Validators.required, Validators.minLength(6)]],\n        rememberMe: [false]\n      });\n      // Clear error message when form values change\n      this.loginForm.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(() => {\n        if (this.errorMessage) {\n          this.errorMessage = '';\n        }\n      });\n    }\n    login() {\n      if (this.loginForm.invalid) {\n        this.markFormGroupTouched();\n        return;\n      }\n      this.isLoading = true;\n      this.errorMessage = '';\n      const {\n        username,\n        password,\n        rememberMe\n      } = this.loginForm.value;\n      this.authService.login(username, password).pipe(takeUntil(this.destroy$)).subscribe({\n        next: () => {\n          this.isLoading = false;\n          // Handle remember me functionality\n          if (rememberMe) {\n            localStorage.setItem('rememberMe', 'true');\n            localStorage.setItem('username', username);\n          } else {\n            localStorage.removeItem('rememberMe');\n            localStorage.removeItem('username');\n          }\n          this.enhancedMessage.showLoginSuccess();\n          // Small delay for better UX\n          setTimeout(() => {\n            this.router.navigate(['/app/index']);\n          }, 1000);\n        },\n        error: error => {\n          this.isLoading = false;\n          this.handleLoginError(error);\n        }\n      });\n    }\n    handleLoginError(error) {\n      let errorMessage = 'Login failed. Please try again.';\n      if (error.status === 401) {\n        errorMessage = 'Invalid username or password. Please check your credentials and try again.';\n      } else if (error.status === 403) {\n        errorMessage = 'Account is disabled. Please contact support.';\n      } else if (error.status === 429) {\n        errorMessage = 'Too many login attempts. Please try again later.';\n      } else if (error.status === 0) {\n        errorMessage = 'Unable to connect to server. Please check your internet connection.';\n      } else if (error.status >= 500) {\n        errorMessage = 'Server error. Please try again later.';\n      }\n      this.errorMessage = errorMessage;\n      // Also add to global error service for the error modal\n      this.errorService.addError(error);\n      // Show enhanced toast message\n      this.enhancedMessage.showError('Login Failed', errorMessage);\n    }\n    markFormGroupTouched() {\n      Object.keys(this.loginForm.controls).forEach(key => {\n        const control = this.loginForm.get(key);\n        control?.markAsTouched();\n      });\n    }\n    redirectRegister() {\n      this.router.navigate(['/auth/register']);\n    }\n    forgotPassword() {\n      this.enhancedMessage.showInfo('Forgot Password', 'Password reset functionality will be available soon.');\n    }\n    // Auto-fill remembered username\n    loadRememberedCredentials() {\n      const rememberMe = localStorage.getItem('rememberMe');\n      const savedUsername = localStorage.getItem('username');\n      if (rememberMe === 'true' && savedUsername) {\n        this.loginForm.patchValue({\n          username: savedUsername,\n          rememberMe: true\n        });\n      }\n    }\n    static #_ = this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ErrorService), i0.ɵɵdirectiveInject(i6.EnhancedMessageService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 38,\n      vars: 13,\n      consts: [[1, \"auth-container\"], [1, \"auth-background\"], [1, \"bg-element\", \"bg-element-1\"], [1, \"bg-element\", \"bg-element-2\"], [1, \"bg-element\", \"bg-element-3\"], [1, \"auth-content\"], [1, \"auth-card\"], [1, \"text-center\", \"mb-4\"], [\"src\", \"assets/layout/images/logo-dark.png\", \"alt\", \"SolarKapital\", \"height\", \"50\", 1, \"mb-3\"], [1, \"text-900\", \"text-2xl\", \"font-semibold\", \"mb-2\"], [1, \"text-600\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"mb-4\"], [\"for\", \"username\", 1, \"block\", \"text-900\", \"font-medium\", \"mb-2\"], [\"id\", \"username\", \"type\", \"text\", \"formControlName\", \"username\", \"placeholder\", \"Enter your username\", \"pInputText\", \"\", 1, \"w-full\", \"p-3\"], [\"class\", \"p-error\", 4, \"ngIf\"], [\"for\", \"password\", 1, \"block\", \"text-900\", \"font-medium\", \"mb-2\"], [\"id\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"Enter your password\", \"styleClass\", \"w-full\", \"inputStyleClass\", \"w-full p-3\", 3, \"toggleMask\", \"feedback\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"mb-4\"], [1, \"flex\", \"align-items-center\"], [\"id\", \"rememberMe\", \"formControlName\", \"rememberMe\", \"styleClass\", \"mr-2\", 3, \"binary\"], [\"for\", \"rememberMe\", 1, \"text-900\"], [1, \"font-medium\", \"no-underline\", \"cursor-pointer\", 2, \"color\", \"var(--primary-color)\", 3, \"click\"], [\"class\", \"p-message p-message-error mb-4\", 4, \"ngIf\"], [\"type\", \"submit\", \"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Sign In\", 1, \"w-full\", \"p-3\", \"text-xl\", \"mb-4\", 3, \"disabled\", \"loading\"], [1, \"text-center\"], [1, \"p-error\"], [4, \"ngIf\"], [1, \"p-message\", \"p-message-error\", \"mb-4\"], [1, \"p-message-wrapper\"], [1, \"p-message-icon\", \"pi\", \"pi-exclamation-triangle\"], [1, \"p-message-text\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7);\n          i0.ɵɵelement(8, \"img\", 8);\n          i0.ɵɵelementStart(9, \"h2\", 9);\n          i0.ɵɵtext(10, \"Welcome Back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p\", 10);\n          i0.ɵɵtext(12, \"Sign in to your account to continue\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"form\", 11);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_13_listener() {\n            return ctx.login();\n          });\n          i0.ɵɵelementStart(14, \"div\", 12)(15, \"label\", 13);\n          i0.ɵɵtext(16, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"input\", 14);\n          i0.ɵɵtemplate(18, LoginComponent_small_18_Template, 3, 2, \"small\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 12)(20, \"label\", 16);\n          i0.ɵɵtext(21, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"p-password\", 17);\n          i0.ɵɵtemplate(23, LoginComponent_small_23_Template, 3, 2, \"small\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"div\", 18)(25, \"div\", 19);\n          i0.ɵɵelement(26, \"p-checkbox\", 20);\n          i0.ɵɵelementStart(27, \"label\", 21);\n          i0.ɵɵtext(28, \"Remember me\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"a\", 22);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_a_click_29_listener() {\n            return ctx.forgotPassword();\n          });\n          i0.ɵɵtext(30, \" Forgot password? \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(31, LoginComponent_div_31_Template, 5, 1, \"div\", 23);\n          i0.ɵɵelement(32, \"button\", 24);\n          i0.ɵɵelementStart(33, \"div\", 25)(34, \"span\", 10);\n          i0.ɵɵtext(35, \"Don't have an account? \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"a\", 22);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_a_click_36_listener() {\n            return ctx.redirectRegister();\n          });\n          i0.ɵɵtext(37, \" Create one here \");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_6_0;\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"p-invalid\", ((tmp_1_0 = ctx.loginForm.get(\"username\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.loginForm.get(\"username\")) == null ? null : tmp_1_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.loginForm.get(\"username\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.loginForm.get(\"username\")) == null ? null : tmp_2_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"p-invalid\", ((tmp_3_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_3_0.touched));\n          i0.ɵɵproperty(\"toggleMask\", true)(\"feedback\", false);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_6_0.touched));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"binary\", true);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.loginForm.invalid || ctx.isLoading)(\"loading\", ctx.isLoading);\n        }\n      },\n      dependencies: [i7.NgIf, i8.ButtonDirective, i9.Checkbox, i10.InputText, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i11.Password],\n      styles: [\".auth-container[_ngcontent-%COMP%]{min-height:100vh;display:flex;align-items:center;justify-content:center;position:relative;overflow:hidden;background:var(--surface-ground);padding:1rem}.auth-background[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;z-index:1;opacity:.1}.auth-background[_ngcontent-%COMP%]   .bg-element[_ngcontent-%COMP%]{position:absolute;border-radius:50%;background:var(--primary-color);animation:_ngcontent-%COMP%_float 6s ease-in-out infinite}.auth-background[_ngcontent-%COMP%]   .bg-element.bg-element-1[_ngcontent-%COMP%]{width:200px;height:200px;top:10%;left:10%;animation-delay:0s}.auth-background[_ngcontent-%COMP%]   .bg-element.bg-element-2[_ngcontent-%COMP%]{width:150px;height:150px;top:60%;right:10%;animation-delay:2s}.auth-background[_ngcontent-%COMP%]   .bg-element.bg-element-3[_ngcontent-%COMP%]{width:100px;height:100px;bottom:20%;left:60%;animation-delay:4s}@keyframes _ngcontent-%COMP%_float{0%,to{transform:translateY(0)}50%{transform:translateY(-20px)}}.auth-content[_ngcontent-%COMP%]{position:relative;z-index:2;width:100%;max-width:450px}.auth-card[_ngcontent-%COMP%]{background:var(--surface-card);border-radius:16px;padding:2rem;box-shadow:0 4px 12px #0000001a;border:1px solid var(--surface-border)}.p-message-error[_ngcontent-%COMP%]{border-radius:8px;margin-bottom:1rem}.p-message-error[_ngcontent-%COMP%]   .p-message-wrapper[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}@media (max-width: 768px){.auth-content[_ngcontent-%COMP%]{padding:1rem;max-width:100%}.auth-card[_ngcontent-%COMP%]{padding:1.5rem;margin:1rem}}\"]\n    });\n  }\n  return LoginComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}