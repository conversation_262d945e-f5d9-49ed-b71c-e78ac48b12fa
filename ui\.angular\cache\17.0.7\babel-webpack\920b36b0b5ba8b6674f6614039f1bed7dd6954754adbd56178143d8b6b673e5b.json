{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RegisterRoutingModule } from './register-routing.module';\nimport { ButtonModule } from 'primeng/button';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { PasswordModule } from 'primeng/password';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { FontAwesomeModule } from '@fortawesome/angular-fontawesome';\nimport { fas } from '@fortawesome/free-solid-svg-icons';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@fortawesome/angular-fontawesome\";\nexport let RegisterModule = /*#__PURE__*/(() => {\n  class RegisterModule {\n    constructor(library) {\n      library.addIconPacks(fas);\n    }\n    static #_ = this.ɵfac = function RegisterModule_Factory(t) {\n      return new (t || RegisterModule)(i0.ɵɵinject(i1.FaIconLibrary));\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: RegisterModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, RegisterRoutingModule, ButtonModule, CheckboxModule, InputTextModule, FormsModule, ReactiveFormsModule, PasswordModule, FontAwesomeModule]\n    });\n  }\n  return RegisterModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}