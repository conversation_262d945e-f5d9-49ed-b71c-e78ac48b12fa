{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../layout/service/app.layout.service\";\nimport * as i2 from \"../../service/stations.service\";\nimport * as i3 from \"../../service/providers.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"../../service/cache.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/breadcrumb\";\nimport * as i10 from \"primeng/tooltip\";\nimport * as i11 from \"@fortawesome/angular-fontawesome\";\nimport * as i12 from \"primeng/card\";\nimport * as i13 from \"primeng/inputtext\";\nimport * as i14 from \"primeng/dropdown\";\nimport * as i15 from \"primeng/password\";\nimport * as i16 from \"primeng/badge\";\nimport * as i17 from \"primeng/tag\";\nimport * as i18 from \"primeng/message\";\nfunction ProvidersComponent_ng_template_5_p_badge_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-badge\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", ctx_r4.userProviders.length);\n  }\n}\nfunction ProvidersComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"fa-icon\", 6);\n    i0.ɵɵelementStart(2, \"h2\", 7);\n    i0.ɵɵtext(3, \"Your Providers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ProvidersComponent_ng_template_5_p_badge_4_Template, 1, 1, \"p-badge\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.userProviders.length > 0);\n  }\n}\nfunction ProvidersComponent_ng_template_6_div_0_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵelement(1, \"fa-icon\", 17);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"You are not registered to any provider. You can start adding providers now!\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProvidersComponent_ng_template_6_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"fa-icon\", 13);\n    i0.ɵɵelementStart(2, \"p-message\", 14);\n    i0.ɵɵtemplate(3, ProvidersComponent_ng_template_6_div_0_ng_template_3_Template, 4, 0, \"ng-template\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"closable\", false);\n  }\n}\nfunction ProvidersComponent_ng_template_6_div_1_div_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23);\n    i0.ɵɵelement(2, \"p-tag\", 24)(3, \"p-badge\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const provider_r9 = i0.ɵɵnextContext().$implicit;\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r11.getSelectedProviderName(provider_r9.providerId));\n  }\n}\nfunction ProvidersComponent_ng_template_6_div_1_div_1_ng_template_3_div_13_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const provider_r9 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", provider_r9.configuration.Stations[0].StationName, \" \");\n  }\n}\nfunction ProvidersComponent_ng_template_6_div_1_div_1_ng_template_3_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"label\", 28);\n    i0.ɵɵelement(2, \"fa-icon\", 33);\n    i0.ɵɵtext(3, \" Station \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ProvidersComponent_ng_template_6_div_1_div_1_ng_template_3_div_13_div_4_Template, 2, 1, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const provider_r9 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", provider_r9.configuration.Stations.length > 0);\n  }\n}\nfunction ProvidersComponent_ng_template_6_div_1_div_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"label\", 28);\n    i0.ɵɵelement(3, \"fa-icon\", 29);\n    i0.ɵɵtext(4, \" Username \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 30);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 27)(8, \"label\", 28);\n    i0.ɵɵelement(9, \"fa-icon\", 31);\n    i0.ɵɵtext(10, \" Password \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 30);\n    i0.ɵɵtext(12, \"\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, ProvidersComponent_ng_template_6_div_1_div_1_ng_template_3_div_13_Template, 5, 1, \"div\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const provider_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(provider_r9.configuration.Username);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", provider_r9.configuration.Stations);\n  }\n}\nfunction ProvidersComponent_ng_template_6_div_1_div_1_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"p-button\", 36);\n    i0.ɵɵlistener(\"click\", function ProvidersComponent_ng_template_6_div_1_div_1_ng_template_4_Template_p_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const i_r10 = i0.ɵɵnextContext().index;\n      const ctx_r20 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r20.editProvider(i_r10));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"p-button\", 37);\n    i0.ɵɵlistener(\"click\", function ProvidersComponent_ng_template_6_div_1_div_1_ng_template_4_Template_p_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const i_r10 = i0.ɵɵnextContext().index;\n      const ctx_r23 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r23.removeProvider(i_r10));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", true);\n  }\n}\nfunction ProvidersComponent_ng_template_6_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"p-card\", 20);\n    i0.ɵɵtemplate(2, ProvidersComponent_ng_template_6_div_1_div_1_ng_template_2_Template, 4, 1, \"ng-template\", 3)(3, ProvidersComponent_ng_template_6_div_1_div_1_ng_template_3_Template, 14, 2, \"ng-template\", 4)(4, ProvidersComponent_ng_template_6_div_1_div_1_ng_template_4_Template, 3, 2, \"ng-template\", 21);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProvidersComponent_ng_template_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵtemplate(1, ProvidersComponent_ng_template_6_div_1_div_1_Template, 5, 0, \"div\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.userProviders);\n  }\n}\nfunction ProvidersComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ProvidersComponent_ng_template_6_div_0_Template, 4, 1, \"div\", 10)(1, ProvidersComponent_ng_template_6_div_1_Template, 2, 1, \"div\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.userProviders.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.userProviders.length > 0);\n  }\n}\nfunction ProvidersComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"fa-icon\", 38);\n    i0.ɵɵelementStart(2, \"h2\", 7);\n    i0.ɵɵtext(3, \"Add New Providers\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProvidersComponent_ng_template_10_div_35_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 16);\n    i0.ɵɵelement(2, \"fa-icon\", 60);\n    i0.ɵɵelementStart(3, \"span\", 61);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"p-button\", 62);\n    i0.ɵɵlistener(\"click\", function ProvidersComponent_ng_template_10_div_35_ng_template_2_Template_p_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const i_r28 = i0.ɵɵnextContext().index;\n      const ctx_r32 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r32.removeProvider(i_r28));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r28 = i0.ɵɵnextContext().index;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Provider Configuration \", i_r28 + 1, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"text\", true);\n  }\n}\nfunction ProvidersComponent_ng_template_10_div_35_ng_template_3_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 27)(2, \"label\", 64);\n    i0.ɵɵelement(3, \"fa-icon\", 72);\n    i0.ɵɵtext(4, \" Portfolio ID * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 73);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r28 = i0.ɵɵnextContext(2).index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"for\", \"portfolio-\", i_r28, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"portfolio-\", i_r28, \"\");\n  }\n}\nfunction ProvidersComponent_ng_template_10_div_35_ng_template_3_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 27)(2, \"label\", 64);\n    i0.ɵɵelement(3, \"fa-icon\", 75);\n    i0.ɵɵtext(4, \" FTP URL * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 76);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r28 = i0.ɵɵnextContext(2).index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"for\", \"ftp-\", i_r28, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"ftp-\", i_r28, \"\");\n  }\n}\nfunction ProvidersComponent_ng_template_10_div_35_ng_template_3_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"div\", 27)(2, \"label\", 64);\n    i0.ɵɵelement(3, \"fa-icon\", 33);\n    i0.ɵɵtext(4, \" Station * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"p-dropdown\", 78);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r41 = i0.ɵɵnextContext(2);\n    const i_r28 = ctx_r41.index;\n    const provider_r27 = ctx_r41.$implicit;\n    let tmp_2_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"for\", \"station-\", i_r28, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"station-\", i_r28, \"\");\n    i0.ɵɵproperty(\"options\", (tmp_2_0 = provider_r27.get(\"stations\")) == null ? null : tmp_2_0.value)(\"showClear\", true);\n  }\n}\nfunction ProvidersComponent_ng_template_10_div_35_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 63)(2, \"div\", 27)(3, \"label\", 64);\n    i0.ɵɵelement(4, \"fa-icon\", 65);\n    i0.ɵɵtext(5, \" Provider * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"p-dropdown\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 63)(8, \"div\", 27)(9, \"label\", 64);\n    i0.ɵɵelement(10, \"fa-icon\", 29);\n    i0.ɵɵtext(11, \" Username * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 67);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 63)(14, \"div\", 27)(15, \"label\", 64);\n    i0.ɵɵelement(16, \"fa-icon\", 31);\n    i0.ɵɵtext(17, \" Password * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"p-password\", 68);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(19, ProvidersComponent_ng_template_10_div_35_ng_template_3_div_19_Template, 6, 2, \"div\", 69)(20, ProvidersComponent_ng_template_10_div_35_ng_template_3_div_20_Template, 6, 2, \"div\", 70)(21, ProvidersComponent_ng_template_10_div_35_ng_template_3_div_21_Template, 6, 4, \"div\", 71);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r42 = i0.ɵɵnextContext();\n    const i_r28 = ctx_r42.index;\n    const provider_r27 = ctx_r42.$implicit;\n    const ctx_r30 = i0.ɵɵnextContext(2);\n    let tmp_10_0;\n    let tmp_11_0;\n    let tmp_12_0;\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"for\", \"provider-\", i_r28, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"provider-\", i_r28, \"\");\n    i0.ɵɵproperty(\"options\", ctx_r30.availableProviders)(\"showClear\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"for\", \"username-\", i_r28, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"username-\", i_r28, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"for\", \"password-\", i_r28, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"password-\", i_r28, \"\");\n    i0.ɵɵproperty(\"toggleMask\", true)(\"feedback\", false);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = provider_r27.get(\"providerId\")) == null ? null : tmp_10_0.value) == 4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_11_0 = provider_r27.get(\"providerId\")) == null ? null : tmp_11_0.value) == 6);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = provider_r27.get(\"stations\")) == null ? null : tmp_12_0.value.length) > 0);\n  }\n}\nfunction ProvidersComponent_ng_template_10_div_35_ng_template_4_p_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 80);\n    i0.ɵɵlistener(\"click\", function ProvidersComponent_ng_template_10_div_35_ng_template_4_p_button_1_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r46);\n      const i_r28 = i0.ɵɵnextContext(2).index;\n      const ctx_r44 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r44.getStations(i_r28));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProvidersComponent_ng_template_10_div_35_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtemplate(1, ProvidersComponent_ng_template_10_div_35_ng_template_4_p_button_1_Template, 1, 0, \"p-button\", 79);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const provider_r27 = i0.ɵɵnextContext().$implicit;\n    const ctx_r31 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_0_0 = provider_r27.get(\"providerId\")) == null ? null : tmp_0_0.value) && ctx_r31.userProvidersForm.valid);\n  }\n}\nfunction ProvidersComponent_ng_template_10_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"p-card\");\n    i0.ɵɵtemplate(2, ProvidersComponent_ng_template_10_div_35_ng_template_2_Template, 6, 2, \"ng-template\", 3)(3, ProvidersComponent_ng_template_10_div_35_ng_template_3_Template, 22, 13, \"ng-template\", 4)(4, ProvidersComponent_ng_template_10_div_35_ng_template_4_Template, 2, 1, \"ng-template\", 21);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r28 = ctx.index;\n    i0.ɵɵproperty(\"formGroupName\", i_r28);\n  }\n}\nfunction ProvidersComponent_ng_template_10_p_button_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-button\", 81);\n  }\n}\nfunction ProvidersComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r49 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40);\n    i0.ɵɵelement(2, \"fa-icon\", 41);\n    i0.ɵɵelementStart(3, \"div\", 42)(4, \"h4\", 43);\n    i0.ɵɵtext(5, \"Form Instructions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"ul\", 44)(7, \"li\", 45);\n    i0.ɵɵelement(8, \"fa-icon\", 46);\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10, \"Fill in all required fields marked with an asterisk (*).\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"li\", 45);\n    i0.ɵɵelement(12, \"fa-icon\", 47);\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \" If you select Provider \");\n    i0.ɵɵelementStart(15, \"strong\");\n    i0.ɵɵtext(16, \"Aurora\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17, \", the Portfolio ID field becomes required. You can find it in the Aurora Portal. \");\n    i0.ɵɵelement(18, \"fa-icon\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"li\", 45);\n    i0.ɵɵelement(20, \"fa-icon\", 49);\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22, \" If you select Provider \");\n    i0.ɵɵelementStart(23, \"strong\");\n    i0.ɵɵtext(24, \"SMA\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(25, \", the FTP Url becomes required. It should be like \");\n    i0.ɵɵelementStart(26, \"em\");\n    i0.ɵɵtext(27, \"ftp://ftp.server.com/\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(28, \". Please use the full path where the stations folders are located. \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"li\", 50);\n    i0.ɵɵelement(30, \"fa-icon\", 51);\n    i0.ɵɵelementStart(31, \"span\");\n    i0.ɵɵtext(32, \"Make sure to select one station from the available list.\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(33, \"form\", 52);\n    i0.ɵɵlistener(\"ngSubmit\", function ProvidersComponent_ng_template_10_Template_form_ngSubmit_33_listener() {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r48 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r48.onSubmit());\n    });\n    i0.ɵɵelementStart(34, \"div\", 53);\n    i0.ɵɵtemplate(35, ProvidersComponent_ng_template_10_div_35_Template, 5, 1, \"div\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 55)(37, \"p-button\", 56);\n    i0.ɵɵlistener(\"click\", function ProvidersComponent_ng_template_10_Template_p_button_click_37_listener() {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r50 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r50.addProvider());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(38, ProvidersComponent_ng_template_10_p_button_38_Template, 1, 0, \"p-button\", 57);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(33);\n    i0.ɵɵproperty(\"formGroup\", ctx_r3.userProvidersForm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.providers.controls);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.userProvidersForm.valid);\n  }\n}\nconst _c0 = () => ({\n  label: \"Providers\"\n});\nconst _c1 = a0 => [a0];\nconst _c2 = () => ({\n  icon: \"pi pi-home\"\n});\nexport let ProvidersComponent = /*#__PURE__*/(() => {\n  class ProvidersComponent {\n    constructor(layoutService, stationsService, providersService, messageService, fb, cacheService) {\n      this.layoutService = layoutService;\n      this.stationsService = stationsService;\n      this.providersService = providersService;\n      this.messageService = messageService;\n      this.fb = fb;\n      this.cacheService = cacheService;\n      this.availableProviders = [];\n      this.userProviders = [];\n      this.stations = [];\n      this.tooltipVisible = false;\n      this.userProvidersForm = this.fb.group({\n        providers: this.fb.array([])\n      });\n    }\n    ngOnInit() {\n      this.providersService.getProviders().then(data => {\n        this.availableProviders = data;\n      });\n      this.getUserProviders();\n      this.addProvider(); // Ξεκινάμε με μία προεπιλεγμένη σειρά\n    }\n\n    addProvider() {\n      const providerGroup = this.fb.group({\n        providerId: ['', Validators.required],\n        username: ['', Validators.required],\n        password: ['', Validators.required],\n        portfolioId: [''],\n        ftpUrl: [''],\n        stations: [[]],\n        selectedStation: ['']\n      });\n      this.providers.push(providerGroup);\n      // Παρακολούθηση του providerId\n      providerGroup.get('providerId')?.valueChanges.subscribe(value => {\n        const portfolioIdControl = providerGroup.get('portfolioId');\n        if (value === '4') {\n          portfolioIdControl?.setValidators(Validators.required);\n        } else {\n          portfolioIdControl?.clearValidators();\n        }\n        portfolioIdControl?.updateValueAndValidity();\n      });\n    }\n    get providers() {\n      return this.userProvidersForm.get('providers');\n    }\n    getSelectedProviderName(id) {\n      return this.availableProviders.find(p => p.id == id).name;\n    }\n    removeProvider(index) {\n      this.providers.removeAt(index);\n    }\n    getStations(index) {\n      const providerId = this.providers.at(index).get('providerId')?.value;\n      if (!providerId) return;\n      let request = {\n        providerId: this.providers.at(index).get('providerId')?.value,\n        username: this.providers.at(index).get('username')?.value,\n        password: this.providers.at(index).get('password')?.value,\n        portfolioId: this.providers.at(index).get('portfolioId')?.value,\n        ftpUrl: this.providers.at(index).get('ftpUrl')?.value\n      };\n      console.log('Form Data:', request);\n      this.stationsService.getStations(request).then(data => {\n        this.providers.at(index).patchValue({\n          stations: data\n        });\n      });\n      const providerGroup = this.providers.at(index);\n      // Set validators based on provider type\n      if (providerId === 4) {\n        // Aurora provider - Portfolio ID is required\n        providerGroup.get('portfolioId')?.setValidators(Validators.required);\n        providerGroup.get('ftpUrl')?.clearValidators();\n        providerGroup.get('ftpUrl')?.setValue('');\n      } else if (providerId === 6) {\n        // SMA provider - FTP URL is required\n        providerGroup.get('ftpUrl')?.setValidators(Validators.required);\n        providerGroup.get('portfolioId')?.clearValidators();\n        providerGroup.get('portfolioId')?.setValue('');\n      } else {\n        // Other providers - clear both\n        providerGroup.get('portfolioId')?.clearValidators();\n        providerGroup.get('portfolioId')?.setValue('');\n        providerGroup.get('ftpUrl')?.clearValidators();\n        providerGroup.get('ftpUrl')?.setValue('');\n      }\n      providerGroup.get('portfolioId')?.updateValueAndValidity();\n      providerGroup.get('ftpUrl')?.updateValueAndValidity();\n    }\n    onSubmit() {\n      if (this.userProvidersForm.invalid) {\n        this.userProvidersForm.markAllAsTouched();\n        return;\n      }\n      if (this.userProvidersForm.valid) {\n        console.log('Form Data (raw):', this.userProvidersForm.value.providers);\n        // Convert providerId from number to string as expected by API\n        const transformedProviders = this.userProvidersForm.value.providers.map(formProvider => ({\n          ...formProvider,\n          providerId: formProvider.providerId.toString() // Convert number to string\n        }));\n\n        console.log('Form Data (transformed):', transformedProviders);\n        let request = {\n          providers: transformedProviders\n        };\n        this.providersService.saveUserProviders(request).then(data => {\n          console.log('Save response:', data);\n          this.getUserProviders();\n        }).catch(error => {\n          console.error('Error saving providers:', error);\n        });\n      }\n    }\n    needsPortfolio(index) {\n      return this.providers && this.providers.length > index && this.providers.at(index).get('providerId')?.value === 4;\n    }\n    getUserProviders() {\n      this.providersService.getUserProviders().then(data => {\n        this.userProviders = data;\n        console.log(this.userProviders);\n        this.userProviders.forEach(up => {\n          up.configuration = JSON.parse(up.configuration);\n        });\n        if (data.length > 0) {\n          this.stationsService.getUserStations().then(data => {\n            this.stations = data;\n            this.cacheService.setStations(this.stations);\n          });\n        }\n      });\n    }\n    static #_ = this.ɵfac = function ProvidersComponent_Factory(t) {\n      return new (t || ProvidersComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.StationsService), i0.ɵɵdirectiveInject(i3.ProvidersService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.FormBuilder), i0.ɵɵdirectiveInject(i6.CacheService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProvidersComponent,\n      selectors: [[\"app-providers\"]],\n      decls: 11,\n      vars: 6,\n      consts: [[1, \"grid\"], [1, \"col-12\"], [3, \"model\", \"home\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"content\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"p-3\"], [\"icon\", \"server\", 1, \"text-primary\", \"text-xl\"], [1, \"text-2xl\", \"font-semibold\", \"m-0\"], [\"severity\", \"info\", 3, \"value\", 4, \"ngIf\"], [\"severity\", \"info\", 3, \"value\"], [\"class\", \"text-center py-6\", 4, \"ngIf\"], [\"class\", \"grid\", 4, \"ngIf\"], [1, \"text-center\", \"py-6\"], [\"icon\", \"inbox\", 1, \"text-6xl\", \"text-300\", \"mb-3\"], [\"severity\", \"info\", 1, \"w-full\", 3, \"closable\"], [\"pTemplate\", \"\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"icon\", \"info-circle\"], [\"class\", \"col-12 lg:col-6 xl:col-4\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"lg:col-6\", \"xl:col-4\"], [1, \"h-full\"], [\"pTemplate\", \"footer\"], [1, \"bg-primary-50\", \"p-3\", \"border-round-top\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\"], [\"severity\", \"success\", \"icon\", \"pi pi-server\", 3, \"value\"], [\"value\", \"Active\", \"severity\", \"success\"], [1, \"space-y-3\"], [1, \"field\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-600\", \"mb-1\"], [\"icon\", \"user\", 1, \"mr-1\"], [1, \"text-900\", \"font-medium\"], [\"icon\", \"lock\", 1, \"mr-1\"], [\"class\", \"field\", 4, \"ngIf\"], [\"icon\", \"solar-panel\", 1, \"mr-1\"], [\"class\", \"text-900 font-medium\", 4, \"ngIf\"], [1, \"flex\", \"gap-2\"], [\"label\", \"Edit\", \"icon\", \"pi pi-pencil\", \"severity\", \"warning\", \"size\", \"small\", \"pTooltip\", \"Edit functionality coming soon\", 3, \"disabled\", \"click\"], [\"label\", \"Remove\", \"icon\", \"pi pi-trash\", \"severity\", \"danger\", \"size\", \"small\", \"pTooltip\", \"Remove functionality coming soon\", 3, \"disabled\", \"click\"], [\"icon\", \"plus-circle\", 1, \"text-primary\", \"text-xl\"], [1, \"bg-blue-50\", \"border-l-4\", \"border-blue-400\", \"p-4\", \"border-round\", \"mb-4\"], [1, \"flex\", \"align-items-start\", \"gap-3\"], [\"icon\", \"info-circle\", 1, \"text-blue-600\", \"text-xl\", \"mt-1\"], [1, \"flex-1\"], [1, \"text-blue-800\", \"font-semibold\", \"mb-3\", \"mt-0\"], [1, \"list-none\", \"p-0\", \"m-0\", \"text-blue-700\"], [1, \"flex\", \"align-items-start\", \"gap-2\", \"mb-2\"], [\"icon\", \"asterisk\", 1, \"text-red-500\", \"text-xs\", \"mt-1\", \"flex-shrink-0\"], [\"icon\", \"lightbulb\", 1, \"text-yellow-600\", \"text-sm\", \"mt-1\", \"flex-shrink-0\"], [\"icon\", \"question-circle\", \"pTooltip\", \"Click to see Aurora Portal example\", \"tooltipPosition\", \"top\", 1, \"cursor-pointer\", \"text-blue-600\", \"ml-1\"], [\"icon\", \"globe\", 1, \"text-blue-600\", \"text-sm\", \"mt-1\", \"flex-shrink-0\"], [1, \"flex\", \"align-items-start\", \"gap-2\"], [\"icon\", \"check-circle\", 1, \"text-green-600\", \"text-sm\", \"mt-1\", \"flex-shrink-0\"], [3, \"formGroup\", \"ngSubmit\"], [\"formArrayName\", \"providers\"], [\"class\", \"mb-4\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"gap-3\", \"mt-4\"], [\"label\", \"Add Provider\", \"icon\", \"pi pi-plus\", \"severity\", \"secondary\", 3, \"click\"], [\"label\", \"Save All Providers\", \"icon\", \"pi pi-save\", \"severity\", \"success\", \"type\", \"submit\", 4, \"ngIf\"], [1, \"mb-4\", 3, \"formGroupName\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"p-3\", \"bg-primary-50\"], [\"icon\", \"cog\", 1, \"text-primary\"], [1, \"font-semibold\"], [\"icon\", \"pi pi-times\", \"severity\", \"danger\", \"size\", \"small\", \"pTooltip\", \"Remove this provider\", 3, \"text\", \"click\"], [1, \"col-12\", \"md:col-6\", \"lg:col-3\", \"xl:col-2\"], [1, \"block\", \"font-medium\", \"mb-2\", \"text-sm\", 3, \"for\"], [\"icon\", \"server\", 1, \"mr-1\"], [\"formControlName\", \"providerId\", \"optionLabel\", \"name\", \"optionValue\", \"id\", \"placeholder\", \"Select provider\", 1, \"w-full\", 3, \"id\", \"options\", \"showClear\"], [\"pInputText\", \"\", \"formControlName\", \"username\", \"placeholder\", \"Username\", 1, \"w-full\", 3, \"id\"], [\"formControlName\", \"password\", \"placeholder\", \"Password\", 1, \"w-full\", 3, \"id\", \"toggleMask\", \"feedback\"], [\"class\", \"col-12 md:col-6 lg:col-3 xl:col-2\", 4, \"ngIf\"], [\"class\", \"col-12 md:col-6 lg:col-3 xl:col-3\", 4, \"ngIf\"], [\"class\", \"col-12 md:col-6 lg:col-3 xl:col-4\", 4, \"ngIf\"], [\"icon\", \"briefcase\", 1, \"mr-1\"], [\"pInputText\", \"\", \"formControlName\", \"portfolioId\", \"placeholder\", \"Portfolio ID\", 1, \"w-full\", 3, \"id\"], [1, \"col-12\", \"md:col-6\", \"lg:col-3\", \"xl:col-3\"], [\"icon\", \"globe\", 1, \"mr-1\"], [\"pInputText\", \"\", \"formControlName\", \"ftpUrl\", \"placeholder\", \"ftp://ftp.server.com/\", 1, \"w-full\", 3, \"id\"], [1, \"col-12\", \"md:col-6\", \"lg:col-3\", \"xl:col-4\"], [\"formControlName\", \"selectedStation\", \"optionLabel\", \"name\", \"optionValue\", \"id\", \"placeholder\", \"Select station\", 1, \"w-full\", 3, \"id\", \"options\", \"showClear\"], [\"label\", \"Get Stations\", \"icon\", \"pi pi-download\", \"severity\", \"info\", \"size\", \"small\", 3, \"click\", 4, \"ngIf\"], [\"label\", \"Get Stations\", \"icon\", \"pi pi-download\", \"severity\", \"info\", \"size\", \"small\", 3, \"click\"], [\"label\", \"Save All Providers\", \"icon\", \"pi pi-save\", \"severity\", \"success\", \"type\", \"submit\"]],\n      template: function ProvidersComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"p-breadcrumb\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 1)(4, \"p-card\");\n          i0.ɵɵtemplate(5, ProvidersComponent_ng_template_5_Template, 5, 1, \"ng-template\", 3)(6, ProvidersComponent_ng_template_6_Template, 2, 2, \"ng-template\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 1)(8, \"p-card\");\n          i0.ɵɵtemplate(9, ProvidersComponent_ng_template_9_Template, 4, 0, \"ng-template\", 3)(10, ProvidersComponent_ng_template_10_Template, 39, 3, \"ng-template\", 4);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"model\", i0.ɵɵpureFunction1(3, _c1, i0.ɵɵpureFunction0(2, _c0)))(\"home\", i0.ɵɵpureFunction0(5, _c2));\n        }\n      },\n      dependencies: [i7.NgForOf, i7.NgIf, i4.PrimeTemplate, i8.Button, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.FormGroupDirective, i5.FormControlName, i5.FormGroupName, i5.FormArrayName, i9.Breadcrumb, i10.Tooltip, i11.FaIconComponent, i12.Card, i13.InputText, i14.Dropdown, i15.Password, i16.Badge, i17.Tag, i18.UIMessage],\n      encapsulation: 2\n    });\n  }\n  return ProvidersComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}