{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { HelpRoutingModule } from './help-routing.module';\nimport { SharedModule } from '../../../shared/shared.module';\n// PrimeNG Modules\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i0 from \"@angular/core\";\nexport let HelpModule = /*#__PURE__*/(() => {\n  class HelpModule {\n    static #_ = this.ɵfac = function HelpModule_Factory(t) {\n      return new (t || HelpModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: HelpModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, HelpRoutingModule, SharedModule, ButtonModule, InputTextModule, BreadcrumbModule, RippleModule]\n    });\n  }\n  return HelpModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}