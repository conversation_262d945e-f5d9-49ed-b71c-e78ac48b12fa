{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./service/app.layout.service\";\nimport * as i2 from \"primeng/tooltip\";\nexport let AppFooterComponent = /*#__PURE__*/(() => {\n  class AppFooterComponent {\n    constructor(layoutService) {\n      this.layoutService = layoutService;\n      this.currentYear = new Date().getFullYear();\n      this.appVersion = '1.0.0';\n    }\n    static #_ = this.ɵfac = function AppFooterComponent_Factory(t) {\n      return new (t || AppFooterComponent)(i0.ɵɵdirectiveInject(i1.LayoutService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppFooterComponent,\n      selectors: [[\"app-footer\"]],\n      decls: 39,\n      vars: 3,\n      consts: [[1, \"layout-footer\"], [1, \"footer-content\"], [1, \"footer-left\"], [1, \"footer-logo\"], [\"alt\", \"SolarKapital\", \"height\", \"24\", 1, \"footer-logo-img\", 3, \"src\"], [1, \"footer-brand\"], [1, \"footer-description\"], [1, \"text-sm\", \"text-600\"], [1, \"footer-center\"], [1, \"footer-links\"], [\"href\", \"#\", 1, \"footer-link\"], [1, \"footer-separator\"], [1, \"footer-right\"], [1, \"footer-info\"], [1, \"text-xs\", \"text-500\"], [1, \"footer-social\"], [\"pTooltip\", \"GitHub\", \"tooltipPosition\", \"top\", 1, \"p-link\", \"footer-social-btn\"], [1, \"pi\", \"pi-github\"], [\"pTooltip\", \"LinkedIn\", \"tooltipPosition\", \"top\", 1, \"p-link\", \"footer-social-btn\"], [1, \"pi\", \"pi-linkedin\"], [\"pTooltip\", \"Twitter\", \"tooltipPosition\", \"top\", 1, \"p-link\", \"footer-social-btn\"], [1, \"pi\", \"pi-twitter\"]],\n      template: function AppFooterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"img\", 4);\n          i0.ɵɵelementStart(5, \"span\", 5);\n          i0.ɵɵtext(6, \"SolarKapital\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"span\", 7);\n          i0.ɵɵtext(9, \"Powering the future with solar energy management\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"a\", 10);\n          i0.ɵɵtext(13, \"Privacy Policy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"span\", 11);\n          i0.ɵɵtext(15, \"\\u2022\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"a\", 10);\n          i0.ɵɵtext(17, \"Terms of Service\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"span\", 11);\n          i0.ɵɵtext(19, \"\\u2022\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"a\", 10);\n          i0.ɵɵtext(21, \"Support\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"span\", 11);\n          i0.ɵɵtext(23, \"\\u2022\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"a\", 10);\n          i0.ɵɵtext(25, \"Documentation\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(26, \"div\", 12)(27, \"div\", 13)(28, \"span\", 7);\n          i0.ɵɵtext(29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"span\", 14);\n          i0.ɵɵtext(31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 15)(33, \"button\", 16);\n          i0.ɵɵelement(34, \"i\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"button\", 18);\n          i0.ɵɵelement(36, \"i\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"button\", 20);\n          i0.ɵɵelement(38, \"i\", 21);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵpropertyInterpolate1(\"src\", \"assets/layout/images/\", ctx.layoutService.config().colorScheme === \"light\" ? \"logo-dark\" : \"logo-white\", \".png\", i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(25);\n          i0.ɵɵtextInterpolate1(\"\\u00A9 \", ctx.currentYear, \" SolarKapital\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"Version \", ctx.appVersion, \"\");\n        }\n      },\n      dependencies: [i2.Tooltip],\n      encapsulation: 2\n    });\n  }\n  return AppFooterComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}