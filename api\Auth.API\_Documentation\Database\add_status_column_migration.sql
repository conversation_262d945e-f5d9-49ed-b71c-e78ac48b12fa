-- Migration script to add Status column to Users table
-- Run this script on your existing database

USE [solar_db]; -- Replace with your actual database name
GO

-- Check if Status column already exists before adding it
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND name = 'Status')
BEGIN
    ALTER TABLE [dbo].[Users] ADD [Status] NVARCHAR(20) NOT NULL DEFAULT 'Active';
    PRINT 'Status column added successfully with default value Active';
    
    -- Create a check constraint to ensure only valid status values
    ALTER TABLE [dbo].[Users] 
    ADD CONSTRAINT CK_Users_Status 
    CHECK ([Status] IN ('Active', 'Inactive', 'Suspended', 'Pending'));
    
    PRINT 'Status check constraint added successfully';
END
ELSE
BEGIN
    PRINT 'Status column already exists';
END

-- Optional: Update existing records to have Active status if they are NULL
-- UPDATE [dbo].[Users] SET [Status] = 'Active' WHERE [Status] IS NULL;

PRINT 'Status column migration completed successfully';
GO
