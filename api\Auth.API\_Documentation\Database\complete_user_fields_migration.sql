-- Complete migration script to add all missing user fields to Users table
-- This script adds <PERSON><PERSON><PERSON>, LastName, and Status columns if they don't exist
-- Run this script on your existing database

USE [solar_db]; -- Replace with your actual database name
GO

PRINT 'Starting complete user fields migration...';

-- Check if FirstName column exists before adding it
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND name = 'FirstName')
BEGIN
    ALTER TABLE [dbo].[Users] ADD [FirstName] NVARCHAR(50) NULL;
    PRINT 'FirstName column added successfully';
END
ELSE
BEGIN
    PRINT 'FirstName column already exists';
END

-- Check if LastName column exists before adding it
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND name = 'LastName')
BEGIN
    ALTER TABLE [dbo].[Users] ADD [LastName] NVARCHAR(50) NULL;
    PRINT 'LastName column added successfully';
END
ELSE
BEGIN
    PRINT 'LastName column already exists';
END

-- Check if Status column exists before adding it
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND name = 'Status')
BEGIN
    ALTER TABLE [dbo].[Users] ADD [Status] NVARCHAR(20) NOT NULL DEFAULT 'Active';
    PRINT 'Status column added successfully with default value Active';
    
    -- Create a check constraint to ensure only valid status values
    ALTER TABLE [dbo].[Users] 
    ADD CONSTRAINT CK_Users_Status 
    CHECK ([Status] IN ('Active', 'Inactive', 'Suspended', 'Pending'));
    
    PRINT 'Status check constraint added successfully';
END
ELSE
BEGIN
    PRINT 'Status column already exists';
END

-- Optional: Update existing records with default values if needed
-- Uncomment the lines below if you want to set default values for existing records

-- UPDATE [dbo].[Users] SET [FirstName] = 'Unknown' WHERE [FirstName] IS NULL;
-- UPDATE [dbo].[Users] SET [LastName] = 'User' WHERE [LastName] IS NULL;
-- UPDATE [dbo].[Users] SET [Status] = 'Active' WHERE [Status] IS NULL OR [Status] = '';

PRINT 'Complete user fields migration completed successfully';
GO
