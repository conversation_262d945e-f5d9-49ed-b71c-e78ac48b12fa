{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ProfileComponent } from './profile.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let ProfileRoutingModule = /*#__PURE__*/(() => {\n  class ProfileRoutingModule {\n    static #_ = this.ɵfac = function ProfileRoutingModule_Factory(t) {\n      return new (t || ProfileRoutingModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ProfileRoutingModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild([{\n        path: '',\n        component: ProfileComponent\n      }]), RouterModule]\n    });\n  }\n  return ProfileRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}