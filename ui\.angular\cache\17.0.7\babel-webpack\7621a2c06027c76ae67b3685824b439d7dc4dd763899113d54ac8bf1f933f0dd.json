{"ast": null, "code": "import { debounceTime } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/layout/service/app.layout.service\";\nimport * as i2 from \"../../service/stations.service\";\nimport * as i3 from \"../../service/providers.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"../../service/cache.service\";\nimport * as i8 from \"../../service/date-utils.service\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"primeng/tooltip\";\nimport * as i11 from \"primeng/button\";\nimport * as i12 from \"primeng/inputtext\";\nimport * as i13 from \"primeng/dropdown\";\nimport * as i14 from \"@coreui/angular-chartjs\";\nimport * as i15 from \"primeng/badge\";\nimport * as i16 from \"@fortawesome/angular-fontawesome\";\nimport * as i17 from \"primeng/card\";\nimport * as i18 from \"primeng/tag\";\nimport * as i19 from \"primeng/ripple\";\nimport * as i20 from \"primeng/paginator\";\nfunction IndexComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"div\", 8);\n    i0.ɵɵelement(3, \"fa-icon\", 9);\n    i0.ɵɵelementStart(4, \"span\", 10);\n    i0.ɵɵtext(5, \"Active Energy Systems\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 11);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"p-tag\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 13);\n    i0.ɵɵelement(10, \"fa-icon\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r0.stations.length);\n  }\n}\nfunction IndexComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"div\", 8);\n    i0.ɵɵelement(3, \"fa-icon\", 15);\n    i0.ɵɵelementStart(4, \"span\", 10);\n    i0.ɵɵtext(5, \"Combined Power Performance\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 11);\n    i0.ɵɵtext(7, \"33% (4.124kW)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"p-tag\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 17);\n    i0.ɵɵelement(10, \"fa-icon\", 18);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction IndexComponent_ng_template_9_p_badge_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-badge\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", ctx_r4.totalItems);\n  }\n}\nfunction IndexComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20);\n    i0.ɵɵelement(2, \"fa-icon\", 21);\n    i0.ɵɵelementStart(3, \"h2\", 22);\n    i0.ɵɵtext(4, \"Energy Stations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, IndexComponent_ng_template_9_p_badge_5_Template, 1, 1, \"p-badge\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 24)(7, \"p-button\", 25);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_9_Template_p_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.clearFilters());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p-button\", 26);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_9_Template_p_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.refreshData());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.totalItems > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"loading\", ctx_r2.isRefreshing);\n  }\n}\nfunction IndexComponent_ng_template_10_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 37);\n    i0.ɵɵelement(2, \"fa-icon\", 38);\n    i0.ɵɵelementStart(3, \"p\", 39);\n    i0.ɵɵtext(4, \"No stations found matching your criteria.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p-button\", 40);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_10_div_8_Template_p_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.clearFilters());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction IndexComponent_ng_template_10_div_9_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 43)(2, \"div\", 20);\n    i0.ɵɵelement(3, \"p-tag\", 44)(4, \"p-tag\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 46);\n    i0.ɵɵelement(6, \"fa-icon\", 47);\n    i0.ɵɵelementStart(7, \"span\", 48);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const station_r13 = i0.ɵɵnextContext().$implicit;\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", station_r13.provider);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", (station_r13.status == null ? null : station_r13.status.toLowerCase()) || \"unknown\")(\"severity\", (station_r13.status == null ? null : station_r13.status.toLowerCase()) === \"online\" ? \"success\" : \"warning\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r14.getStationLastUpdate(station_r13.id));\n  }\n}\nfunction IndexComponent_ng_template_10_div_9_ng_template_3_c_chart_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"c-chart\", 74);\n  }\n  if (rf & 2) {\n    const station_r13 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"data\", ctx_r17.getStationsRealTimeData(station_r13))(\"options\", ctx_r17.lineOptions);\n  }\n}\nfunction IndexComponent_ng_template_10_div_9_ng_template_3_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵelement(1, \"fa-icon\", 76);\n    i0.ɵɵelementStart(2, \"p\", 77);\n    i0.ɵɵtext(3, \"No chart data\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction IndexComponent_ng_template_10_div_9_ng_template_3_p_badge_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-badge\", 78);\n  }\n  if (rf & 2) {\n    const station_r13 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", \"Inv: \" + ctx_r19.getStationsInverters(station_r13.id));\n  }\n}\nfunction IndexComponent_ng_template_10_div_9_ng_template_3_p_badge_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-badge\", 79);\n  }\n  if (rf & 2) {\n    const station_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"value\", \"MMPT: \" + station_r13.mmpt);\n  }\n}\nfunction IndexComponent_ng_template_10_div_9_ng_template_3_p_badge_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-badge\", 80);\n  }\n  if (rf & 2) {\n    const station_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"value\", \"Str: \" + station_r13.string);\n  }\n}\nfunction IndexComponent_ng_template_10_div_9_ng_template_3_p_badge_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-badge\", 81);\n  }\n  if (rf & 2) {\n    const station_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"value\", \"PVN: \" + station_r13.pvn);\n  }\n}\nfunction IndexComponent_ng_template_10_div_9_ng_template_3_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵelement(1, \"fa-icon\", 82);\n    i0.ɵɵelementStart(2, \"p\", 77);\n    i0.ɵɵtext(3, \"No equipment data\");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c0 = a1 => [\"/app/station\", a1];\nfunction IndexComponent_ng_template_10_div_9_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50)(2, \"div\", 51)(3, \"h3\", 52)(4, \"a\", 53);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 54);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 55)(9, \"div\", 56)(10, \"div\", 57);\n    i0.ɵɵelement(11, \"fa-icon\", 58);\n    i0.ɵɵelementStart(12, \"span\", 59);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 60);\n    i0.ɵɵelement(15, \"fa-icon\", 61);\n    i0.ɵɵelementStart(16, \"span\", 62);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(18, \"div\", 55)(19, \"div\", 56);\n    i0.ɵɵtemplate(20, IndexComponent_ng_template_10_div_9_ng_template_3_c_chart_20_Template, 1, 2, \"c-chart\", 63)(21, IndexComponent_ng_template_10_div_9_ng_template_3_div_21_Template, 4, 0, \"div\", 64);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 50)(23, \"div\", 65);\n    i0.ɵɵtemplate(24, IndexComponent_ng_template_10_div_9_ng_template_3_p_badge_24_Template, 1, 1, \"p-badge\", 66)(25, IndexComponent_ng_template_10_div_9_ng_template_3_p_badge_25_Template, 1, 1, \"p-badge\", 67)(26, IndexComponent_ng_template_10_div_9_ng_template_3_p_badge_26_Template, 1, 1, \"p-badge\", 68)(27, IndexComponent_ng_template_10_div_9_ng_template_3_p_badge_27_Template, 1, 1, \"p-badge\", 69)(28, IndexComponent_ng_template_10_div_9_ng_template_3_div_28_Template, 4, 0, \"div\", 70);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 55)(30, \"div\", 56)(31, \"div\", 71);\n    i0.ɵɵelement(32, \"i\", 72);\n    i0.ɵɵelementStart(33, \"span\", 73);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const station_r13 = i0.ɵɵnextContext().$implicit;\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(13, _c0, station_r13.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", station_r13.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(station_r13.location || \"Location not specified\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r15.getStationsSumData(station_r13.id), \"kW\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", station_r13.irradiance, \"kWh/m\\u00B2\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.getStationsRealTimeData(station_r13));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r15.getStationsRealTimeData(station_r13));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.getStationsInverters(station_r13.id) > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", station_r13.mmpt && station_r13.mmpt > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", station_r13.string && station_r13.string > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", station_r13.pvn && station_r13.pvn > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r15.hasEquipmentData(station_r13));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", station_r13.temperature, \"\\u00B0C\");\n  }\n}\nfunction IndexComponent_ng_template_10_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"p-card\", 41);\n    i0.ɵɵtemplate(2, IndexComponent_ng_template_10_div_9_ng_template_2_Template, 9, 4, \"ng-template\", 5)(3, IndexComponent_ng_template_10_div_9_ng_template_3_Template, 35, 15, \"ng-template\", 3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const station_r13 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", \"station-status-\" + (station_r13.status == null ? null : station_r13.status.toLowerCase()));\n  }\n}\nconst _c1 = () => [5, 10, 20, 50];\nfunction IndexComponent_ng_template_10_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"p-paginator\", 84);\n    i0.ɵɵlistener(\"onPageChange\", function IndexComponent_ng_template_10_div_10_Template_p_paginator_onPageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r30.onPageChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"rows\", ctx_r10.itemsPerPage)(\"totalRecords\", ctx_r10.totalItems)(\"first\", ctx_r10.currentPage * ctx_r10.itemsPerPage)(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(5, _c1))(\"showCurrentPageReport\", true);\n  }\n}\nfunction IndexComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 24)(2, \"p-dropdown\", 29);\n    i0.ɵɵlistener(\"ngModelChange\", function IndexComponent_ng_template_10_Template_p_dropdown_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.selectedProvider = $event);\n    })(\"onChange\", function IndexComponent_ng_template_10_Template_p_dropdown_onChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.onProviderChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-dropdown\", 30);\n    i0.ɵɵlistener(\"ngModelChange\", function IndexComponent_ng_template_10_Template_p_dropdown_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.selectedStatus = $event);\n    })(\"onChange\", function IndexComponent_ng_template_10_Template_p_dropdown_onChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.onStatusChange($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"span\", 31);\n    i0.ɵɵelement(5, \"i\", 32);\n    i0.ɵɵelementStart(6, \"input\", 33);\n    i0.ɵɵlistener(\"ngModelChange\", function IndexComponent_ng_template_10_Template_input_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.searchTerm = $event);\n    })(\"input\", function IndexComponent_ng_template_10_Template_input_input_6_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.onSearchChange($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 0);\n    i0.ɵɵtemplate(8, IndexComponent_ng_template_10_div_8_Template, 6, 0, \"div\", 34)(9, IndexComponent_ng_template_10_div_9_Template, 4, 1, \"div\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, IndexComponent_ng_template_10_div_10_Template, 2, 6, \"div\", 36);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"options\", ctx_r3.sortOptionsCountry)(\"ngModel\", ctx_r3.selectedProvider)(\"showClear\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"options\", ctx_r3.sortOptionsStatus)(\"ngModel\", ctx_r3.selectedStatus)(\"showClear\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.searchTerm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.paginatedStations.length === 0 && !ctx_r3.isRefreshing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.paginatedStations);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.totalItems > ctx_r3.itemsPerPage);\n  }\n}\nexport class IndexComponent {\n  constructor(layoutService, stationsService, providersService, messageService, fb, router, cacheService, dateUtils) {\n    this.layoutService = layoutService;\n    this.stationsService = stationsService;\n    this.providersService = providersService;\n    this.messageService = messageService;\n    this.fb = fb;\n    this.router = router;\n    this.cacheService = cacheService;\n    this.dateUtils = dateUtils;\n    this.stations = [];\n    this.filteredStations = [];\n    this.paginatedStations = [];\n    this.sortOptionsCountry = [];\n    this.sortOptionsStatus = [];\n    this.sortOrder = 0;\n    this.sortField = '';\n    // Filtering properties\n    this.searchTerm = '';\n    this.selectedProvider = '';\n    this.selectedStatus = '';\n    // Pagination properties\n    this.currentPage = 0;\n    this.itemsPerPage = 5;\n    this.totalItems = 0;\n    // Loading state\n    this.isRefreshing = false;\n    this.sourceCities = [];\n    this.targetCities = [];\n    this.orderCities = [];\n    this.stationsData = new Map();\n    this.stationsRawData = new Map();\n    this.stationsSumData = new Map();\n    this.subscription = this.layoutService.configUpdate$.pipe(debounceTime(25)).subscribe(config => {\n      //   this.initChart();\n    });\n  }\n  ngOnInit() {\n    this.getUserProviders();\n    this.barOptions = {\n      maintainAspectRatio: false,\n      plugins: {\n        legend: {\n          display: false\n        }\n      },\n      scales: {\n        x: {\n          display: false\n        },\n        y: {\n          display: false\n        }\n      }\n    };\n    this.lineOptions = {\n      responsive: true,\n      maintainAspectRatio: false,\n      interaction: {\n        intersect: false,\n        mode: 'index'\n      },\n      elements: {\n        line: {\n          tension: 0.4,\n          borderWidth: 2\n        },\n        point: {\n          radius: 0,\n          hoverRadius: 4,\n          hitRadius: 10\n        }\n      },\n      plugins: {\n        legend: {\n          display: false\n        },\n        tooltip: {\n          enabled: true,\n          mode: 'index',\n          intersect: false,\n          position: 'nearest',\n          external: null,\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          titleColor: '#ffffff',\n          bodyColor: '#ffffff',\n          borderColor: 'rgba(255, 255, 255, 0.1)',\n          borderWidth: 1,\n          cornerRadius: 8,\n          displayColors: false,\n          padding: 12,\n          caretPadding: 6,\n          caretSize: 5,\n          titleFont: {\n            size: 14,\n            weight: 'bold'\n          },\n          bodyFont: {\n            size: 13\n          },\n          callbacks: {\n            title: function (context) {\n              if (context && context.length > 0) {\n                return 'Time: ' + context[0].label;\n              }\n              return 'Time: --:--';\n            },\n            label: function (context) {\n              const label = context.dataset.label || '';\n              const value = context.parsed.y;\n              return `${label}: ${value.toFixed(2)} kW`;\n            }\n          }\n        }\n      },\n      scales: {\n        x: {\n          display: false,\n          grid: {\n            display: false\n          }\n        },\n        y: {\n          display: false,\n          grid: {\n            display: false\n          }\n        }\n      },\n      animation: {\n        duration: 750,\n        easing: 'easeInOutQuart'\n      }\n    };\n  }\n  getUserProviders() {\n    this.isRefreshing = true;\n    this.providersService.getUserProviders().then(providersData => {\n      if (providersData.length > 0) {\n        this.stationsService.getUserStations().then(stationsData => {\n          this.cacheService.setStations(stationsData);\n          this.stations = stationsData;\n          console.log(\"stations set\");\n          console.log(stationsData);\n          // Initialize filter options\n          this.initializeFilterOptions();\n          // Apply filters and pagination\n          this.applyFilters();\n          // Φορτώνουμε τα δεδομένα για κάθε σταθμό\n          this.loadAllStationsData();\n          this.isRefreshing = false;\n        });\n      } else {\n        this.router.navigate(['/app/providers']);\n      }\n    }).catch(error => {\n      console.error('Error loading providers:', error);\n      this.isRefreshing = false;\n    });\n  }\n  // Νέα μέθοδος για φόρτωση δεδομένων όλων των σταθμών\n  loadAllStationsData() {\n    if (!this.stations || this.stations.length === 0) return;\n    // Φορτώνουμε τα δεδομένα για κάθε σταθμό\n    this.stations.forEach(station => {\n      this.loadStationData(station);\n    });\n  }\n  // Νέα μέθοδος για φόρτωση δεδομένων ενός σταθμού\n  loadStationData(station) {\n    if (!station) return;\n    const now = new Date();\n    const formattedStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0).toISOString();\n    const formattedEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0).toISOString();\n    let request = {\n      devIds: station.deviceIds,\n      devTypeId: 1,\n      startDateTime: formattedStartDate,\n      endDateTime: formattedEndDate,\n      separated: true,\n      searchType: null,\n      stationId: station.id\n    };\n    this.stationsService.getStationHistoricData(request).then(data => {\n      if (data && data.data) {\n        // Filter data to not show beyond current time\n        const filteredData = this.filterDataByCurrentTime(data.data);\n        const documentStyle = getComputedStyle(document.documentElement);\n        const lineData = {\n          labels: filteredData.map((e, index) => index % 2 === 0 ? this.dateUtils.formatTimeForChart(e.dateTime) : ''),\n          datasets: [{\n            label: 'Active Power',\n            data: filteredData.map(e => e.activePower),\n            fill: false,\n            backgroundColor: documentStyle.getPropertyValue('--primary-500'),\n            borderColor: documentStyle.getPropertyValue('--primary-500'),\n            tension: .4\n          }, {\n            label: 'Total Input Power',\n            data: filteredData.map(e => e.totalInputPower),\n            fill: false,\n            backgroundColor: documentStyle.getPropertyValue('--primary-200'),\n            borderColor: documentStyle.getPropertyValue('--primary-200'),\n            tension: .4\n          }]\n        };\n        // Αποθηκεύουμε τα δεδομένα στο Map με κλειδί το ID του σταθμού\n        this.stationsData.set(station.id, lineData);\n        this.stationsRawData.set(station.id, filteredData);\n        this.stationsSumData.set(station.id, data.sum);\n      }\n    }).catch(error => {\n      console.error(`Error loading data for station ${station.id}:`, error);\n    });\n  }\n  // Τροποποιημένη μέθοδος που επιστρέφει τα προ-φορτωμένα δεδομένα\n  getStationsRealTimeData(station) {\n    // Επιστρέφουμε τα δεδομένα από το Map αν υπάρχουν\n    if (station && station.id && this.stationsData.has(station.id)) {\n      return this.stationsData.get(station.id);\n    }\n    // Αν δεν έχουν φορτωθεί ακόμα, επιστρέφουμε null\n    return null;\n  }\n  getStationsSumData(stationId) {\n    return this.stationsSumData.get(stationId);\n  }\n  getStationsInverters(stationId) {\n    var data = this.stationsRawData.get(stationId);\n    if (!data || data.length === 0) {\n      return 0;\n    } else return new Set(data.map(item => item.name)).size;\n  }\n  getStationLastUpdate(stationId) {\n    const data = this.stationsRawData.get(stationId);\n    if (!data || data.length === 0) return \"-\";\n    const latest = data.reduce((latestSoFar, current) => {\n      return new Date(current.dateTime).getTime() > new Date(latestSoFar.dateTime).getTime() ? current : latestSoFar;\n    });\n    return new Date(latest.dateTime).toLocaleString(\"en-GB\", {\n      hour: '2-digit',\n      minute: '2-digit',\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric',\n      timeZone: 'Europe/Athens'\n    });\n  }\n  onSortChange(event) {\n    const value = event.value;\n    if (value.indexOf('!') === 0) {\n      this.sortOrder = -1;\n      this.sortField = value.substring(1, value.length);\n    } else {\n      this.sortOrder = 1;\n      this.sortField = value;\n    }\n  }\n  onFilter(dv, event) {\n    dv.filter(event.target.value);\n  }\n  // Initialize filter options based on available stations\n  initializeFilterOptions() {\n    // Get unique providers\n    const providers = [...new Set(this.stations.map(station => station.provider))];\n    this.sortOptionsCountry = providers.map(provider => ({\n      label: provider,\n      value: provider\n    }));\n    // Get unique statuses\n    const statuses = [...new Set(this.stations.map(station => station.status))];\n    this.sortOptionsStatus = statuses.map(status => ({\n      label: status,\n      value: status\n    }));\n  }\n  // Apply all filters and update pagination\n  applyFilters() {\n    let filtered = [...this.stations];\n    // Apply search filter\n    if (this.searchTerm) {\n      const searchLower = this.searchTerm.toLowerCase();\n      filtered = filtered.filter(station => station.name.toLowerCase().includes(searchLower) || station.provider.toLowerCase().includes(searchLower) || station.location && station.location.toLowerCase().includes(searchLower));\n    }\n    // Apply provider filter\n    if (this.selectedProvider) {\n      filtered = filtered.filter(station => station.provider === this.selectedProvider);\n    }\n    // Apply status filter\n    if (this.selectedStatus) {\n      filtered = filtered.filter(station => station.status === this.selectedStatus);\n    }\n    this.filteredStations = filtered;\n    this.totalItems = filtered.length;\n    this.currentPage = 0; // Reset to first page\n    this.updatePagination();\n  }\n  // Update pagination based on current page\n  updatePagination() {\n    const startIndex = this.currentPage * this.itemsPerPage;\n    const endIndex = startIndex + this.itemsPerPage;\n    this.paginatedStations = this.filteredStations.slice(startIndex, endIndex);\n  }\n  // Handle search input change\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.applyFilters();\n  }\n  // Handle provider filter change\n  onProviderChange(event) {\n    this.selectedProvider = event.value || '';\n    this.applyFilters();\n  }\n  // Handle status filter change\n  onStatusChange(event) {\n    this.selectedStatus = event.value || '';\n    this.applyFilters();\n  }\n  // Handle pagination change\n  onPageChange(event) {\n    this.currentPage = event.page;\n    this.itemsPerPage = event.rows;\n    this.updatePagination();\n  }\n  // Refresh data\n  refreshData() {\n    this.getUserProviders();\n  }\n  // Clear all filters\n  clearFilters() {\n    this.searchTerm = '';\n    this.selectedProvider = '';\n    this.selectedStatus = '';\n    this.applyFilters();\n  }\n  // Check if station has equipment data\n  hasEquipmentData(station) {\n    const inverters = this.getStationsInverters(station.id || '');\n    return inverters > 0 || station.mmpt && station.mmpt > 0 || station.string && station.string > 0 || station.pvn && station.pvn > 0;\n  }\n  filterDataByCurrentTime(data) {\n    const now = new Date();\n    const currentTime = now.getTime();\n    return data.filter(item => {\n      const itemDateTime = new Date(item.dateTime);\n      const itemTime = itemDateTime.getTime();\n      // Only include data points that are not in the future\n      return itemTime <= currentTime;\n    });\n  }\n  initMap() {\n    this.mapSrc = \"https://www.digitaltrends.com/wp-content/uploads/2020/02/custom-map.jpg?fit=720%2C479&p=1\";\n  }\n  // initChart() {\n  //     const documentStyle = getComputedStyle(document.documentElement);\n  //     const textColor = documentStyle.getPropertyValue('--text-color');\n  //     const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n  //     const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\n  //     this.chartData = {\n  //         labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\n  //         datasets: [\n  //             {\n  //                 label: 'First Dataset',\n  //                 data: [65, 59, 80, 81, 56, 55, 40],\n  //                 fill: false,\n  //                 backgroundColor: documentStyle.getPropertyValue('--bluegray-700'),\n  //                 borderColor: documentStyle.getPropertyValue('--bluegray-700'),\n  //                 tension: .4\n  //             },\n  //             {\n  //                 label: 'Second Dataset',\n  //                 data: [28, 48, 40, 19, 86, 27, 90],\n  //                 fill: false,\n  //                 backgroundColor: documentStyle.getPropertyValue('--green-600'),\n  //                 borderColor: documentStyle.getPropertyValue('--green-600'),\n  //                 tension: .4\n  //             }\n  //         ]\n  //     };\n  //     this.chartOptions = {\n  //         plugins: {\n  //             legend: {\n  //                 labels: {\n  //                     color: textColor\n  //                 }\n  //             }\n  //         },\n  //         scales: {\n  //             x: {\n  //                 ticks: {\n  //                     color: textColorSecondary\n  //                 },\n  //                 grid: {\n  //                     color: surfaceBorder,\n  //                     drawBorder: false\n  //                 }\n  //             },\n  //             y: {\n  //                 ticks: {\n  //                     color: textColorSecondary\n  //                 },\n  //                 grid: {\n  //                     color: surfaceBorder,\n  //                     drawBorder: false\n  //                 }\n  //             }\n  //         }\n  //     };\n  // }\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n  static #_ = this.ɵfac = function IndexComponent_Factory(t) {\n    return new (t || IndexComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.StationsService), i0.ɵɵdirectiveInject(i3.ProvidersService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.FormBuilder), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.CacheService), i0.ɵɵdirectiveInject(i8.DateUtilsService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: IndexComponent,\n    selectors: [[\"ng-component\"]],\n    decls: 11,\n    vars: 0,\n    consts: [[1, \"grid\"], [1, \"col-12\", \"md:col-6\", \"lg:col-6\"], [1, \"h-full\"], [\"pTemplate\", \"content\"], [1, \"col-12\"], [\"pTemplate\", \"header\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\"], [1, \"flex-1\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"mb-3\"], [\"icon\", \"solar-panel\", 1, \"text-primary\", \"text-lg\"], [1, \"text-600\", \"font-medium\"], [1, \"text-900\", \"font-bold\", \"text-3xl\"], [\"value\", \"Online\", \"severity\", \"success\", 1, \"mt-2\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-primary-50\", \"border-round-lg\", 2, \"width\", \"4rem\", \"height\", \"4rem\"], [\"icon\", \"solar-panel\", 1, \"text-primary\", \"text-2xl\"], [\"icon\", \"bolt\", 1, \"text-orange-500\", \"text-lg\"], [\"value\", \"Optimal\", \"severity\", \"warning\", 1, \"mt-2\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-orange-50\", \"border-round-lg\", 2, \"width\", \"4rem\", \"height\", \"4rem\"], [\"icon\", \"bolt\", 1, \"text-orange-500\", \"text-2xl\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"p-3\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"icon\", \"list\", 1, \"text-primary\", \"text-xl\"], [1, \"text-2xl\", \"font-semibold\", \"m-0\"], [\"severity\", \"info\", 3, \"value\", 4, \"ngIf\"], [1, \"flex\", \"gap-2\"], [\"icon\", \"pi pi-filter-slash\", \"severity\", \"secondary\", \"size\", \"small\", \"pTooltip\", \"Clear all filters\", 3, \"click\"], [\"icon\", \"pi pi-refresh\", \"severity\", \"info\", \"size\", \"small\", \"pTooltip\", \"Refresh data\", 3, \"loading\", \"click\"], [\"severity\", \"info\", 3, \"value\"], [1, \"flex\", \"flex-column\", \"md:flex-row\", \"md:justify-content-between\", \"gap-3\", \"mb-4\"], [\"placeholder\", \"Filter by Provider\", 1, \"w-full\", \"md:w-auto\", 3, \"options\", \"ngModel\", \"showClear\", \"ngModelChange\", \"onChange\"], [\"placeholder\", \"Filter by Status\", 1, \"w-full\", \"md:w-auto\", 3, \"options\", \"ngModel\", \"showClear\", \"ngModelChange\", \"onChange\"], [1, \"p-input-icon-left\"], [1, \"pi\", \"pi-search\"], [\"type\", \"search\", \"pInputText\", \"\", \"placeholder\", \"Search stations...\", 1, \"w-full\", \"md:w-auto\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"class\", \"col-12\", 4, \"ngIf\"], [\"class\", \"col-12\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"mt-4\", 4, \"ngIf\"], [1, \"text-center\", \"py-6\"], [\"icon\", \"inbox\", 1, \"text-6xl\", \"text-300\", \"mb-3\"], [1, \"text-lg\", \"text-600\"], [\"label\", \"Clear Filters\", \"icon\", \"pi pi-filter-slash\", \"severity\", \"secondary\", \"size\", \"small\", 3, \"click\"], [\"pRipple\", \"\", 1, \"mb-3\", 3, \"ngClass\"], [1, \"bg-primary-50\", \"p-3\", \"border-round-top\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\"], [\"severity\", \"info\", \"icon\", \"pi pi-server\", 3, \"value\"], [3, \"value\", \"severity\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"text-600\"], [\"icon\", \"clock\", 1, \"text-sm\"], [1, \"text-sm\"], [1, \"grid\", \"align-items-center\"], [1, \"col-12\", \"md:col-6\", \"lg:col-3\"], [1, \"flex\", \"flex-column\", \"gap-2\"], [1, \"text-xl\", \"font-bold\", \"m-0\"], [1, \"text-primary\", \"no-underline\", \"hover:underline\", 3, \"routerLink\"], [1, \"text-600\", \"text-sm\"], [1, \"col-12\", \"md:col-6\", \"lg:col-2\"], [1, \"text-center\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-1\", \"mb-1\"], [\"icon\", \"bolt\", 1, \"text-orange-500\"], [1, \"font-bold\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-1\"], [\"icon\", \"sun\", 1, \"text-yellow-500\"], [1, \"text-sm\", \"text-600\"], [\"type\", \"line\", \"class\", \"mx-auto\", \"height\", \"50\", \"width\", \"120\", 3, \"data\", \"options\", 4, \"ngIf\"], [\"class\", \"text-center p-2\", 4, \"ngIf\"], [1, \"flex\", \"flex-wrap\", \"gap-2\", \"justify-content-center\"], [\"severity\", \"contrast\", \"size\", \"small\", 3, \"value\", 4, \"ngIf\"], [\"severity\", \"success\", \"size\", \"small\", 3, \"value\", 4, \"ngIf\"], [\"severity\", \"info\", \"size\", \"small\", 3, \"value\", 4, \"ngIf\"], [\"severity\", \"warning\", \"size\", \"small\", 3, \"value\", 4, \"ngIf\"], [\"class\", \"text-center\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-2\"], [1, \"pi\", \"pi-cloud\", \"text-blue-500\", \"text-xl\"], [1, \"text-xl\", \"font-semibold\"], [\"type\", \"line\", \"height\", \"50\", \"width\", \"120\", 1, \"mx-auto\", 3, \"data\", \"options\"], [1, \"text-center\", \"p-2\"], [\"icon\", \"chart-line\", 1, \"text-300\", \"text-2xl\", \"mb-1\"], [1, \"text-xs\", \"text-600\", \"mt-1\", \"mb-0\"], [\"severity\", \"contrast\", \"size\", \"small\", 3, \"value\"], [\"severity\", \"success\", \"size\", \"small\", 3, \"value\"], [\"severity\", \"info\", \"size\", \"small\", 3, \"value\"], [\"severity\", \"warning\", \"size\", \"small\", 3, \"value\"], [\"icon\", \"cog\", 1, \"text-300\", \"text-lg\"], [1, \"mt-4\"], [\"currentPageReportTemplate\", \"Showing {first} to {last} of {totalRecords} stations\", 3, \"rows\", \"totalRecords\", \"first\", \"rowsPerPageOptions\", \"showCurrentPageReport\", \"onPageChange\"]],\n    template: function IndexComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"p-card\", 2);\n        i0.ɵɵtemplate(3, IndexComponent_ng_template_3_Template, 11, 1, \"ng-template\", 3);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(4, \"div\", 1)(5, \"p-card\", 2);\n        i0.ɵɵtemplate(6, IndexComponent_ng_template_6_Template, 11, 0, \"ng-template\", 3);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"div\", 4)(8, \"p-card\");\n        i0.ɵɵtemplate(9, IndexComponent_ng_template_9_Template, 9, 2, \"ng-template\", 5)(10, IndexComponent_ng_template_10_Template, 11, 10, \"ng-template\", 3);\n        i0.ɵɵelementEnd()()();\n      }\n    },\n    dependencies: [i9.NgClass, i9.NgForOf, i9.NgIf, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel, i6.RouterLink, i10.Tooltip, i4.PrimeTemplate, i11.Button, i12.InputText, i13.Dropdown, i14.ChartjsComponent, i15.Badge, i16.FaIconComponent, i17.Card, i18.Tag, i19.Ripple, i20.Paginator],\n    styles: [\"\\n\\n.chart-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: visible;\\n}\\n\\n\\n\\nc-chart[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: block;\\n}\\nc-chart[_ngcontent-%COMP%]   canvas[_ngcontent-%COMP%] {\\n  position: relative !important;\\n}\\n\\n\\n\\n[_nghost-%COMP%]     {\\n  \\n\\n  \\n\\n}\\n[_nghost-%COMP%]     .chartjs-tooltip {\\n  position: absolute !important;\\n  pointer-events: none;\\n  z-index: 1000;\\n}\\n[_nghost-%COMP%]     .p-card-content {\\n  overflow: visible;\\n}\\n[_nghost-%COMP%]     canvas {\\n  position: relative !important;\\n}\\n\\n\\n\\n.station-card[_ngcontent-%COMP%]   .chart-section[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: visible;\\n  min-height: 80px;\\n}\\n.station-card[_ngcontent-%COMP%]   .chart-section[_ngcontent-%COMP%]   c-chart[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  c-chart[_ngcontent-%COMP%] {\\n    height: 40px !important;\\n    width: 100px !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["debounceTime", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "stations", "length", "ɵɵproperty", "ctx_r4", "totalItems", "ɵɵtemplate", "IndexComponent_ng_template_9_p_badge_5_Template", "ɵɵlistener", "IndexComponent_ng_template_9_Template_p_button_click_7_listener", "ɵɵrestoreView", "_r6", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "clearFilters", "IndexComponent_ng_template_9_Template_p_button_click_8_listener", "ctx_r7", "refreshData", "ctx_r2", "isRefreshing", "IndexComponent_ng_template_10_div_8_Template_p_button_click_5_listener", "_r12", "ctx_r11", "station_r13", "provider", "status", "toLowerCase", "ctx_r14", "getStationLastUpdate", "id", "ctx_r17", "getStationsRealTimeData", "lineOptions", "ctx_r19", "getStationsInverters", "mmpt", "string", "pvn", "IndexComponent_ng_template_10_div_9_ng_template_3_c_chart_20_Template", "IndexComponent_ng_template_10_div_9_ng_template_3_div_21_Template", "IndexComponent_ng_template_10_div_9_ng_template_3_p_badge_24_Template", "IndexComponent_ng_template_10_div_9_ng_template_3_p_badge_25_Template", "IndexComponent_ng_template_10_div_9_ng_template_3_p_badge_26_Template", "IndexComponent_ng_template_10_div_9_ng_template_3_p_badge_27_Template", "IndexComponent_ng_template_10_div_9_ng_template_3_div_28_Template", "ɵɵpureFunction1", "_c0", "ɵɵtextInterpolate1", "name", "location", "ctx_r15", "getStationsSumData", "irradiance", "hasEquipmentData", "temperature", "IndexComponent_ng_template_10_div_9_ng_template_2_Template", "IndexComponent_ng_template_10_div_9_ng_template_3_Template", "IndexComponent_ng_template_10_div_10_Template_p_paginator_onPageChange_1_listener", "$event", "_r31", "ctx_r30", "onPageChange", "ctx_r10", "itemsPerPage", "currentPage", "ɵɵpureFunction0", "_c1", "IndexComponent_ng_template_10_Template_p_dropdown_ngModelChange_2_listener", "_r33", "ctx_r32", "<PERSON><PERSON><PERSON><PERSON>", "IndexComponent_ng_template_10_Template_p_dropdown_onChange_2_listener", "ctx_r34", "onProviderChange", "IndexComponent_ng_template_10_Template_p_dropdown_ngModelChange_3_listener", "ctx_r35", "selectedStatus", "IndexComponent_ng_template_10_Template_p_dropdown_onChange_3_listener", "ctx_r36", "onStatusChange", "IndexComponent_ng_template_10_Template_input_ngModelChange_6_listener", "ctx_r37", "searchTerm", "IndexComponent_ng_template_10_Template_input_input_6_listener", "ctx_r38", "onSearchChange", "IndexComponent_ng_template_10_div_8_Template", "IndexComponent_ng_template_10_div_9_Template", "IndexComponent_ng_template_10_div_10_Template", "ctx_r3", "sortOptionsCountry", "sortOptionsStatus", "paginatedStations", "IndexComponent", "constructor", "layoutService", "stationsService", "providersService", "messageService", "fb", "router", "cacheService", "dateUtils", "filteredStations", "sortOrder", "sortField", "sourceCities", "targetCities", "orderCities", "stationsData", "Map", "stationsRawData", "stationsSumData", "subscription", "configUpdate$", "pipe", "subscribe", "config", "ngOnInit", "getUserProviders", "barOptions", "maintainAspectRatio", "plugins", "legend", "display", "scales", "x", "y", "responsive", "interaction", "intersect", "mode", "elements", "line", "tension", "borderWidth", "point", "radius", "hoverRadius", "hitRadius", "tooltip", "enabled", "position", "external", "backgroundColor", "titleColor", "bodyColor", "borderColor", "cornerRadius", "displayColors", "padding", "caretPadding", "caretSize", "titleFont", "size", "weight", "bodyFont", "callbacks", "title", "context", "label", "dataset", "value", "parsed", "toFixed", "grid", "animation", "duration", "easing", "then", "providersData", "getUserStations", "setStations", "console", "log", "initializeFilterOptions", "applyFilters", "loadAllStationsData", "navigate", "catch", "error", "for<PERSON>ach", "station", "loadStationData", "now", "Date", "formattedStartDate", "getFullYear", "getMonth", "getDate", "toISOString", "formattedEndDate", "request", "devIds", "deviceIds", "devTypeId", "startDateTime", "endDateTime", "separated", "searchType", "stationId", "getStationHistoricData", "data", "filteredData", "filterDataByCurrentTime", "documentStyle", "getComputedStyle", "document", "documentElement", "lineData", "labels", "map", "e", "index", "formatTimeForChart", "dateTime", "datasets", "activePower", "fill", "getPropertyValue", "totalInputPower", "set", "sum", "has", "get", "Set", "item", "latest", "reduce", "latestSoFar", "current", "getTime", "toLocaleString", "hour", "minute", "day", "month", "year", "timeZone", "onSortChange", "event", "indexOf", "substring", "onFilter", "dv", "filter", "target", "providers", "statuses", "filtered", "searchLower", "includes", "updatePagination", "startIndex", "endIndex", "slice", "page", "rows", "inverters", "currentTime", "itemDateTime", "itemTime", "initMap", "mapSrc", "ngOnDestroy", "unsubscribe", "_", "ɵɵdirectiveInject", "i1", "LayoutService", "i2", "StationsService", "i3", "ProvidersService", "i4", "MessageService", "i5", "FormBuilder", "i6", "Router", "i7", "CacheService", "i8", "DateUtilsService", "_2", "selectors", "decls", "vars", "consts", "template", "IndexComponent_Template", "rf", "ctx", "IndexComponent_ng_template_3_Template", "IndexComponent_ng_template_6_Template", "IndexComponent_ng_template_9_Template", "IndexComponent_ng_template_10_Template"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\index\\index.component.ts", "D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\index\\index.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { MenuItem, MessageService } from 'primeng/api';\r\nimport { Product } from '../../api/product';\r\nimport { ProductService } from '../../service/product.service';\r\nimport { Subscription, debounceTime } from 'rxjs';\r\nimport { LayoutService } from 'src/app/layout/service/app.layout.service';\r\nimport { SelectItem } from 'primeng/api';\r\nimport { DataView } from 'primeng/dataview';\r\nimport { StationsService } from '../../service/stations.service';\r\nimport { Station } from '../../api/station';\r\nimport { CacheService } from '../../service/cache.service';\r\nimport { DateUtilsService } from '../../service/date-utils.service';\r\nimport { ProvidersService } from '../../service/providers.service';\r\nimport { IProvider, IUserProvider } from '../../api/responses';\r\nimport { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { GetHistoricDataRequest, SaveUserProvidersRequest } from '../../api/requests';\r\nimport { Router } from '@angular/router';\r\n\r\n\r\n@Component({\r\n    templateUrl: './index.component.html',\r\n    styleUrls: ['./index.component.scss']\r\n})\r\nexport class IndexComponent implements OnInit, OnDestroy {\r\n\r\n    items!: MenuItem[];\r\n\r\n    stations: Station[] = [];\r\n    filteredStations: Station[] = [];\r\n    paginatedStations: Station[] = [];\r\n\r\n    chartData: any;\r\n\r\n    chartOptions: any;\r\n\r\n    subscription!: Subscription;\r\n\r\n    sortOptionsCountry: SelectItem[] = [];\r\n\r\n    sortOptionsStatus: SelectItem[] = [];\r\n\r\n    sortOrder: number = 0;\r\n\r\n    sortField: string = '';\r\n\r\n    // Filtering properties\r\n    searchTerm: string = '';\r\n    selectedProvider: string = '';\r\n    selectedStatus: string = '';\r\n\r\n    // Pagination properties\r\n    currentPage: number = 0;\r\n    itemsPerPage: number = 5;\r\n    totalItems: number = 0;\r\n\r\n    // Loading state\r\n    isRefreshing: boolean = false;\r\n\r\n    sourceCities: any[] = [];\r\n\r\n    targetCities: any[] = [];\r\n\r\n    orderCities: any[] = [];\r\n\r\n    mapSrc:string;\r\n\r\n    barOptions:any;\r\n    lineOptions:any;\r\n    stationsData:Map<string,any> = new Map();\r\n    stationsRawData:Map<string,any> = new Map();\r\n    stationsSumData:Map<string,number> = new Map();\r\n    \r\n\r\n    constructor(public layoutService: LayoutService,\r\n        private stationsService: StationsService,\r\n        private providersService: ProvidersService,\r\n        private messageService: MessageService,\r\n        private fb: FormBuilder,\r\n        private router: Router,\r\n        private cacheService: CacheService,\r\n        private dateUtils: DateUtilsService) {\r\n        this.subscription = this.layoutService.configUpdate$\r\n        .pipe(debounceTime(25))\r\n        .subscribe((config) => {\r\n         //   this.initChart();\r\n        });\r\n    }\r\n\r\n    ngOnInit() {\r\n        this.getUserProviders();\r\n\r\n        this.barOptions = {\r\n            maintainAspectRatio: false,\r\n            plugins: {\r\n              legend: {\r\n                display: false\r\n              }\r\n            },\r\n            scales: {\r\n              x: {\r\n                display: false\r\n              },\r\n              y: {\r\n                display: false\r\n              }\r\n            }\r\n          };\r\n        \r\n          this.lineOptions = {\r\n            responsive: true,\r\n            maintainAspectRatio: false,\r\n            interaction: {\r\n              intersect: false,\r\n              mode: 'index'\r\n            },\r\n            elements: {\r\n              line: {\r\n                tension: 0.4,\r\n                borderWidth: 2\r\n              },\r\n              point: {\r\n                radius: 0,\r\n                hoverRadius: 4,\r\n                hitRadius: 10\r\n              }\r\n            },\r\n            plugins: {\r\n              legend: {\r\n                display: false\r\n              },\r\n              tooltip: {\r\n                enabled: true,\r\n                mode: 'index',\r\n                intersect: false,\r\n                position: 'nearest',\r\n                external: null, // Ensure we use the default tooltip positioning\r\n                backgroundColor: 'rgba(0, 0, 0, 0.8)',\r\n                titleColor: '#ffffff',\r\n                bodyColor: '#ffffff',\r\n                borderColor: 'rgba(255, 255, 255, 0.1)',\r\n                borderWidth: 1,\r\n                cornerRadius: 8,\r\n                displayColors: false,\r\n                padding: 12,\r\n                caretPadding: 6,\r\n                caretSize: 5,\r\n                titleFont: {\r\n                  size: 14,\r\n                  weight: 'bold'\r\n                },\r\n                bodyFont: {\r\n                  size: 13\r\n                },\r\n                callbacks: {\r\n                  title: function(context: any) {\r\n                    if (context && context.length > 0) {\r\n                      return 'Time: ' + context[0].label;\r\n                    }\r\n                    return 'Time: --:--';\r\n                  },\r\n                  label: function(context: any) {\r\n                    const label = context.dataset.label || '';\r\n                    const value = context.parsed.y;\r\n                    return `${label}: ${value.toFixed(2)} kW`;\r\n                  }\r\n                }\r\n              }\r\n            },\r\n            scales: {\r\n              x: {\r\n                display: false,\r\n                grid: {\r\n                  display: false\r\n                }\r\n              },\r\n              y: {\r\n                display: false,\r\n                grid: {\r\n                  display: false\r\n                }\r\n              }\r\n            },\r\n            animation: {\r\n              duration: 750,\r\n              easing: 'easeInOutQuart'\r\n            }\r\n          };\r\n\r\n\r\n          \r\n        \r\n    }\r\n\r\n    getUserProviders(){\r\n      this.isRefreshing = true;\r\n      this.providersService.getUserProviders().then(providersData => {\r\n        if (providersData.length > 0){\r\n          this.stationsService.getUserStations().then(stationsData => {\r\n            this.cacheService.setStations(stationsData);\r\n            this.stations = stationsData;\r\n            console.log(\"stations set\")\r\n            console.log(stationsData)\r\n\r\n            // Initialize filter options\r\n            this.initializeFilterOptions();\r\n\r\n            // Apply filters and pagination\r\n            this.applyFilters();\r\n\r\n            // Φορτώνουμε τα δεδομένα για κάθε σταθμό\r\n            this.loadAllStationsData();\r\n\r\n            this.isRefreshing = false;\r\n          });\r\n        }else{\r\n          this.router.navigate(['/app/providers']);\r\n        }\r\n      }).catch(error => {\r\n        console.error('Error loading providers:', error);\r\n        this.isRefreshing = false;\r\n      });\r\n    }\r\n\r\n    // Νέα μέθοδος για φόρτωση δεδομένων όλων των σταθμών\r\n    loadAllStationsData() {\r\n        if (!this.stations || this.stations.length === 0) return;\r\n        \r\n        // Φορτώνουμε τα δεδομένα για κάθε σταθμό\r\n        this.stations.forEach(station => {\r\n            this.loadStationData(station);\r\n        });\r\n    }\r\n\r\n    // Νέα μέθοδος για φόρτωση δεδομένων ενός σταθμού\r\n    loadStationData(station: Station) {\r\n        if (!station) return;\r\n        \r\n        const now = new Date();\r\n        const formattedStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0).toISOString();\r\n        const formattedEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0).toISOString();\r\n        \r\n        let request: GetHistoricDataRequest = {\r\n            devIds: station.deviceIds,\r\n            devTypeId: 1,\r\n            startDateTime: formattedStartDate,\r\n            endDateTime: formattedEndDate,\r\n            separated: true,\r\n            searchType: null,\r\n            stationId: station.id\r\n        };\r\n        \r\n        this.stationsService.getStationHistoricData(request).then(data => {\r\n            if (data && data.data) {\r\n                // Filter data to not show beyond current time\r\n                const filteredData = this.filterDataByCurrentTime(data.data);\r\n\r\n                const documentStyle = getComputedStyle(document.documentElement);\r\n\r\n                const lineData = {\r\n                    labels: filteredData.map((e, index) =>\r\n                        index % 2 === 0 ? this.dateUtils.formatTimeForChart(e.dateTime) : ''\r\n                    ),\r\n                    datasets: [\r\n                        {\r\n                            label: 'Active Power',\r\n                            data: filteredData.map(e => e.activePower),\r\n                            fill: false,\r\n                            backgroundColor: documentStyle.getPropertyValue('--primary-500'),\r\n                            borderColor: documentStyle.getPropertyValue('--primary-500'),\r\n                            tension: .4\r\n                        },\r\n                        {\r\n                            label: 'Total Input Power',\r\n                            data: filteredData.map(e => e.totalInputPower),\r\n                            fill: false,\r\n                            backgroundColor: documentStyle.getPropertyValue('--primary-200'),\r\n                            borderColor: documentStyle.getPropertyValue('--primary-200'),\r\n                            tension: .4\r\n                        }\r\n                    ]\r\n                };\r\n\r\n                // Αποθηκεύουμε τα δεδομένα στο Map με κλειδί το ID του σταθμού\r\n                this.stationsData.set(station.id, lineData);\r\n                this.stationsRawData.set(station.id, filteredData);\r\n                this.stationsSumData.set(station.id, data.sum);\r\n            }\r\n        }).catch(error => {\r\n            console.error(`Error loading data for station ${station.id}:`, error);\r\n        });\r\n    }\r\n    \r\n\r\n    // Τροποποιημένη μέθοδος που επιστρέφει τα προ-φορτωμένα δεδομένα\r\n    getStationsRealTimeData(station: Station) {\r\n        // Επιστρέφουμε τα δεδομένα από το Map αν υπάρχουν\r\n        if (station && station.id && this.stationsData.has(station.id)) {\r\n            return this.stationsData.get(station.id);\r\n        }\r\n        \r\n        // Αν δεν έχουν φορτωθεί ακόμα, επιστρέφουμε null\r\n        return null;\r\n    }\r\n\r\n    getStationsSumData(stationId:string){\r\n      return this.stationsSumData.get(stationId);\r\n    }\r\n\r\n    getStationsInverters(stationId:string){\r\n      var data = this.stationsRawData.get(stationId);\r\n      if (!data || data.length === 0) {\r\n        return 0;\r\n      }\r\n      else\r\n        return new Set(data.map(item => item.name)).size;\r\n    }\r\n\r\n    getStationLastUpdate(stationId: string) {\r\n      const data = this.stationsRawData.get(stationId);\r\n      if (!data || data.length === 0) return \"-\";\r\n\r\n      const latest = data.reduce((latestSoFar, current) => {\r\n        return new Date(current.dateTime).getTime() > new Date(latestSoFar.dateTime).getTime()\r\n          ? current\r\n          : latestSoFar;\r\n      });\r\n\r\n      return new Date(latest.dateTime).toLocaleString(\"en-GB\", {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        day: '2-digit',\r\n        month: '2-digit',\r\n        year: 'numeric',\r\n        timeZone: 'Europe/Athens'\r\n      });\r\n    }\r\n    \r\n\r\n    onSortChange(event: any) {\r\n        const value = event.value;\r\n\r\n        if (value.indexOf('!') === 0) {\r\n            this.sortOrder = -1;\r\n            this.sortField = value.substring(1, value.length);\r\n        } else {\r\n            this.sortOrder = 1;\r\n            this.sortField = value;\r\n        }\r\n    }\r\n\r\n    onFilter(dv: DataView, event: Event) {\r\n        dv.filter((event.target as HTMLInputElement).value);\r\n    }\r\n\r\n    // Initialize filter options based on available stations\r\n    initializeFilterOptions() {\r\n        // Get unique providers\r\n        const providers = [...new Set(this.stations.map(station => station.provider))];\r\n        this.sortOptionsCountry = providers.map(provider => ({\r\n            label: provider,\r\n            value: provider\r\n        }));\r\n\r\n        // Get unique statuses\r\n        const statuses = [...new Set(this.stations.map(station => station.status))];\r\n        this.sortOptionsStatus = statuses.map(status => ({\r\n            label: status,\r\n            value: status\r\n        }));\r\n    }\r\n\r\n    // Apply all filters and update pagination\r\n    applyFilters() {\r\n        let filtered = [...this.stations];\r\n\r\n        // Apply search filter\r\n        if (this.searchTerm) {\r\n            const searchLower = this.searchTerm.toLowerCase();\r\n            filtered = filtered.filter(station =>\r\n                station.name.toLowerCase().includes(searchLower) ||\r\n                station.provider.toLowerCase().includes(searchLower) ||\r\n                (station.location && station.location.toLowerCase().includes(searchLower))\r\n            );\r\n        }\r\n\r\n        // Apply provider filter\r\n        if (this.selectedProvider) {\r\n            filtered = filtered.filter(station => station.provider === this.selectedProvider);\r\n        }\r\n\r\n        // Apply status filter\r\n        if (this.selectedStatus) {\r\n            filtered = filtered.filter(station => station.status === this.selectedStatus);\r\n        }\r\n\r\n        this.filteredStations = filtered;\r\n        this.totalItems = filtered.length;\r\n        this.currentPage = 0; // Reset to first page\r\n        this.updatePagination();\r\n    }\r\n\r\n    // Update pagination based on current page\r\n    updatePagination() {\r\n        const startIndex = this.currentPage * this.itemsPerPage;\r\n        const endIndex = startIndex + this.itemsPerPage;\r\n        this.paginatedStations = this.filteredStations.slice(startIndex, endIndex);\r\n    }\r\n\r\n    // Handle search input change\r\n    onSearchChange(event: Event) {\r\n        this.searchTerm = (event.target as HTMLInputElement).value;\r\n        this.applyFilters();\r\n    }\r\n\r\n    // Handle provider filter change\r\n    onProviderChange(event: any) {\r\n        this.selectedProvider = event.value || '';\r\n        this.applyFilters();\r\n    }\r\n\r\n    // Handle status filter change\r\n    onStatusChange(event: any) {\r\n        this.selectedStatus = event.value || '';\r\n        this.applyFilters();\r\n    }\r\n\r\n    // Handle pagination change\r\n    onPageChange(event: any) {\r\n        this.currentPage = event.page;\r\n        this.itemsPerPage = event.rows;\r\n        this.updatePagination();\r\n    }\r\n\r\n    // Refresh data\r\n    refreshData() {\r\n        this.getUserProviders();\r\n    }\r\n\r\n    // Clear all filters\r\n    clearFilters() {\r\n        this.searchTerm = '';\r\n        this.selectedProvider = '';\r\n        this.selectedStatus = '';\r\n        this.applyFilters();\r\n    }\r\n\r\n    // Check if station has equipment data\r\n    hasEquipmentData(station: Station): boolean {\r\n        const inverters = this.getStationsInverters(station.id || '');\r\n        return inverters > 0 ||\r\n               (station.mmpt && station.mmpt > 0) ||\r\n               (station.string && station.string > 0) ||\r\n               (station.pvn && station.pvn > 0);\r\n    }\r\n\r\n    private filterDataByCurrentTime(data: any[]): any[] {\r\n        const now = new Date();\r\n        const currentTime = now.getTime();\r\n\r\n        return data.filter(item => {\r\n            const itemDateTime = new Date(item.dateTime);\r\n            const itemTime = itemDateTime.getTime();\r\n\r\n            // Only include data points that are not in the future\r\n            return itemTime <= currentTime;\r\n        });\r\n    }\r\n\r\n\r\n    initMap(){\r\n        this.mapSrc = \"https://www.digitaltrends.com/wp-content/uploads/2020/02/custom-map.jpg?fit=720%2C479&p=1\"\r\n    }\r\n\r\n    // initChart() {\r\n    //     const documentStyle = getComputedStyle(document.documentElement);\r\n    //     const textColor = documentStyle.getPropertyValue('--text-color');\r\n    //     const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\r\n    //     const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\r\n\r\n    //     this.chartData = {\r\n    //         labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\r\n    //         datasets: [\r\n    //             {\r\n    //                 label: 'First Dataset',\r\n    //                 data: [65, 59, 80, 81, 56, 55, 40],\r\n    //                 fill: false,\r\n    //                 backgroundColor: documentStyle.getPropertyValue('--bluegray-700'),\r\n    //                 borderColor: documentStyle.getPropertyValue('--bluegray-700'),\r\n    //                 tension: .4\r\n    //             },\r\n    //             {\r\n    //                 label: 'Second Dataset',\r\n    //                 data: [28, 48, 40, 19, 86, 27, 90],\r\n    //                 fill: false,\r\n    //                 backgroundColor: documentStyle.getPropertyValue('--green-600'),\r\n    //                 borderColor: documentStyle.getPropertyValue('--green-600'),\r\n    //                 tension: .4\r\n    //             }\r\n    //         ]\r\n    //     };\r\n\r\n    //     this.chartOptions = {\r\n    //         plugins: {\r\n    //             legend: {\r\n    //                 labels: {\r\n    //                     color: textColor\r\n    //                 }\r\n    //             }\r\n    //         },\r\n    //         scales: {\r\n    //             x: {\r\n    //                 ticks: {\r\n    //                     color: textColorSecondary\r\n    //                 },\r\n    //                 grid: {\r\n    //                     color: surfaceBorder,\r\n    //                     drawBorder: false\r\n    //                 }\r\n    //             },\r\n    //             y: {\r\n    //                 ticks: {\r\n    //                     color: textColorSecondary\r\n    //                 },\r\n    //                 grid: {\r\n    //                     color: surfaceBorder,\r\n    //                     drawBorder: false\r\n    //                 }\r\n    //             }\r\n    //         }\r\n    //     };\r\n    // }\r\n\r\n    ngOnDestroy() {\r\n        if (this.subscription) {\r\n            this.subscription.unsubscribe();\r\n        }\r\n    }\r\n}\r\n", "<div class=\"grid\">\r\n    <!-- Summary Cards -->\r\n    <div class=\"col-12 md:col-6 lg:col-6\">\r\n        <p-card class=\"h-full\">\r\n            <ng-template pTemplate=\"content\">\r\n                <div class=\"flex justify-content-between align-items-center\">\r\n                    <div class=\"flex-1\">\r\n                        <div class=\"flex align-items-center gap-2 mb-3\">\r\n                            <fa-icon icon=\"solar-panel\" class=\"text-primary text-lg\"></fa-icon>\r\n                            <span class=\"text-600 font-medium\">Active Energy Systems</span>\r\n                        </div>\r\n                        <div class=\"text-900 font-bold text-3xl\">{{stations.length}}</div>\r\n                        <p-tag value=\"Online\" severity=\"success\" class=\"mt-2\"></p-tag>\r\n                    </div>\r\n                    <div class=\"flex align-items-center justify-content-center bg-primary-50 border-round-lg\"\r\n                         style=\"width: 4rem; height: 4rem;\">\r\n                        <fa-icon icon=\"solar-panel\" class=\"text-primary text-2xl\"></fa-icon>\r\n                    </div>\r\n                </div>\r\n            </ng-template>\r\n        </p-card>\r\n    </div>\r\n\r\n    <div class=\"col-12 md:col-6 lg:col-6\">\r\n        <p-card class=\"h-full\">\r\n            <ng-template pTemplate=\"content\">\r\n                <div class=\"flex justify-content-between align-items-center\">\r\n                    <div class=\"flex-1\">\r\n                        <div class=\"flex align-items-center gap-2 mb-3\">\r\n                            <fa-icon icon=\"bolt\" class=\"text-orange-500 text-lg\"></fa-icon>\r\n                            <span class=\"text-600 font-medium\">Combined Power Performance</span>\r\n                        </div>\r\n                        <div class=\"text-900 font-bold text-3xl\">33% (4.124kW)</div>\r\n                        <p-tag value=\"Optimal\" severity=\"warning\" class=\"mt-2\"></p-tag>\r\n                    </div>\r\n                    <div class=\"flex align-items-center justify-content-center bg-orange-50 border-round-lg\"\r\n                         style=\"width: 4rem; height: 4rem;\">\r\n                        <fa-icon icon=\"bolt\" class=\"text-orange-500 text-2xl\"></fa-icon>\r\n                    </div>\r\n                </div>\r\n            </ng-template>\r\n        </p-card>\r\n    </div>\r\n\t \r\n\r\n    <!-- Stations List -->\r\n    <div class=\"col-12\">\r\n        <p-card>\r\n            <ng-template pTemplate=\"header\">\r\n                <div class=\"flex align-items-center justify-content-between p-3\">\r\n                    <div class=\"flex align-items-center gap-2\">\r\n                        <fa-icon icon=\"list\" class=\"text-primary text-xl\"></fa-icon>\r\n                        <h2 class=\"text-2xl font-semibold m-0\">Energy Stations</h2>\r\n                        <p-badge *ngIf=\"totalItems > 0\" [value]=\"totalItems\" severity=\"info\"></p-badge>\r\n                    </div>\r\n                    <div class=\"flex gap-2\">\r\n                        <p-button icon=\"pi pi-filter-slash\"\r\n                                  severity=\"secondary\"\r\n                                  size=\"small\"\r\n                                  pTooltip=\"Clear all filters\"\r\n                                  (click)=\"clearFilters()\">\r\n                        </p-button>\r\n                        <p-button icon=\"pi pi-refresh\"\r\n                                  severity=\"info\"\r\n                                  size=\"small\"\r\n                                  [loading]=\"isRefreshing\"\r\n                                  pTooltip=\"Refresh data\"\r\n                                  (click)=\"refreshData()\">\r\n                        </p-button>\r\n                    </div>\r\n                </div>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"content\">\r\n                <!-- Filters -->\r\n                <div class=\"flex flex-column md:flex-row md:justify-content-between gap-3 mb-4\">\r\n                    <div class=\"flex gap-2\">\r\n                        <p-dropdown [options]=\"sortOptionsCountry\"\r\n                                   placeholder=\"Filter by Provider\"\r\n                                   [(ngModel)]=\"selectedProvider\"\r\n                                   (onChange)=\"onProviderChange($event)\"\r\n                                   [showClear]=\"true\"\r\n                                   class=\"w-full md:w-auto\">\r\n                        </p-dropdown>\r\n                        <p-dropdown [options]=\"sortOptionsStatus\"\r\n                                   placeholder=\"Filter by Status\"\r\n                                   [(ngModel)]=\"selectedStatus\"\r\n                                   (onChange)=\"onStatusChange($event)\"\r\n                                   [showClear]=\"true\"\r\n                                   class=\"w-full md:w-auto\">\r\n                        </p-dropdown>\r\n                    </div>\r\n                    <span class=\"p-input-icon-left\">\r\n                        <i class=\"pi pi-search\"></i>\r\n                        <input type=\"search\"\r\n                               pInputText\r\n                               [(ngModel)]=\"searchTerm\"\r\n                               (input)=\"onSearchChange($event)\"\r\n                               placeholder=\"Search stations...\"\r\n                               class=\"w-full md:w-auto\">\r\n                    </span>\r\n                </div>\r\n\r\n                <!-- Stations Grid -->\r\n                <div class=\"grid\">\r\n                    <div *ngIf=\"paginatedStations.length === 0 && !isRefreshing\" class=\"col-12\">\r\n                        <div class=\"text-center py-6\">\r\n                            <fa-icon icon=\"inbox\" class=\"text-6xl text-300 mb-3\"></fa-icon>\r\n                            <p class=\"text-lg text-600\">No stations found matching your criteria.</p>\r\n                            <p-button label=\"Clear Filters\"\r\n                                      icon=\"pi pi-filter-slash\"\r\n                                      severity=\"secondary\"\r\n                                      size=\"small\"\r\n                                      (click)=\"clearFilters()\">\r\n                            </p-button>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div *ngFor=\"let station of paginatedStations\" class=\"col-12\">\r\n                        <p-card class=\"mb-3\"\r\n                                [ngClass]=\"'station-status-' + station.status?.toLowerCase()\"\r\n                                pRipple>\r\n                            <ng-template pTemplate=\"header\">\r\n                                <div class=\"bg-primary-50 p-3 border-round-top\">\r\n                                    <div class=\"flex align-items-center justify-content-between\">\r\n                                        <div class=\"flex align-items-center gap-2\">\r\n                                            <p-tag [value]=\"station.provider\"\r\n                                                   severity=\"info\"\r\n                                                   icon=\"pi pi-server\">\r\n                                            </p-tag>\r\n                                            <p-tag [value]=\"station.status?.toLowerCase() || 'unknown'\"\r\n                                                   [severity]=\"station.status?.toLowerCase() === 'online' ? 'success' : 'warning'\">\r\n                                            </p-tag>\r\n                                        </div>\r\n                                        <div class=\"flex align-items-center gap-2 text-600\">\r\n                                            <fa-icon icon=\"clock\" class=\"text-sm\"></fa-icon>\r\n                                            <span class=\"text-sm\">{{getStationLastUpdate(station.id)}}</span>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </ng-template>\r\n\r\n                            <ng-template pTemplate=\"content\">\r\n                                <div class=\"grid align-items-center\">\r\n                                    <!-- Station Info -->\r\n                                    <div class=\"col-12 md:col-6 lg:col-3\">\r\n                                        <div class=\"flex flex-column gap-2\">\r\n                                            <h3 class=\"text-xl font-bold m-0\">\r\n                                                <a [routerLink]=\"['/app/station',station.id]\"\r\n                                                   class=\"text-primary no-underline hover:underline\">\r\n                                                    {{station.name}}\r\n                                                </a>\r\n                                            </h3>\r\n                                            <div class=\"text-600 text-sm\">{{station.location || 'Location not specified'}}</div>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    <!-- Power & Irradiance -->\r\n                                    <div class=\"col-12 md:col-6 lg:col-2\">\r\n                                        <div class=\"text-center\">\r\n                                            <div class=\"flex align-items-center justify-content-center gap-1 mb-1\">\r\n                                                <fa-icon icon=\"bolt\" class=\"text-orange-500\"></fa-icon>\r\n                                                <span class=\"font-bold\">{{getStationsSumData(station.id)}}kW</span>\r\n                                            </div>\r\n                                            <div class=\"flex align-items-center justify-content-center gap-1\">\r\n                                                <fa-icon icon=\"sun\" class=\"text-yellow-500\"></fa-icon>\r\n                                                <span class=\"text-sm text-600\">{{station.irradiance}}kWh/m²</span>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    <!-- Chart -->\r\n                                    <div class=\"col-12 md:col-6 lg:col-2\">\r\n                                        <div class=\"text-center\">\r\n                                            <c-chart *ngIf=\"getStationsRealTimeData(station)\"\r\n                                                     [data]=\"getStationsRealTimeData(station)\"\r\n                                                     [options]=\"lineOptions\"\r\n                                                     type=\"line\"\r\n                                                     class=\"mx-auto\"\r\n                                                     height=\"50\"\r\n                                                     width=\"120\" />\r\n                                            <div *ngIf=\"!getStationsRealTimeData(station)\" class=\"text-center p-2\">\r\n                                                <fa-icon icon=\"chart-line\" class=\"text-300 text-2xl mb-1\"></fa-icon>\r\n                                                <p class=\"text-xs text-600 mt-1 mb-0\">No chart data</p>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    <!-- Equipment Badges -->\r\n                                    <div class=\"col-12 md:col-6 lg:col-3\">\r\n                                        <div class=\"flex flex-wrap gap-2 justify-content-center\">\r\n                                            <p-badge *ngIf=\"getStationsInverters(station.id) > 0\"\r\n                                                     [value]=\"'Inv: ' + getStationsInverters(station.id)\"\r\n                                                     severity=\"contrast\"\r\n                                                     size=\"small\">\r\n                                            </p-badge>\r\n                                            <p-badge *ngIf=\"station.mmpt && station.mmpt > 0\"\r\n                                                     [value]=\"'MMPT: ' + station.mmpt\"\r\n                                                     severity=\"success\"\r\n                                                     size=\"small\">\r\n                                            </p-badge>\r\n                                            <p-badge *ngIf=\"station.string && station.string > 0\"\r\n                                                     [value]=\"'Str: ' + station.string\"\r\n                                                     severity=\"info\"\r\n                                                     size=\"small\">\r\n                                            </p-badge>\r\n                                            <p-badge *ngIf=\"station.pvn && station.pvn > 0\"\r\n                                                     [value]=\"'PVN: ' + station.pvn\"\r\n                                                     severity=\"warning\"\r\n                                                     size=\"small\">\r\n                                            </p-badge>\r\n                                            <div *ngIf=\"!hasEquipmentData(station)\" class=\"text-center\">\r\n                                                <fa-icon icon=\"cog\" class=\"text-300 text-lg\"></fa-icon>\r\n                                                <p class=\"text-xs text-600 mt-1 mb-0\">No equipment data</p>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                    <!-- Weather -->\r\n                                    <div class=\"col-12 md:col-6 lg:col-2\">\r\n                                        <div class=\"text-center\">\r\n                                            <div class=\"flex align-items-center justify-content-center gap-2\">\r\n                                                <i class=\"pi pi-cloud text-blue-500 text-xl\"></i>\r\n                                                <span class=\"text-xl font-semibold\">{{station.temperature}}°C</span>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </ng-template>\r\n                        </p-card>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- Pagination -->\r\n                <div *ngIf=\"totalItems > itemsPerPage\" class=\"mt-4\">\r\n                    <p-paginator [rows]=\"itemsPerPage\"\r\n                                 [totalRecords]=\"totalItems\"\r\n                                 [first]=\"currentPage * itemsPerPage\"\r\n                                 [rowsPerPageOptions]=\"[5, 10, 20, 50]\"\r\n                                 [showCurrentPageReport]=\"true\"\r\n                                 currentPageReportTemplate=\"Showing {first} to {last} of {totalRecords} stations\"\r\n                                 (onPageChange)=\"onPageChange($event)\">\r\n                    </p-paginator>\r\n                </div>\r\n            </ng-template>\r\n        </p-card>\r\n    </div>\r\n</div>"], "mappings": "AAIA,SAAuBA,YAAY,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;ICCjCC,EAAA,CAAAC,cAAA,aAA6D;IAGjDD,EAAA,CAAAE,SAAA,iBAAmE;IACnEF,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAG,MAAA,4BAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEnEJ,EAAA,CAAAC,cAAA,cAAyC;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAClEJ,EAAA,CAAAE,SAAA,gBAA8D;IAClEF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cACwC;IACpCD,EAAA,CAAAE,SAAA,mBAAoE;IACxEF,EAAA,CAAAI,YAAA,EAAM;;;;IANuCJ,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA,CAAmB;;;;;IAepET,EAAA,CAAAC,cAAA,aAA6D;IAGjDD,EAAA,CAAAE,SAAA,kBAA+D;IAC/DF,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAG,MAAA,iCAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAExEJ,EAAA,CAAAC,cAAA,cAAyC;IAAAD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC5DJ,EAAA,CAAAE,SAAA,gBAA+D;IACnEF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cACwC;IACpCD,EAAA,CAAAE,SAAA,mBAAgE;IACpEF,EAAA,CAAAI,YAAA,EAAM;;;;;IAeFJ,EAAA,CAAAE,SAAA,kBAA+E;;;;IAA/CF,EAAA,CAAAU,UAAA,UAAAC,MAAA,CAAAC,UAAA,CAAoB;;;;;;IAJ5DZ,EAAA,CAAAC,cAAA,cAAiE;IAEzDD,EAAA,CAAAE,SAAA,kBAA4D;IAC5DF,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC3DJ,EAAA,CAAAa,UAAA,IAAAC,+CAAA,sBAA+E;IACnFd,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAAwB;IAKVD,EAAA,CAAAe,UAAA,mBAAAC,gEAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAnB,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IAClCtB,EAAA,CAAAI,YAAA,EAAW;IACXJ,EAAA,CAAAC,cAAA,mBAKkC;IAAxBD,EAAA,CAAAe,UAAA,mBAAAQ,gEAAA;MAAAvB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAM,MAAA,GAAAxB,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAG,MAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IACjCzB,EAAA,CAAAI,YAAA,EAAW;;;;IAfDJ,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAU,UAAA,SAAAgB,MAAA,CAAAd,UAAA,KAAoB;IAYpBZ,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAU,UAAA,YAAAgB,MAAA,CAAAC,YAAA,CAAwB;;;;;;IAwCtC3B,EAAA,CAAAC,cAAA,aAA4E;IAEpED,EAAA,CAAAE,SAAA,kBAA+D;IAC/DF,EAAA,CAAAC,cAAA,YAA4B;IAAAD,EAAA,CAAAG,MAAA,gDAAyC;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACzEJ,EAAA,CAAAC,cAAA,mBAImC;IAAzBD,EAAA,CAAAe,UAAA,mBAAAa,uEAAA;MAAA5B,EAAA,CAAAiB,aAAA,CAAAY,IAAA;MAAA,MAAAC,OAAA,GAAA9B,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAS,OAAA,CAAAR,YAAA,EAAc;IAAA,EAAC;IAClCtB,EAAA,CAAAI,YAAA,EAAW;;;;;IASPJ,EAAA,CAAAC,cAAA,cAAgD;IAGpCD,EAAA,CAAAE,SAAA,gBAGQ;IAIZF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAAoD;IAChDD,EAAA,CAAAE,SAAA,kBAAgD;IAChDF,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAG,MAAA,GAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAV1DJ,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAU,UAAA,UAAAqB,WAAA,CAAAC,QAAA,CAA0B;IAI1BhC,EAAA,CAAAK,SAAA,GAAoD;IAApDL,EAAA,CAAAU,UAAA,WAAAqB,WAAA,CAAAE,MAAA,kBAAAF,WAAA,CAAAE,MAAA,CAAAC,WAAA,iBAAoD,cAAAH,WAAA,CAAAE,MAAA,kBAAAF,WAAA,CAAAE,MAAA,CAAAC,WAAA;IAMrClC,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAM,iBAAA,CAAA6B,OAAA,CAAAC,oBAAA,CAAAL,WAAA,CAAAM,EAAA,EAAoC;;;;;IAsC1DrC,EAAA,CAAAE,SAAA,kBAMuB;;;;;IALdF,EAAA,CAAAU,UAAA,SAAA4B,OAAA,CAAAC,uBAAA,CAAAR,WAAA,EAAyC,YAAAO,OAAA,CAAAE,WAAA;;;;;IAMlDxC,EAAA,CAAAC,cAAA,cAAuE;IACnED,EAAA,CAAAE,SAAA,kBAAoE;IACpEF,EAAA,CAAAC,cAAA,YAAsC;IAAAD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAQ3DJ,EAAA,CAAAE,SAAA,kBAIU;;;;;IAHDF,EAAA,CAAAU,UAAA,oBAAA+B,OAAA,CAAAC,oBAAA,CAAAX,WAAA,CAAAM,EAAA,EAAoD;;;;;IAI7DrC,EAAA,CAAAE,SAAA,kBAIU;;;;IAHDF,EAAA,CAAAU,UAAA,qBAAAqB,WAAA,CAAAY,IAAA,CAAiC;;;;;IAI1C3C,EAAA,CAAAE,SAAA,kBAIU;;;;IAHDF,EAAA,CAAAU,UAAA,oBAAAqB,WAAA,CAAAa,MAAA,CAAkC;;;;;IAI3C5C,EAAA,CAAAE,SAAA,kBAIU;;;;IAHDF,EAAA,CAAAU,UAAA,oBAAAqB,WAAA,CAAAc,GAAA,CAA+B;;;;;IAIxC7C,EAAA,CAAAC,cAAA,cAA4D;IACxDD,EAAA,CAAAE,SAAA,kBAAuD;IACvDF,EAAA,CAAAC,cAAA,YAAsC;IAAAD,EAAA,CAAAG,MAAA,wBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;IAtE3EJ,EAAA,CAAAC,cAAA,cAAqC;IAOjBD,EAAA,CAAAG,MAAA,GACJ;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAERJ,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAG,MAAA,GAAgD;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAK5FJ,EAAA,CAAAC,cAAA,cAAsC;IAG1BD,EAAA,CAAAE,SAAA,mBAAuD;IACvDF,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAG,MAAA,IAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEvEJ,EAAA,CAAAC,cAAA,eAAkE;IAC9DD,EAAA,CAAAE,SAAA,mBAAsD;IACtDF,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAG,MAAA,IAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAM9EJ,EAAA,CAAAC,cAAA,eAAsC;IAE9BD,EAAA,CAAAa,UAAA,KAAAiC,qEAAA,sBAMuB,KAAAC,iEAAA;IAK3B/C,EAAA,CAAAI,YAAA,EAAM;IAIVJ,EAAA,CAAAC,cAAA,eAAsC;IAE9BD,EAAA,CAAAa,UAAA,KAAAmC,qEAAA,sBAIU,KAAAC,qEAAA,2BAAAC,qEAAA,2BAAAC,qEAAA,2BAAAC,iEAAA;IAoBdpD,EAAA,CAAAI,YAAA,EAAM;IAIVJ,EAAA,CAAAC,cAAA,eAAsC;IAG1BD,EAAA,CAAAE,SAAA,aAAiD;IACjDF,EAAA,CAAAC,cAAA,gBAAoC;IAAAD,EAAA,CAAAG,MAAA,IAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IA3EjEJ,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAU,UAAA,eAAAV,EAAA,CAAAqD,eAAA,KAAAC,GAAA,EAAAvB,WAAA,CAAAM,EAAA,EAA0C;IAEzCrC,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAuD,kBAAA,MAAAxB,WAAA,CAAAyB,IAAA,MACJ;IAE0BxD,EAAA,CAAAK,SAAA,GAAgD;IAAhDL,EAAA,CAAAM,iBAAA,CAAAyB,WAAA,CAAA0B,QAAA,6BAAgD;IASlDzD,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAuD,kBAAA,KAAAG,OAAA,CAAAC,kBAAA,CAAA5B,WAAA,CAAAM,EAAA,QAAoC;IAI7BrC,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAuD,kBAAA,KAAAxB,WAAA,CAAA6B,UAAA,gBAA4B;IAQrD5D,EAAA,CAAAK,SAAA,GAAsC;IAAtCL,EAAA,CAAAU,UAAA,SAAAgD,OAAA,CAAAnB,uBAAA,CAAAR,WAAA,EAAsC;IAO1C/B,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAAU,UAAA,UAAAgD,OAAA,CAAAnB,uBAAA,CAAAR,WAAA,EAAuC;IAUnC/B,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAU,UAAA,SAAAgD,OAAA,CAAAhB,oBAAA,CAAAX,WAAA,CAAAM,EAAA,MAA0C;IAK1CrC,EAAA,CAAAK,SAAA,GAAsC;IAAtCL,EAAA,CAAAU,UAAA,SAAAqB,WAAA,CAAAY,IAAA,IAAAZ,WAAA,CAAAY,IAAA,KAAsC;IAKtC3C,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAU,UAAA,SAAAqB,WAAA,CAAAa,MAAA,IAAAb,WAAA,CAAAa,MAAA,KAA0C;IAK1C5C,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAU,UAAA,SAAAqB,WAAA,CAAAc,GAAA,IAAAd,WAAA,CAAAc,GAAA,KAAoC;IAKxC7C,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAU,UAAA,UAAAgD,OAAA,CAAAG,gBAAA,CAAA9B,WAAA,EAAgC;IAYE/B,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAuD,kBAAA,KAAAxB,WAAA,CAAA+B,WAAA,YAAyB;;;;;IAzGzF9D,EAAA,CAAAC,cAAA,aAA8D;IAItDD,EAAA,CAAAa,UAAA,IAAAkD,0DAAA,yBAkBc,IAAAC,0DAAA;IAyFlBhE,EAAA,CAAAI,YAAA,EAAS;;;;IA7GDJ,EAAA,CAAAK,SAAA,GAA6D;IAA7DL,EAAA,CAAAU,UAAA,iCAAAqB,WAAA,CAAAE,MAAA,kBAAAF,WAAA,CAAAE,MAAA,CAAAC,WAAA,IAA6D;;;;;;;IAkH7ElC,EAAA,CAAAC,cAAA,cAAoD;IAOnCD,EAAA,CAAAe,UAAA,0BAAAkD,kFAAAC,MAAA;MAAAlE,EAAA,CAAAiB,aAAA,CAAAkD,IAAA;MAAA,MAAAC,OAAA,GAAApE,EAAA,CAAAoB,aAAA;MAAA,OAAgBpB,EAAA,CAAAqB,WAAA,CAAA+C,OAAA,CAAAC,YAAA,CAAAH,MAAA,CAAoB;IAAA,EAAC;IAClDlE,EAAA,CAAAI,YAAA,EAAc;;;;IAPDJ,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAU,UAAA,SAAA4D,OAAA,CAAAC,YAAA,CAAqB,iBAAAD,OAAA,CAAA1D,UAAA,WAAA0D,OAAA,CAAAE,WAAA,GAAAF,OAAA,CAAAC,YAAA,wBAAAvE,EAAA,CAAAyE,eAAA,IAAAC,GAAA;;;;;;IAhKtC1E,EAAA,CAAAC,cAAA,cAAgF;IAI7DD,EAAA,CAAAe,UAAA,2BAAA4D,2EAAAT,MAAA;MAAAlE,EAAA,CAAAiB,aAAA,CAAA2D,IAAA;MAAA,MAAAC,OAAA,GAAA7E,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAAAwD,OAAA,CAAAC,gBAAA,GAAAZ,MAAA;IAAA,EAA8B,sBAAAa,sEAAAb,MAAA;MAAAlE,EAAA,CAAAiB,aAAA,CAAA2D,IAAA;MAAA,MAAAI,OAAA,GAAAhF,EAAA,CAAAoB,aAAA;MAAA,OAClBpB,EAAA,CAAAqB,WAAA,CAAA2D,OAAA,CAAAC,gBAAA,CAAAf,MAAA,CAAwB;IAAA,EADN;IAIzClE,EAAA,CAAAI,YAAA,EAAa;IACbJ,EAAA,CAAAC,cAAA,qBAKoC;IAHzBD,EAAA,CAAAe,UAAA,2BAAAmE,2EAAAhB,MAAA;MAAAlE,EAAA,CAAAiB,aAAA,CAAA2D,IAAA;MAAA,MAAAO,OAAA,GAAAnF,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAAA8D,OAAA,CAAAC,cAAA,GAAAlB,MAAA;IAAA,EAA4B,sBAAAmB,sEAAAnB,MAAA;MAAAlE,EAAA,CAAAiB,aAAA,CAAA2D,IAAA;MAAA,MAAAU,OAAA,GAAAtF,EAAA,CAAAoB,aAAA;MAAA,OAChBpB,EAAA,CAAAqB,WAAA,CAAAiE,OAAA,CAAAC,cAAA,CAAArB,MAAA,CAAsB;IAAA,EADN;IAIvClE,EAAA,CAAAI,YAAA,EAAa;IAEjBJ,EAAA,CAAAC,cAAA,eAAgC;IAC5BD,EAAA,CAAAE,SAAA,YAA4B;IAC5BF,EAAA,CAAAC,cAAA,gBAKgC;IAHzBD,EAAA,CAAAe,UAAA,2BAAAyE,sEAAAtB,MAAA;MAAAlE,EAAA,CAAAiB,aAAA,CAAA2D,IAAA;MAAA,MAAAa,OAAA,GAAAzF,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAAAoE,OAAA,CAAAC,UAAA,GAAAxB,MAAA;IAAA,EAAwB,mBAAAyB,8DAAAzB,MAAA;MAAAlE,EAAA,CAAAiB,aAAA,CAAA2D,IAAA;MAAA,MAAAgB,OAAA,GAAA5F,EAAA,CAAAoB,aAAA;MAAA,OACfpB,EAAA,CAAAqB,WAAA,CAAAuE,OAAA,CAAAC,cAAA,CAAA3B,MAAA,CAAsB;IAAA,EADP;IAF/BlE,EAAA,CAAAI,YAAA,EAKgC;IAKxCJ,EAAA,CAAAC,cAAA,aAAkB;IACdD,EAAA,CAAAa,UAAA,IAAAiF,4CAAA,kBAWM,IAAAC,4CAAA;IAmHV/F,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAa,UAAA,KAAAmF,6CAAA,kBASM;;;;IAtKchG,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAU,UAAA,YAAAuF,MAAA,CAAAC,kBAAA,CAA8B,YAAAD,MAAA,CAAAnB,gBAAA;IAO9B9E,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAU,UAAA,YAAAuF,MAAA,CAAAE,iBAAA,CAA6B,YAAAF,MAAA,CAAAb,cAAA;IAYlCpF,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAU,UAAA,YAAAuF,MAAA,CAAAP,UAAA,CAAwB;IAS7B1F,EAAA,CAAAK,SAAA,GAAqD;IAArDL,EAAA,CAAAU,UAAA,SAAAuF,MAAA,CAAAG,iBAAA,CAAA3F,MAAA,WAAAwF,MAAA,CAAAtE,YAAA,CAAqD;IAalC3B,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAU,UAAA,YAAAuF,MAAA,CAAAG,iBAAA,CAAoB;IAoH3CpG,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAU,UAAA,SAAAuF,MAAA,CAAArF,UAAA,GAAAqF,MAAA,CAAA1B,YAAA,CAA+B;;;ADnNrD,OAAM,MAAO8B,cAAc;EAkDvBC,YAAmBC,aAA4B,EACnCC,eAAgC,EAChCC,gBAAkC,EAClCC,cAA8B,EAC9BC,EAAe,EACfC,MAAc,EACdC,YAA0B,EAC1BC,SAA2B;IAPpB,KAAAP,aAAa,GAAbA,aAAa;IACpB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,SAAS,GAATA,SAAS;IArDrB,KAAAtG,QAAQ,GAAc,EAAE;IACxB,KAAAuG,gBAAgB,GAAc,EAAE;IAChC,KAAAX,iBAAiB,GAAc,EAAE;IAQjC,KAAAF,kBAAkB,GAAiB,EAAE;IAErC,KAAAC,iBAAiB,GAAiB,EAAE;IAEpC,KAAAa,SAAS,GAAW,CAAC;IAErB,KAAAC,SAAS,GAAW,EAAE;IAEtB;IACA,KAAAvB,UAAU,GAAW,EAAE;IACvB,KAAAZ,gBAAgB,GAAW,EAAE;IAC7B,KAAAM,cAAc,GAAW,EAAE;IAE3B;IACA,KAAAZ,WAAW,GAAW,CAAC;IACvB,KAAAD,YAAY,GAAW,CAAC;IACxB,KAAA3D,UAAU,GAAW,CAAC;IAEtB;IACA,KAAAe,YAAY,GAAY,KAAK;IAE7B,KAAAuF,YAAY,GAAU,EAAE;IAExB,KAAAC,YAAY,GAAU,EAAE;IAExB,KAAAC,WAAW,GAAU,EAAE;IAMvB,KAAAC,YAAY,GAAmB,IAAIC,GAAG,EAAE;IACxC,KAAAC,eAAe,GAAmB,IAAID,GAAG,EAAE;IAC3C,KAAAE,eAAe,GAAsB,IAAIF,GAAG,EAAE;IAW1C,IAAI,CAACG,YAAY,GAAG,IAAI,CAAClB,aAAa,CAACmB,aAAa,CACnDC,IAAI,CAAC5H,YAAY,CAAC,EAAE,CAAC,CAAC,CACtB6H,SAAS,CAAEC,MAAM,IAAI;MACrB;IAAA,CACA,CAAC;EACN;EAEAC,QAAQA,CAAA;IACJ,IAAI,CAACC,gBAAgB,EAAE;IAEvB,IAAI,CAACC,UAAU,GAAG;MACdC,mBAAmB,EAAE,KAAK;MAC1BC,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE;;OAEZ;MACDC,MAAM,EAAE;QACNC,CAAC,EAAE;UACDF,OAAO,EAAE;SACV;QACDG,CAAC,EAAE;UACDH,OAAO,EAAE;;;KAGd;IAED,IAAI,CAAC5F,WAAW,GAAG;MACjBgG,UAAU,EAAE,IAAI;MAChBP,mBAAmB,EAAE,KAAK;MAC1BQ,WAAW,EAAE;QACXC,SAAS,EAAE,KAAK;QAChBC,IAAI,EAAE;OACP;MACDC,QAAQ,EAAE;QACRC,IAAI,EAAE;UACJC,OAAO,EAAE,GAAG;UACZC,WAAW,EAAE;SACd;QACDC,KAAK,EAAE;UACLC,MAAM,EAAE,CAAC;UACTC,WAAW,EAAE,CAAC;UACdC,SAAS,EAAE;;OAEd;MACDjB,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE;SACV;QACDgB,OAAO,EAAE;UACPC,OAAO,EAAE,IAAI;UACbV,IAAI,EAAE,OAAO;UACbD,SAAS,EAAE,KAAK;UAChBY,QAAQ,EAAE,SAAS;UACnBC,QAAQ,EAAE,IAAI;UACdC,eAAe,EAAE,oBAAoB;UACrCC,UAAU,EAAE,SAAS;UACrBC,SAAS,EAAE,SAAS;UACpBC,WAAW,EAAE,0BAA0B;UACvCZ,WAAW,EAAE,CAAC;UACda,YAAY,EAAE,CAAC;UACfC,aAAa,EAAE,KAAK;UACpBC,OAAO,EAAE,EAAE;UACXC,YAAY,EAAE,CAAC;UACfC,SAAS,EAAE,CAAC;UACZC,SAAS,EAAE;YACTC,IAAI,EAAE,EAAE;YACRC,MAAM,EAAE;WACT;UACDC,QAAQ,EAAE;YACRF,IAAI,EAAE;WACP;UACDG,SAAS,EAAE;YACTC,KAAK,EAAE,SAAAA,CAASC,OAAY;cAC1B,IAAIA,OAAO,IAAIA,OAAO,CAAC9J,MAAM,GAAG,CAAC,EAAE;gBACjC,OAAO,QAAQ,GAAG8J,OAAO,CAAC,CAAC,CAAC,CAACC,KAAK;;cAEpC,OAAO,aAAa;YACtB,CAAC;YACDA,KAAK,EAAE,SAAAA,CAASD,OAAY;cAC1B,MAAMC,KAAK,GAAGD,OAAO,CAACE,OAAO,CAACD,KAAK,IAAI,EAAE;cACzC,MAAME,KAAK,GAAGH,OAAO,CAACI,MAAM,CAACpC,CAAC;cAC9B,OAAO,GAAGiC,KAAK,KAAKE,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC,KAAK;YAC3C;;;OAGL;MACDvC,MAAM,EAAE;QACNC,CAAC,EAAE;UACDF,OAAO,EAAE,KAAK;UACdyC,IAAI,EAAE;YACJzC,OAAO,EAAE;;SAEZ;QACDG,CAAC,EAAE;UACDH,OAAO,EAAE,KAAK;UACdyC,IAAI,EAAE;YACJzC,OAAO,EAAE;;;OAGd;MACD0C,SAAS,EAAE;QACTC,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAE;;KAEX;EAKP;EAEAjD,gBAAgBA,CAAA;IACd,IAAI,CAACpG,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC8E,gBAAgB,CAACsB,gBAAgB,EAAE,CAACkD,IAAI,CAACC,aAAa,IAAG;MAC5D,IAAIA,aAAa,CAACzK,MAAM,GAAG,CAAC,EAAC;QAC3B,IAAI,CAAC+F,eAAe,CAAC2E,eAAe,EAAE,CAACF,IAAI,CAAC5D,YAAY,IAAG;UACzD,IAAI,CAACR,YAAY,CAACuE,WAAW,CAAC/D,YAAY,CAAC;UAC3C,IAAI,CAAC7G,QAAQ,GAAG6G,YAAY;UAC5BgE,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;UAC3BD,OAAO,CAACC,GAAG,CAACjE,YAAY,CAAC;UAEzB;UACA,IAAI,CAACkE,uBAAuB,EAAE;UAE9B;UACA,IAAI,CAACC,YAAY,EAAE;UAEnB;UACA,IAAI,CAACC,mBAAmB,EAAE;UAE1B,IAAI,CAAC9J,YAAY,GAAG,KAAK;QAC3B,CAAC,CAAC;OACH,MAAI;QACH,IAAI,CAACiF,MAAM,CAAC8E,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;;IAE5C,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAG;MACfP,OAAO,CAACO,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,IAAI,CAACjK,YAAY,GAAG,KAAK;IAC3B,CAAC,CAAC;EACJ;EAEA;EACA8J,mBAAmBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACjL,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;IAElD;IACA,IAAI,CAACD,QAAQ,CAACqL,OAAO,CAACC,OAAO,IAAG;MAC5B,IAAI,CAACC,eAAe,CAACD,OAAO,CAAC;IACjC,CAAC,CAAC;EACN;EAEA;EACAC,eAAeA,CAACD,OAAgB;IAC5B,IAAI,CAACA,OAAO,EAAE;IAEd,MAAME,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,kBAAkB,GAAG,IAAID,IAAI,CAACD,GAAG,CAACG,WAAW,EAAE,EAAEH,GAAG,CAACI,QAAQ,EAAE,EAAEJ,GAAG,CAACK,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,EAAE;IAC5G,MAAMC,gBAAgB,GAAG,IAAIN,IAAI,CAACD,GAAG,CAACG,WAAW,EAAE,EAAEH,GAAG,CAACI,QAAQ,EAAE,EAAEJ,GAAG,CAACK,OAAO,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,EAAE;IAE9G,IAAIE,OAAO,GAA2B;MAClCC,MAAM,EAAEX,OAAO,CAACY,SAAS;MACzBC,SAAS,EAAE,CAAC;MACZC,aAAa,EAAEV,kBAAkB;MACjCW,WAAW,EAAEN,gBAAgB;MAC7BO,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAElB,OAAO,CAACzJ;KACtB;IAED,IAAI,CAACmE,eAAe,CAACyG,sBAAsB,CAACT,OAAO,CAAC,CAACvB,IAAI,CAACiC,IAAI,IAAG;MAC7D,IAAIA,IAAI,IAAIA,IAAI,CAACA,IAAI,EAAE;QACnB;QACA,MAAMC,YAAY,GAAG,IAAI,CAACC,uBAAuB,CAACF,IAAI,CAACA,IAAI,CAAC;QAE5D,MAAMG,aAAa,GAAGC,gBAAgB,CAACC,QAAQ,CAACC,eAAe,CAAC;QAEhE,MAAMC,QAAQ,GAAG;UACbC,MAAM,EAAEP,YAAY,CAACQ,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAC9BA,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC/G,SAAS,CAACgH,kBAAkB,CAACF,CAAC,CAACG,QAAQ,CAAC,GAAG,EAAE,CACvE;UACDC,QAAQ,EAAE,CACN;YACIxD,KAAK,EAAE,cAAc;YACrB0C,IAAI,EAAEC,YAAY,CAACQ,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACK,WAAW,CAAC;YAC1CC,IAAI,EAAE,KAAK;YACX1E,eAAe,EAAE6D,aAAa,CAACc,gBAAgB,CAAC,eAAe,CAAC;YAChExE,WAAW,EAAE0D,aAAa,CAACc,gBAAgB,CAAC,eAAe,CAAC;YAC5DrF,OAAO,EAAE;WACZ,EACD;YACI0B,KAAK,EAAE,mBAAmB;YAC1B0C,IAAI,EAAEC,YAAY,CAACQ,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACQ,eAAe,CAAC;YAC9CF,IAAI,EAAE,KAAK;YACX1E,eAAe,EAAE6D,aAAa,CAACc,gBAAgB,CAAC,eAAe,CAAC;YAChExE,WAAW,EAAE0D,aAAa,CAACc,gBAAgB,CAAC,eAAe,CAAC;YAC5DrF,OAAO,EAAE;WACZ;SAER;QAED;QACA,IAAI,CAACzB,YAAY,CAACgH,GAAG,CAACvC,OAAO,CAACzJ,EAAE,EAAEoL,QAAQ,CAAC;QAC3C,IAAI,CAAClG,eAAe,CAAC8G,GAAG,CAACvC,OAAO,CAACzJ,EAAE,EAAE8K,YAAY,CAAC;QAClD,IAAI,CAAC3F,eAAe,CAAC6G,GAAG,CAACvC,OAAO,CAACzJ,EAAE,EAAE6K,IAAI,CAACoB,GAAG,CAAC;;IAEtD,CAAC,CAAC,CAAC3C,KAAK,CAACC,KAAK,IAAG;MACbP,OAAO,CAACO,KAAK,CAAC,kCAAkCE,OAAO,CAACzJ,EAAE,GAAG,EAAEuJ,KAAK,CAAC;IACzE,CAAC,CAAC;EACN;EAGA;EACArJ,uBAAuBA,CAACuJ,OAAgB;IACpC;IACA,IAAIA,OAAO,IAAIA,OAAO,CAACzJ,EAAE,IAAI,IAAI,CAACgF,YAAY,CAACkH,GAAG,CAACzC,OAAO,CAACzJ,EAAE,CAAC,EAAE;MAC5D,OAAO,IAAI,CAACgF,YAAY,CAACmH,GAAG,CAAC1C,OAAO,CAACzJ,EAAE,CAAC;;IAG5C;IACA,OAAO,IAAI;EACf;EAEAsB,kBAAkBA,CAACqJ,SAAgB;IACjC,OAAO,IAAI,CAACxF,eAAe,CAACgH,GAAG,CAACxB,SAAS,CAAC;EAC5C;EAEAtK,oBAAoBA,CAACsK,SAAgB;IACnC,IAAIE,IAAI,GAAG,IAAI,CAAC3F,eAAe,CAACiH,GAAG,CAACxB,SAAS,CAAC;IAC9C,IAAI,CAACE,IAAI,IAAIA,IAAI,CAACzM,MAAM,KAAK,CAAC,EAAE;MAC9B,OAAO,CAAC;KACT,MAEC,OAAO,IAAIgO,GAAG,CAACvB,IAAI,CAACS,GAAG,CAACe,IAAI,IAAIA,IAAI,CAAClL,IAAI,CAAC,CAAC,CAAC0G,IAAI;EACpD;EAEA9H,oBAAoBA,CAAC4K,SAAiB;IACpC,MAAME,IAAI,GAAG,IAAI,CAAC3F,eAAe,CAACiH,GAAG,CAACxB,SAAS,CAAC;IAChD,IAAI,CAACE,IAAI,IAAIA,IAAI,CAACzM,MAAM,KAAK,CAAC,EAAE,OAAO,GAAG;IAE1C,MAAMkO,MAAM,GAAGzB,IAAI,CAAC0B,MAAM,CAAC,CAACC,WAAW,EAAEC,OAAO,KAAI;MAClD,OAAO,IAAI7C,IAAI,CAAC6C,OAAO,CAACf,QAAQ,CAAC,CAACgB,OAAO,EAAE,GAAG,IAAI9C,IAAI,CAAC4C,WAAW,CAACd,QAAQ,CAAC,CAACgB,OAAO,EAAE,GAClFD,OAAO,GACPD,WAAW;IACjB,CAAC,CAAC;IAEF,OAAO,IAAI5C,IAAI,CAAC0C,MAAM,CAACZ,QAAQ,CAAC,CAACiB,cAAc,CAAC,OAAO,EAAE;MACvDC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE,SAAS;MACjBC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE;KACX,CAAC;EACJ;EAGAC,YAAYA,CAACC,KAAU;IACnB,MAAM9E,KAAK,GAAG8E,KAAK,CAAC9E,KAAK;IAEzB,IAAIA,KAAK,CAAC+E,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;MAC1B,IAAI,CAACzI,SAAS,GAAG,CAAC,CAAC;MACnB,IAAI,CAACC,SAAS,GAAGyD,KAAK,CAACgF,SAAS,CAAC,CAAC,EAAEhF,KAAK,CAACjK,MAAM,CAAC;KACpD,MAAM;MACH,IAAI,CAACuG,SAAS,GAAG,CAAC;MAClB,IAAI,CAACC,SAAS,GAAGyD,KAAK;;EAE9B;EAEAiF,QAAQA,CAACC,EAAY,EAAEJ,KAAY;IAC/BI,EAAE,CAACC,MAAM,CAAEL,KAAK,CAACM,MAA2B,CAACpF,KAAK,CAAC;EACvD;EAEA;EACAa,uBAAuBA,CAAA;IACnB;IACA,MAAMwE,SAAS,GAAG,CAAC,GAAG,IAAItB,GAAG,CAAC,IAAI,CAACjO,QAAQ,CAACmN,GAAG,CAAC7B,OAAO,IAAIA,OAAO,CAAC9J,QAAQ,CAAC,CAAC,CAAC;IAC9E,IAAI,CAACkE,kBAAkB,GAAG6J,SAAS,CAACpC,GAAG,CAAC3L,QAAQ,KAAK;MACjDwI,KAAK,EAAExI,QAAQ;MACf0I,KAAK,EAAE1I;KACV,CAAC,CAAC;IAEH;IACA,MAAMgO,QAAQ,GAAG,CAAC,GAAG,IAAIvB,GAAG,CAAC,IAAI,CAACjO,QAAQ,CAACmN,GAAG,CAAC7B,OAAO,IAAIA,OAAO,CAAC7J,MAAM,CAAC,CAAC,CAAC;IAC3E,IAAI,CAACkE,iBAAiB,GAAG6J,QAAQ,CAACrC,GAAG,CAAC1L,MAAM,KAAK;MAC7CuI,KAAK,EAAEvI,MAAM;MACbyI,KAAK,EAAEzI;KACV,CAAC,CAAC;EACP;EAEA;EACAuJ,YAAYA,CAAA;IACR,IAAIyE,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACzP,QAAQ,CAAC;IAEjC;IACA,IAAI,IAAI,CAACkF,UAAU,EAAE;MACjB,MAAMwK,WAAW,GAAG,IAAI,CAACxK,UAAU,CAACxD,WAAW,EAAE;MACjD+N,QAAQ,GAAGA,QAAQ,CAACJ,MAAM,CAAC/D,OAAO,IAC9BA,OAAO,CAACtI,IAAI,CAACtB,WAAW,EAAE,CAACiO,QAAQ,CAACD,WAAW,CAAC,IAChDpE,OAAO,CAAC9J,QAAQ,CAACE,WAAW,EAAE,CAACiO,QAAQ,CAACD,WAAW,CAAC,IACnDpE,OAAO,CAACrI,QAAQ,IAAIqI,OAAO,CAACrI,QAAQ,CAACvB,WAAW,EAAE,CAACiO,QAAQ,CAACD,WAAW,CAAE,CAC7E;;IAGL;IACA,IAAI,IAAI,CAACpL,gBAAgB,EAAE;MACvBmL,QAAQ,GAAGA,QAAQ,CAACJ,MAAM,CAAC/D,OAAO,IAAIA,OAAO,CAAC9J,QAAQ,KAAK,IAAI,CAAC8C,gBAAgB,CAAC;;IAGrF;IACA,IAAI,IAAI,CAACM,cAAc,EAAE;MACrB6K,QAAQ,GAAGA,QAAQ,CAACJ,MAAM,CAAC/D,OAAO,IAAIA,OAAO,CAAC7J,MAAM,KAAK,IAAI,CAACmD,cAAc,CAAC;;IAGjF,IAAI,CAAC2B,gBAAgB,GAAGkJ,QAAQ;IAChC,IAAI,CAACrP,UAAU,GAAGqP,QAAQ,CAACxP,MAAM;IACjC,IAAI,CAAC+D,WAAW,GAAG,CAAC,CAAC,CAAC;IACtB,IAAI,CAAC4L,gBAAgB,EAAE;EAC3B;EAEA;EACAA,gBAAgBA,CAAA;IACZ,MAAMC,UAAU,GAAG,IAAI,CAAC7L,WAAW,GAAG,IAAI,CAACD,YAAY;IACvD,MAAM+L,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC9L,YAAY;IAC/C,IAAI,CAAC6B,iBAAiB,GAAG,IAAI,CAACW,gBAAgB,CAACwJ,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC9E;EAEA;EACAzK,cAAcA,CAAC2J,KAAY;IACvB,IAAI,CAAC9J,UAAU,GAAI8J,KAAK,CAACM,MAA2B,CAACpF,KAAK;IAC1D,IAAI,CAACc,YAAY,EAAE;EACvB;EAEA;EACAvG,gBAAgBA,CAACuK,KAAU;IACvB,IAAI,CAAC1K,gBAAgB,GAAG0K,KAAK,CAAC9E,KAAK,IAAI,EAAE;IACzC,IAAI,CAACc,YAAY,EAAE;EACvB;EAEA;EACAjG,cAAcA,CAACiK,KAAU;IACrB,IAAI,CAACpK,cAAc,GAAGoK,KAAK,CAAC9E,KAAK,IAAI,EAAE;IACvC,IAAI,CAACc,YAAY,EAAE;EACvB;EAEA;EACAnH,YAAYA,CAACmL,KAAU;IACnB,IAAI,CAAChL,WAAW,GAAGgL,KAAK,CAACgB,IAAI;IAC7B,IAAI,CAACjM,YAAY,GAAGiL,KAAK,CAACiB,IAAI;IAC9B,IAAI,CAACL,gBAAgB,EAAE;EAC3B;EAEA;EACA3O,WAAWA,CAAA;IACP,IAAI,CAACsG,gBAAgB,EAAE;EAC3B;EAEA;EACAzG,YAAYA,CAAA;IACR,IAAI,CAACoE,UAAU,GAAG,EAAE;IACpB,IAAI,CAACZ,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACM,cAAc,GAAG,EAAE;IACxB,IAAI,CAACoG,YAAY,EAAE;EACvB;EAEA;EACA3H,gBAAgBA,CAACiI,OAAgB;IAC7B,MAAM4E,SAAS,GAAG,IAAI,CAAChO,oBAAoB,CAACoJ,OAAO,CAACzJ,EAAE,IAAI,EAAE,CAAC;IAC7D,OAAOqO,SAAS,GAAG,CAAC,IACZ5E,OAAO,CAACnJ,IAAI,IAAImJ,OAAO,CAACnJ,IAAI,GAAG,CAAE,IACjCmJ,OAAO,CAAClJ,MAAM,IAAIkJ,OAAO,CAAClJ,MAAM,GAAG,CAAE,IACrCkJ,OAAO,CAACjJ,GAAG,IAAIiJ,OAAO,CAACjJ,GAAG,GAAG,CAAE;EAC3C;EAEQuK,uBAAuBA,CAACF,IAAW;IACvC,MAAMlB,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAM0E,WAAW,GAAG3E,GAAG,CAAC+C,OAAO,EAAE;IAEjC,OAAO7B,IAAI,CAAC2C,MAAM,CAACnB,IAAI,IAAG;MACtB,MAAMkC,YAAY,GAAG,IAAI3E,IAAI,CAACyC,IAAI,CAACX,QAAQ,CAAC;MAC5C,MAAM8C,QAAQ,GAAGD,YAAY,CAAC7B,OAAO,EAAE;MAEvC;MACA,OAAO8B,QAAQ,IAAIF,WAAW;IAClC,CAAC,CAAC;EACN;EAGAG,OAAOA,CAAA;IACH,IAAI,CAACC,MAAM,GAAG,2FAA2F;EAC7G;EAEA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEAC,WAAWA,CAAA;IACP,IAAI,IAAI,CAACvJ,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAACwJ,WAAW,EAAE;;EAEvC;EAAC,QAAAC,CAAA,G;qBAjgBQ7K,cAAc,EAAArG,EAAA,CAAAmR,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAArR,EAAA,CAAAmR,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAvR,EAAA,CAAAmR,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAzR,EAAA,CAAAmR,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA3R,EAAA,CAAAmR,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAA7R,EAAA,CAAAmR,iBAAA,CAAAW,EAAA,CAAAC,MAAA,GAAA/R,EAAA,CAAAmR,iBAAA,CAAAa,EAAA,CAAAC,YAAA,GAAAjS,EAAA,CAAAmR,iBAAA,CAAAe,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAd/L,cAAc;IAAAgM,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCvB3B3S,EAAA,CAAAC,cAAA,aAAkB;QAIND,EAAA,CAAAa,UAAA,IAAAgS,qCAAA,0BAec;QAClB7S,EAAA,CAAAI,YAAA,EAAS;QAGbJ,EAAA,CAAAC,cAAA,aAAsC;QAE9BD,EAAA,CAAAa,UAAA,IAAAiS,qCAAA,0BAec;QAClB9S,EAAA,CAAAI,YAAA,EAAS;QAKbJ,EAAA,CAAAC,cAAA,aAAoB;QAEZD,EAAA,CAAAa,UAAA,IAAAkS,qCAAA,yBAuBc,KAAAC,sCAAA;QA8KlBhT,EAAA,CAAAI,YAAA,EAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}