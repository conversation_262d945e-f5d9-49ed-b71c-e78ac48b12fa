# Timezone Fix Testing Guide

## Problem Description
- **Local PC**: Athens timezone (UTC+2/+3)
- **Production Server**: Canada timezone (UTC-8)
- **Issue**: Timestamps were being converted incorrectly, showing wrong times in charts

## Changes Made

### 1. ApiService.cs - Data Saving Fix
**Before:**
```csharp
timestamp = ((DateTimeOffset)data.TimeStamp.ToUniversalTime()).ToUnixTimeMilliseconds(),
```

**After:**
```csharp
// Convert timestamp to Athens timezone consistently
TimeZoneInfo athensTimeZone = TimeZoneInfo.FindSystemTimeZoneById("GTB Standard Time");

// First, treat the timestamp as UTC (since it comes from the data source)
var utcDateTime = DateTime.SpecifyKind(data.TimeStamp, DateTimeKind.Utc);
var athensDateTime = TimeZoneInfo.ConvertTimeFromUtc(utcDateTime, athensTimeZone);

// Store as Athens time, use UTC for timestamp
dateTime = athensDateTime,
timestamp = ((DateTimeOffset)utcDateTime).ToUnixTimeMilliseconds(),
```

### 2. ApiService.cs - Data Reading Fix
**Before:**
```csharp
dateTime = DateTimeOffset.FromUnixTimeMilliseconds(g.Key).UtcDateTime,
```

**After:**
```csharp
dateTime = ConvertUnixTimestampToAthensDateTime(g.Key),
```

### 3. Added Helper Method
```csharp
private DateTime ConvertUnixTimestampToAthensDateTime(long unixTimestampMs)
{
    var utcDateTime = DateTimeOffset.FromUnixTimeMilliseconds(unixTimestampMs).UtcDateTime;
    TimeZoneInfo athensTimeZone = TimeZoneInfo.FindSystemTimeZoneById("GTB Standard Time");
    var athensDateTime = TimeZoneInfo.ConvertTimeFromUtc(utcDateTime, athensTimeZone);
    return athensDateTime;
}
```

## Testing Steps

### 1. Build and Deploy API
```bash
dotnet build
# Deploy to production server
```

### 2. Test Data Collection
- Check that new data is being saved with correct timestamps
- Verify that Unix timestamps are consistent

### 3. Test Chart Display
- Open charts in UI
- Verify that times shown match Athens timezone
- Check that tooltips show correct Athens time

### 4. Verification Points
- **Chart Labels**: Should show Athens time (e.g., 16:15 for 4:15 PM Athens)
- **Tooltips**: Should show full Athens date/time
- **Database**: Unix timestamps should be consistent regardless of server timezone

## Expected Results
- Charts will show data at correct Athens times
- No more +3 hour offset issues
- Consistent behavior regardless of server location
- Unix timestamp: 1750338943000 should display as correct Athens time

## Rollback Plan
If issues occur, revert the changes in ApiService.cs:
1. Restore original timestamp calculation
2. Remove helper method
3. Redeploy
