{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { TimesCircleIcon } from 'primeng/icons/timescircle';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { UniqueComponentId } from 'primeng/utils';\nconst _c0 = [\"inputtext\"];\nconst _c1 = [\"container\"];\nfunction Chips_li_3_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Chips_li_3_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r8.field ? ctx_r8.resolveFieldData(item_r4, ctx_r8.field) : item_r4);\n  }\n}\nfunction Chips_li_3_ng_container_4_TimesCircleIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"TimesCircleIcon\", 15);\n    i0.ɵɵlistener(\"click\", function Chips_li_3_ng_container_4_TimesCircleIcon_1_Template_TimesCircleIcon_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r15);\n      const i_r5 = i0.ɵɵnextContext(2).index;\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.removeItem($event, i_r5));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-chips-token-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"removeTokenIcon\")(\"aria-hidden\", true);\n  }\n}\nfunction Chips_li_3_ng_container_4_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Chips_li_3_ng_container_4_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Chips_li_3_ng_container_4_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Chips_li_3_ng_container_4_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 16);\n    i0.ɵɵlistener(\"click\", function Chips_li_3_ng_container_4_span_2_Template_span_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const i_r5 = i0.ɵɵnextContext(2).index;\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.removeItem($event, i_r5));\n    });\n    i0.ɵɵtemplate(1, Chips_li_3_ng_container_4_span_2_1_Template, 1, 0, null, 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(3);\n    i0.ɵɵattribute(\"data-pc-section\", \"removeTokenIcon\")(\"aria-hidden\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r12.removeTokenIconTemplate);\n  }\n}\nfunction Chips_li_3_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Chips_li_3_ng_container_4_TimesCircleIcon_1_Template, 1, 3, \"TimesCircleIcon\", 13)(2, Chips_li_3_ng_container_4_span_2_Template, 2, 3, \"span\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.removeTokenIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.removeTokenIconTemplate);\n  }\n}\nconst _c2 = a1 => ({\n  \"p-chips-token\": true,\n  \"p-focus\": a1\n});\nconst _c3 = a0 => ({\n  $implicit: a0\n});\nfunction Chips_li_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 8, 9);\n    i0.ɵɵlistener(\"click\", function Chips_li_3_Template_li_click_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r22);\n      const item_r4 = restoredCtx.$implicit;\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.onItemClick($event, item_r4));\n    });\n    i0.ɵɵtemplate(2, Chips_li_3_ng_container_2_Template, 1, 0, \"ng-container\", 10)(3, Chips_li_3_span_3_Template, 2, 2, \"span\", 11)(4, Chips_li_3_ng_container_4_Template, 3, 2, \"ng-container\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c2, ctx_r1.focusedIndex === i_r5));\n    i0.ɵɵattribute(\"id\", ctx_r1.id + \"_chips_item_\" + i_r5)(\"ariaLabel\", item_r4)(\"aria-selected\", true)(\"aria-setsize\", ctx_r1.value.length)(\"aria-pointset\", i_r5 + 1)(\"data-p-focused\", ctx_r1.focusedIndex === i_r5)(\"data-pc-section\", \"token\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(14, _c3, item_r4));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.itemTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.disabled);\n  }\n}\nfunction Chips_li_7_TimesIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"TimesIcon\", 15);\n    i0.ɵɵlistener(\"click\", function Chips_li_7_TimesIcon_1_Template_TimesIcon_click_0_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.clear());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-chips-clear-icon\");\n  }\n}\nfunction Chips_li_7_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Chips_li_7_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Chips_li_7_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Chips_li_7_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵlistener(\"click\", function Chips_li_7_span_2_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r29.clear());\n    });\n    i0.ɵɵtemplate(1, Chips_li_7_span_2_1_Template, 1, 0, null, 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r24.clearIconTemplate);\n  }\n}\nfunction Chips_li_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtemplate(1, Chips_li_7_TimesIcon_1_Template, 1, 1, \"TimesIcon\", 13)(2, Chips_li_7_span_2_Template, 2, 1, \"span\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.clearIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.clearIconTemplate);\n  }\n}\nconst _c4 = (a1, a2, a3, a4) => ({\n  \"p-chips p-component p-input-wrapper\": true,\n  \"p-disabled\": a1,\n  \"p-focus\": a2,\n  \"p-inputwrapper-filled\": a3,\n  \"p-inputwrapper-focus\": a4\n});\nconst _c5 = () => ({\n  \"p-inputtext p-chips-multiple-container\": true\n});\nconst _c6 = a0 => ({\n  \"p-chips-clearable\": a0\n});\nconst CHIPS_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => Chips),\n  multi: true\n};\n/**\n * Chips groups a collection of contents in tabs.\n * @group Components\n */\nlet Chips = /*#__PURE__*/(() => {\n  class Chips {\n    document;\n    el;\n    cd;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    disabled;\n    /**\n     * Name of the property to display on a chip.\n     * @group Props\n     */\n    field;\n    /**\n     * Advisory information to display on input.\n     * @group Props\n     */\n    placeholder;\n    /**\n     * Maximum number of entries allowed.\n     * @group Props\n     */\n    max;\n    /**\n     * Defines a string that labels the input for accessibility.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    tabindex;\n    /**\n     * Identifier of the focus input to match a label defined for the component.\n     * @group Props\n     */\n    inputId;\n    /**\n     * Whether to allow duplicate values or not.\n     * @group Props\n     */\n    allowDuplicate = true;\n    /**\n     * Inline style of the input field.\n     * @group Props\n     */\n    inputStyle;\n    /**\n     * Style class of the input field.\n     * @group Props\n     */\n    inputStyleClass;\n    /**\n     * Whether to add an item on tab key press.\n     * @group Props\n     */\n    addOnTab;\n    /**\n     * Whether to add an item when the input loses focus.\n     * @group Props\n     */\n    addOnBlur;\n    /**\n     * Separator char to add an item when pressed in addition to the enter key.\n     * @group Props\n     */\n    separator;\n    /**\n     * When enabled, a clear icon is displayed to clear the value.\n     * @group Props\n     */\n    showClear = false;\n    /**\n     * Callback to invoke on chip add.\n     * @param {ChipsAddEvent} event - Custom chip add event.\n     * @group Emits\n     */\n    onAdd = new EventEmitter();\n    /**\n     * Callback to invoke on chip remove.\n     * @param {ChipsRemoveEvent} event - Custom chip remove event.\n     * @group Emits\n     */\n    onRemove = new EventEmitter();\n    /**\n     * Callback to invoke on focus of input field.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onFocus = new EventEmitter();\n    /**\n     * Callback to invoke on blur of input field.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onBlur = new EventEmitter();\n    /**\n     * Callback to invoke on chip clicked.\n     * @param {ChipsClickEvent} event - Custom chip click event.\n     * @group Emits\n     */\n    onChipClick = new EventEmitter();\n    /**\n     * Callback to invoke on clear token clicked.\n     * @group Emits\n     */\n    onClear = new EventEmitter();\n    inputViewChild;\n    containerViewChild;\n    templates;\n    itemTemplate;\n    removeTokenIconTemplate;\n    clearIconTemplate;\n    value;\n    onModelChange = () => {};\n    onModelTouched = () => {};\n    valueChanged;\n    id = UniqueComponentId();\n    focused;\n    focusedIndex;\n    filled;\n    get focusedOptionId() {\n      return this.focusedIndex !== null ? `${this.id}_chips_item_${this.focusedIndex}` : null;\n    }\n    get isMaxedOut() {\n      return this.max && this.value && this.max === this.value.length;\n    }\n    constructor(document, el, cd) {\n      this.document = document;\n      this.el = el;\n      this.cd = cd;\n    }\n    ngAfterContentInit() {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'item':\n            this.itemTemplate = item.template;\n            break;\n          case 'removetokenicon':\n            this.removeTokenIconTemplate = item.template;\n            break;\n          case 'clearicon':\n            this.clearIconTemplate = item.template;\n            break;\n          default:\n            this.itemTemplate = item.template;\n            break;\n        }\n      });\n      this.updateFilledState();\n    }\n    onWrapperClick() {\n      this.inputViewChild?.nativeElement.focus();\n    }\n    onContainerFocus() {\n      this.focused = true;\n    }\n    onContainerBlur() {\n      this.focusedIndex = -1;\n      this.focused = false;\n    }\n    onContainerKeyDown(event) {\n      switch (event.code) {\n        case 'ArrowLeft':\n          this.onArrowLeftKeyOn();\n          break;\n        case 'ArrowRight':\n          this.onArrowRightKeyOn();\n          break;\n        case 'Backspace':\n          this.onBackspaceKeyOn(event);\n          break;\n        default:\n          break;\n      }\n    }\n    onArrowLeftKeyOn() {\n      if (this.inputViewChild.nativeElement.value.length === 0 && this.value && this.value.length > 0) {\n        this.focusedIndex = this.focusedIndex === null ? this.value.length - 1 : this.focusedIndex - 1;\n        if (this.focusedIndex < 0) this.focusedIndex = 0;\n      }\n    }\n    onArrowRightKeyOn() {\n      if (this.inputViewChild.nativeElement.value.length === 0 && this.value && this.value.length > 0) {\n        if (this.focusedIndex === this.value.length - 1) {\n          this.focusedIndex = null;\n          this.inputViewChild?.nativeElement.focus();\n        } else {\n          this.focusedIndex++;\n        }\n      }\n    }\n    onBackspaceKeyOn(event) {\n      if (this.focusedIndex !== null) {\n        this.removeItem(event, this.focusedIndex);\n      }\n    }\n    onInput() {\n      this.updateFilledState();\n      this.focusedIndex = null;\n    }\n    onPaste(event) {\n      if (!this.disabled) {\n        if (this.separator) {\n          const pastedData = (event.clipboardData || this.document.defaultView['clipboardData']).getData('Text');\n          pastedData.split(this.separator).forEach(val => {\n            this.addItem(event, val, true);\n          });\n          this.inputViewChild.nativeElement.value = '';\n        }\n        this.updateFilledState();\n      }\n    }\n    updateFilledState() {\n      if (!this.value || this.value.length === 0) {\n        this.filled = this.inputViewChild && this.inputViewChild.nativeElement && this.inputViewChild.nativeElement.value != '';\n      } else {\n        this.filled = true;\n      }\n    }\n    onItemClick(event, item) {\n      this.onChipClick.emit({\n        originalEvent: event,\n        value: item\n      });\n    }\n    writeValue(value) {\n      this.value = value;\n      this.updateMaxedOut();\n      this.updateFilledState();\n      this.cd.markForCheck();\n    }\n    registerOnChange(fn) {\n      this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n      this.onModelTouched = fn;\n    }\n    setDisabledState(val) {\n      this.disabled = val;\n      this.cd.markForCheck();\n    }\n    resolveFieldData(data, field) {\n      if (data && field) {\n        if (field.indexOf('.') == -1) {\n          return data[field];\n        } else {\n          let fields = field.split('.');\n          let value = data;\n          for (var i = 0, len = fields.length; i < len; ++i) {\n            value = value[fields[i]];\n          }\n          return value;\n        }\n      } else {\n        return null;\n      }\n    }\n    onInputFocus(event) {\n      this.focused = true;\n      this.focusedIndex = null;\n      this.onFocus.emit(event);\n    }\n    onInputBlur(event) {\n      this.focused = false;\n      this.focusedIndex = null;\n      if (this.addOnBlur && this.inputViewChild.nativeElement.value) {\n        this.addItem(event, this.inputViewChild.nativeElement.value, false);\n      }\n      this.onModelTouched();\n      this.onBlur.emit(event);\n    }\n    removeItem(event, index) {\n      if (this.disabled) {\n        return;\n      }\n      let removedItem = this.value[index];\n      this.value = this.value.filter((val, i) => i != index);\n      this.focusedIndex = null;\n      this.inputViewChild.nativeElement.focus();\n      this.onModelChange(this.value);\n      this.onRemove.emit({\n        originalEvent: event,\n        value: removedItem\n      });\n      this.updateFilledState();\n      this.updateMaxedOut();\n    }\n    addItem(event, item, preventDefault) {\n      this.value = this.value || [];\n      if (item && item.trim().length) {\n        if ((this.allowDuplicate || this.value.indexOf(item) === -1) && !this.isMaxedOut) {\n          this.value = [...this.value, item];\n          this.onModelChange(this.value);\n          this.onAdd.emit({\n            originalEvent: event,\n            value: item\n          });\n        }\n      }\n      this.updateFilledState();\n      this.updateMaxedOut();\n      this.inputViewChild.nativeElement.value = '';\n      if (preventDefault) {\n        event.preventDefault();\n      }\n    }\n    clear() {\n      this.value = null;\n      this.updateFilledState();\n      this.onModelChange(this.value);\n      this.updateMaxedOut();\n      this.onClear.emit();\n    }\n    onKeyDown(event) {\n      const inputValue = event.target.value;\n      switch (event.code) {\n        case 'Backspace':\n          if (inputValue.length === 0 && this.value && this.value.length > 0) {\n            if (this.focusedIndex !== null) {\n              this.removeItem(event, this.focusedIndex);\n            } else this.removeItem(event, this.value.length - 1);\n          }\n          break;\n        case 'Enter':\n          if (inputValue && inputValue.trim().length && !this.isMaxedOut) {\n            this.addItem(event, inputValue, true);\n          }\n          break;\n        case 'ArrowLeft':\n          if (inputValue.length === 0 && this.value && this.value.length > 0) {\n            this.containerViewChild?.nativeElement.focus();\n          }\n          break;\n        case 'ArrowRight':\n          event.stopPropagation();\n          break;\n        default:\n          if (this.separator) {\n            if (this.separator === event.key || event.key.match(this.separator)) {\n              this.addItem(event, inputValue, true);\n            }\n          }\n          break;\n      }\n    }\n    updateMaxedOut() {\n      if (this.inputViewChild && this.inputViewChild.nativeElement) {\n        if (this.isMaxedOut) {\n          // Calling `blur` is necessary because firefox does not call `onfocus` events\n          // for disabled inputs, unlike chromium browsers.\n          this.inputViewChild.nativeElement.blur();\n          this.inputViewChild.nativeElement.disabled = true;\n        } else {\n          if (this.disabled) {\n            this.inputViewChild.nativeElement.blur();\n          }\n          this.inputViewChild.nativeElement.disabled = this.disabled || false;\n        }\n      }\n    }\n    static ɵfac = function Chips_Factory(t) {\n      return new (t || Chips)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Chips,\n      selectors: [[\"p-chips\"]],\n      contentQueries: function Chips_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function Chips_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inputViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\", \"p-inputwrapper\"],\n      hostVars: 6,\n      hostBindings: function Chips_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"p-inputwrapper-filled\", ctx.filled)(\"p-inputwrapper-focus\", ctx.focused)(\"p-chips-clearable\", ctx.showClear);\n        }\n      },\n      inputs: {\n        style: \"style\",\n        styleClass: \"styleClass\",\n        disabled: \"disabled\",\n        field: \"field\",\n        placeholder: \"placeholder\",\n        max: \"max\",\n        ariaLabel: \"ariaLabel\",\n        ariaLabelledBy: \"ariaLabelledBy\",\n        tabindex: \"tabindex\",\n        inputId: \"inputId\",\n        allowDuplicate: \"allowDuplicate\",\n        inputStyle: \"inputStyle\",\n        inputStyleClass: \"inputStyleClass\",\n        addOnTab: \"addOnTab\",\n        addOnBlur: \"addOnBlur\",\n        separator: \"separator\",\n        showClear: \"showClear\"\n      },\n      outputs: {\n        onAdd: \"onAdd\",\n        onRemove: \"onRemove\",\n        onFocus: \"onFocus\",\n        onBlur: \"onBlur\",\n        onChipClick: \"onChipClick\",\n        onClear: \"onClear\"\n      },\n      features: [i0.ɵɵProvidersFeature([CHIPS_VALUE_ACCESSOR])],\n      decls: 8,\n      vars: 31,\n      consts: [[3, \"ngClass\", \"ngStyle\"], [\"tabindex\", \"-1\", \"role\", \"listbox\", 3, \"ngClass\", \"click\", \"focus\", \"blur\", \"keydown\"], [\"container\", \"\"], [\"role\", \"option\", 3, \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"role\", \"option\", 1, \"p-chips-input-token\", 3, \"ngClass\"], [\"type\", \"text\", 3, \"disabled\", \"ngStyle\", \"keydown\", \"input\", \"paste\", \"focus\", \"blur\"], [\"inputtext\", \"\"], [4, \"ngIf\"], [\"role\", \"option\", 3, \"ngClass\", \"click\"], [\"token\", \"\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"class\", \"p-chips-token-label\", 4, \"ngIf\"], [1, \"p-chips-token-label\"], [3, \"styleClass\", \"click\", 4, \"ngIf\"], [\"class\", \"p-chips-token-icon\", 3, \"click\", 4, \"ngIf\"], [3, \"styleClass\", \"click\"], [1, \"p-chips-token-icon\", 3, \"click\"], [4, \"ngTemplateOutlet\"], [\"class\", \"p-chips-clear-icon\", 3, \"click\", 4, \"ngIf\"], [1, \"p-chips-clear-icon\", 3, \"click\"]],\n      template: function Chips_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"ul\", 1, 2);\n          i0.ɵɵlistener(\"click\", function Chips_Template_ul_click_1_listener() {\n            return ctx.onWrapperClick();\n          })(\"focus\", function Chips_Template_ul_focus_1_listener() {\n            return ctx.onContainerFocus();\n          })(\"blur\", function Chips_Template_ul_blur_1_listener() {\n            return ctx.onContainerBlur();\n          })(\"keydown\", function Chips_Template_ul_keydown_1_listener($event) {\n            return ctx.onContainerKeyDown($event);\n          });\n          i0.ɵɵtemplate(3, Chips_li_3_Template, 5, 16, \"li\", 3);\n          i0.ɵɵelementStart(4, \"li\", 4)(5, \"input\", 5, 6);\n          i0.ɵɵlistener(\"keydown\", function Chips_Template_input_keydown_5_listener($event) {\n            return ctx.onKeyDown($event);\n          })(\"input\", function Chips_Template_input_input_5_listener() {\n            return ctx.onInput();\n          })(\"paste\", function Chips_Template_input_paste_5_listener($event) {\n            return ctx.onPaste($event);\n          })(\"focus\", function Chips_Template_input_focus_5_listener($event) {\n            return ctx.onInputFocus($event);\n          })(\"blur\", function Chips_Template_input_blur_5_listener($event) {\n            return ctx.onInputBlur($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(7, Chips_li_7_Template, 3, 2, \"li\", 7);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(23, _c4, ctx.disabled, ctx.focused, ctx.value && ctx.value.length || (ctx.inputViewChild == null ? null : ctx.inputViewChild.nativeElement.value) && (ctx.inputViewChild == null ? null : ctx.inputViewChild.nativeElement.value.length), ctx.focused))(\"ngStyle\", ctx.style);\n          i0.ɵɵattribute(\"data-pc-name\", \"chips\")(\"data-pc-section\", \"root\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(28, _c5));\n          i0.ɵɵattribute(\"aria-labelledby\", ctx.ariaLabelledBy)(\"aria-label\", ctx.ariaLabel)(\"aria-activedescendant\", ctx.focused ? ctx.focusedOptionId : undefined)(\"aria-orientation\", \"horizontal\")(\"data-pc-section\", \"container\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.value);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(29, _c6, ctx.showClear && !ctx.disabled));\n          i0.ɵɵattribute(\"data-pc-section\", \"inputToken\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.inputStyleClass);\n          i0.ɵɵproperty(\"disabled\", ctx.disabled || ctx.isMaxedOut)(\"ngStyle\", ctx.inputStyle);\n          i0.ɵɵattribute(\"id\", ctx.inputId)(\"placeholder\", ctx.value && ctx.value.length ? null : ctx.placeholder)(\"tabindex\", ctx.tabindex);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.value != null && ctx.filled && !ctx.disabled && ctx.showClear);\n        }\n      },\n      dependencies: () => [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, TimesCircleIcon, TimesIcon],\n      styles: [\"@layer primeng{.p-chips{display:inline-flex}.p-chips-multiple-container{margin:0;padding:0;list-style-type:none;cursor:text;overflow:hidden;display:flex;align-items:center;flex-wrap:wrap}.p-chips-token{cursor:default;display:inline-flex;align-items:center;flex:0 0 auto;max-width:100%}.p-chips-token-label{min-width:0%;overflow:auto}.p-chips-token-label::-webkit-scrollbar{display:none}.p-chips-input-token{flex:1 1 auto;display:inline-flex}.p-chips-token-icon{cursor:pointer}.p-chips-input-token input{border:0 none;outline:0 none;background-color:transparent;margin:0;padding:0;box-shadow:none;border-radius:0;width:100%}.p-fluid .p-chips{display:flex}.p-chips-clear-icon{position:absolute;top:50%;margin-top:-.5rem;cursor:pointer}.p-chips-clearable .p-inputtext{position:relative}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return Chips;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ChipsModule = /*#__PURE__*/(() => {\n  class ChipsModule {\n    static ɵfac = function ChipsModule_Factory(t) {\n      return new (t || ChipsModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ChipsModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, InputTextModule, SharedModule, TimesCircleIcon, TimesIcon, InputTextModule, SharedModule]\n    });\n  }\n  return ChipsModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CHIPS_VALUE_ACCESSOR, Chips, ChipsModule };\n//# sourceMappingURL=primeng-chips.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}