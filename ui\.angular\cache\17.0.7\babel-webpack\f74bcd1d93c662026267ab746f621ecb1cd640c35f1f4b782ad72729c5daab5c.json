{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { FiltersComponent } from './filters.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class FiltersRoutingModule {\n  static #_ = this.ɵfac = function FiltersRoutingModule_Factory(t) {\n    return new (t || FiltersRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: FiltersRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild([{\n      path: '',\n      component: FiltersComponent\n    }]), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(FiltersRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "FiltersComponent", "FiltersRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "path", "component", "imports", "i1", "exports"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\filters\\filters-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule } from '@angular/router';\r\nimport { FiltersComponent } from './filters.component';\r\n\r\n@NgModule({\r\n    imports: [RouterModule.forChild([\r\n        { path: '', component: FiltersComponent }\r\n    ])],\r\n    exports: [RouterModule]\r\n})\r\nexport class FiltersRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,qBAAqB;;;AAQtD,OAAM,MAAOC,oBAAoB;EAAA,QAAAC,CAAA,G;qBAApBD,oBAAoB;EAAA;EAAA,QAAAE,EAAA,G;UAApBF;EAAoB;EAAA,QAAAG,EAAA,G;cALnBL,YAAY,CAACM,QAAQ,CAAC,CAC5B;MAAEC,IAAI,EAAE,EAAE;MAAEC,SAAS,EAAEP;IAAgB,CAAE,CAC5C,CAAC,EACQD,YAAY;EAAA;;;2EAEbE,oBAAoB;IAAAO,OAAA,GAAAC,EAAA,CAAAV,YAAA;IAAAW,OAAA,GAFnBX,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}