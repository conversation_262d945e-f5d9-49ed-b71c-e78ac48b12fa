import { Component, OnInit, OnDestroy } from '@angular/core';
import { MenuItem, MessageService } from 'primeng/api';
import { Product } from '../../api/product';
import { ProductService } from '../../service/product.service';
import { Subscription, debounceTime } from 'rxjs';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { SelectItem } from 'primeng/api';
import { DataView } from 'primeng/dataview';
import { StationsService } from '../../service/stations.service';
import { Station } from '../../api/station';
import { CacheService } from '../../service/cache.service';
import { DateUtilsService } from '../../service/date-utils.service';
import { ProvidersService } from '../../service/providers.service';
import { IProvider, IUserProvider } from '../../api/responses';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { GetHistoricDataRequest, SaveUserProvidersRequest } from '../../api/requests';
import { Router } from '@angular/router';


@Component({
    templateUrl: './index.component.html',
})
export class IndexComponent implements OnInit, OnDestroy {

    items!: MenuItem[];

    stations: Station[] = [];
    filteredStations: Station[] = [];
    paginatedStations: Station[] = [];

    chartData: any;

    chartOptions: any;

    subscription!: Subscription;

    sortOptionsCountry: SelectItem[] = [];

    sortOptionsStatus: SelectItem[] = [];

    sortOrder: number = 0;

    sortField: string = '';

    // Filtering properties
    searchTerm: string = '';
    selectedProvider: string = '';
    selectedStatus: string = '';

    // Pagination properties
    currentPage: number = 0;
    itemsPerPage: number = 5;
    totalItems: number = 0;

    // Loading state
    isRefreshing: boolean = false;

    sourceCities: any[] = [];

    targetCities: any[] = [];

    orderCities: any[] = [];

    mapSrc:string;

    barOptions:any;
    lineOptions:any;
    stationsData:Map<string,any> = new Map();
    stationsRawData:Map<string,any> = new Map();
    stationsSumData:Map<string,number> = new Map();
    

    constructor(public layoutService: LayoutService,
        private stationsService: StationsService,
        private providersService: ProvidersService,
        private messageService: MessageService,
        private fb: FormBuilder,
        private router: Router,
        private cacheService: CacheService,
        private dateUtils: DateUtilsService) {
        this.subscription = this.layoutService.configUpdate$
        .pipe(debounceTime(25))
        .subscribe((config) => {
         //   this.initChart();
        });
    }

    ngOnInit() {
        this.getUserProviders();

        this.barOptions = {
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              }
            },
            scales: {
              x: {
                display: false
              },
              y: {
                display: false
              }
            }
          };
        
          this.lineOptions = {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
              intersect: false,
              mode: 'index'
            },
            elements: {
              line: {
                tension: 0.4,
                borderWidth: 2
              },
              point: {
                radius: 0,
                hoverRadius: 4,
                hitRadius: 10
              }
            },
            plugins: {
              legend: {
                display: false
              },
              tooltip: {
                enabled: true,
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#ffffff',
                bodyColor: '#ffffff',
                borderColor: 'rgba(255, 255, 255, 0.1)',
                borderWidth: 1,
                cornerRadius: 8,
                displayColors: false,
                padding: 8,
                callbacks: {
                  title: function(context: any) {
                    return 'Time: ' + context[0].label;
                  },
                  label: function(context: any) {
                    return 'Power: ' + context.parsed.y + ' kW';
                  }
                }
              }
            },
            scales: {
              x: {
                display: false,
                grid: {
                  display: false
                }
              },
              y: {
                display: false,
                grid: {
                  display: false
                }
              }
            },
            animation: {
              duration: 750,
              easing: 'easeInOutQuart'
            }
          };


          
        
    }

    getUserProviders(){
      this.isRefreshing = true;
      this.providersService.getUserProviders().then(providersData => {
        if (providersData.length > 0){
          this.stationsService.getUserStations().then(stationsData => {
            this.cacheService.setStations(stationsData);
            this.stations = stationsData;
            console.log("stations set")
            console.log(stationsData)

            // Initialize filter options
            this.initializeFilterOptions();

            // Apply filters and pagination
            this.applyFilters();

            // Φορτώνουμε τα δεδομένα για κάθε σταθμό
            this.loadAllStationsData();

            this.isRefreshing = false;
          });
        }else{
          this.router.navigate(['/app/providers']);
        }
      }).catch(error => {
        console.error('Error loading providers:', error);
        this.isRefreshing = false;
      });
    }

    // Νέα μέθοδος για φόρτωση δεδομένων όλων των σταθμών
    loadAllStationsData() {
        if (!this.stations || this.stations.length === 0) return;
        
        // Φορτώνουμε τα δεδομένα για κάθε σταθμό
        this.stations.forEach(station => {
            this.loadStationData(station);
        });
    }

    // Νέα μέθοδος για φόρτωση δεδομένων ενός σταθμού
    loadStationData(station: Station) {
        if (!station) return;
        
        const now = new Date();
        const formattedStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0).toISOString();
        const formattedEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0).toISOString();
        
        let request: GetHistoricDataRequest = {
            devIds: station.deviceIds,
            devTypeId: 1,
            startDateTime: formattedStartDate,
            endDateTime: formattedEndDate,
            separated: true,
            searchType: null,
            stationId: station.id
        };
        
        this.stationsService.getStationHistoricData(request).then(data => {
            if (data && data.data) {
                // Filter data to not show beyond current time
                const filteredData = this.filterDataByCurrentTime(data.data);

                const documentStyle = getComputedStyle(document.documentElement);

                const lineData = {
                    labels: filteredData.map((e, index) =>
                        index % 2 === 0 ? this.dateUtils.formatTimeForChart(e.dateTime) : ''
                    ),
                    datasets: [
                        {
                            label: 'Active Power',
                            data: filteredData.map(e => e.activePower),
                            fill: false,
                            backgroundColor: documentStyle.getPropertyValue('--primary-500'),
                            borderColor: documentStyle.getPropertyValue('--primary-500'),
                            tension: .4
                        },
                        {
                            label: 'Total Input Power',
                            data: filteredData.map(e => e.totalInputPower),
                            fill: false,
                            backgroundColor: documentStyle.getPropertyValue('--primary-200'),
                            borderColor: documentStyle.getPropertyValue('--primary-200'),
                            tension: .4
                        }
                    ]
                };

                // Αποθηκεύουμε τα δεδομένα στο Map με κλειδί το ID του σταθμού
                this.stationsData.set(station.id, lineData);
                this.stationsRawData.set(station.id, filteredData);
                this.stationsSumData.set(station.id, data.sum);
            }
        }).catch(error => {
            console.error(`Error loading data for station ${station.id}:`, error);
        });
    }
    

    // Τροποποιημένη μέθοδος που επιστρέφει τα προ-φορτωμένα δεδομένα
    getStationsRealTimeData(station: Station) {
        // Επιστρέφουμε τα δεδομένα από το Map αν υπάρχουν
        if (station && station.id && this.stationsData.has(station.id)) {
            return this.stationsData.get(station.id);
        }
        
        // Αν δεν έχουν φορτωθεί ακόμα, επιστρέφουμε null
        return null;
    }

    getStationsSumData(stationId:string){
      return this.stationsSumData.get(stationId);
    }

    getStationsInverters(stationId:string){
      var data = this.stationsRawData.get(stationId);
      if (!data || data.length === 0) {
        return 0;
      }
      else
        return new Set(data.map(item => item.name)).size;
    }

    getStationLastUpdate(stationId: string) {
      const data = this.stationsRawData.get(stationId);
      if (!data || data.length === 0) return "-";

      const latest = data.reduce((latestSoFar, current) => {
        return new Date(current.dateTime).getTime() > new Date(latestSoFar.dateTime).getTime()
          ? current
          : latestSoFar;
      });

      return new Date(latest.dateTime).toLocaleString("en-GB", {
        hour: '2-digit',
        minute: '2-digit',
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        timeZone: 'Europe/Athens'
      });
    }
    

    onSortChange(event: any) {
        const value = event.value;

        if (value.indexOf('!') === 0) {
            this.sortOrder = -1;
            this.sortField = value.substring(1, value.length);
        } else {
            this.sortOrder = 1;
            this.sortField = value;
        }
    }

    onFilter(dv: DataView, event: Event) {
        dv.filter((event.target as HTMLInputElement).value);
    }

    // Initialize filter options based on available stations
    initializeFilterOptions() {
        // Get unique providers
        const providers = [...new Set(this.stations.map(station => station.provider))];
        this.sortOptionsCountry = providers.map(provider => ({
            label: provider,
            value: provider
        }));

        // Get unique statuses
        const statuses = [...new Set(this.stations.map(station => station.status))];
        this.sortOptionsStatus = statuses.map(status => ({
            label: status,
            value: status
        }));
    }

    // Apply all filters and update pagination
    applyFilters() {
        let filtered = [...this.stations];

        // Apply search filter
        if (this.searchTerm) {
            const searchLower = this.searchTerm.toLowerCase();
            filtered = filtered.filter(station =>
                station.name.toLowerCase().includes(searchLower) ||
                station.provider.toLowerCase().includes(searchLower) ||
                (station.location && station.location.toLowerCase().includes(searchLower))
            );
        }

        // Apply provider filter
        if (this.selectedProvider) {
            filtered = filtered.filter(station => station.provider === this.selectedProvider);
        }

        // Apply status filter
        if (this.selectedStatus) {
            filtered = filtered.filter(station => station.status === this.selectedStatus);
        }

        this.filteredStations = filtered;
        this.totalItems = filtered.length;
        this.currentPage = 0; // Reset to first page
        this.updatePagination();
    }

    // Update pagination based on current page
    updatePagination() {
        const startIndex = this.currentPage * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        this.paginatedStations = this.filteredStations.slice(startIndex, endIndex);
    }

    // Handle search input change
    onSearchChange(event: Event) {
        this.searchTerm = (event.target as HTMLInputElement).value;
        this.applyFilters();
    }

    // Handle provider filter change
    onProviderChange(event: any) {
        this.selectedProvider = event.value || '';
        this.applyFilters();
    }

    // Handle status filter change
    onStatusChange(event: any) {
        this.selectedStatus = event.value || '';
        this.applyFilters();
    }

    // Handle pagination change
    onPageChange(event: any) {
        this.currentPage = event.page;
        this.itemsPerPage = event.rows;
        this.updatePagination();
    }

    // Refresh data
    refreshData() {
        this.getUserProviders();
    }

    // Clear all filters
    clearFilters() {
        this.searchTerm = '';
        this.selectedProvider = '';
        this.selectedStatus = '';
        this.applyFilters();
    }

    // Check if station has equipment data
    hasEquipmentData(station: Station): boolean {
        const inverters = this.getStationsInverters(station.id || '');
        return inverters > 0 ||
               (station.mmpt && station.mmpt > 0) ||
               (station.string && station.string > 0) ||
               (station.pvn && station.pvn > 0);
    }

    private filterDataByCurrentTime(data: any[]): any[] {
        const now = new Date();
        const currentTime = now.getTime();

        return data.filter(item => {
            const itemDateTime = new Date(item.dateTime);
            const itemTime = itemDateTime.getTime();

            // Only include data points that are not in the future
            return itemTime <= currentTime;
        });
    }


    initMap(){
        this.mapSrc = "https://www.digitaltrends.com/wp-content/uploads/2020/02/custom-map.jpg?fit=720%2C479&p=1"
    }

    // initChart() {
    //     const documentStyle = getComputedStyle(document.documentElement);
    //     const textColor = documentStyle.getPropertyValue('--text-color');
    //     const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');
    //     const surfaceBorder = documentStyle.getPropertyValue('--surface-border');

    //     this.chartData = {
    //         labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],
    //         datasets: [
    //             {
    //                 label: 'First Dataset',
    //                 data: [65, 59, 80, 81, 56, 55, 40],
    //                 fill: false,
    //                 backgroundColor: documentStyle.getPropertyValue('--bluegray-700'),
    //                 borderColor: documentStyle.getPropertyValue('--bluegray-700'),
    //                 tension: .4
    //             },
    //             {
    //                 label: 'Second Dataset',
    //                 data: [28, 48, 40, 19, 86, 27, 90],
    //                 fill: false,
    //                 backgroundColor: documentStyle.getPropertyValue('--green-600'),
    //                 borderColor: documentStyle.getPropertyValue('--green-600'),
    //                 tension: .4
    //             }
    //         ]
    //     };

    //     this.chartOptions = {
    //         plugins: {
    //             legend: {
    //                 labels: {
    //                     color: textColor
    //                 }
    //             }
    //         },
    //         scales: {
    //             x: {
    //                 ticks: {
    //                     color: textColorSecondary
    //                 },
    //                 grid: {
    //                     color: surfaceBorder,
    //                     drawBorder: false
    //                 }
    //             },
    //             y: {
    //                 ticks: {
    //                     color: textColorSecondary
    //                 },
    //                 grid: {
    //                     color: surfaceBorder,
    //                     drawBorder: false
    //                 }
    //             }
    //         }
    //     };
    // }

    ngOnDestroy() {
        if (this.subscription) {
            this.subscription.unsubscribe();
        }
    }
}
