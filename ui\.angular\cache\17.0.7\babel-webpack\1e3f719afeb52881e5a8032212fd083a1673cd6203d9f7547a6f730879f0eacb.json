{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@fortawesome/angular-fontawesome\";\nfunction ModernBreadcrumbComponent_ng_container_5_fa_icon_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"fa-icon\", 11);\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"icon\", item_r1.icon);\n  }\n}\nfunction ModernBreadcrumbComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 6);\n    i0.ɵɵelement(2, \"fa-icon\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 8)(4, \"div\", 3);\n    i0.ɵɵtemplate(5, ModernBreadcrumbComponent_ng_container_5_fa_icon_5_Template, 1, 1, \"fa-icon\", 9);\n    i0.ɵɵelementStart(6, \"span\", 10);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    const last_r3 = ctx.last;\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"active\", last_r3);\n    i0.ɵɵproperty(\"routerLink\", item_r1.routerLink)(\"queryParams\", item_r1.queryParams);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", item_r1.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.label);\n  }\n}\nexport let ModernBreadcrumbComponent = /*#__PURE__*/(() => {\n  class ModernBreadcrumbComponent {\n    constructor() {\n      this.items = [];\n      this.homeRoute = '/app/index';\n      this.homeIcon = 'home';\n    }\n    static #_ = this.ɵfac = function ModernBreadcrumbComponent_Factory(t) {\n      return new (t || ModernBreadcrumbComponent)();\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ModernBreadcrumbComponent,\n      selectors: [[\"app-modern-breadcrumb\"]],\n      inputs: {\n        items: \"items\",\n        homeRoute: \"homeRoute\",\n        homeIcon: \"homeIcon\"\n      },\n      decls: 6,\n      vars: 3,\n      consts: [[1, \"modern-breadcrumb\"], [1, \"breadcrumb-container\"], [1, \"breadcrumb-item\", \"home-item\", 3, \"routerLink\"], [1, \"breadcrumb-content\"], [1, \"home-icon\", 3, \"icon\"], [4, \"ngFor\", \"ngForOf\"], [1, \"breadcrumb-separator\"], [\"icon\", \"chevron-right\", 1, \"separator-icon\"], [1, \"breadcrumb-item\", 3, \"routerLink\", \"queryParams\"], [\"class\", \"item-icon\", 3, \"icon\", 4, \"ngIf\"], [1, \"item-label\"], [1, \"item-icon\", 3, \"icon\"]],\n      template: function ModernBreadcrumbComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"fa-icon\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(5, ModernBreadcrumbComponent_ng_container_5_Template, 8, 6, \"ng-container\", 5);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"routerLink\", ctx.homeRoute);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"icon\", ctx.homeIcon);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.items);\n        }\n      },\n      dependencies: [i1.NgForOf, i1.NgIf, i2.RouterLink, i3.FaIconComponent],\n      styles: [\".modern-breadcrumb[_ngcontent-%COMP%]{margin-bottom:1.5rem}.modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-container[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.75rem 1rem;background:linear-gradient(135deg,#f8fafc 0%,#f1f5f9 100%);border:1px solid #e2e8f0;border-radius:12px;box-shadow:0 1px 3px #0000001a;transition:all .2s ease}.modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-container[_ngcontent-%COMP%]:hover{box-shadow:0 4px 12px #00000026}.modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]{display:flex;align-items:center;cursor:pointer;transition:all .2s ease;border-radius:8px;padding:.375rem .75rem;text-decoration:none;color:inherit}.modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]:hover:not(.active){background:rgba(59,130,246,.1);color:#3b82f6;transform:translateY(-1px)}.modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-item.home-item[_ngcontent-%COMP%]{background:linear-gradient(135deg,#3b82f6 0%,#1d4ed8 100%);color:#fff;box-shadow:0 2px 4px #3b82f64d}.modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-item.home-item[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#1d4ed8 0%,#1e40af 100%);transform:translateY(-1px);box-shadow:0 4px 8px #3b82f666}.modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-item.home-item[_ngcontent-%COMP%]   .home-icon[_ngcontent-%COMP%]{font-size:1rem}.modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-item.active[_ngcontent-%COMP%]{background:linear-gradient(135deg,#10b981 0%,#059669 100%);color:#fff;font-weight:600;box-shadow:0 2px 4px #10b9814d;cursor:default}.modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]   .breadcrumb-content[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]   .item-icon[_ngcontent-%COMP%]{font-size:.875rem;opacity:.8}.modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%]{font-size:.875rem;font-weight:500;white-space:nowrap}.modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-separator[_ngcontent-%COMP%]{display:flex;align-items:center;color:#64748b}.modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-separator[_ngcontent-%COMP%]   .separator-icon[_ngcontent-%COMP%]{font-size:.75rem;opacity:.6}.dark[_nghost-%COMP%]   .modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-container[_ngcontent-%COMP%]{background:linear-gradient(135deg,#1e293b 0%,#0f172a 100%);border-color:#334155}.dark[_nghost-%COMP%]   .modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]{color:#e2e8f0}.dark[_nghost-%COMP%]   .modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]:hover:not(.active), .dark   [_nghost-%COMP%]   .modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]:hover:not(.active){background:rgba(59,130,246,.2);color:#60a5fa}.dark[_nghost-%COMP%]   .modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-item.home-item[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-item.home-item[_ngcontent-%COMP%]{background:linear-gradient(135deg,#3b82f6 0%,#1d4ed8 100%)}.dark[_nghost-%COMP%]   .modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-item.active[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-item.active[_ngcontent-%COMP%]{background:linear-gradient(135deg,#10b981 0%,#059669 100%)}.dark[_nghost-%COMP%]   .modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-separator[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-separator[_ngcontent-%COMP%]{color:#94a3b8}@media (max-width: 768px){.modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-container[_ngcontent-%COMP%]{padding:.5rem .75rem;gap:.25rem}.modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]{padding:.25rem .5rem}.modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]   .item-label[_ngcontent-%COMP%], .modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-item[_ngcontent-%COMP%]   .item-icon[_ngcontent-%COMP%]{font-size:.8rem}.modern-breadcrumb[_ngcontent-%COMP%]   .breadcrumb-separator[_ngcontent-%COMP%]   .separator-icon[_ngcontent-%COMP%]{font-size:.7rem}}.modern-breadcrumb[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_breadcrumbSlideIn .3s ease-out}@keyframes _ngcontent-%COMP%_breadcrumbSlideIn{0%{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}\"]\n    });\n  }\n  return ModernBreadcrumbComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}