{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../service/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"primeng/button\";\nimport * as i4 from \"primeng/checkbox\";\nimport * as i5 from \"primeng/inputtext\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"primeng/password\";\nexport class LoginComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.username = '';\n    this.password = '';\n    this.errorMessage = '';\n  }\n  login() {\n    this.authService.login(this.username, this.password).subscribe({\n      next: () => {\n        console.log('Login επιτυχές! Redirecting...'); // ✅ Έλεγχος\n        this.router.navigate(['/home']);\n      },\n      error: () => {\n        this.errorMessage = 'Invalid credentials';\n        console.log('Login απέτυχε!');\n      }\n    });\n  }\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/login']);\n  }\n  static #_ = this.ɵfac = function LoginComponent_Factory(t) {\n    return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LoginComponent,\n    selectors: [[\"app-login\"]],\n    decls: 25,\n    vars: 4,\n    consts: [[1, \"surface-ground\", \"flex\", \"align-items-center\", \"justify-content-center\", \"min-h-screen\", \"min-w-screen\", \"overflow-hidden\"], [1, \"flex\", \"flex-column\", \"align-items-center\", \"justify-content-center\"], [2, \"border-radius\", \"56px\", \"padding\", \"0.3rem\", \"background\", \"linear-gradient(180deg, var(--primary-color) 10%, rgba(33, 150, 243, 0) 30%)\"], [1, \"w-full\", \"surface-card\", \"py-8\", \"px-5\", \"sm:px-8\", 2, \"border-radius\", \"53px\"], [1, \"text-center\", \"mb-5\"], [\"src\", \"assets/demo/images/login/avatar.png\", \"alt\", \"Image\", \"height\", \"50\", 1, \"mb-3\"], [1, \"text-900\", \"text-3xl\", \"font-medium\", \"mb-3\"], [1, \"text-600\", \"font-medium\"], [\"for\", \"username\", 1, \"block\", \"text-900\", \"text-xl\", \"font-medium\", \"mb-2\"], [\"id\", \"username\", \"type\", \"text\", \"placeholder\", \"Username\", \"pInputText\", \"\", 1, \"w-full\", \"md:w-30rem\", \"mb-5\", 2, \"padding\", \"1rem\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"password1\", 1, \"block\", \"text-900\", \"font-medium\", \"text-xl\", \"mb-2\"], [\"id\", \"password1\", \"placeholder\", \"Password\", \"styleClass\", \"mb-5\", \"inputStyleClass\", \"w-full p-3 md:w-30rem\", 3, \"ngModel\", \"toggleMask\", \"ngModelChange\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"mb-5\", \"gap-5\"], [1, \"flex\", \"align-items-center\"], [\"id\", \"rememberme1\", \"styleClass\", \"mr-2\", 3, \"binary\"], [\"for\", \"rememberme1\"], [1, \"font-medium\", \"no-underline\", \"ml-2\", \"text-right\", \"cursor-pointer\", 2, \"color\", \"var(--primary-color)\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Sign In\", 1, \"w-full\", \"p-3\", \"text-xl\", 3, \"click\"]],\n    template: function LoginComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n        i0.ɵɵelement(5, \"img\", 5);\n        i0.ɵɵelementStart(6, \"div\", 6);\n        i0.ɵɵtext(7, \"Welcome, Isabel!\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"span\", 7);\n        i0.ɵɵtext(9, \"Sign in to continue\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\")(11, \"label\", 8);\n        i0.ɵɵtext(12, \"Username\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"input\", 9);\n        i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_13_listener($event) {\n          return ctx.username = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"label\", 10);\n        i0.ɵɵtext(15, \"Password\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"p-password\", 11);\n        i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_p_password_ngModelChange_16_listener($event) {\n          return ctx.password = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"div\", 12)(18, \"div\", 13);\n        i0.ɵɵelement(19, \"p-checkbox\", 14);\n        i0.ɵɵelementStart(20, \"label\", 15);\n        i0.ɵɵtext(21, \"Remember me\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(22, \"a\", 16);\n        i0.ɵɵtext(23, \"Forgot password?\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(24, \"button\", 17);\n        i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_24_listener() {\n          return ctx.login();\n        });\n        i0.ɵɵelementEnd()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(13);\n        i0.ɵɵproperty(\"ngModel\", ctx.username);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngModel\", ctx.password)(\"toggleMask\", true);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"binary\", true);\n      }\n    },\n    dependencies: [i3.ButtonDirective, i4.Checkbox, i5.InputText, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel, i7.Password],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["LoginComponent", "constructor", "authService", "router", "username", "password", "errorMessage", "login", "subscribe", "next", "console", "log", "navigate", "error", "logout", "_", "i0", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "_2", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "LoginComponent_Template_input_ngModelChange_13_listener", "$event", "LoginComponent_Template_p_password_ngModelChange_16_listener", "LoginComponent_Template_button_click_24_listener", "ɵɵadvance", "ɵɵproperty"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\auth\\login\\login.component.ts", "D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\auth\\login\\login.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../../service/auth.service';\n\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html'\n})\nexport class LoginComponent {\n  username = '';\n  password = '';\n  errorMessage = '';\n\n  constructor(private authService: AuthService, private router: Router) {}\n\n  login() {\n    this.authService.login(this.username, this.password).subscribe({\n      next: () => {\n        console.log('Login επιτυχές! Redirecting...'); // ✅ Έλεγχος\n        this.router.navigate(['/home']); \n      },\n      error: () => {\n        this.errorMessage = 'Invalid credentials';\n        console.log('Login απέτυχε!');\n      }\n    });\n  }\n\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/login']);\n  }\n}\n\n", "<div class=\"surface-ground flex align-items-center justify-content-center min-h-screen min-w-screen overflow-hidden\">\n    <div class=\"flex flex-column align-items-center justify-content-center\">\n        <!-- <img src=\"assets/layout/images/{{layoutService.config().colorScheme === 'light' ? 'logo-dark' : 'logo-white'}}.svg\" alt=\"Sakai logo\" class=\"mb-5 w-6rem flex-shrink-0\">                 -->\n        <div style=\"border-radius:56px; padding:0.3rem; background: linear-gradient(180deg, var(--primary-color) 10%, rgba(33, 150, 243, 0) 30%);\">\n            <div class=\"w-full surface-card py-8 px-5 sm:px-8\" style=\"border-radius:53px\">\n                <div class=\"text-center mb-5\">\n                    <img src=\"assets/demo/images/login/avatar.png\" alt=\"Image\" height=\"50\" class=\"mb-3\">\n                    <div class=\"text-900 text-3xl font-medium mb-3\">Welcome, <PERSON>!</div>\n                    <span class=\"text-600 font-medium\">Sign in to continue</span>\n                </div>\n\n                <div>\n                    <label for=\"username\" class=\"block text-900 text-xl font-medium mb-2\">Username</label>\n                    <input id=\"username\" type=\"text\" [(ngModel)]=\"username\" placeholder=\"Username\" pInputText class=\"w-full md:w-30rem mb-5\" style=\"padding:1rem\">\n\n                    <label for=\"password1\" class=\"block text-900 font-medium text-xl mb-2\">Password</label>\n                    <p-password id=\"password1\" [(ngModel)]=\"password\" placeholder=\"Password\" [toggleMask]=\"true\" styleClass=\"mb-5\" inputStyleClass=\"w-full p-3 md:w-30rem\"></p-password>\n\n                    <div class=\"flex align-items-center justify-content-between mb-5 gap-5\">\n                        <div class=\"flex align-items-center\">\n                            <p-checkbox id=\"rememberme1\" [binary]=\"true\" styleClass=\"mr-2\"></p-checkbox>\n                            <label for=\"rememberme1\">Remember me</label>\n                        </div>\n                        <a class=\"font-medium no-underline ml-2 text-right cursor-pointer\" style=\"color: var(--primary-color)\">Forgot password?</a>\n                    </div>\n                    <button pButton pRipple label=\"Sign In\" class=\"w-full p-3 text-xl\" (click)=login();></button>\n                </div>\n            </div>\n        </div>\n    </div>\n</div>\n"], "mappings": ";;;;;;;;AASA,OAAM,MAAOA,cAAc;EAKzBC,YAAoBC,WAAwB,EAAUC,MAAc;IAAhD,KAAAD,WAAW,GAAXA,WAAW;IAAuB,KAAAC,MAAM,GAANA,MAAM;IAJ5D,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,YAAY,GAAG,EAAE;EAEsD;EAEvEC,KAAKA,CAAA;IACH,IAAI,CAACL,WAAW,CAACK,KAAK,CAAC,IAAI,CAACH,QAAQ,EAAE,IAAI,CAACC,QAAQ,CAAC,CAACG,SAAS,CAAC;MAC7DC,IAAI,EAAEA,CAAA,KAAK;QACTC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC,CAAC,CAAC;QAC/C,IAAI,CAACR,MAAM,CAACS,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;MACjC,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACP,YAAY,GAAG,qBAAqB;QACzCI,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;MAC/B;KACD,CAAC;EACJ;EAEAG,MAAMA,CAAA;IACJ,IAAI,CAACZ,WAAW,CAACY,MAAM,EAAE;IACzB,IAAI,CAACX,MAAM,CAACS,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAAC,QAAAG,CAAA,G;qBAvBUf,cAAc,EAAAgB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAdtB,cAAc;IAAAuB,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCT3Bb,EAAA,CAAAe,cAAA,aAAqH;QAMjGf,EAAA,CAAAgB,SAAA,aAAoF;QACpFhB,EAAA,CAAAe,cAAA,aAAgD;QAAAf,EAAA,CAAAiB,MAAA,uBAAgB;QAAAjB,EAAA,CAAAkB,YAAA,EAAM;QACtElB,EAAA,CAAAe,cAAA,cAAmC;QAAAf,EAAA,CAAAiB,MAAA,0BAAmB;QAAAjB,EAAA,CAAAkB,YAAA,EAAO;QAGjElB,EAAA,CAAAe,cAAA,WAAK;QACqEf,EAAA,CAAAiB,MAAA,gBAAQ;QAAAjB,EAAA,CAAAkB,YAAA,EAAQ;QACtFlB,EAAA,CAAAe,cAAA,gBAA8I;QAA7Gf,EAAA,CAAAmB,UAAA,2BAAAC,wDAAAC,MAAA;UAAA,OAAAP,GAAA,CAAA1B,QAAA,GAAAiC,MAAA;QAAA,EAAsB;QAAvDrB,EAAA,CAAAkB,YAAA,EAA8I;QAE9IlB,EAAA,CAAAe,cAAA,iBAAuE;QAAAf,EAAA,CAAAiB,MAAA,gBAAQ;QAAAjB,EAAA,CAAAkB,YAAA,EAAQ;QACvFlB,EAAA,CAAAe,cAAA,sBAAuJ;QAA5Hf,EAAA,CAAAmB,UAAA,2BAAAG,6DAAAD,MAAA;UAAA,OAAAP,GAAA,CAAAzB,QAAA,GAAAgC,MAAA;QAAA,EAAsB;QAAsGrB,EAAA,CAAAkB,YAAA,EAAa;QAEpKlB,EAAA,CAAAe,cAAA,eAAwE;QAEhEf,EAAA,CAAAgB,SAAA,sBAA4E;QAC5EhB,EAAA,CAAAe,cAAA,iBAAyB;QAAAf,EAAA,CAAAiB,MAAA,mBAAW;QAAAjB,EAAA,CAAAkB,YAAA,EAAQ;QAEhDlB,EAAA,CAAAe,cAAA,aAAuG;QAAAf,EAAA,CAAAiB,MAAA,wBAAgB;QAAAjB,EAAA,CAAAkB,YAAA,EAAI;QAE/HlB,EAAA,CAAAe,cAAA,kBAAoF;QAAjBf,EAAA,CAAAmB,UAAA,mBAAAI,iDAAA;UAAA,OAAQT,GAAA,CAAAvB,KAAA,EAAO;QAAA,EAAC;QAACS,EAAA,CAAAkB,YAAA,EAAS;;;QAZ5DlB,EAAA,CAAAwB,SAAA,IAAsB;QAAtBxB,EAAA,CAAAyB,UAAA,YAAAX,GAAA,CAAA1B,QAAA,CAAsB;QAG5BY,EAAA,CAAAwB,SAAA,GAAsB;QAAtBxB,EAAA,CAAAyB,UAAA,YAAAX,GAAA,CAAAzB,QAAA,CAAsB;QAIZW,EAAA,CAAAwB,SAAA,GAAe;QAAfxB,EAAA,CAAAyB,UAAA,gBAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}