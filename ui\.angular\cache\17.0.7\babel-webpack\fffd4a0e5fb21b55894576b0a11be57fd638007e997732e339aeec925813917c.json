{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, AfterRenderPhase, afterNextRender, numberAttribute, booleanAttribute, Component, ChangeDetectionStrategy, Input, HostBinding, Output, ViewChild, NgModule } from '@angular/core';\nimport merge from 'lodash-es/merge';\nimport { registerables, Chart } from 'chart.js';\nimport { customTooltips } from '@coreui/chartjs';\nconst _c0 = [\"canvasElement\"];\nconst _c1 = [\"*\"];\nChart.register(...registerables);\nlet nextId = 0;\nlet ChartjsComponent = /*#__PURE__*/(() => {\n  class ChartjsComponent {\n    get hostClasses() {\n      return {\n        'chart-wrapper': this.wrapper\n      };\n    }\n    constructor(elementRef, ngZone, renderer, changeDetectorRef) {\n      this.elementRef = elementRef;\n      this.ngZone = ngZone;\n      this.renderer = renderer;\n      this.changeDetectorRef = changeDetectorRef;\n      this.customTooltips = true;\n      this.id = `c-chartjs-${nextId++}`;\n      this.plugins = [];\n      this.redraw = false;\n      this.type = 'bar';\n      this.wrapper = true;\n      this.getDatasetAtEvent = new EventEmitter();\n      this.getElementAtEvent = new EventEmitter();\n      this.getElementsAtEvent = new EventEmitter();\n      this.chartRef = new EventEmitter();\n      afterNextRender(() => {\n        this.ctx = this.canvasElement?.nativeElement?.getContext('2d');\n        this.chartRender();\n      }, {\n        phase: AfterRenderPhase.Read\n      });\n    }\n    ngAfterViewInit() {\n      this.chartRender();\n    }\n    ngOnChanges(changes) {\n      if (changes['data'] && !changes['data'].firstChange) {\n        this.chartUpdate();\n      }\n    }\n    ngOnDestroy() {\n      this.chartDestroy();\n    }\n    handleClick($event) {\n      if (!this.chart) {\n        return;\n      }\n      const datasetAtEvent = this.chart.getElementsAtEventForMode($event, 'dataset', {\n        intersect: true\n      }, false);\n      this.getDatasetAtEvent.emit(datasetAtEvent);\n      const elementAtEvent = this.chart.getElementsAtEventForMode($event, 'nearest', {\n        intersect: true\n      }, false);\n      this.getElementAtEvent.emit(elementAtEvent);\n      const elementsAtEvent = this.chart.getElementsAtEventForMode($event, 'index', {\n        intersect: true\n      }, false);\n      this.getElementsAtEvent.emit(elementsAtEvent);\n    }\n    chartDestroy() {\n      this.chart?.destroy();\n      this.chartRef.emit(undefined);\n    }\n    chartRender() {\n      if (!this.canvasElement?.nativeElement || !this.ctx) {\n        return;\n      }\n      this.ngZone.runOutsideAngular(() => {\n        const config = this.chartConfig();\n        if (config) {\n          setTimeout(() => {\n            this.chart = new Chart(this.ctx, config);\n            this.renderer.setStyle(this.canvasElement.nativeElement, 'display', 'block');\n            this.changeDetectorRef.markForCheck();\n            this.chartRef.emit(this.chart);\n          });\n        }\n      });\n    }\n    chartUpdate() {\n      if (!this.chart) {\n        return;\n      }\n      if (this.redraw) {\n        this.chartDestroy();\n        setTimeout(() => {\n          this.chartRender();\n        });\n        return;\n      }\n      const config = this.chartConfig();\n      if (this.options) {\n        Object.assign(this.chart.options ?? {}, config.options ?? {});\n      }\n      if (!this.chart.config.data) {\n        this.chart.config.data = {\n          ...config.data\n        };\n        this.chartUpdateOutsideAngular();\n      }\n      if (this.chart) {\n        Object.assign(this.chart.config.options ?? {}, config.options ?? {});\n        Object.assign(this.chart.config.plugins ?? [], config.plugins ?? []);\n        Object.assign(this.chart.config.data, config.data);\n      }\n      this.chartUpdateOutsideAngular();\n    }\n    chartUpdateOutsideAngular() {\n      setTimeout(() => {\n        this.ngZone.runOutsideAngular(() => {\n          this.chart?.update();\n          this.changeDetectorRef.markForCheck();\n        });\n      });\n    }\n    chartToBase64Image() {\n      return this.chart?.toBase64Image();\n    }\n    chartDataConfig() {\n      return {\n        labels: this.data?.labels ?? [],\n        datasets: this.data?.datasets ?? []\n      };\n    }\n    chartOptions() {\n      return this.options;\n    }\n    chartConfig() {\n      this.chartCustomTooltips();\n      return {\n        data: this.chartDataConfig(),\n        options: this.chartOptions(),\n        plugins: this.plugins,\n        type: this.type\n      };\n    }\n    chartCustomTooltips() {\n      if (this.customTooltips) {\n        const options = this.options;\n        // @ts-ignore\n        const plugins = this.options?.plugins;\n        // @ts-ignore\n        const tooltip = this.options?.plugins?.tooltip;\n        this.options = merge({\n          ...options,\n          plugins: {\n            ...plugins,\n            tooltip: {\n              ...tooltip,\n              enabled: false,\n              mode: 'index',\n              position: 'nearest',\n              external: customTooltips\n            }\n          }\n        });\n      }\n    }\n    static #_ = this.ɵfac = function ChartjsComponent_Factory(t) {\n      return new (t || ChartjsComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static #_2 = this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ChartjsComponent,\n      selectors: [[\"c-chart\"]],\n      viewQuery: function ChartjsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.canvasElement = _t.first);\n        }\n      },\n      hostVars: 6,\n      hostBindings: function ChartjsComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.hostClasses);\n          i0.ɵɵstyleProp(\"height\", ctx.height, \"px\")(\"width\", ctx.width, \"px\");\n        }\n      },\n      inputs: {\n        customTooltips: \"customTooltips\",\n        data: \"data\",\n        height: [\"height\", \"height\", value => numberAttribute(value, undefined)],\n        id: \"id\",\n        options: \"options\",\n        plugins: \"plugins\",\n        redraw: [\"redraw\", \"redraw\", booleanAttribute],\n        type: \"type\",\n        width: [\"width\", \"width\", value => numberAttribute(value, undefined)],\n        wrapper: \"wrapper\"\n      },\n      outputs: {\n        getDatasetAtEvent: \"getDatasetAtEvent\",\n        getElementAtEvent: \"getElementAtEvent\",\n        getElementsAtEvent: \"getElementsAtEvent\",\n        chartRef: \"chartRef\"\n      },\n      exportAs: [\"cChart\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c1,\n      decls: 3,\n      vars: 3,\n      consts: [[\"role\", \"img\", 2, \"display\", \"none\", 3, \"height\", \"id\", \"width\", \"click\"], [\"canvasElement\", \"\"]],\n      template: function ChartjsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"canvas\", 0, 1);\n          i0.ɵɵlistener(\"click\", function ChartjsComponent_Template_canvas_click_0_listener($event) {\n            return ctx.handleClick($event);\n          });\n          i0.ɵɵprojection(2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"height\", ctx.height)(\"id\", ctx.id)(\"width\", ctx.width);\n        }\n      },\n      styles: [\".chart-wrapper[_nghost-%COMP%]{display:block}\"],\n      changeDetection: 0\n    });\n  }\n  return ChartjsComponent;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ChartjsModule = /*#__PURE__*/(() => {\n  class ChartjsModule {\n    static #_ = this.ɵfac = function ChartjsModule_Factory(t) {\n      return new (t || ChartjsModule)();\n    };\n    static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ChartjsModule\n    });\n    static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n  return ChartjsModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/*\n * Public API Surface of coreui-angular-chartjs\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ChartjsComponent, ChartjsModule };\n//# sourceMappingURL=coreui-angular-chartjs.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}