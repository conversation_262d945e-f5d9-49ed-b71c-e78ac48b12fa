{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { NotfoundComponent } from './demo/components/notfound/notfound.component';\nimport { AppLayoutComponent } from \"./layout/app.layout.component\";\nimport { AuthGuard } from './demo/guards/auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AppRoutingModule {\n  static #_ = this.ɵfac = function AppRoutingModule_Factory(t) {\n    return new (t || AppRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AppRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forRoot([{\n      path: '',\n      component: AppLayoutComponent,\n      children: [{\n        path: '',\n        redirectTo: 'app/index',\n        pathMatch: 'full'\n      }, {\n        path: 'app',\n        loadChildren: () => import('./demo/components/uikit/uikit.module').then(m => m.UIModule),\n        canActivate: [AuthGuard]\n      }, {\n        path: 'utilities',\n        loadChildren: () => import('./demo/components/utilities/utilities.module').then(m => m.UtilitiesModule)\n      }, {\n        path: 'documentation',\n        loadChildren: () => import('./demo/components/documentation/documentation.module').then(m => m.DocumentationModule)\n      }, {\n        path: 'blocks',\n        loadChildren: () => import('./demo/components/primeblocks/primeblocks.module').then(m => m.PrimeBlocksModule)\n      }, {\n        path: 'pages',\n        loadChildren: () => import('./demo/components/pages/pages.module').then(m => m.PagesModule)\n      }]\n    }, {\n      path: 'auth',\n      loadChildren: () => import('./demo/components/auth/auth.module').then(m => m.AuthModule)\n    }, {\n      path: 'landing',\n      loadChildren: () => import('./demo/components/landing/landing.module').then(m => m.LandingModule)\n    }, {\n      path: 'notfound',\n      component: NotfoundComponent\n    }, {\n      path: '**',\n      redirectTo: '/notfound'\n    }], {\n      scrollPositionRestoration: 'enabled',\n      anchorScrolling: 'enabled',\n      onSameUrlNavigation: 'reload'\n    }), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "NotfoundComponent", "AppLayoutComponent", "<PERSON><PERSON><PERSON><PERSON>", "AppRoutingModule", "_", "_2", "_3", "forRoot", "path", "component", "children", "redirectTo", "pathMatch", "loadChildren", "then", "m", "UIModule", "canActivate", "UtilitiesModule", "DocumentationModule", "PrimeBlocksModule", "PagesModule", "AuthModule", "LandingModule", "scrollPositionRestoration", "anchorScrolling", "onSameUrlNavigation", "imports", "i1", "exports"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { RouterModule } from '@angular/router';\r\nimport { NgModule } from '@angular/core';\r\nimport { NotfoundComponent } from './demo/components/notfound/notfound.component';\r\nimport { AppLayoutComponent } from \"./layout/app.layout.component\";\r\nimport { AuthGuard } from './demo/guards/auth.guard';\r\n\r\n@NgModule({\r\n    imports: [\r\n        RouterModule.forRoot([\r\n            {\r\n                path: '', component: AppLayoutComponent,\r\n                children: [\r\n                    { path: '', redirectTo: 'app/index', pathMatch: 'full' },\r\n                    { path: 'app', loadChildren: () => import('./demo/components/uikit/uikit.module').then(m => m.UIModule), canActivate:[AuthGuard] },\r\n                    { path: 'utilities', loadChildren: () => import('./demo/components/utilities/utilities.module').then(m => m.UtilitiesModule) },\r\n                    { path: 'documentation', loadChildren: () => import('./demo/components/documentation/documentation.module').then(m => m.DocumentationModule) },\r\n                    { path: 'blocks', loadChildren: () => import('./demo/components/primeblocks/primeblocks.module').then(m => m.PrimeBlocksModule) },\r\n                    { path: 'pages', loadChildren: () => import('./demo/components/pages/pages.module').then(m => m.PagesModule) }\r\n                ]\r\n            },\r\n            { path: 'auth', loadChildren: () => import('./demo/components/auth/auth.module').then(m => m.AuthModule) },\r\n            { path: 'landing', loadChildren: () => import('./demo/components/landing/landing.module').then(m => m.LandingModule) },\r\n            { path: 'notfound', component: NotfoundComponent },\r\n            { path: '**', redirectTo: '/notfound' },\r\n        ], { scrollPositionRestoration: 'enabled', anchorScrolling: 'enabled', onSameUrlNavigation: 'reload' })\r\n    ],\r\n    exports: [RouterModule]\r\n})\r\nexport class AppRoutingModule {\r\n}\r\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,iBAAiB,QAAQ,+CAA+C;AACjF,SAASC,kBAAkB,QAAQ,+BAA+B;AAClE,SAASC,SAAS,QAAQ,0BAA0B;;;AAwBpD,OAAM,MAAOC,gBAAgB;EAAA,QAAAC,CAAA,G;qBAAhBD,gBAAgB;EAAA;EAAA,QAAAE,EAAA,G;UAAhBF;EAAgB;EAAA,QAAAG,EAAA,G;cApBrBP,YAAY,CAACQ,OAAO,CAAC,CACjB;MACIC,IAAI,EAAE,EAAE;MAAEC,SAAS,EAAER,kBAAkB;MACvCS,QAAQ,EAAE,CACN;QAAEF,IAAI,EAAE,EAAE;QAAEG,UAAU,EAAE,WAAW;QAAEC,SAAS,EAAE;MAAM,CAAE,EACxD;QAAEJ,IAAI,EAAE,KAAK;QAAEK,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC;QAAEC,WAAW,EAAC,CAACf,SAAS;MAAC,CAAE,EAClI;QAAEM,IAAI,EAAE,WAAW;QAAEK,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,8CAA8C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,eAAe;MAAC,CAAE,EAC9H;QAAEV,IAAI,EAAE,eAAe;QAAEK,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sDAAsD,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,mBAAmB;MAAC,CAAE,EAC9I;QAAEX,IAAI,EAAE,QAAQ;QAAEK,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,kDAAkD,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,iBAAiB;MAAC,CAAE,EACjI;QAAEZ,IAAI,EAAE,OAAO;QAAEK,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACM,WAAW;MAAC,CAAE;KAErH,EACD;MAAEb,IAAI,EAAE,MAAM;MAAEK,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACO,UAAU;IAAC,CAAE,EAC1G;MAAEd,IAAI,EAAE,SAAS;MAAEK,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACQ,aAAa;IAAC,CAAE,EACtH;MAAEf,IAAI,EAAE,UAAU;MAAEC,SAAS,EAAET;IAAiB,CAAE,EAClD;MAAEQ,IAAI,EAAE,IAAI;MAAEG,UAAU,EAAE;IAAW,CAAE,CAC1C,EAAE;MAAEa,yBAAyB,EAAE,SAAS;MAAEC,eAAe,EAAE,SAAS;MAAEC,mBAAmB,EAAE;IAAQ,CAAE,CAAC,EAEjG3B,YAAY;EAAA;;;2EAEbI,gBAAgB;IAAAwB,OAAA,GAAAC,EAAA,CAAA7B,YAAA;IAAA8B,OAAA,GAFf9B,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}