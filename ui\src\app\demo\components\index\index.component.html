<div class="grid">
    <!-- Summary Cards -->
    <div class="col-12 md:col-6 lg:col-6">
        <p-card class="h-full">
            <ng-template pTemplate="content">
                <div class="flex justify-content-between align-items-center">
                    <div class="flex-1">
                        <div class="flex align-items-center gap-2 mb-3">
                            <fa-icon icon="solar-panel" class="text-primary text-lg"></fa-icon>
                            <span class="text-600 font-medium">Active Energy Systems</span>
                        </div>
                        <div class="text-900 font-bold text-3xl">{{stations.length}}</div>
                        <p-tag value="Online" severity="success" class="mt-2"></p-tag>
                    </div>
                    <div class="flex align-items-center justify-content-center bg-primary-50 border-round-lg"
                         style="width: 4rem; height: 4rem;">
                        <fa-icon icon="solar-panel" class="text-primary text-2xl"></fa-icon>
                    </div>
                </div>
            </ng-template>
        </p-card>
    </div>

    <div class="col-12 md:col-6 lg:col-6">
        <p-card class="h-full">
            <ng-template pTemplate="content">
                <div class="flex justify-content-between align-items-center">
                    <div class="flex-1">
                        <div class="flex align-items-center gap-2 mb-3">
                            <fa-icon icon="bolt" class="text-orange-500 text-lg"></fa-icon>
                            <span class="text-600 font-medium">Combined Power Performance</span>
                        </div>
                        <div class="text-900 font-bold text-3xl">33% (4.124kW)</div>
                        <p-tag value="Optimal" severity="warning" class="mt-2"></p-tag>
                    </div>
                    <div class="flex align-items-center justify-content-center bg-orange-50 border-round-lg"
                         style="width: 4rem; height: 4rem;">
                        <fa-icon icon="bolt" class="text-orange-500 text-2xl"></fa-icon>
                    </div>
                </div>
            </ng-template>
        </p-card>
    </div>
	 

    <!-- Stations List -->
    <div class="col-12">
        <p-card>
            <ng-template pTemplate="header">
                <div class="flex align-items-center justify-content-between p-3">
                    <div class="flex align-items-center gap-2">
                        <fa-icon icon="list" class="text-primary text-xl"></fa-icon>
                        <h2 class="text-2xl font-semibold m-0">Energy Stations</h2>
                        <p-badge *ngIf="totalItems > 0" [value]="totalItems" severity="info"></p-badge>
                    </div>
                    <div class="flex gap-2">
                        <p-button icon="pi pi-filter-slash"
                                  severity="secondary"
                                  size="small"
                                  pTooltip="Clear all filters"
                                  (click)="clearFilters()">
                        </p-button>
                        <p-button icon="pi pi-refresh"
                                  severity="info"
                                  size="small"
                                  [loading]="isRefreshing"
                                  pTooltip="Refresh data"
                                  (click)="refreshData()">
                        </p-button>
                    </div>
                </div>
            </ng-template>

            <ng-template pTemplate="content">
                <!-- Filters -->
                <div class="flex flex-column md:flex-row md:justify-content-between gap-3 mb-4">
                    <div class="flex gap-2">
                        <p-dropdown [options]="sortOptionsCountry"
                                   placeholder="Filter by Provider"
                                   [(ngModel)]="selectedProvider"
                                   (onChange)="onProviderChange($event)"
                                   [showClear]="true"
                                   class="w-full md:w-auto">
                        </p-dropdown>
                        <p-dropdown [options]="sortOptionsStatus"
                                   placeholder="Filter by Status"
                                   [(ngModel)]="selectedStatus"
                                   (onChange)="onStatusChange($event)"
                                   [showClear]="true"
                                   class="w-full md:w-auto">
                        </p-dropdown>
                    </div>
                    <span class="p-input-icon-left">
                        <i class="pi pi-search"></i>
                        <input type="search"
                               pInputText
                               [(ngModel)]="searchTerm"
                               (input)="onSearchChange($event)"
                               placeholder="Search stations..."
                               class="w-full md:w-auto">
                    </span>
                </div>

                <!-- Stations Grid -->
                <div class="grid">
                    <div *ngIf="paginatedStations.length === 0 && !isRefreshing" class="col-12">
                        <div class="text-center py-6">
                            <fa-icon icon="inbox" class="text-6xl text-300 mb-3"></fa-icon>
                            <p class="text-lg text-600">No stations found matching your criteria.</p>
                            <p-button label="Clear Filters"
                                      icon="pi pi-filter-slash"
                                      severity="secondary"
                                      size="small"
                                      (click)="clearFilters()">
                            </p-button>
                        </div>
                    </div>

                    <div *ngFor="let station of paginatedStations" class="col-12">
                        <p-card class="mb-3"
                                [ngClass]="'station-status-' + station.status?.toLowerCase()"
                                pRipple>
                            <ng-template pTemplate="header">
                                <div class="bg-primary-50 p-3 border-round-top">
                                    <div class="flex align-items-center justify-content-between">
                                        <div class="flex align-items-center gap-2">
                                            <p-tag [value]="station.provider"
                                                   severity="info"
                                                   icon="pi pi-server">
                                            </p-tag>
                                            <p-tag [value]="station.status?.toLowerCase() || 'unknown'"
                                                   [severity]="station.status?.toLowerCase() === 'online' ? 'success' : 'warning'">
                                            </p-tag>
                                        </div>
                                        <div class="flex align-items-center gap-2 text-600">
                                            <fa-icon icon="clock" class="text-sm"></fa-icon>
                                            <span class="text-sm">{{getStationLastUpdate(station.id)}}</span>
                                        </div>
                                    </div>
                                </div>
                            </ng-template>

                            <ng-template pTemplate="content">
                                <div class="grid align-items-center">
                                    <!-- Station Info -->
                                    <div class="col-12 md:col-6 lg:col-3">
                                        <div class="flex flex-column gap-2">
                                            <h3 class="text-xl font-bold m-0">
                                                <a [routerLink]="['/app/station',station.id]"
                                                   class="text-primary no-underline hover:underline">
                                                    {{station.name}}
                                                </a>
                                            </h3>
                                            <div class="text-600 text-sm">{{station.location || 'Location not specified'}}</div>
                                        </div>
                                    </div>

                                    <!-- Power & Irradiance -->
                                    <div class="col-12 md:col-6 lg:col-2">
                                        <div class="text-center">
                                            <div class="flex align-items-center justify-content-center gap-1 mb-1">
                                                <fa-icon icon="bolt" class="text-orange-500"></fa-icon>
                                                <span class="font-bold">{{getStationsSumData(station.id)}}kW</span>
                                            </div>
                                            <div class="flex align-items-center justify-content-center gap-1">
                                                <fa-icon icon="sun" class="text-yellow-500"></fa-icon>
                                                <span class="text-sm text-600">{{station.irradiance}}kWh/m²</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Chart -->
                                    <div class="col-12 md:col-6 lg:col-2">
                                        <div class="text-center chart-container">
                                            <c-chart *ngIf="getStationsRealTimeData(station)"
                                                     [data]="getStationsRealTimeData(station)"
                                                     [options]="lineOptions"
                                                     type="line"
                                                     class="mx-auto chart-section"
                                                     height="50"
                                                     width="120" />
                                            <div *ngIf="!getStationsRealTimeData(station)" class="text-center p-2">
                                                <fa-icon icon="chart-line" class="text-300 text-2xl mb-1"></fa-icon>
                                                <p class="text-xs text-600 mt-1 mb-0">No chart data</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Equipment Badges -->
                                    <div class="col-12 md:col-6 lg:col-3">
                                        <div class="flex flex-wrap gap-2 justify-content-center">
                                            <p-badge *ngIf="getStationsInverters(station.id) > 0"
                                                     [value]="'Inv: ' + getStationsInverters(station.id)"
                                                     severity="contrast"
                                                     size="small">
                                            </p-badge>
                                            <p-badge *ngIf="station.mmpt && station.mmpt > 0"
                                                     [value]="'MMPT: ' + station.mmpt"
                                                     severity="success"
                                                     size="small">
                                            </p-badge>
                                            <p-badge *ngIf="station.string && station.string > 0"
                                                     [value]="'Str: ' + station.string"
                                                     severity="info"
                                                     size="small">
                                            </p-badge>
                                            <p-badge *ngIf="station.pvn && station.pvn > 0"
                                                     [value]="'PVN: ' + station.pvn"
                                                     severity="warning"
                                                     size="small">
                                            </p-badge>
                                            <div *ngIf="!hasEquipmentData(station)" class="text-center">
                                                <fa-icon icon="cog" class="text-300 text-lg"></fa-icon>
                                                <p class="text-xs text-600 mt-1 mb-0">No equipment data</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Weather -->
                                    <div class="col-12 md:col-6 lg:col-2">
                                        <div class="text-center">
                                            <div class="flex align-items-center justify-content-center gap-2">
                                                <i class="pi pi-cloud text-blue-500 text-xl"></i>
                                                <span class="text-xl font-semibold">{{station.temperature}}°C</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </ng-template>
                        </p-card>
                    </div>
                </div>

                <!-- Pagination -->
                <div *ngIf="totalItems > itemsPerPage" class="mt-4">
                    <p-paginator [rows]="itemsPerPage"
                                 [totalRecords]="totalItems"
                                 [first]="currentPage * itemsPerPage"
                                 [rowsPerPageOptions]="[5, 10, 20, 50]"
                                 [showCurrentPageReport]="true"
                                 currentPageReportTemplate="Showing {first} to {last} of {totalRecords} stations"
                                 (onPageChange)="onPageChange($event)">
                    </p-paginator>
                </div>
            </ng-template>
        </p-card>
    </div>
</div>