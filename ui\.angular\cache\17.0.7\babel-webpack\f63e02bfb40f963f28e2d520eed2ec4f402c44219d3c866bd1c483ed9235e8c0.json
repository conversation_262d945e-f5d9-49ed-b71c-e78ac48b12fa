{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IndexComponent } from './index.component';\nimport { ChartModule } from 'primeng/chart';\nimport { MenuModule } from 'primeng/menu';\nimport { TableModule } from 'primeng/table';\nimport { ButtonModule } from 'primeng/button';\nimport { StyleClassModule } from 'primeng/styleclass';\nimport { PanelMenuModule } from 'primeng/panelmenu';\nimport { IndexRoutingModule } from './index-routing.module';\nimport { DataViewModule } from 'primeng/dataview';\nimport { KnobModule } from 'primeng/knob';\nimport { PickListModule } from 'primeng/picklist';\nimport { OrderListModule } from 'primeng/orderlist';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { RatingModule } from 'primeng/rating';\nimport { FontAwesomeModule } from '@fortawesome/angular-fontawesome';\nimport { fas } from '@fortawesome/free-solid-svg-icons';\nimport { ChartjsComponent } from '@coreui/angular-chartjs';\nimport { ColComponent, RowComponent, TextColorDirective, WidgetStatEComponent } from '@coreui/angular';\nimport { BadgeModule } from 'primeng/badge';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@fortawesome/angular-fontawesome\";\nexport class IndexModule {\n  constructor(library) {\n    //library.addIcons(faSmile);\n    library.addIconPacks(fas);\n  }\n  static #_ = this.ɵfac = function IndexModule_Factory(t) {\n    return new (t || IndexModule)(i0.ɵɵinject(i1.FaIconLibrary));\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: IndexModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, FormsModule, ChartModule, MenuModule, TableModule, StyleClassModule, PanelMenuModule, ButtonModule, IndexRoutingModule, DataViewModule, KnobModule, CommonModule, FormsModule, DataViewModule, PickListModule, OrderListModule, InputTextModule, DropdownModule, RatingModule, ButtonModule, FontAwesomeModule, ChartjsComponent, WidgetStatEComponent, ColComponent, RowComponent, BadgeModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(IndexModule, {\n    declarations: [IndexComponent],\n    imports: [CommonModule, FormsModule, ChartModule, MenuModule, TableModule, StyleClassModule, PanelMenuModule, ButtonModule, IndexRoutingModule, DataViewModule, KnobModule, CommonModule, FormsModule, DataViewModule, PickListModule, OrderListModule, InputTextModule, DropdownModule, RatingModule, ButtonModule, FontAwesomeModule, ChartjsComponent, WidgetStatEComponent, ColComponent, RowComponent, TextColorDirective, BadgeModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "IndexComponent", "ChartModule", "MenuModule", "TableModule", "ButtonModule", "StyleClassModule", "PanelMenuModule", "IndexRoutingModule", "DataViewModule", "KnobModule", "PickListModule", "OrderListModule", "InputTextModule", "DropdownModule", "RatingModule", "FontAwesomeModule", "fas", "ChartjsComponent", "ColComponent", "RowComponent", "TextColorDirective", "WidgetStatEComponent", "BadgeModule", "IndexModule", "constructor", "library", "addIconPacks", "_", "i0", "ɵɵinject", "i1", "FaIconLibrary", "_2", "_3", "declarations", "imports"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\index\\index.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { IndexComponent } from './index.component';\r\nimport { ChartModule } from 'primeng/chart';\r\nimport { MenuModule } from 'primeng/menu';\r\nimport { TableModule } from 'primeng/table';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { StyleClassModule } from 'primeng/styleclass';\r\nimport { PanelMenuModule } from 'primeng/panelmenu';\r\nimport { IndexRoutingModule } from './index-routing.module';\r\nimport { DataViewModule } from 'primeng/dataview';\r\nimport { KnobModule } from 'primeng/knob';\r\nimport { PickListModule } from 'primeng/picklist';\r\nimport { OrderListModule } from 'primeng/orderlist';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { RatingModule } from 'primeng/rating';\r\nimport { FaIconLibrary ,FontAwesomeModule } from '@fortawesome/angular-fontawesome';\r\nimport { fas } from '@fortawesome/free-solid-svg-icons';\r\nimport { ChartjsComponent } from '@coreui/angular-chartjs';\r\nimport { ColComponent, RowComponent, TextColorDirective, WidgetStatEComponent } from '@coreui/angular';\r\nimport { ChartData, ChartOptions } from 'chart.js';\r\nimport { BadgeModule } from 'primeng/badge';\r\n\r\n@NgModule({\r\n    imports: [\r\n        CommonModule,\r\n        FormsModule,\r\n        ChartModule,\r\n        MenuModule,\r\n        TableModule,\r\n        StyleClassModule,\r\n        PanelMenuModule,\r\n        ButtonModule,\r\n        IndexRoutingModule,\r\n        DataViewModule,\r\n        KnobModule,\r\n        CommonModule,\r\n\t\tFormsModule,\r\n\t\tDataViewModule,\r\n\t\tPickListModule,\r\n\t\tOrderListModule,\r\n\t\tInputTextModule,\r\n\t\tDropdownModule,\r\n\t\tRatingModule,\r\n\t\tButtonModule,\r\n        FontAwesomeModule,\r\n        ChartjsComponent,\r\n        WidgetStatEComponent,\r\n        ColComponent,\r\n        RowComponent,\r\n        TextColorDirective,\r\n        BadgeModule\r\n\r\n    ],\r\n    declarations: [IndexComponent]\r\n})\r\nexport class IndexModule {\r\n    constructor(library: FaIconLibrary){\r\n        //library.addIcons(faSmile);\r\n        library.addIconPacks(fas);\r\n    }\r\n }"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAAwBC,iBAAiB,QAAQ,kCAAkC;AACnF,SAASC,GAAG,QAAQ,mCAAmC;AACvD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,YAAY,EAAEC,YAAY,EAAEC,kBAAkB,EAAEC,oBAAoB,QAAQ,iBAAiB;AAEtG,SAASC,WAAW,QAAQ,eAAe;;;AAmC3C,OAAM,MAAOC,WAAW;EACpBC,YAAYC,OAAsB;IAC9B;IACAA,OAAO,CAACC,YAAY,CAACV,GAAG,CAAC;EAC7B;EAAC,QAAAW,CAAA,G;qBAJQJ,WAAW,EAAAK,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAXT;EAAW;EAAA,QAAAU,EAAA,G;cA/BhBnC,YAAY,EACZC,WAAW,EACXE,WAAW,EACXC,UAAU,EACVC,WAAW,EACXE,gBAAgB,EAChBC,eAAe,EACfF,YAAY,EACZG,kBAAkB,EAClBC,cAAc,EACdC,UAAU,EACVX,YAAY,EAClBC,WAAW,EACXS,cAAc,EACdE,cAAc,EACdC,eAAe,EACfC,eAAe,EACfC,cAAc,EACdC,YAAY,EACZV,YAAY,EACNW,iBAAiB,EACjBE,gBAAgB,EAChBI,oBAAoB,EACpBH,YAAY,EACZC,YAAY,EAEZG,WAAW;EAAA;;;2EAKNC,WAAW;IAAAW,YAAA,GAFLlC,cAAc;IAAAmC,OAAA,GA7BzBrC,YAAY,EACZC,WAAW,EACXE,WAAW,EACXC,UAAU,EACVC,WAAW,EACXE,gBAAgB,EAChBC,eAAe,EACfF,YAAY,EACZG,kBAAkB,EAClBC,cAAc,EACdC,UAAU,EACVX,YAAY,EAClBC,WAAW,EACXS,cAAc,EACdE,cAAc,EACdC,eAAe,EACfC,eAAe,EACfC,cAAc,EACdC,YAAY,EACZV,YAAY,EACNW,iBAAiB,EACjBE,gBAAgB,EAChBI,oBAAoB,EACpBH,YAAY,EACZC,YAAY,EACZC,kBAAkB,EAClBE,WAAW;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}