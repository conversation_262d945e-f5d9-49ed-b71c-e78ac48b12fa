{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ProfileRoutingModule } from './profile-routing.module';\nimport { SharedModule } from '../../../shared/shared.module';\n// PrimeNG Modules\nimport { ButtonModule } from 'primeng/button';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { CardModule } from 'primeng/card';\nimport { TagModule } from 'primeng/tag';\nimport { ToastModule } from 'primeng/toast';\nimport { RippleModule } from 'primeng/ripple';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { MessageModule } from 'primeng/message';\nimport { MessagesModule } from 'primeng/messages';\n// FontAwesome\nimport { FontAwesomeModule } from '@fortawesome/angular-fontawesome';\nimport * as i0 from \"@angular/core\";\nexport let ProfileModule = /*#__PURE__*/(() => {\n  class ProfileModule {\n    static #_ = this.ɵfac = function ProfileModule_Factory(t) {\n      return new (t || ProfileModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ProfileModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, ReactiveFormsModule, ProfileRoutingModule, SharedModule, ButtonModule, InputTextModule, CardModule, TagModule, ToastModule, RippleModule, TooltipModule, MessageModule, MessagesModule, FontAwesomeModule]\n    });\n  }\n  return ProfileModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}