<div class="profile-container">
    <!-- Modern Breadcrumb -->
    <div class="col-12 mb-4">
        <app-modern-breadcrumb [items]="breadcrumbItems"></app-modern-breadcrumb>
    </div>

    <!-- Profile Header -->
    <div class="col-12 mb-4">
        <div class="profile-header">
            <div class="profile-avatar-section">
                <div class="profile-avatar-large">
                    <fa-icon icon="user" class="avatar-icon"></fa-icon>
                </div>
                <div class="profile-header-info">
                    <h1 class="profile-name">{{ user.firstName }} {{ user.lastName }}</h1>
                    <p class="profile-username">{{ '@' + user.username }}</p>
                    <div class="profile-badges">
                        <p-tag [value]="user.role" 
                               [severity]="getRoleColor(user.role)" 
                               icon="pi pi-shield">
                        </p-tag>
                        <p-tag [value]="user.status || 'Active'"
                               [severity]="getStatusColorByStatus(user.status || 'Active')"
                               icon="pi pi-circle-fill">
                        </p-tag>
                    </div>
                </div>
            </div>
            <div class="profile-actions">
                <p-button *ngIf="!isEditing" 
                          label="Edit Profile" 
                          icon="pi pi-pencil" 
                          severity="info"
                          (click)="toggleEdit()">
                </p-button>
                <div *ngIf="isEditing" class="edit-actions">
                    <p-button label="Cancel" 
                              icon="pi pi-times" 
                              severity="secondary"
                              [text]="true"
                              (click)="toggleEdit()">
                    </p-button>
                    <p-button label="Save Changes" 
                              icon="pi pi-check" 
                              severity="success"
                              [loading]="isLoading"
                              (click)="saveProfile()">
                    </p-button>
                </div>
            </div>
        </div>
    </div>

    <div class="grid">
        <!-- Profile Information Card -->
        <div class="col-12 lg:col-8">
            <p-card>
                <ng-template pTemplate="header">
                    <div class="card-header">
                        <div class="flex align-items-center gap-2">
                            <fa-icon icon="user-circle" class="text-primary text-xl"></fa-icon>
                            <h3 class="m-0">Profile Information</h3>
                        </div>
                        <p-tag *ngIf="isEditing" value="Editing" severity="warning" icon="pi pi-pencil"></p-tag>
                    </div>
                </ng-template>

                <ng-template pTemplate="content">
                    <form [formGroup]="profileForm" class="profile-form">
                        <div class="grid">
                            <!-- Username (Read-only) -->
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="username" class="field-label">
                                        <fa-icon icon="user" class="mr-1"></fa-icon>
                                        Username
                                    </label>
                                    <input pInputText 
                                           id="username"
                                           formControlName="username"
                                           class="w-full"
                                           readonly>
                                    <small class="field-help">Username cannot be changed</small>
                                </div>
                            </div>

                            <!-- Email -->
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="email" class="field-label required">
                                        <fa-icon icon="envelope" class="mr-1"></fa-icon>
                                        Email Address
                                    </label>
                                    <input pInputText 
                                           id="email"
                                           formControlName="email"
                                           [readonly]="!isEditing"
                                           [class]="'w-full ' + (getFieldError('email') ? 'ng-invalid ng-dirty' : '')"
                                           placeholder="Enter your email address">
                                    <small *ngIf="getFieldError('email')" class="field-error">
                                        {{ getFieldError('email') }}
                                    </small>
                                </div>
                            </div>

                            <!-- First Name -->
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="firstName" class="field-label">
                                        <fa-icon icon="id-card" class="mr-1"></fa-icon>
                                        First Name
                                    </label>
                                    <input pInputText 
                                           id="firstName"
                                           formControlName="firstName"
                                           [readonly]="!isEditing"
                                           class="w-full"
                                           placeholder="Enter your first name">
                                </div>
                            </div>

                            <!-- Last Name -->
                            <div class="col-12 md:col-6">
                                <div class="field">
                                    <label for="lastName" class="field-label">
                                        <fa-icon icon="id-card" class="mr-1"></fa-icon>
                                        Last Name
                                    </label>
                                    <input pInputText 
                                           id="lastName"
                                           formControlName="lastName"
                                           [readonly]="!isEditing"
                                           class="w-full"
                                           placeholder="Enter your last name">
                                </div>
                            </div>
                        </div>
                    </form>
                </ng-template>
            </p-card>
        </div>

        <!-- Account Details Card -->
        <div class="col-12 lg:col-4">
            <p-card>
                <ng-template pTemplate="header">
                    <div class="card-header">
                        <div class="flex align-items-center gap-2">
                            <fa-icon icon="info-circle" class="text-info text-xl"></fa-icon>
                            <h3 class="m-0">Account Details</h3>
                        </div>
                    </div>
                </ng-template>

                <ng-template pTemplate="content">
                    <div class="account-details">
                        <!-- Registration Date -->
                        <div class="detail-item">
                            <div class="detail-label">
                                <fa-icon icon="calendar-plus" class="text-green-600"></fa-icon>
                                <span>Member Since</span>
                            </div>
                            <div class="detail-value">{{ formatDate(user.registrationDate) }}</div>
                        </div>

                        <!-- Last Login -->
                        <div class="detail-item">
                            <div class="detail-label">
                                <fa-icon icon="clock" class="text-blue-600"></fa-icon>
                                <span>Last Login</span>
                            </div>
                            <div class="detail-value">{{ formatDateTime(user.lastLogin) }}</div>
                        </div>

                        <!-- Account Status -->
                        <div class="detail-item">
                            <div class="detail-label">
                                <fa-icon icon="shield-alt" class="text-purple-600"></fa-icon>
                                <span>Account Status</span>
                            </div>
                            <div class="detail-value">
                                <p-tag [value]="user.status || 'Active'"
                                       [severity]="getStatusColorByStatus(user.status || 'Active')"
                                       icon="pi pi-circle-fill">
                                </p-tag>
                            </div>
                        </div>

                        <!-- User Role -->
                        <div class="detail-item">
                            <div class="detail-label">
                                <fa-icon icon="user-tag" class="text-orange-600"></fa-icon>
                                <span>Role</span>
                            </div>
                            <div class="detail-value">
                                <p-tag [value]="user.role" 
                                       [severity]="getRoleColor(user.role)" 
                                       icon="pi pi-shield">
                                </p-tag>
                            </div>
                        </div>
                    </div>
                </ng-template>
            </p-card>
        </div>
    </div>

    <!-- Toast Messages -->
    <p-toast></p-toast>
</div>
