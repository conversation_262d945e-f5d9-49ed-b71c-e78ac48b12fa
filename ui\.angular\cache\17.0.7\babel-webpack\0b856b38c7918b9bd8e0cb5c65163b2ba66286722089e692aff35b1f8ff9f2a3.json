{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../service/auth.service\";\nimport * as i3 from \"../../service/user.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../../../shared/components/modern-breadcrumb/modern-breadcrumb.component\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/inputtext\";\nimport * as i9 from \"primeng/card\";\nimport * as i10 from \"primeng/tag\";\nimport * as i11 from \"primeng/toast\";\nimport * as i12 from \"@fortawesome/angular-fontawesome\";\nfunction ProfileComponent_p_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 21);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_p_button_17_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.toggleEdit());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProfileComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"p-button\", 23);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_18_Template_p_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.toggleEdit());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"p-button\", 24);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_18_Template_p_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.saveProfile());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"text\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"loading\", ctx_r1.isLoading);\n  }\n}\nfunction ProfileComponent_ng_template_22_p_tag_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-tag\", 30);\n  }\n}\nfunction ProfileComponent_ng_template_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26);\n    i0.ɵɵelement(2, \"fa-icon\", 27);\n    i0.ɵɵelementStart(3, \"h3\", 28);\n    i0.ɵɵtext(4, \"Profile Information\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, ProfileComponent_ng_template_22_p_tag_5_Template, 1, 0, \"p-tag\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isEditing);\n  }\n}\nfunction ProfileComponent_ng_template_23_small_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.getFieldError(\"email\"), \" \");\n  }\n}\nfunction ProfileComponent_ng_template_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"form\", 31)(1, \"div\", 16)(2, \"div\", 32)(3, \"div\", 33)(4, \"label\", 34);\n    i0.ɵɵelement(5, \"fa-icon\", 35);\n    i0.ɵɵtext(6, \" Username \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"input\", 36);\n    i0.ɵɵelementStart(8, \"small\", 37);\n    i0.ɵɵtext(9, \"Username cannot be changed\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 32)(11, \"div\", 33)(12, \"label\", 38);\n    i0.ɵɵelement(13, \"fa-icon\", 39);\n    i0.ɵɵtext(14, \" Email Address \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"input\", 40);\n    i0.ɵɵtemplate(16, ProfileComponent_ng_template_23_small_16_Template, 2, 1, \"small\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 32)(18, \"div\", 33)(19, \"label\", 42);\n    i0.ɵɵelement(20, \"fa-icon\", 43);\n    i0.ɵɵtext(21, \" First Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"input\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 32)(24, \"div\", 33)(25, \"label\", 45);\n    i0.ɵɵelement(26, \"fa-icon\", 43);\n    i0.ɵɵtext(27, \" Last Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(28, \"input\", 46);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r3.profileForm);\n    i0.ɵɵadvance(15);\n    i0.ɵɵclassMap(\"w-full \" + (ctx_r3.getFieldError(\"email\") ? \"ng-invalid ng-dirty\" : \"\"));\n    i0.ɵɵproperty(\"readonly\", !ctx_r3.isEditing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.getFieldError(\"email\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"readonly\", !ctx_r3.isEditing);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"readonly\", !ctx_r3.isEditing);\n  }\n}\nfunction ProfileComponent_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26);\n    i0.ɵɵelement(2, \"fa-icon\", 48);\n    i0.ɵɵelementStart(3, \"h3\", 28);\n    i0.ɵɵtext(4, \"Account Details\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProfileComponent_ng_template_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50)(2, \"div\", 51);\n    i0.ɵɵelement(3, \"fa-icon\", 52);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Member Since\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 53);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 50)(9, \"div\", 51);\n    i0.ɵɵelement(10, \"fa-icon\", 54);\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"Last Login\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 53);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 50)(16, \"div\", 51);\n    i0.ɵɵelement(17, \"fa-icon\", 55);\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19, \"Account Status\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 53);\n    i0.ɵɵelement(21, \"p-tag\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 50)(23, \"div\", 51);\n    i0.ɵɵelement(24, \"fa-icon\", 56);\n    i0.ɵɵelementStart(25, \"span\");\n    i0.ɵɵtext(26, \"Role\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 53);\n    i0.ɵɵelement(28, \"p-tag\", 11);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r5.formatDate(ctx_r5.user.registrationDate));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r5.formatDateTime(ctx_r5.user.lastLogin));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"value\", ctx_r5.getStatusText(ctx_r5.user.isActive))(\"severity\", ctx_r5.getStatusColor(ctx_r5.user.isActive));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"value\", ctx_r5.user.role)(\"severity\", ctx_r5.getRoleColor(ctx_r5.user.role));\n  }\n}\nexport class ProfileComponent {\n  constructor(fb, authService, userService, messageService) {\n    this.fb = fb;\n    this.authService = authService;\n    this.userService = userService;\n    this.messageService = messageService;\n    this.destroy$ = new Subject();\n    this.user = {};\n    this.isEditing = false;\n    this.isLoading = false;\n    this.breadcrumbItems = [{\n      label: 'Dashboard',\n      routerLink: '/app/index'\n    }, {\n      label: 'Settings',\n      routerLink: '/app/settings'\n    }, {\n      label: 'Profile'\n    }];\n    this.initializeForm();\n  }\n  ngOnInit() {\n    this.loadUserData();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  initializeForm() {\n    this.profileForm = this.fb.group({\n      username: [{\n        value: '',\n        disabled: true\n      }],\n      email: ['', [Validators.required, Validators.email]],\n      firstName: [''],\n      lastName: ['']\n    });\n  }\n  loadUserData() {\n    this.isLoading = true;\n    this.userService.getUser().pipe(takeUntil(this.destroy$)).subscribe({\n      next: user => {\n        this.user = {\n          id: user.userId?.toString() || '',\n          username: user.username || '',\n          email: user.email || '',\n          role: user.role || 'User',\n          registrationDate: user.createdAt || new Date().toISOString(),\n          firstName: user.firstName || '',\n          lastName: user.lastName || '',\n          isActive: user.status === 'Active',\n          lastLogin: new Date().toISOString(),\n          status: user.status || 'Active'\n        };\n        // Update form with user data\n        this.profileForm.patchValue({\n          username: this.user.username,\n          email: this.user.email,\n          firstName: this.user.firstName,\n          lastName: this.user.lastName\n        });\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error loading user data:', error);\n        this.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to load user data. Please try again.'\n        });\n        this.isLoading = false;\n      }\n    });\n  }\n  toggleEdit() {\n    this.isEditing = !this.isEditing;\n    if (!this.isEditing) {\n      // Cancel editing - reset form\n      this.profileForm.patchValue({\n        email: this.user.email,\n        firstName: this.user.firstName,\n        lastName: this.user.lastName\n      });\n    }\n  }\n  saveProfile() {\n    if (this.profileForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n    this.isLoading = true;\n    const updateRequest = {\n      email: this.profileForm.get('email')?.value,\n      firstName: this.profileForm.get('firstName')?.value,\n      lastName: this.profileForm.get('lastName')?.value\n    };\n    this.userService.updateUser(updateRequest).pipe(takeUntil(this.destroy$)).subscribe({\n      next: response => {\n        if (response.success && response.user) {\n          // Update local user data\n          this.user = {\n            ...this.user,\n            email: response.user.email || this.user.email,\n            firstName: response.user.firstName || this.user.firstName,\n            lastName: response.user.lastName || this.user.lastName\n          };\n          this.messageService.add({\n            severity: 'success',\n            summary: 'Success',\n            detail: response.message || 'Profile updated successfully!'\n          });\n          this.isEditing = false;\n        } else {\n          this.messageService.add({\n            severity: 'error',\n            summary: 'Error',\n            detail: response.message || 'Failed to update profile.'\n          });\n        }\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('Error updating profile:', error);\n        let errorMessage = 'Failed to update profile. Please try again.';\n        if (error.error && error.error.message) {\n          errorMessage = error.error.message;\n        } else if (error.error && typeof error.error === 'string') {\n          errorMessage = error.error;\n        }\n        this.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: errorMessage\n        });\n        this.isLoading = false;\n      }\n    });\n  }\n  markFormGroupTouched() {\n    Object.keys(this.profileForm.controls).forEach(key => {\n      const control = this.profileForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  getFieldError(fieldName) {\n    const control = this.profileForm.get(fieldName);\n    if (control?.errors && control.touched) {\n      if (control.errors['required']) {\n        return `${fieldName} is required`;\n      }\n      if (control.errors['email']) {\n        return 'Please enter a valid email address';\n      }\n    }\n    return '';\n  }\n  formatDate(dateString) {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-GB', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  }\n  formatDateTime(dateString) {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleString('en-GB', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  getRoleColor(role) {\n    switch (role?.toLowerCase()) {\n      case 'admin':\n        return 'danger';\n      case 'manager':\n        return 'warning';\n      case 'user':\n        return 'info';\n      default:\n        return 'secondary';\n    }\n  }\n  getStatusColor(isActive) {\n    return isActive ? 'success' : 'danger';\n  }\n  getStatusText(isActive) {\n    return isActive ? 'Active' : 'Inactive';\n  }\n  static #_ = this.ɵfac = function ProfileComponent_Factory(t) {\n    return new (t || ProfileComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.UserService), i0.ɵɵdirectiveInject(i4.MessageService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProfileComponent,\n    selectors: [[\"app-profile\"]],\n    decls: 29,\n    vars: 10,\n    consts: [[1, \"profile-container\"], [1, \"col-12\", \"mb-4\"], [3, \"items\"], [1, \"profile-header\"], [1, \"profile-avatar-section\"], [1, \"profile-avatar-large\"], [\"icon\", \"user\", 1, \"avatar-icon\"], [1, \"profile-header-info\"], [1, \"profile-name\"], [1, \"profile-username\"], [1, \"profile-badges\"], [\"icon\", \"pi pi-shield\", 3, \"value\", \"severity\"], [\"icon\", \"pi pi-circle-fill\", 3, \"value\", \"severity\"], [1, \"profile-actions\"], [\"label\", \"Edit Profile\", \"icon\", \"pi pi-pencil\", \"severity\", \"info\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"edit-actions\", 4, \"ngIf\"], [1, \"grid\"], [1, \"col-12\", \"lg:col-8\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"content\"], [1, \"col-12\", \"lg:col-4\"], [\"label\", \"Edit Profile\", \"icon\", \"pi pi-pencil\", \"severity\", \"info\", 3, \"click\"], [1, \"edit-actions\"], [\"label\", \"Cancel\", \"icon\", \"pi pi-times\", \"severity\", \"secondary\", 3, \"text\", \"click\"], [\"label\", \"Save Changes\", \"icon\", \"pi pi-check\", \"severity\", \"success\", 3, \"loading\", \"click\"], [1, \"card-header\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"icon\", \"user-circle\", 1, \"text-primary\", \"text-xl\"], [1, \"m-0\"], [\"value\", \"Editing\", \"severity\", \"warning\", \"icon\", \"pi pi-pencil\", 4, \"ngIf\"], [\"value\", \"Editing\", \"severity\", \"warning\", \"icon\", \"pi pi-pencil\"], [1, \"profile-form\", 3, \"formGroup\"], [1, \"col-12\", \"md:col-6\"], [1, \"field\"], [\"for\", \"username\", 1, \"field-label\"], [\"icon\", \"user\", 1, \"mr-1\"], [\"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"username\", \"readonly\", \"\", 1, \"w-full\"], [1, \"field-help\"], [\"for\", \"email\", 1, \"field-label\", \"required\"], [\"icon\", \"envelope\", 1, \"mr-1\"], [\"pInputText\", \"\", \"id\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email address\", 3, \"readonly\"], [\"class\", \"field-error\", 4, \"ngIf\"], [\"for\", \"firstName\", 1, \"field-label\"], [\"icon\", \"id-card\", 1, \"mr-1\"], [\"pInputText\", \"\", \"id\", \"firstName\", \"formControlName\", \"firstName\", \"placeholder\", \"Enter your first name\", 1, \"w-full\", 3, \"readonly\"], [\"for\", \"lastName\", 1, \"field-label\"], [\"pInputText\", \"\", \"id\", \"lastName\", \"formControlName\", \"lastName\", \"placeholder\", \"Enter your last name\", 1, \"w-full\", 3, \"readonly\"], [1, \"field-error\"], [\"icon\", \"info-circle\", 1, \"text-info\", \"text-xl\"], [1, \"account-details\"], [1, \"detail-item\"], [1, \"detail-label\"], [\"icon\", \"calendar-plus\", 1, \"text-green-600\"], [1, \"detail-value\"], [\"icon\", \"clock\", 1, \"text-blue-600\"], [\"icon\", \"shield-alt\", 1, \"text-purple-600\"], [\"icon\", \"user-tag\", 1, \"text-orange-600\"]],\n    template: function ProfileComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"app-modern-breadcrumb\", 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 1)(4, \"div\", 3)(5, \"div\", 4)(6, \"div\", 5);\n        i0.ɵɵelement(7, \"fa-icon\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"div\", 7)(9, \"h1\", 8);\n        i0.ɵɵtext(10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"p\", 9);\n        i0.ɵɵtext(12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"div\", 10);\n        i0.ɵɵelement(14, \"p-tag\", 11)(15, \"p-tag\", 12);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(16, \"div\", 13);\n        i0.ɵɵtemplate(17, ProfileComponent_p_button_17_Template, 1, 0, \"p-button\", 14)(18, ProfileComponent_div_18_Template, 3, 2, \"div\", 15);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(19, \"div\", 16)(20, \"div\", 17)(21, \"p-card\");\n        i0.ɵɵtemplate(22, ProfileComponent_ng_template_22_Template, 6, 1, \"ng-template\", 18)(23, ProfileComponent_ng_template_23_Template, 29, 7, \"ng-template\", 19);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(24, \"div\", 20)(25, \"p-card\");\n        i0.ɵɵtemplate(26, ProfileComponent_ng_template_26_Template, 5, 0, \"ng-template\", 18)(27, ProfileComponent_ng_template_27_Template, 29, 6, \"ng-template\", 19);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelement(28, \"p-toast\");\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"items\", ctx.breadcrumbItems);\n        i0.ɵɵadvance(8);\n        i0.ɵɵtextInterpolate2(\"\", ctx.user.firstName, \" \", ctx.user.lastName, \"\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(\"@\" + ctx.user.username);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"value\", ctx.user.role)(\"severity\", ctx.getRoleColor(ctx.user.role));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"value\", ctx.getStatusText(ctx.user.isActive))(\"severity\", ctx.getStatusColor(ctx.user.isActive));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isEditing);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isEditing);\n      }\n    },\n    dependencies: [i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.ModernBreadcrumbComponent, i7.Button, i4.PrimeTemplate, i8.InputText, i9.Card, i10.Tag, i11.Toast, i12.FaIconComponent],\n    styles: [\".profile-container[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.profile-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 2rem;\\n  background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);\\n  border-radius: 16px;\\n  border: 1px solid var(--primary-200);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  margin-bottom: 1.5rem;\\n}\\n@media (max-width: 768px) {\\n  .profile-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1.5rem;\\n    text-align: center;\\n  }\\n}\\n\\n.profile-avatar-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1.5rem;\\n}\\n@media (max-width: 768px) {\\n  .profile-avatar-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n}\\n\\n.profile-avatar-large[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);\\n  border: 4px solid white;\\n}\\n.profile-avatar-large[_ngcontent-%COMP%]   .avatar-icon[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  color: white;\\n}\\n\\n.profile-header-info[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 700;\\n  margin: 0 0 0.5rem 0;\\n  color: var(--text-color);\\n}\\n.profile-header-info[_ngcontent-%COMP%]   .profile-username[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  color: var(--text-color-secondary);\\n  margin: 0 0 1rem 0;\\n  font-weight: 500;\\n}\\n.profile-header-info[_ngcontent-%COMP%]   .profile-badges[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.75rem;\\n  flex-wrap: wrap;\\n}\\n@media (max-width: 768px) {\\n  .profile-header-info[_ngcontent-%COMP%]   .profile-badges[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n}\\n\\n.profile-actions[_ngcontent-%COMP%]   .edit-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.75rem;\\n  align-items: center;\\n}\\n@media (max-width: 768px) {\\n  .profile-actions[_ngcontent-%COMP%]   .edit-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    width: 100%;\\n  }\\n}\\n\\n.card-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 1.5rem;\\n  background: var(--surface-50);\\n  border-bottom: 1px solid var(--surface-border);\\n}\\n.card-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: var(--text-color);\\n  font-weight: 600;\\n}\\n\\n.profile-form[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n}\\n.profile-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n.profile-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-weight: 600;\\n  color: var(--text-color);\\n  margin-bottom: 0.5rem;\\n  font-size: 0.9rem;\\n}\\n.profile-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .field-label.required[_ngcontent-%COMP%]::after {\\n  content: \\\" *\\\";\\n  color: var(--red-500);\\n}\\n.profile-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .field-help[_ngcontent-%COMP%] {\\n  color: var(--text-color-secondary);\\n  font-size: 0.8rem;\\n  margin-top: 0.25rem;\\n  display: block;\\n}\\n.profile-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   .field-error[_ngcontent-%COMP%] {\\n  color: var(--red-500);\\n  font-size: 0.8rem;\\n  margin-top: 0.25rem;\\n  display: block;\\n}\\n.profile-form[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]   input[readonly][_ngcontent-%COMP%] {\\n  background-color: var(--surface-100);\\n  color: var(--text-color-secondary);\\n  cursor: not-allowed;\\n}\\n\\n.account-details[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n}\\n.account-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 1rem 0;\\n  border-bottom: 1px solid var(--surface-border);\\n}\\n.account-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.account-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  font-weight: 500;\\n  color: var(--text-color);\\n  font-size: 0.9rem;\\n}\\n.account-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--text-color);\\n  text-align: right;\\n}\\n\\n@media (max-width: 992px) {\\n  .profile-container[_ngcontent-%COMP%] {\\n    padding: 0.5rem;\\n  }\\n  .profile-header[_ngcontent-%COMP%] {\\n    padding: 1.5rem;\\n  }\\n  .card-header[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n    flex-direction: column;\\n    gap: 1rem;\\n    text-align: center;\\n  }\\n  .profile-form[_ngcontent-%COMP%], .account-details[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .profile-header-info[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  .profile-avatar-large[_ngcontent-%COMP%] {\\n    width: 80px;\\n    height: 80px;\\n  }\\n  .profile-avatar-large[_ngcontent-%COMP%]   .avatar-icon[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .detail-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 0.5rem;\\n  }\\n  .detail-item[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n    text-align: left;\\n  }\\n}\\n.profile-header[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideInDown 0.5s ease-out;\\n}\\n\\n.profile-form[_ngcontent-%COMP%], .account-details[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 0.6s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideInDown {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.dark[_nghost-%COMP%]   .profile-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .profile-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--surface-800) 0%, var(--surface-900) 100%);\\n  border-color: var(--surface-700);\\n}\\n.dark[_nghost-%COMP%]   .card-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .card-header[_ngcontent-%COMP%] {\\n  background: var(--surface-800);\\n}\\n.dark[_nghost-%COMP%]   .profile-form[_ngcontent-%COMP%]   input[readonly][_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .profile-form[_ngcontent-%COMP%]   input[readonly][_ngcontent-%COMP%] {\\n  background-color: var(--surface-800);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵlistener", "ProfileComponent_p_button_17_Template_p_button_click_0_listener", "ɵɵrestoreView", "_r7", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "toggleEdit", "ɵɵelementEnd", "ProfileComponent_div_18_Template_p_button_click_1_listener", "_r9", "ctx_r8", "ProfileComponent_div_18_Template_p_button_click_2_listener", "ctx_r10", "saveProfile", "ɵɵadvance", "ɵɵproperty", "ctx_r1", "isLoading", "ɵɵelement", "ɵɵtext", "ɵɵtemplate", "ProfileComponent_ng_template_22_p_tag_5_Template", "ctx_r2", "isEditing", "ɵɵtextInterpolate1", "ctx_r12", "getFieldError", "ProfileComponent_ng_template_23_small_16_Template", "ctx_r3", "profileForm", "ɵɵclassMap", "ɵɵtextInterpolate", "ctx_r5", "formatDate", "user", "registrationDate", "formatDateTime", "lastLogin", "getStatusText", "isActive", "getStatusColor", "role", "getRoleColor", "ProfileComponent", "constructor", "fb", "authService", "userService", "messageService", "destroy$", "breadcrumbItems", "label", "routerLink", "initializeForm", "ngOnInit", "loadUserData", "ngOnDestroy", "next", "complete", "group", "username", "value", "disabled", "email", "required", "firstName", "lastName", "getUser", "pipe", "subscribe", "id", "userId", "toString", "createdAt", "Date", "toISOString", "status", "patchValue", "error", "console", "add", "severity", "summary", "detail", "invalid", "markFormGroupTouched", "updateRequest", "get", "updateUser", "response", "success", "message", "errorMessage", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "fieldName", "errors", "touched", "dateString", "toLocaleDateString", "year", "month", "day", "toLocaleString", "hour", "minute", "toLowerCase", "_", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "UserService", "i4", "MessageService", "_2", "selectors", "decls", "vars", "consts", "template", "ProfileComponent_Template", "rf", "ctx", "ProfileComponent_p_button_17_Template", "ProfileComponent_div_18_Template", "ProfileComponent_ng_template_22_Template", "ProfileComponent_ng_template_23_Template", "ProfileComponent_ng_template_26_Template", "ProfileComponent_ng_template_27_Template", "ɵɵtextInterpolate2"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\settings\\profile.component.ts", "D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\settings\\profile.component.html"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { MessageService } from 'primeng/api';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { User } from '../../api/user';\nimport { AuthService } from '../../service/auth.service';\nimport { UserService } from '../../service/user.service';\n\n@Component({\n  selector: 'app-profile',\n  templateUrl: './profile.component.html',\n  styleUrls: ['./profile.component.scss']\n})\nexport class ProfileComponent implements OnInit, OnDestroy {\n  private destroy$ = new Subject<void>();\n  \n  user: User = {};\n  profileForm: FormGroup;\n  isEditing = false;\n  isLoading = false;\n  \n  breadcrumbItems = [\n    { label: 'Dashboard', routerLink: '/app/index' },\n    { label: 'Settings', routerLink: '/app/settings' },\n    { label: 'Profile' }\n  ];\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthService,\n    private userService: UserService,\n    private messageService: MessageService\n  ) {\n    this.initializeForm();\n  }\n\n  ngOnInit() {\n    this.loadUserData();\n  }\n\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private initializeForm() {\n    this.profileForm = this.fb.group({\n      username: [{ value: '', disabled: true }], // Username is not editable\n      email: ['', [Validators.required, Validators.email]],\n      firstName: [''],\n      lastName: ['']\n    });\n  }\n\n  private loadUserData() {\n    this.isLoading = true;\n\n    this.userService.getUser()\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (user) => {\n          this.user = {\n            id: user.userId?.toString() || '',\n            username: user.username || '',\n            email: user.email || '',\n            role: user.role || 'User',\n            registrationDate: user.createdAt || new Date().toISOString(),\n            firstName: user.firstName || '',\n            lastName: user.lastName || '',\n            isActive: user.status === 'Active',\n            lastLogin: new Date().toISOString(), // This field is not returned by API yet\n            status: user.status || 'Active'\n          };\n\n          // Update form with user data\n          this.profileForm.patchValue({\n            username: this.user.username,\n            email: this.user.email,\n            firstName: this.user.firstName,\n            lastName: this.user.lastName\n          });\n\n          this.isLoading = false;\n        },\n        error: (error) => {\n          console.error('Error loading user data:', error);\n          this.messageService.add({\n            severity: 'error',\n            summary: 'Error',\n            detail: 'Failed to load user data. Please try again.'\n          });\n          this.isLoading = false;\n        }\n      });\n  }\n\n  toggleEdit() {\n    this.isEditing = !this.isEditing;\n    \n    if (!this.isEditing) {\n      // Cancel editing - reset form\n      this.profileForm.patchValue({\n        email: this.user.email,\n        firstName: this.user.firstName,\n        lastName: this.user.lastName\n      });\n    }\n  }\n\n  saveProfile() {\n    if (this.profileForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n\n    this.isLoading = true;\n\n    const updateRequest = {\n      email: this.profileForm.get('email')?.value,\n      firstName: this.profileForm.get('firstName')?.value,\n      lastName: this.profileForm.get('lastName')?.value\n    };\n\n    this.userService.updateUser(updateRequest)\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (response) => {\n          if (response.success && response.user) {\n            // Update local user data\n            this.user = {\n              ...this.user,\n              email: response.user.email || this.user.email,\n              firstName: response.user.firstName || this.user.firstName,\n              lastName: response.user.lastName || this.user.lastName\n            };\n\n            this.messageService.add({\n              severity: 'success',\n              summary: 'Success',\n              detail: response.message || 'Profile updated successfully!'\n            });\n            this.isEditing = false;\n          } else {\n            this.messageService.add({\n              severity: 'error',\n              summary: 'Error',\n              detail: response.message || 'Failed to update profile.'\n            });\n          }\n          this.isLoading = false;\n        },\n        error: (error) => {\n          console.error('Error updating profile:', error);\n          let errorMessage = 'Failed to update profile. Please try again.';\n\n          if (error.error && error.error.message) {\n            errorMessage = error.error.message;\n          } else if (error.error && typeof error.error === 'string') {\n            errorMessage = error.error;\n          }\n\n          this.messageService.add({\n            severity: 'error',\n            summary: 'Error',\n            detail: errorMessage\n          });\n          this.isLoading = false;\n        }\n      });\n  }\n\n  private markFormGroupTouched() {\n    Object.keys(this.profileForm.controls).forEach(key => {\n      const control = this.profileForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  getFieldError(fieldName: string): string {\n    const control = this.profileForm.get(fieldName);\n    if (control?.errors && control.touched) {\n      if (control.errors['required']) {\n        return `${fieldName} is required`;\n      }\n      if (control.errors['email']) {\n        return 'Please enter a valid email address';\n      }\n    }\n    return '';\n  }\n\n  formatDate(dateString: string): string {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-GB', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  }\n\n  formatDateTime(dateString: string): string {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleString('en-GB', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n\n  getRoleColor(role: string): string {\n    switch (role?.toLowerCase()) {\n      case 'admin': return 'danger';\n      case 'manager': return 'warning';\n      case 'user': return 'info';\n      default: return 'secondary';\n    }\n  }\n\n  getStatusColor(isActive: boolean): string {\n    return isActive ? 'success' : 'danger';\n  }\n\n  getStatusText(isActive: boolean): string {\n    return isActive ? 'Active' : 'Inactive';\n  }\n}\n", "<div class=\"profile-container\">\n    <!-- Modern Breadcrumb -->\n    <div class=\"col-12 mb-4\">\n        <app-modern-breadcrumb [items]=\"breadcrumbItems\"></app-modern-breadcrumb>\n    </div>\n\n    <!-- Profile Header -->\n    <div class=\"col-12 mb-4\">\n        <div class=\"profile-header\">\n            <div class=\"profile-avatar-section\">\n                <div class=\"profile-avatar-large\">\n                    <fa-icon icon=\"user\" class=\"avatar-icon\"></fa-icon>\n                </div>\n                <div class=\"profile-header-info\">\n                    <h1 class=\"profile-name\">{{ user.firstName }} {{ user.lastName }}</h1>\n                    <p class=\"profile-username\">{{ '@' + user.username }}</p>\n                    <div class=\"profile-badges\">\n                        <p-tag [value]=\"user.role\" \n                               [severity]=\"getRoleColor(user.role)\" \n                               icon=\"pi pi-shield\">\n                        </p-tag>\n                        <p-tag [value]=\"getStatusText(user.isActive)\" \n                               [severity]=\"getStatusColor(user.isActive)\" \n                               icon=\"pi pi-circle-fill\">\n                        </p-tag>\n                    </div>\n                </div>\n            </div>\n            <div class=\"profile-actions\">\n                <p-button *ngIf=\"!isEditing\" \n                          label=\"Edit Profile\" \n                          icon=\"pi pi-pencil\" \n                          severity=\"info\"\n                          (click)=\"toggleEdit()\">\n                </p-button>\n                <div *ngIf=\"isEditing\" class=\"edit-actions\">\n                    <p-button label=\"Cancel\" \n                              icon=\"pi pi-times\" \n                              severity=\"secondary\"\n                              [text]=\"true\"\n                              (click)=\"toggleEdit()\">\n                    </p-button>\n                    <p-button label=\"Save Changes\" \n                              icon=\"pi pi-check\" \n                              severity=\"success\"\n                              [loading]=\"isLoading\"\n                              (click)=\"saveProfile()\">\n                    </p-button>\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <div class=\"grid\">\n        <!-- Profile Information Card -->\n        <div class=\"col-12 lg:col-8\">\n            <p-card>\n                <ng-template pTemplate=\"header\">\n                    <div class=\"card-header\">\n                        <div class=\"flex align-items-center gap-2\">\n                            <fa-icon icon=\"user-circle\" class=\"text-primary text-xl\"></fa-icon>\n                            <h3 class=\"m-0\">Profile Information</h3>\n                        </div>\n                        <p-tag *ngIf=\"isEditing\" value=\"Editing\" severity=\"warning\" icon=\"pi pi-pencil\"></p-tag>\n                    </div>\n                </ng-template>\n\n                <ng-template pTemplate=\"content\">\n                    <form [formGroup]=\"profileForm\" class=\"profile-form\">\n                        <div class=\"grid\">\n                            <!-- Username (Read-only) -->\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field\">\n                                    <label for=\"username\" class=\"field-label\">\n                                        <fa-icon icon=\"user\" class=\"mr-1\"></fa-icon>\n                                        Username\n                                    </label>\n                                    <input pInputText \n                                           id=\"username\"\n                                           formControlName=\"username\"\n                                           class=\"w-full\"\n                                           readonly>\n                                    <small class=\"field-help\">Username cannot be changed</small>\n                                </div>\n                            </div>\n\n                            <!-- Email -->\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field\">\n                                    <label for=\"email\" class=\"field-label required\">\n                                        <fa-icon icon=\"envelope\" class=\"mr-1\"></fa-icon>\n                                        Email Address\n                                    </label>\n                                    <input pInputText \n                                           id=\"email\"\n                                           formControlName=\"email\"\n                                           [readonly]=\"!isEditing\"\n                                           [class]=\"'w-full ' + (getFieldError('email') ? 'ng-invalid ng-dirty' : '')\"\n                                           placeholder=\"Enter your email address\">\n                                    <small *ngIf=\"getFieldError('email')\" class=\"field-error\">\n                                        {{ getFieldError('email') }}\n                                    </small>\n                                </div>\n                            </div>\n\n                            <!-- First Name -->\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field\">\n                                    <label for=\"firstName\" class=\"field-label\">\n                                        <fa-icon icon=\"id-card\" class=\"mr-1\"></fa-icon>\n                                        First Name\n                                    </label>\n                                    <input pInputText \n                                           id=\"firstName\"\n                                           formControlName=\"firstName\"\n                                           [readonly]=\"!isEditing\"\n                                           class=\"w-full\"\n                                           placeholder=\"Enter your first name\">\n                                </div>\n                            </div>\n\n                            <!-- Last Name -->\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field\">\n                                    <label for=\"lastName\" class=\"field-label\">\n                                        <fa-icon icon=\"id-card\" class=\"mr-1\"></fa-icon>\n                                        Last Name\n                                    </label>\n                                    <input pInputText \n                                           id=\"lastName\"\n                                           formControlName=\"lastName\"\n                                           [readonly]=\"!isEditing\"\n                                           class=\"w-full\"\n                                           placeholder=\"Enter your last name\">\n                                </div>\n                            </div>\n                        </div>\n                    </form>\n                </ng-template>\n            </p-card>\n        </div>\n\n        <!-- Account Details Card -->\n        <div class=\"col-12 lg:col-4\">\n            <p-card>\n                <ng-template pTemplate=\"header\">\n                    <div class=\"card-header\">\n                        <div class=\"flex align-items-center gap-2\">\n                            <fa-icon icon=\"info-circle\" class=\"text-info text-xl\"></fa-icon>\n                            <h3 class=\"m-0\">Account Details</h3>\n                        </div>\n                    </div>\n                </ng-template>\n\n                <ng-template pTemplate=\"content\">\n                    <div class=\"account-details\">\n                        <!-- Registration Date -->\n                        <div class=\"detail-item\">\n                            <div class=\"detail-label\">\n                                <fa-icon icon=\"calendar-plus\" class=\"text-green-600\"></fa-icon>\n                                <span>Member Since</span>\n                            </div>\n                            <div class=\"detail-value\">{{ formatDate(user.registrationDate) }}</div>\n                        </div>\n\n                        <!-- Last Login -->\n                        <div class=\"detail-item\">\n                            <div class=\"detail-label\">\n                                <fa-icon icon=\"clock\" class=\"text-blue-600\"></fa-icon>\n                                <span>Last Login</span>\n                            </div>\n                            <div class=\"detail-value\">{{ formatDateTime(user.lastLogin) }}</div>\n                        </div>\n\n                        <!-- Account Status -->\n                        <div class=\"detail-item\">\n                            <div class=\"detail-label\">\n                                <fa-icon icon=\"shield-alt\" class=\"text-purple-600\"></fa-icon>\n                                <span>Account Status</span>\n                            </div>\n                            <div class=\"detail-value\">\n                                <p-tag [value]=\"getStatusText(user.isActive)\" \n                                       [severity]=\"getStatusColor(user.isActive)\" \n                                       icon=\"pi pi-circle-fill\">\n                                </p-tag>\n                            </div>\n                        </div>\n\n                        <!-- User Role -->\n                        <div class=\"detail-item\">\n                            <div class=\"detail-label\">\n                                <fa-icon icon=\"user-tag\" class=\"text-orange-600\"></fa-icon>\n                                <span>Role</span>\n                            </div>\n                            <div class=\"detail-value\">\n                                <p-tag [value]=\"user.role\" \n                                       [severity]=\"getRoleColor(user.role)\" \n                                       icon=\"pi pi-shield\">\n                                </p-tag>\n                            </div>\n                        </div>\n                    </div>\n                </ng-template>\n            </p-card>\n        </div>\n    </div>\n\n    <!-- Toast Messages -->\n    <p-toast></p-toast>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAEnE,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;ICyB1BC,EAAA,CAAAC,cAAA,mBAIiC;IAAvBD,EAAA,CAAAE,UAAA,mBAAAC,gEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,UAAA,EAAY;IAAA,EAAC;IAChCT,EAAA,CAAAU,YAAA,EAAW;;;;;;IACXV,EAAA,CAAAC,cAAA,cAA4C;IAK9BD,EAAA,CAAAE,UAAA,mBAAAS,2DAAA;MAAAX,EAAA,CAAAI,aAAA,CAAAQ,GAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAK,MAAA,CAAAJ,UAAA,EAAY;IAAA,EAAC;IAChCT,EAAA,CAAAU,YAAA,EAAW;IACXV,EAAA,CAAAC,cAAA,mBAIkC;IAAxBD,EAAA,CAAAE,UAAA,mBAAAY,2DAAA;MAAAd,EAAA,CAAAI,aAAA,CAAAQ,GAAA;MAAA,MAAAG,OAAA,GAAAf,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAO,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IACjChB,EAAA,CAAAU,YAAA,EAAW;;;;IARDV,EAAA,CAAAiB,SAAA,GAAa;IAAbjB,EAAA,CAAAkB,UAAA,cAAa;IAMblB,EAAA,CAAAiB,SAAA,GAAqB;IAArBjB,EAAA,CAAAkB,UAAA,YAAAC,MAAA,CAAAC,SAAA,CAAqB;;;;;IAkB3BpB,EAAA,CAAAqB,SAAA,gBAAwF;;;;;IAL5FrB,EAAA,CAAAC,cAAA,cAAyB;IAEjBD,EAAA,CAAAqB,SAAA,kBAAmE;IACnErB,EAAA,CAAAC,cAAA,aAAgB;IAAAD,EAAA,CAAAsB,MAAA,0BAAmB;IAAAtB,EAAA,CAAAU,YAAA,EAAK;IAE5CV,EAAA,CAAAuB,UAAA,IAAAC,gDAAA,oBAAwF;IAC5FxB,EAAA,CAAAU,YAAA,EAAM;;;;IADMV,EAAA,CAAAiB,SAAA,GAAe;IAAfjB,EAAA,CAAAkB,UAAA,SAAAO,MAAA,CAAAC,SAAA,CAAe;;;;;IAoCX1B,EAAA,CAAAC,cAAA,gBAA0D;IACtDD,EAAA,CAAAsB,MAAA,GACJ;IAAAtB,EAAA,CAAAU,YAAA,EAAQ;;;;IADJV,EAAA,CAAAiB,SAAA,GACJ;IADIjB,EAAA,CAAA2B,kBAAA,MAAAC,OAAA,CAAAC,aAAA,eACJ;;;;;IAjChB7B,EAAA,CAAAC,cAAA,eAAqD;IAMjCD,EAAA,CAAAqB,SAAA,kBAA4C;IAC5CrB,EAAA,CAAAsB,MAAA,iBACJ;IAAAtB,EAAA,CAAAU,YAAA,EAAQ;IACRV,EAAA,CAAAqB,SAAA,gBAIgB;IAChBrB,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAsB,MAAA,iCAA0B;IAAAtB,EAAA,CAAAU,YAAA,EAAQ;IAKpEV,EAAA,CAAAC,cAAA,eAA6B;IAGjBD,EAAA,CAAAqB,SAAA,mBAAgD;IAChDrB,EAAA,CAAAsB,MAAA,uBACJ;IAAAtB,EAAA,CAAAU,YAAA,EAAQ;IACRV,EAAA,CAAAqB,SAAA,iBAK8C;IAC9CrB,EAAA,CAAAuB,UAAA,KAAAO,iDAAA,oBAEQ;IACZ9B,EAAA,CAAAU,YAAA,EAAM;IAIVV,EAAA,CAAAC,cAAA,eAA6B;IAGjBD,EAAA,CAAAqB,SAAA,mBAA+C;IAC/CrB,EAAA,CAAAsB,MAAA,oBACJ;IAAAtB,EAAA,CAAAU,YAAA,EAAQ;IACRV,EAAA,CAAAqB,SAAA,iBAK2C;IAC/CrB,EAAA,CAAAU,YAAA,EAAM;IAIVV,EAAA,CAAAC,cAAA,eAA6B;IAGjBD,EAAA,CAAAqB,SAAA,mBAA+C;IAC/CrB,EAAA,CAAAsB,MAAA,mBACJ;IAAAtB,EAAA,CAAAU,YAAA,EAAQ;IACRV,EAAA,CAAAqB,SAAA,iBAK0C;IAC9CrB,EAAA,CAAAU,YAAA,EAAM;;;;IAlEZV,EAAA,CAAAkB,UAAA,cAAAa,MAAA,CAAAC,WAAA,CAAyB;IA6BRhC,EAAA,CAAAiB,SAAA,IAA2E;IAA3EjB,EAAA,CAAAiC,UAAA,cAAAF,MAAA,CAAAF,aAAA,wCAA2E;IAD3E7B,EAAA,CAAAkB,UAAA,cAAAa,MAAA,CAAAL,SAAA,CAAuB;IAGtB1B,EAAA,CAAAiB,SAAA,GAA4B;IAA5BjB,EAAA,CAAAkB,UAAA,SAAAa,MAAA,CAAAF,aAAA,UAA4B;IAgB7B7B,EAAA,CAAAiB,SAAA,GAAuB;IAAvBjB,EAAA,CAAAkB,UAAA,cAAAa,MAAA,CAAAL,SAAA,CAAuB;IAgBvB1B,EAAA,CAAAiB,SAAA,GAAuB;IAAvBjB,EAAA,CAAAkB,UAAA,cAAAa,MAAA,CAAAL,SAAA,CAAuB;;;;;IAe9C1B,EAAA,CAAAC,cAAA,cAAyB;IAEjBD,EAAA,CAAAqB,SAAA,kBAAgE;IAChErB,EAAA,CAAAC,cAAA,aAAgB;IAAAD,EAAA,CAAAsB,MAAA,sBAAe;IAAAtB,EAAA,CAAAU,YAAA,EAAK;;;;;IAM5CV,EAAA,CAAAC,cAAA,cAA6B;IAIjBD,EAAA,CAAAqB,SAAA,kBAA+D;IAC/DrB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAsB,MAAA,mBAAY;IAAAtB,EAAA,CAAAU,YAAA,EAAO;IAE7BV,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAsB,MAAA,GAAuC;IAAAtB,EAAA,CAAAU,YAAA,EAAM;IAI3EV,EAAA,CAAAC,cAAA,cAAyB;IAEjBD,EAAA,CAAAqB,SAAA,mBAAsD;IACtDrB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAsB,MAAA,kBAAU;IAAAtB,EAAA,CAAAU,YAAA,EAAO;IAE3BV,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAsB,MAAA,IAAoC;IAAAtB,EAAA,CAAAU,YAAA,EAAM;IAIxEV,EAAA,CAAAC,cAAA,eAAyB;IAEjBD,EAAA,CAAAqB,SAAA,mBAA6D;IAC7DrB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAsB,MAAA,sBAAc;IAAAtB,EAAA,CAAAU,YAAA,EAAO;IAE/BV,EAAA,CAAAC,cAAA,eAA0B;IACtBD,EAAA,CAAAqB,SAAA,iBAGQ;IACZrB,EAAA,CAAAU,YAAA,EAAM;IAIVV,EAAA,CAAAC,cAAA,eAAyB;IAEjBD,EAAA,CAAAqB,SAAA,mBAA2D;IAC3DrB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAsB,MAAA,YAAI;IAAAtB,EAAA,CAAAU,YAAA,EAAO;IAErBV,EAAA,CAAAC,cAAA,eAA0B;IACtBD,EAAA,CAAAqB,SAAA,iBAGQ;IACZrB,EAAA,CAAAU,YAAA,EAAM;;;;IArCoBV,EAAA,CAAAiB,SAAA,GAAuC;IAAvCjB,EAAA,CAAAkC,iBAAA,CAAAC,MAAA,CAAAC,UAAA,CAAAD,MAAA,CAAAE,IAAA,CAAAC,gBAAA,EAAuC;IASvCtC,EAAA,CAAAiB,SAAA,GAAoC;IAApCjB,EAAA,CAAAkC,iBAAA,CAAAC,MAAA,CAAAI,cAAA,CAAAJ,MAAA,CAAAE,IAAA,CAAAG,SAAA,EAAoC;IAUnDxC,EAAA,CAAAiB,SAAA,GAAsC;IAAtCjB,EAAA,CAAAkB,UAAA,UAAAiB,MAAA,CAAAM,aAAA,CAAAN,MAAA,CAAAE,IAAA,CAAAK,QAAA,EAAsC,aAAAP,MAAA,CAAAQ,cAAA,CAAAR,MAAA,CAAAE,IAAA,CAAAK,QAAA;IActC1C,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAkB,UAAA,UAAAiB,MAAA,CAAAE,IAAA,CAAAO,IAAA,CAAmB,aAAAT,MAAA,CAAAU,YAAA,CAAAV,MAAA,CAAAE,IAAA,CAAAO,IAAA;;;ADrL1D,OAAM,MAAOE,gBAAgB;EAc3BC,YACUC,EAAe,EACfC,WAAwB,EACxBC,WAAwB,EACxBC,cAA8B;IAH9B,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IAjBhB,KAAAC,QAAQ,GAAG,IAAItD,OAAO,EAAQ;IAEtC,KAAAuC,IAAI,GAAS,EAAE;IAEf,KAAAX,SAAS,GAAG,KAAK;IACjB,KAAAN,SAAS,GAAG,KAAK;IAEjB,KAAAiC,eAAe,GAAG,CAChB;MAAEC,KAAK,EAAE,WAAW;MAAEC,UAAU,EAAE;IAAY,CAAE,EAChD;MAAED,KAAK,EAAE,UAAU;MAAEC,UAAU,EAAE;IAAe,CAAE,EAClD;MAAED,KAAK,EAAE;IAAS,CAAE,CACrB;IAQC,IAAI,CAACE,cAAc,EAAE;EACvB;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACP,QAAQ,CAACQ,IAAI,EAAE;IACpB,IAAI,CAACR,QAAQ,CAACS,QAAQ,EAAE;EAC1B;EAEQL,cAAcA,CAAA;IACpB,IAAI,CAACxB,WAAW,GAAG,IAAI,CAACgB,EAAE,CAACc,KAAK,CAAC;MAC/BC,QAAQ,EAAE,CAAC;QAAEC,KAAK,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;MACzCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACrE,UAAU,CAACsE,QAAQ,EAAEtE,UAAU,CAACqE,KAAK,CAAC,CAAC;MACpDE,SAAS,EAAE,CAAC,EAAE,CAAC;MACfC,QAAQ,EAAE,CAAC,EAAE;KACd,CAAC;EACJ;EAEQX,YAAYA,CAAA;IAClB,IAAI,CAACtC,SAAS,GAAG,IAAI;IAErB,IAAI,CAAC8B,WAAW,CAACoB,OAAO,EAAE,CACvBC,IAAI,CAACxE,SAAS,CAAC,IAAI,CAACqD,QAAQ,CAAC,CAAC,CAC9BoB,SAAS,CAAC;MACTZ,IAAI,EAAGvB,IAAI,IAAI;QACb,IAAI,CAACA,IAAI,GAAG;UACVoC,EAAE,EAAEpC,IAAI,CAACqC,MAAM,EAAEC,QAAQ,EAAE,IAAI,EAAE;UACjCZ,QAAQ,EAAE1B,IAAI,CAAC0B,QAAQ,IAAI,EAAE;UAC7BG,KAAK,EAAE7B,IAAI,CAAC6B,KAAK,IAAI,EAAE;UACvBtB,IAAI,EAAEP,IAAI,CAACO,IAAI,IAAI,MAAM;UACzBN,gBAAgB,EAAED,IAAI,CAACuC,SAAS,IAAI,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;UAC5DV,SAAS,EAAE/B,IAAI,CAAC+B,SAAS,IAAI,EAAE;UAC/BC,QAAQ,EAAEhC,IAAI,CAACgC,QAAQ,IAAI,EAAE;UAC7B3B,QAAQ,EAAEL,IAAI,CAAC0C,MAAM,KAAK,QAAQ;UAClCvC,SAAS,EAAE,IAAIqC,IAAI,EAAE,CAACC,WAAW,EAAE;UACnCC,MAAM,EAAE1C,IAAI,CAAC0C,MAAM,IAAI;SACxB;QAED;QACA,IAAI,CAAC/C,WAAW,CAACgD,UAAU,CAAC;UAC1BjB,QAAQ,EAAE,IAAI,CAAC1B,IAAI,CAAC0B,QAAQ;UAC5BG,KAAK,EAAE,IAAI,CAAC7B,IAAI,CAAC6B,KAAK;UACtBE,SAAS,EAAE,IAAI,CAAC/B,IAAI,CAAC+B,SAAS;UAC9BC,QAAQ,EAAE,IAAI,CAAChC,IAAI,CAACgC;SACrB,CAAC;QAEF,IAAI,CAACjD,SAAS,GAAG,KAAK;MACxB,CAAC;MACD6D,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAAC9B,cAAc,CAACgC,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,OAAO;UAChBC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAAClE,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACN;EAEAX,UAAUA,CAAA;IACR,IAAI,CAACiB,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAEhC,IAAI,CAAC,IAAI,CAACA,SAAS,EAAE;MACnB;MACA,IAAI,CAACM,WAAW,CAACgD,UAAU,CAAC;QAC1Bd,KAAK,EAAE,IAAI,CAAC7B,IAAI,CAAC6B,KAAK;QACtBE,SAAS,EAAE,IAAI,CAAC/B,IAAI,CAAC+B,SAAS;QAC9BC,QAAQ,EAAE,IAAI,CAAChC,IAAI,CAACgC;OACrB,CAAC;;EAEN;EAEArD,WAAWA,CAAA;IACT,IAAI,IAAI,CAACgB,WAAW,CAACuD,OAAO,EAAE;MAC5B,IAAI,CAACC,oBAAoB,EAAE;MAC3B;;IAGF,IAAI,CAACpE,SAAS,GAAG,IAAI;IAErB,MAAMqE,aAAa,GAAG;MACpBvB,KAAK,EAAE,IAAI,CAAClC,WAAW,CAAC0D,GAAG,CAAC,OAAO,CAAC,EAAE1B,KAAK;MAC3CI,SAAS,EAAE,IAAI,CAACpC,WAAW,CAAC0D,GAAG,CAAC,WAAW,CAAC,EAAE1B,KAAK;MACnDK,QAAQ,EAAE,IAAI,CAACrC,WAAW,CAAC0D,GAAG,CAAC,UAAU,CAAC,EAAE1B;KAC7C;IAED,IAAI,CAACd,WAAW,CAACyC,UAAU,CAACF,aAAa,CAAC,CACvClB,IAAI,CAACxE,SAAS,CAAC,IAAI,CAACqD,QAAQ,CAAC,CAAC,CAC9BoB,SAAS,CAAC;MACTZ,IAAI,EAAGgC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACvD,IAAI,EAAE;UACrC;UACA,IAAI,CAACA,IAAI,GAAG;YACV,GAAG,IAAI,CAACA,IAAI;YACZ6B,KAAK,EAAE0B,QAAQ,CAACvD,IAAI,CAAC6B,KAAK,IAAI,IAAI,CAAC7B,IAAI,CAAC6B,KAAK;YAC7CE,SAAS,EAAEwB,QAAQ,CAACvD,IAAI,CAAC+B,SAAS,IAAI,IAAI,CAAC/B,IAAI,CAAC+B,SAAS;YACzDC,QAAQ,EAAEuB,QAAQ,CAACvD,IAAI,CAACgC,QAAQ,IAAI,IAAI,CAAChC,IAAI,CAACgC;WAC/C;UAED,IAAI,CAAClB,cAAc,CAACgC,GAAG,CAAC;YACtBC,QAAQ,EAAE,SAAS;YACnBC,OAAO,EAAE,SAAS;YAClBC,MAAM,EAAEM,QAAQ,CAACE,OAAO,IAAI;WAC7B,CAAC;UACF,IAAI,CAACpE,SAAS,GAAG,KAAK;SACvB,MAAM;UACL,IAAI,CAACyB,cAAc,CAACgC,GAAG,CAAC;YACtBC,QAAQ,EAAE,OAAO;YACjBC,OAAO,EAAE,OAAO;YAChBC,MAAM,EAAEM,QAAQ,CAACE,OAAO,IAAI;WAC7B,CAAC;;QAEJ,IAAI,CAAC1E,SAAS,GAAG,KAAK;MACxB,CAAC;MACD6D,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAIc,YAAY,GAAG,6CAA6C;QAEhE,IAAId,KAAK,CAACA,KAAK,IAAIA,KAAK,CAACA,KAAK,CAACa,OAAO,EAAE;UACtCC,YAAY,GAAGd,KAAK,CAACA,KAAK,CAACa,OAAO;SACnC,MAAM,IAAIb,KAAK,CAACA,KAAK,IAAI,OAAOA,KAAK,CAACA,KAAK,KAAK,QAAQ,EAAE;UACzDc,YAAY,GAAGd,KAAK,CAACA,KAAK;;QAG5B,IAAI,CAAC9B,cAAc,CAACgC,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,OAAO;UAChBC,MAAM,EAAES;SACT,CAAC;QACF,IAAI,CAAC3E,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACN;EAEQoE,oBAAoBA,CAAA;IAC1BQ,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjE,WAAW,CAACkE,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACnD,MAAMC,OAAO,GAAG,IAAI,CAACrE,WAAW,CAAC0D,GAAG,CAACU,GAAG,CAAC;MACzCC,OAAO,EAAEC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAzE,aAAaA,CAAC0E,SAAiB;IAC7B,MAAMF,OAAO,GAAG,IAAI,CAACrE,WAAW,CAAC0D,GAAG,CAACa,SAAS,CAAC;IAC/C,IAAIF,OAAO,EAAEG,MAAM,IAAIH,OAAO,CAACI,OAAO,EAAE;MACtC,IAAIJ,OAAO,CAACG,MAAM,CAAC,UAAU,CAAC,EAAE;QAC9B,OAAO,GAAGD,SAAS,cAAc;;MAEnC,IAAIF,OAAO,CAACG,MAAM,CAAC,OAAO,CAAC,EAAE;QAC3B,OAAO,oCAAoC;;;IAG/C,OAAO,EAAE;EACX;EAEApE,UAAUA,CAACsE,UAAkB;IAC3B,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,OAAO,IAAI7B,IAAI,CAAC6B,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;KACN,CAAC;EACJ;EAEAvE,cAAcA,CAACmE,UAAkB;IAC/B,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,OAAO,IAAI7B,IAAI,CAAC6B,UAAU,CAAC,CAACK,cAAc,CAAC,OAAO,EAAE;MAClDH,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdE,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEApE,YAAYA,CAACD,IAAY;IACvB,QAAQA,IAAI,EAAEsE,WAAW,EAAE;MACzB,KAAK,OAAO;QAAE,OAAO,QAAQ;MAC7B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,MAAM;QAAE,OAAO,MAAM;MAC1B;QAAS,OAAO,WAAW;;EAE/B;EAEAvE,cAAcA,CAACD,QAAiB;IAC9B,OAAOA,QAAQ,GAAG,SAAS,GAAG,QAAQ;EACxC;EAEAD,aAAaA,CAACC,QAAiB;IAC7B,OAAOA,QAAQ,GAAG,QAAQ,GAAG,UAAU;EACzC;EAAC,QAAAyE,CAAA,G;qBArNUrE,gBAAgB,EAAA9C,EAAA,CAAAoH,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtH,EAAA,CAAAoH,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAxH,EAAA,CAAAoH,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA1H,EAAA,CAAAoH,iBAAA,CAAAO,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAhB/E,gBAAgB;IAAAgF,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCd7BpI,EAAA,CAAAC,cAAA,aAA+B;QAGvBD,EAAA,CAAAqB,SAAA,+BAAyE;QAC7ErB,EAAA,CAAAU,YAAA,EAAM;QAGNV,EAAA,CAAAC,cAAA,aAAyB;QAITD,EAAA,CAAAqB,SAAA,iBAAmD;QACvDrB,EAAA,CAAAU,YAAA,EAAM;QACNV,EAAA,CAAAC,cAAA,aAAiC;QACJD,EAAA,CAAAsB,MAAA,IAAwC;QAAAtB,EAAA,CAAAU,YAAA,EAAK;QACtEV,EAAA,CAAAC,cAAA,YAA4B;QAAAD,EAAA,CAAAsB,MAAA,IAAyB;QAAAtB,EAAA,CAAAU,YAAA,EAAI;QACzDV,EAAA,CAAAC,cAAA,eAA4B;QACxBD,EAAA,CAAAqB,SAAA,iBAGQ;QAKZrB,EAAA,CAAAU,YAAA,EAAM;QAGdV,EAAA,CAAAC,cAAA,eAA6B;QACzBD,EAAA,CAAAuB,UAAA,KAAA+G,qCAAA,uBAKW,KAAAC,gCAAA;QAefvI,EAAA,CAAAU,YAAA,EAAM;QAIdV,EAAA,CAAAC,cAAA,eAAkB;QAIND,EAAA,CAAAuB,UAAA,KAAAiH,wCAAA,0BAQc,KAAAC,wCAAA;QA0ElBzI,EAAA,CAAAU,YAAA,EAAS;QAIbV,EAAA,CAAAC,cAAA,eAA6B;QAErBD,EAAA,CAAAuB,UAAA,KAAAmH,wCAAA,0BAOc,KAAAC,wCAAA;QAmDlB3I,EAAA,CAAAU,YAAA,EAAS;QAKjBV,EAAA,CAAAqB,SAAA,eAAmB;QACvBrB,EAAA,CAAAU,YAAA,EAAM;;;QA9MyBV,EAAA,CAAAiB,SAAA,GAAyB;QAAzBjB,EAAA,CAAAkB,UAAA,UAAAmH,GAAA,CAAAhF,eAAA,CAAyB;QAWXrD,EAAA,CAAAiB,SAAA,GAAwC;QAAxCjB,EAAA,CAAA4I,kBAAA,KAAAP,GAAA,CAAAhG,IAAA,CAAA+B,SAAA,OAAAiE,GAAA,CAAAhG,IAAA,CAAAgC,QAAA,KAAwC;QACrCrE,EAAA,CAAAiB,SAAA,GAAyB;QAAzBjB,EAAA,CAAAkC,iBAAA,OAAAmG,GAAA,CAAAhG,IAAA,CAAA0B,QAAA,CAAyB;QAE1C/D,EAAA,CAAAiB,SAAA,GAAmB;QAAnBjB,EAAA,CAAAkB,UAAA,UAAAmH,GAAA,CAAAhG,IAAA,CAAAO,IAAA,CAAmB,aAAAyF,GAAA,CAAAxF,YAAA,CAAAwF,GAAA,CAAAhG,IAAA,CAAAO,IAAA;QAInB5C,EAAA,CAAAiB,SAAA,GAAsC;QAAtCjB,EAAA,CAAAkB,UAAA,UAAAmH,GAAA,CAAA5F,aAAA,CAAA4F,GAAA,CAAAhG,IAAA,CAAAK,QAAA,EAAsC,aAAA2F,GAAA,CAAA1F,cAAA,CAAA0F,GAAA,CAAAhG,IAAA,CAAAK,QAAA;QAQ1C1C,EAAA,CAAAiB,SAAA,GAAgB;QAAhBjB,EAAA,CAAAkB,UAAA,UAAAmH,GAAA,CAAA3G,SAAA,CAAgB;QAMrB1B,EAAA,CAAAiB,SAAA,GAAe;QAAfjB,EAAA,CAAAkB,UAAA,SAAAmH,GAAA,CAAA3G,SAAA,CAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}