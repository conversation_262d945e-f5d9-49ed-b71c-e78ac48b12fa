{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class UIkitRoutingModule {\n  static #_ = this.ɵfac = function UIkitRoutingModule_Factory(t) {\n    return new (t || UIkitRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: UIkitRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild([{\n      path: 'index',\n      data: {\n        breadcrumb: 'Index'\n      },\n      loadChildren: () => import('.././index/index.module').then(m => m.IndexModule)\n    }, {\n      path: 'station/:id',\n      data: {\n        breadcrumb: 'Stations'\n      },\n      loadChildren: () => import('.././stations/view.module').then(m => m.ViewStationModule)\n    }, {\n      path: 'add',\n      data: {\n        breadcrumb: 'Add Station'\n      },\n      loadChildren: () => import('.././stations/add.module').then(m => m.AddStationModule)\n    }, {\n      path: 'filters',\n      data: {\n        breadcrumb: 'Filters'\n      },\n      loadChildren: () => import('.././filters/filters.module').then(m => m.FiltersModule)\n    }, {\n      path: 'button',\n      data: {\n        breadcrumb: 'Button'\n      },\n      loadChildren: () => import('./button/buttondemo.module').then(m => m.ButtonDemoModule)\n    }, {\n      path: 'charts',\n      data: {\n        breadcrumb: 'Charts'\n      },\n      loadChildren: () => import('./charts/chartsdemo.module').then(m => m.ChartsDemoModule)\n    }, {\n      path: 'file',\n      data: {\n        breadcrumb: 'File'\n      },\n      loadChildren: () => import('./file/filedemo.module').then(m => m.FileDemoModule)\n    }, {\n      path: 'floatlabel',\n      data: {\n        breadcrumb: 'Float Label'\n      },\n      loadChildren: () => import('./floatlabel/floatlabeldemo.module').then(m => m.FloatlabelDemoModule)\n    }, {\n      path: 'formlayout',\n      data: {\n        breadcrumb: 'Form Layout'\n      },\n      loadChildren: () => import('./formlayout/formlayoutdemo.module').then(m => m.FormLayoutDemoModule)\n    }, {\n      path: 'input',\n      data: {\n        breadcrumb: 'Input'\n      },\n      loadChildren: () => import('./input/inputdemo.module').then(m => m.InputDemoModule)\n    }, {\n      path: 'invalidstate',\n      data: {\n        breadcrumb: 'Invalid State'\n      },\n      loadChildren: () => import('./invalid/invalidstatedemo.module').then(m => m.InvalidStateDemoModule)\n    }, {\n      path: 'list',\n      data: {\n        breadcrumb: 'List'\n      },\n      loadChildren: () => import('./list/listdemo.module').then(m => m.ListDemoModule)\n    }, {\n      path: 'media',\n      data: {\n        breadcrumb: 'Media'\n      },\n      loadChildren: () => import('./media/mediademo.module').then(m => m.MediaDemoModule)\n    }, {\n      path: 'message',\n      data: {\n        breadcrumb: 'Message'\n      },\n      loadChildren: () => import('./messages/messagesdemo.module').then(m => m.MessagesDemoModule)\n    }, {\n      path: 'misc',\n      data: {\n        breadcrumb: 'Misc'\n      },\n      loadChildren: () => import('./misc/miscdemo.module').then(m => m.MiscDemoModule)\n    }, {\n      path: 'overlay',\n      data: {\n        breadcrumb: 'Overlay'\n      },\n      loadChildren: () => import('./overlays/overlaysdemo.module').then(m => m.OverlaysDemoModule)\n    }, {\n      path: 'panel',\n      data: {\n        breadcrumb: 'Panel'\n      },\n      loadChildren: () => import('./panels/panelsdemo.module').then(m => m.PanelsDemoModule)\n    }, {\n      path: 'table',\n      data: {\n        breadcrumb: 'Table'\n      },\n      loadChildren: () => import('./table/tabledemo.module').then(m => m.TableDemoModule)\n    }, {\n      path: 'tree',\n      data: {\n        breadcrumb: 'Tree'\n      },\n      loadChildren: () => import('./tree/treedemo.module').then(m => m.TreeDemoModule)\n    }, {\n      path: 'menu',\n      data: {\n        breadcrumb: 'Menu'\n      },\n      loadChildren: () => import('./menus/menus.module').then(m => m.MenusModule)\n    }, {\n      path: '**',\n      redirectTo: '/notfound'\n    }]), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(UIkitRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "UIkitRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "path", "data", "breadcrumb", "loadChildren", "then", "m", "IndexModule", "ViewStationModule", "AddStationModule", "FiltersModule", "ButtonDemoModule", "ChartsDemoModule", "FileDemoModule", "FloatlabelDemoModule", "FormLayoutDemoModule", "InputDemoModule", "InvalidStateDemoModule", "ListDemoModule", "MediaDemoModule", "MessagesDemoModule", "MiscDemoModule", "OverlaysDemoModule", "PanelsDemoModule", "TableDemoModule", "TreeDemoModule", "MenusModule", "redirectTo", "imports", "i1", "exports"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\uikit\\uikit-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule } from '@angular/router';\n\n@NgModule({\n    imports: [RouterModule.forChild([\n        { path: 'index', data: { breadcrumb: 'Index' }, loadChildren: () => import('.././index/index.module').then(m => m.IndexModule) },\n        { path: 'station/:id', data: { breadcrumb: 'Stations' }, loadChildren: () => import('.././stations/view.module').then(m => m.ViewStationModule) },\n        { path: 'add', data: { breadcrumb: 'Add Station' }, loadChildren: () => import('.././stations/add.module').then(m => m.AddStationModule) },\n        { path: 'filters', data: { breadcrumb: 'Filters' }, loadChildren: () => import('.././filters/filters.module').then(m => m.FiltersModule) },\n        { path: 'button', data: { breadcrumb: 'Button' }, loadChildren: () => import('./button/buttondemo.module').then(m => m.ButtonDemoModule) },\n        { path: 'charts', data: { breadcrumb: 'Charts' }, loadChildren: () => import('./charts/chartsdemo.module').then(m => m.ChartsDemoModule) },\n        { path: 'file', data: { breadcrumb: 'File' }, loadChildren: () => import('./file/filedemo.module').then(m => m.FileDemoModule) },\n        { path: 'floatlabel', data: { breadcrumb: 'Float Label' }, loadChildren: () => import('./floatlabel/floatlabeldemo.module').then(m => m.FloatlabelDemoModule) },\n        { path: 'formlayout', data: { breadcrumb: 'Form Layout' }, loadChildren: () => import('./formlayout/formlayoutdemo.module').then(m => m.FormLayoutDemoModule) },\n        { path: 'input', data: { breadcrumb: 'Input' }, loadChildren: () => import('./input/inputdemo.module').then(m => m.InputDemoModule) },\n        { path: 'invalidstate', data: { breadcrumb: 'Invalid State' }, loadChildren: () => import('./invalid/invalidstatedemo.module').then(m => m.InvalidStateDemoModule) },\n        { path: 'list', data: { breadcrumb: 'List' }, loadChildren: () => import('./list/listdemo.module').then(m => m.ListDemoModule) },\n        { path: 'media', data: { breadcrumb: 'Media' }, loadChildren: () => import('./media/mediademo.module').then(m => m.MediaDemoModule) },\n        { path: 'message', data: { breadcrumb: 'Message' }, loadChildren: () => import('./messages/messagesdemo.module').then(m => m.MessagesDemoModule) },\n        { path: 'misc', data: { breadcrumb: 'Misc' }, loadChildren: () => import('./misc/miscdemo.module').then(m => m.MiscDemoModule) },\n        { path: 'overlay', data: { breadcrumb: 'Overlay' }, loadChildren: () => import('./overlays/overlaysdemo.module').then(m => m.OverlaysDemoModule) },\n        { path: 'panel', data: { breadcrumb: 'Panel' }, loadChildren: () => import('./panels/panelsdemo.module').then(m => m.PanelsDemoModule) },\n        { path: 'table', data: { breadcrumb: 'Table' }, loadChildren: () => import('./table/tabledemo.module').then(m => m.TableDemoModule) },\n        { path: 'tree', data: { breadcrumb: 'Tree' }, loadChildren: () => import('./tree/treedemo.module').then(m => m.TreeDemoModule) },\n        { path: 'menu', data: { breadcrumb: 'Menu' }, loadChildren: () => import('./menus/menus.module').then(m => m.MenusModule) },\n        { path: '**', redirectTo: '/notfound' }\n    ])],\n    exports: [RouterModule]\n})\nexport class UIkitRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;;AA4B9C,OAAM,MAAOC,kBAAkB;EAAA,QAAAC,CAAA,G;qBAAlBD,kBAAkB;EAAA;EAAA,QAAAE,EAAA,G;UAAlBF;EAAkB;EAAA,QAAAG,EAAA,G;cAzBjBJ,YAAY,CAACK,QAAQ,CAAC,CAC5B;MAAEC,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAO,CAAE;MAAEC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW;IAAC,CAAE,EAChI;MAAEN,IAAI,EAAE,aAAa;MAAEC,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAU,CAAE;MAAEC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,iBAAiB;IAAC,CAAE,EACjJ;MAAEP,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAa,CAAE;MAAEC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,gBAAgB;IAAC,CAAE,EAC1I;MAAER,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAEC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,aAAa;IAAC,CAAE,EAC1I;MAAET,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAEC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,gBAAgB;IAAC,CAAE,EAC1I;MAAEV,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAEC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACM,gBAAgB;IAAC,CAAE,EAC1I;MAAEX,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAM,CAAE;MAAEC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACO,cAAc;IAAC,CAAE,EAChI;MAAEZ,IAAI,EAAE,YAAY;MAAEC,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAa,CAAE;MAAEC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACQ,oBAAoB;IAAC,CAAE,EAC/J;MAAEb,IAAI,EAAE,YAAY;MAAEC,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAa,CAAE;MAAEC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACS,oBAAoB;IAAC,CAAE,EAC/J;MAAEd,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAO,CAAE;MAAEC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACU,eAAe;IAAC,CAAE,EACrI;MAAEf,IAAI,EAAE,cAAc;MAAEC,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAe,CAAE;MAAEC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACW,sBAAsB;IAAC,CAAE,EACpK;MAAEhB,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAM,CAAE;MAAEC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACY,cAAc;IAAC,CAAE,EAChI;MAAEjB,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAO,CAAE;MAAEC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACa,eAAe;IAAC,CAAE,EACrI;MAAElB,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAEC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACc,kBAAkB;IAAC,CAAE,EAClJ;MAAEnB,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAM,CAAE;MAAEC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACe,cAAc;IAAC,CAAE,EAChI;MAAEpB,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAEC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACgB,kBAAkB;IAAC,CAAE,EAClJ;MAAErB,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAO,CAAE;MAAEC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACiB,gBAAgB;IAAC,CAAE,EACxI;MAAEtB,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAO,CAAE;MAAEC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACkB,eAAe;IAAC,CAAE,EACrI;MAAEvB,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAM,CAAE;MAAEC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACmB,cAAc;IAAC,CAAE,EAChI;MAAExB,IAAI,EAAE,MAAM;MAAEC,IAAI,EAAE;QAAEC,UAAU,EAAE;MAAM,CAAE;MAAEC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACoB,WAAW;IAAC,CAAE,EAC3H;MAAEzB,IAAI,EAAE,IAAI;MAAE0B,UAAU,EAAE;IAAW,CAAE,CAC1C,CAAC,EACQhC,YAAY;EAAA;;;2EAEbC,kBAAkB;IAAAgC,OAAA,GAAAC,EAAA,CAAAlC,YAAA;IAAAmC,OAAA,GAFjBnC,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}