﻿using Auth.API.HuaweiModels;
using Auth.API.Types;
using AutoMapper;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Text;
using System.Threading.Tasks;
using Auth.API.AuroraModels;
using System;
using System.Text.Json.Serialization;
using System.Linq;
using Microsoft.Extensions.Logging;
using Auth.API.Models;
using static System.Runtime.InteropServices.JavaScript.JSType;
using System.Globalization;

namespace Auth.API.Implementation
{
    public class AuroraApiService
    {
        private readonly HttpClient _httpClient;
        private readonly AuroraTokenService _auroraTokenService;
        private readonly IMapper _mapper;

        private readonly Dictionary<string, string> auroraSearchTypeMapping = new Dictionary<string, string>
        {
            {"monthly", "Day"},
            {"yearly", "Month" },
            {"lifetime","Year" }
        };
    

        public AuroraApiService(HttpClient httpClient, AuroraTokenService auroraTokenService, IMapper mapper) {
            _httpClient = httpClient;
            _auroraTokenService = auroraTokenService;
            _mapper = mapper;

        }


        public async Task<List<Station>> GetStationsAsync(string username, string password, string portfolioId, string baseUrl)
        {
            string token = await _auroraTokenService.GetAuroraTokenAsync(username, password);

            _httpClient.DefaultRequestHeaders.Add("X-AuroraVision-Token", token);

            // Δημιουργούμε το JSON body από το request object
            //var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");

            // Εκτελούμε το POST request
            HttpResponseMessage response = await _httpClient.GetAsync(baseUrl + $"/v1/portfolio/{portfolioId}/plants");

            if (response.IsSuccessStatusCode)
            {
                string jsonResponse = await response.Content.ReadAsStringAsync();

                // Κάνουμε deserialize το JSON σε GetStationsResponse object
                AuroraGetStationsResponse apiResponse = JsonSerializer.Deserialize<AuroraGetStationsResponse>(jsonResponse, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true // Για να διαβάζει properties ανεξάρτητα από το casing
                });

                var stations = _mapper.Map<List<AuroraStation>, List<Station>>(apiResponse.result.Stations);

                return stations;
            }
            else
            {
                throw new Exception($"Failed to get data: {response.StatusCode}");
            }
        }

        public async Task<List<Device>> GetStationDevicesAsync(string stationId, string username, string password, string baseUrl)
        {
            string token = await _auroraTokenService.GetAuroraTokenAsync(username, password);

            _httpClient.DefaultRequestHeaders.Add("X-AuroraVision-Token", token);

            
            // Εκτελούμε το GET request
            HttpResponseMessage response = await _httpClient.GetAsync(baseUrl + $"/v1/plant/{stationId}/loggers");

            
            if (response.IsSuccessStatusCode)
            {
                string jsonResponse = await response.Content.ReadAsStringAsync();

                // Κάνουμε deserialize το JSON σε GetStationsResponse object
                AuroraGetLoggersResponse apiResponse = JsonSerializer.Deserialize<AuroraGetLoggersResponse>(jsonResponse, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true, // Για να διαβάζει properties ανεξάρτητα από το casing
                    Converters = { new JsonStringEnumConverter() }
                });

                var loggersList = apiResponse.result.loggers.ToList();

                var devices = new List<Device>();

                foreach (AuroraLogger logger in loggersList) {
                    
                    // Εκτελούμε το GET request
                    HttpResponseMessage deviceResponse = await _httpClient.GetAsync(baseUrl + $"/v1/logger/{logger.loggerEntityID}/devices");

                    if (response.IsSuccessStatusCode)
                    {
                        string jsonDeviceResponse = await deviceResponse.Content.ReadAsStringAsync();

                        // Κάνουμε deserialize το JSON σε GetStationsResponse object
                        AuroraGetDevicesResponse apiDeviceResponse = JsonSerializer.Deserialize<AuroraGetDevicesResponse>(jsonDeviceResponse, new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true, // Για να διαβάζει properties ανεξάρτητα από το casing
                            Converters = { new JsonStringEnumConverter() }
                        });
                        //TODO filter devices only inverters??
                        var devicesList = _mapper.Map<List<AuroraDevice>, List<Device>>(apiDeviceResponse.result.devices.Where(d=> d.deviceCategory != null && d.deviceCategory.Contains("Inverter")).ToList());
                        devices.AddRange(devicesList);
                        
                    }
                    else
                    {
                        throw new Exception($"Failed to get data: {response.StatusCode}");
                    }
                }

                return devices;

                
            }
            else
            {
                throw new Exception($"Failed to get data: {response.StatusCode}");
            }

        }


        public async Task<SumData> GetSumData(string stationId, string username, string password, string baseUrl)
        {
            string token = await _auroraTokenService.GetAuroraTokenAsync(username, password);

            _httpClient.DefaultRequestHeaders.Add("X-AuroraVision-Token", token);



            //var request = new HuaweiGetSumDataRequest()
            //{
            //    stationCodes = stationId
            //};


            //// Δημιουργούμε το JSON body από το request object
            //var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");

            //// Εκτελούμε το POST request
            //HttpResponseMessage response = await _httpClient.PostAsync(baseUrl + "thirdData/getStationRealKpi", content);

            //if (response.IsSuccessStatusCode)
            //{
            //    string jsonResponse = await response.Content.ReadAsStringAsync();

            //    // Κάνουμε deserialize το JSON σε GetStationsResponse object
            //    HuaweiGetSumDataResponse apiResponse = JsonSerializer.Deserialize<HuaweiGetSumDataResponse>(jsonResponse, new JsonSerializerOptions
            //    {
            //        PropertyNameCaseInsensitive = true // Για να διαβάζει properties ανεξάρτητα από το casing
            //        //Converters = { new JsonStringEnumConverter() }
            //    });

            //    if (apiResponse.FailCode != 0)
            //    {
            //        throw new Exception(apiResponse.Message);
            //    }

            //    var stations = _mapper.Map<DataItemMap, SumData>(apiResponse.Data[0].DataItemMap);

            //    return stations;
            //}
            //else
            //{
            //    throw new Exception($"Failed to get data: {response.StatusCode}");
            //}

            return new SumData()
            {

            };
        }

        //public async Task<RealTimeData> GetRealTimeData(GetDataRequest req, string username, string password, string baseUrl)
        //{
        //    string token = await _auroraTokenService.GetAuroraTokenAsync(username, password);

        //    _httpClient.DefaultRequestHeaders.Add("X-AuroraVision-Token", token);



        //    var request = new HuaweiGetRealTimeDataRequest()
        //    {
        //        devIds = req.DevIds,
        //        devTypeId = req.DevTypeId
        //    };


        //    // Δημιουργούμε το JSON body από το request object
        //    var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");

        //    // Εκτύπωση του JSON περιεχομένου
        //    Console.WriteLine("Αίτηση JSON:");
        //    Console.WriteLine(content.ReadAsStringAsync().Result);

        //    // Εκτελούμε το POST request
        //    HttpResponseMessage response = await _httpClient.PostAsync(baseUrl + "thirdData/getDevRealKpi", content);

        //    if (response.IsSuccessStatusCode)
        //    {
        //        string jsonResponse = await response.Content.ReadAsStringAsync();

        //        // Κάνουμε deserialize το JSON σε GetStationsResponse object
        //        HuaweiGetRealTimeDataResponse apiResponse = JsonSerializer.Deserialize<HuaweiGetRealTimeDataResponse>(jsonResponse, new JsonSerializerOptions
        //        {
        //            PropertyNameCaseInsensitive = true // Για να διαβάζει properties ανεξάρτητα από το casing
        //            //Converters = { new JsonStringEnumConverter() }
        //        });


        //        var res = new RealTimeData()
        //        {
        //            activePower = apiResponse.Data.Sum(d => d.ActivePower ?? 0m),
        //            timestamp = apiResponse.Params.CurrentTime,
        //            dateTime = DateTimeOffset.FromUnixTimeMilliseconds(apiResponse.Params.CurrentTime).UtcDateTime
        //        };

        //        return res;
        //    }
        //    else
        //    {
        //        throw new Exception($"Failed to get data: {response.StatusCode}");
        //    }
        //}


        public async Task<HistoricTimeData> GetHistoricTimeData(GetDataWithTimestampRequest req, string username, string password, string baseUrl)
        {
            // Convert to Athens timezone for date formatting, independent of server timezone
            TimeZoneInfo athensTimeZone = TimeZoneInfo.FindSystemTimeZoneById("GTB Standard Time");

            var startDateAthens = TimeZoneInfo.ConvertTimeFromUtc(req.StartDateTime.ToUniversalTime(), athensTimeZone);
            var endDateAthens = TimeZoneInfo.ConvertTimeFromUtc(req.EndDateTime.ToUniversalTime(), athensTimeZone);

            var startDate = startDateAthens.ToString("yyyyMMdd");
            var endDate = endDateAthens.ToString("yyyyMMdd");

            List<RealTimeData> stationData = new List<RealTimeData>();
            List<RealTimeData> responsedeviceData = new List<RealTimeData>();
            if (req.Separated)
            {
                var stationDevices = await GetStationDevicesAsync(req.StationId, username, password, baseUrl);
                var counter = 0;
                foreach (string deviceId in req.DevIds.Split(","))
                {
                    var deviceData = await GetElementHistoricTimeData(_httpClient, deviceId, startDate, endDate, baseUrl, req.SearchType);
                    deviceData.ForEach(d => d.name = stationDevices.FirstOrDefault(s => s.Id == deviceId).Name);

                    stationData.AddRange(deviceData);
                }

                if (req.SearchType is null)
                {
                    responsedeviceData = stationData;
                }
                else {
                    switch (req.SearchType.ToLower())
                    {
                        case "monthly":
                            responsedeviceData = stationData
                                .GroupBy(d => d.dateTime.Date) // Πρώτο grouping ανά ημερομηνία
                                .SelectMany(dateGroup => dateGroup
                                    .GroupBy(d => d.name) // Δεύτερο grouping ανά name
                                    .Select(nameGroup => new RealTimeData
                                    {
                                        name = $"{nameGroup.Key}",
                                        activePower = nameGroup.Sum(d => d.activePower),
                                        totalInputPower = nameGroup.Sum(d => d.totalInputPower),
                                        dateTime = dateGroup.Key, // Η κοινή ημερομηνία της πρώτης ομάδας
                                        dateDescription = dateGroup.Key.ToString("dd/MM")
                                    })
                                )
                                .ToList();
                            break;
                        case "yearly":
                            responsedeviceData = stationData
                                .GroupBy(d => new { d.dateTime.Year, d.dateTime.Month }) // Πρώτο GroupBy (Έτος, Μήνας)
                                .SelectMany(monthGroup => monthGroup
                                    .GroupBy(d => d.name) // Δεύτερο GroupBy (Name)
                                    .Select(nameGroup => new RealTimeData
                                    {
                                        name = $"{nameGroup.Key}",
                                        activePower = nameGroup.Sum(d => d.activePower),
                                        totalInputPower = nameGroup.Sum(d => d.totalInputPower),
                                        dateTime = new DateTime(monthGroup.Key.Year, monthGroup.Key.Month, 1),
                                        dateDescription = new DateTime(monthGroup.Key.Year, monthGroup.Key.Month, 1).ToString("MMMM", CultureInfo.InvariantCulture)
                                    })
                                )
                                .ToList();

                            break;
                        case "lifetime":
                            responsedeviceData = stationData
                                .GroupBy(d => d.dateTime.Year)  // Πρώτο grouping με το Year
                                .Select(g => new
                                {
                                    Year = g.Key,
                                    GroupedByName = g
                                        .GroupBy(d => d.name)  // Δεύτερο grouping με το name
                                        .Select(gg => new RealTimeData
                                        {
                                            name = $"{gg.Key}",
                                            activePower = gg.Sum(d => d.activePower),
                                            totalInputPower = gg.Sum(d => d.totalInputPower),
                                            dateTime = new DateTime(g.Key, 1, 1),
                                            dateDescription = new DateTime(g.Key, 1, 1).ToString("MMMM yyyy", CultureInfo.InvariantCulture)
                                        })
                                        .ToList()
                                })
                                .SelectMany(yearGroup => yearGroup.GroupedByName)  // Εξέταση όλων των grouped δεδομένων για να επιστρέψουν ένα επίπεδο
                                .ToList();
                            break;
                        default:
                            throw new ArgumentException("Invalid search type");

                    }
                }

               
            }
            else
            {
                string token = await _auroraTokenService.GetAuroraTokenAsync(username, password);

                _httpClient.DefaultRequestHeaders.Add("X-AuroraVision-Token", token);
                responsedeviceData = await GetElementHistoricTimeData(_httpClient, req.StationId, startDate, endDate, baseUrl, req.SearchType);

                //remove future values
                responsedeviceData.RemoveAll(r => r.dateTime > DateTime.Now);

                responsedeviceData.ForEach(d =>
                {
                    if (req.SearchType != null)
                    {
                        switch (req.SearchType)
                        {
                            case "monthly":
                                d.name = d.dateTime.ToString("dd/MM");
                                d.dateDescription = d.dateTime.ToString("dd/MM");
                                break;
                            case "yearly":
                                d.name = CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(d.dateTime.Month);
                                d.dateDescription = CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(d.dateTime.Month);
                                break;
                            case "lifetime":
                                d.name = d.dateTime.Year.ToString();
                                d.dateDescription = d.dateTime.Year.ToString();
                                break;
                        }
                    }
                    else
                    {
                        d.name = d.dateTime.Date.ToString();
                    }
                });
            }

            var allData = new HistoricTimeData()
            {
                Data = responsedeviceData,
                Sum = responsedeviceData.Sum(s => s.activePower)
            };

            return allData;

            
        }

        private async Task<List<RealTimeData>> GetElementHistoricTimeData(HttpClient _httpClient, string elementId, string startDate, string endDate, string baseUrl, string searchType) 
        {
            var type = searchType != null ? auroraSearchTypeMapping[searchType] : "Min15";
            
            // Εκτελούμε το GET request
            HttpResponseMessage response = await _httpClient.GetAsync(baseUrl + $"/v1/stats/power/timeseries/{elementId}/GenerationPower/average?sampleSize={type}&startDate={startDate}&endDate={endDate}&timeZone=Europe/Athens");

            if (response.IsSuccessStatusCode)
            {
                string jsonResponse = await response.Content.ReadAsStringAsync();

                // Κάνουμε deserialize το JSON σε GetStationsResponse object
                AuroraGetHistoricTimeDataResponse apiResponse = JsonSerializer.Deserialize<AuroraGetHistoricTimeDataResponse>(jsonResponse, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true // Για να διαβάζει properties ανεξάρτητα από το casing
                                                       //Converters = {new HuaweiDataConverter()}
                });

                var elementData = _mapper.Map<List<AuroraHistoriTimeDataResult>, List<RealTimeData>>(apiResponse.result.ToList());

                

                return elementData;
            }
            else
            {
                throw new Exception($"Failed to get data: {response.StatusCode}");
            }
        }

    }
}
