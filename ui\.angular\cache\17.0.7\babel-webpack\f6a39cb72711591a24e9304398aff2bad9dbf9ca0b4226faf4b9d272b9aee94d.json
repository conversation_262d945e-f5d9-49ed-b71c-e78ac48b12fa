{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, signal, computed, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ViewChild, ContentChild, ContentChildren, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i1 from 'primeng/api';\nimport { Header, Footer, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { ObjectUtils, UniqueComponentId } from 'primeng/utils';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { SearchIcon } from 'primeng/icons/search';\nimport { CheckIcon } from 'primeng/icons/check';\nimport * as i4 from 'primeng/scroller';\nimport { ScrollerModule } from 'primeng/scroller';\nconst _c0 = [\"headerchkbox\"];\nconst _c1 = [\"filter\"];\nconst _c2 = [\"lastHiddenFocusableElement\"];\nconst _c3 = [\"firstHiddenFocusableElement\"];\nconst _c4 = [\"scroller\"];\nconst _c5 = [\"list\"];\nfunction Listbox_div_3_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c6 = (a0, a1) => ({\n  $implicit: a0,\n  options: a1\n});\nfunction Listbox_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵprojection(1);\n    i0.ɵɵtemplate(2, Listbox_div_3_ng_container_2_Template, 1, 0, \"ng-container\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.headerTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c6, ctx_r1.modelValue(), ctx_r1.visibleOptions()));\n  }\n}\nfunction Listbox_div_4_div_1_ng_container_5_CheckIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"CheckIcon\", 24);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-checkbox-icon\");\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction Listbox_div_4_div_1_ng_container_5_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Listbox_div_4_div_1_ng_container_5_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Listbox_div_4_div_1_ng_container_5_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Listbox_div_4_div_1_ng_container_5_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 25);\n    i0.ɵɵtemplate(1, Listbox_div_4_div_1_ng_container_5_span_2_1_Template, 1, 0, null, 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(4);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r18.checkIconTemplate);\n  }\n}\nfunction Listbox_div_4_div_1_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Listbox_div_4_div_1_ng_container_5_CheckIcon_1_Template, 1, 2, \"CheckIcon\", 22)(2, Listbox_div_4_div_1_ng_container_5_span_2_Template, 2, 2, \"span\", 23);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r16.checkIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r16.checkIconTemplate);\n  }\n}\nconst _c7 = a0 => ({\n  \"p-checkbox-disabled\": a0\n});\nconst _c8 = (a0, a1, a2) => ({\n  \"p-highlight\": a0,\n  \"p-focus\": a1,\n  \"p-disabled\": a2\n});\nfunction Listbox_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵlistener(\"click\", function Listbox_div_4_div_1_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r21.onToggleAll($event));\n    })(\"keydown\", function Listbox_div_4_div_1_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r23 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r23.onHeaderCheckboxKeyDown($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 18)(2, \"input\", 19, 20);\n    i0.ɵɵlistener(\"focus\", function Listbox_div_4_div_1_Template_input_focus_2_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r24 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r24.onHeaderCheckboxFocus($event));\n    })(\"blur\", function Listbox_div_4_div_1_Template_input_blur_2_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.onHeaderCheckboxBlur($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 21);\n    i0.ɵɵtemplate(5, Listbox_div_4_div_1_ng_container_5_Template, 3, 2, \"ng-container\", 6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c7, ctx_r11.disabled || ctx_r11.toggleAllDisabled));\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"data-p-hidden-accessible\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r11.disabled || ctx_r11.toggleAllDisabled);\n    i0.ɵɵattribute(\"checked\", ctx_r11.allSelected())(\"aria-label\", ctx_r11.toggleAllAriaLabel);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(10, _c8, ctx_r11.allSelected(), ctx_r11.headerCheckboxFocus, ctx_r11.disabled || ctx_r11.toggleAllDisabled));\n    i0.ɵɵattribute(\"aria-checked\", ctx_r11.allSelected());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.allSelected());\n  }\n}\nfunction Listbox_div_4_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c9 = a0 => ({\n  options: a0\n});\nfunction Listbox_div_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Listbox_div_4_ng_container_2_ng_container_1_Template, 1, 0, \"ng-container\", 13);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r12.filterTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c9, ctx_r12.filterOptions));\n  }\n}\nfunction Listbox_div_4_ng_template_3_div_0_SearchIcon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"SearchIcon\", 24);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-listbox-filter-icon\");\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction Listbox_div_4_ng_template_3_div_0_span_4_1_ng_template_0_Template(rf, ctx) {}\nfunction Listbox_div_4_ng_template_3_div_0_span_4_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Listbox_div_4_ng_template_3_div_0_span_4_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Listbox_div_4_ng_template_3_div_0_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 33);\n    i0.ɵɵtemplate(1, Listbox_div_4_ng_template_3_div_0_span_4_1_Template, 1, 0, null, 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext(4);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r30.filterIconTemplate);\n  }\n}\nfunction Listbox_div_4_ng_template_3_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"input\", 30, 31);\n    i0.ɵɵlistener(\"input\", function Listbox_div_4_ng_template_3_div_0_Template_input_input_1_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r33 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r33.onFilterChange($event));\n    })(\"keydown\", function Listbox_div_4_ng_template_3_div_0_Template_input_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r35 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r35.onFilterKeyDown($event));\n    })(\"blur\", function Listbox_div_4_ng_template_3_div_0_Template_input_blur_1_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r36 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r36.onFilterBlur($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, Listbox_div_4_ng_template_3_div_0_SearchIcon_3_Template, 1, 2, \"SearchIcon\", 22)(4, Listbox_div_4_ng_template_3_div_0_span_4_Template, 2, 2, \"span\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", ctx_r27._filterValue() || \"\")(\"disabled\", ctx_r27.disabled)(\"tabindex\", !ctx_r27.disabled && !ctx_r27.focused ? ctx_r27.tabindex : -1);\n    i0.ɵɵattribute(\"aria-owns\", ctx_r27.id + \"_list\")(\"aria-activedescendant\", ctx_r27.focusedOptionId)(\"placeholder\", ctx_r27.filterPlaceHolder)(\"aria-label\", ctx_r27.ariaFilterLabel);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r27.filterIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r27.filterIconTemplate);\n  }\n}\nfunction Listbox_div_4_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Listbox_div_4_ng_template_3_div_0_Template, 5, 9, \"div\", 27);\n    i0.ɵɵelementStart(1, \"span\", 28);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r13.filter);\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"data-p-hidden-accessible\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r13.filterResultMessageText, \" \");\n  }\n}\nfunction Listbox_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtemplate(1, Listbox_div_4_div_1_Template, 6, 14, \"div\", 14)(2, Listbox_div_4_ng_container_2_Template, 2, 4, \"ng-container\", 15)(3, Listbox_div_4_ng_template_3_Template, 3, 3, \"ng-template\", null, 16, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const _r14 = i0.ɵɵreference(4);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.checkbox && ctx_r2.multiple && ctx_r2.showToggleAll);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.filterTemplate)(\"ngIfElse\", _r14);\n  }\n}\nfunction Listbox_p_scroller_6_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Listbox_p_scroller_6_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Listbox_p_scroller_6_ng_template_2_ng_container_0_Template, 1, 0, \"ng-container\", 13);\n  }\n  if (rf & 2) {\n    const items_r40 = ctx.$implicit;\n    const scrollerOptions_r41 = ctx.options;\n    i0.ɵɵnextContext(2);\n    const _r6 = i0.ɵɵreference(9);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r6)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c6, items_r40, scrollerOptions_r41));\n  }\n}\nfunction Listbox_p_scroller_6_ng_container_3_ng_template_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Listbox_p_scroller_6_ng_container_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Listbox_p_scroller_6_ng_container_3_ng_template_1_ng_container_0_Template, 1, 0, \"ng-container\", 13);\n  }\n  if (rf & 2) {\n    const scrollerOptions_r44 = ctx.options;\n    const ctx_r43 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r43.loaderTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c9, scrollerOptions_r44));\n  }\n}\nfunction Listbox_p_scroller_6_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Listbox_p_scroller_6_ng_container_3_ng_template_1_Template, 1, 4, \"ng-template\", 37);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nconst _c10 = a0 => ({\n  height: a0\n});\nfunction Listbox_p_scroller_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r47 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-scroller\", 34, 35);\n    i0.ɵɵlistener(\"onLazyLoad\", function Listbox_p_scroller_6_Template_p_scroller_onLazyLoad_0_listener($event) {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r46 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r46.onLazyLoad.emit($event));\n    });\n    i0.ɵɵtemplate(2, Listbox_p_scroller_6_ng_template_2_Template, 1, 5, \"ng-template\", 36)(3, Listbox_p_scroller_6_ng_container_3_Template, 2, 0, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction1(9, _c10, ctx_r3.scrollHeight));\n    i0.ɵɵproperty(\"items\", ctx_r3.visibleOptions())(\"itemSize\", ctx_r3.virtualScrollItemSize)(\"autoSize\", true)(\"tabindex\", -1)(\"lazy\", ctx_r3.lazy)(\"options\", ctx_r3.virtualScrollOptions);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.loaderTemplate);\n  }\n}\nfunction Listbox_ng_container_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c11 = () => ({});\nfunction Listbox_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Listbox_ng_container_7_ng_container_1_Template, 1, 0, \"ng-container\", 13);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    const _r6 = i0.ɵɵreference(9);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r6)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(3, _c6, ctx_r4.visibleOptions(), i0.ɵɵpureFunction0(2, _c11)));\n  }\n}\nfunction Listbox_ng_template_8_ng_template_2_ng_container_0_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r55 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r59 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r59.getOptionGroupLabel(option_r55.optionGroup));\n  }\n}\nfunction Listbox_ng_template_8_ng_template_2_ng_container_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c12 = a0 => ({\n  $implicit: a0\n});\nfunction Listbox_ng_template_8_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 42);\n    i0.ɵɵtemplate(2, Listbox_ng_template_8_ng_template_2_ng_container_0_span_2_Template, 2, 1, \"span\", 6)(3, Listbox_ng_template_8_ng_template_2_ng_container_0_ng_container_3_Template, 1, 0, \"ng-container\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r63 = i0.ɵɵnextContext();\n    const i_r56 = ctx_r63.index;\n    const option_r55 = ctx_r63.$implicit;\n    const scrollerOptions_r50 = i0.ɵɵnextContext().options;\n    const ctx_r57 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(5, _c10, scrollerOptions_r50.itemSize + \"px\"));\n    i0.ɵɵattribute(\"id\", ctx_r57.id + \"_\" + ctx_r57.getOptionIndex(i_r56, scrollerOptions_r50));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r57.groupTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r57.groupTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(7, _c12, option_r55.optionGroup));\n  }\n}\nfunction Listbox_ng_template_8_ng_template_2_ng_container_1_div_2_ng_container_2_CheckIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"CheckIcon\", 24);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-checkbox-icon\");\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction Listbox_ng_template_8_ng_template_2_ng_container_1_div_2_ng_container_2_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Listbox_ng_template_8_ng_template_2_ng_container_1_div_2_ng_container_2_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Listbox_ng_template_8_ng_template_2_ng_container_1_div_2_ng_container_2_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Listbox_ng_template_8_ng_template_2_ng_container_1_div_2_ng_container_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 25);\n    i0.ɵɵtemplate(1, Listbox_ng_template_8_ng_template_2_ng_container_1_div_2_ng_container_2_span_2_1_Template, 1, 0, null, 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r69 = i0.ɵɵnextContext(6);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r69.checkIconTemplate);\n  }\n}\nfunction Listbox_ng_template_8_ng_template_2_ng_container_1_div_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Listbox_ng_template_8_ng_template_2_ng_container_1_div_2_ng_container_2_CheckIcon_1_Template, 1, 2, \"CheckIcon\", 22)(2, Listbox_ng_template_8_ng_template_2_ng_container_1_div_2_ng_container_2_span_2_Template, 2, 2, \"span\", 23);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r67 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r67.checkIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r67.checkIconTemplate);\n  }\n}\nconst _c13 = a0 => ({\n  \"p-highlight\": a0\n});\nfunction Listbox_ng_template_8_ng_template_2_ng_container_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46);\n    i0.ɵɵtemplate(2, Listbox_ng_template_8_ng_template_2_ng_container_1_div_2_ng_container_2_Template, 3, 2, \"ng-container\", 6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const option_r55 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r64 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c7, ctx_r64.disabled || ctx_r64.isOptionDisabled(option_r55)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c13, ctx_r64.isSelected(option_r55)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r64.isSelected(option_r55));\n  }\n}\nfunction Listbox_ng_template_8_ng_template_2_ng_container_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r55 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r65 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r65.getOptionLabel(option_r55));\n  }\n}\nfunction Listbox_ng_template_8_ng_template_2_ng_container_1_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nconst _c14 = (a1, a2, a3) => ({\n  \"p-listbox-item\": true,\n  \"p-highlight\": a1,\n  \"p-focus\": a2,\n  \"p-disabled\": a3\n});\nconst _c15 = (a0, a1) => ({\n  $implicit: a0,\n  index: a1\n});\nfunction Listbox_ng_template_8_ng_template_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r76 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 43);\n    i0.ɵɵlistener(\"click\", function Listbox_ng_template_8_ng_template_2_ng_container_1_Template_li_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r75 = i0.ɵɵnextContext();\n      const option_r55 = ctx_r75.$implicit;\n      const i_r56 = ctx_r75.index;\n      const scrollerOptions_r50 = i0.ɵɵnextContext().options;\n      const ctx_r74 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r74.onOptionSelect($event, option_r55, ctx_r74.getOptionIndex(i_r56, scrollerOptions_r50)));\n    })(\"dblclick\", function Listbox_ng_template_8_ng_template_2_ng_container_1_Template_li_dblclick_1_listener($event) {\n      i0.ɵɵrestoreView(_r76);\n      const option_r55 = i0.ɵɵnextContext().$implicit;\n      const ctx_r78 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r78.onOptionDoubleClick($event, option_r55));\n    })(\"mousedown\", function Listbox_ng_template_8_ng_template_2_ng_container_1_Template_li_mousedown_1_listener($event) {\n      i0.ɵɵrestoreView(_r76);\n      const i_r56 = i0.ɵɵnextContext().index;\n      const scrollerOptions_r50 = i0.ɵɵnextContext().options;\n      const ctx_r80 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r80.onOptionMouseDown($event, ctx_r80.getOptionIndex(i_r56, scrollerOptions_r50)));\n    })(\"mouseenter\", function Listbox_ng_template_8_ng_template_2_ng_container_1_Template_li_mouseenter_1_listener($event) {\n      i0.ɵɵrestoreView(_r76);\n      const i_r56 = i0.ɵɵnextContext().index;\n      const scrollerOptions_r50 = i0.ɵɵnextContext().options;\n      const ctx_r83 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r83.onOptionMouseEnter($event, ctx_r83.getOptionIndex(i_r56, scrollerOptions_r50)));\n    })(\"touchend\", function Listbox_ng_template_8_ng_template_2_ng_container_1_Template_li_touchend_1_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r86 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r86.onOptionTouchEnd());\n    });\n    i0.ɵɵtemplate(2, Listbox_ng_template_8_ng_template_2_ng_container_1_div_2_Template, 3, 7, \"div\", 44)(3, Listbox_ng_template_8_ng_template_2_ng_container_1_span_3_Template, 2, 1, \"span\", 6)(4, Listbox_ng_template_8_ng_template_2_ng_container_1_ng_container_4_Template, 1, 0, \"ng-container\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r88 = i0.ɵɵnextContext();\n    const option_r55 = ctx_r88.$implicit;\n    const i_r56 = ctx_r88.index;\n    const scrollerOptions_r50 = i0.ɵɵnextContext().options;\n    const ctx_r58 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(12, _c10, scrollerOptions_r50.itemSize + \"px\"))(\"ngClass\", i0.ɵɵpureFunction3(14, _c14, ctx_r58.isSelected(option_r55), ctx_r58.focusedOptionIndex() === ctx_r58.getOptionIndex(i_r56, scrollerOptions_r50), ctx_r58.isOptionDisabled(option_r55)))(\"ariaPosInset\", ctx_r58.getAriaPosInset(ctx_r58.getOptionIndex(i_r56, scrollerOptions_r50)));\n    i0.ɵɵattribute(\"id\", ctx_r58.id + \"_\" + ctx_r58.getOptionIndex(i_r56, scrollerOptions_r50))(\"aria-label\", ctx_r58.getOptionLabel(option_r55))(\"aria-selected\", ctx_r58.isSelected(option_r55))(\"aria-disabled\", ctx_r58.isOptionDisabled(option_r55))(\"aria-setsize\", ctx_r58.ariaSetSize);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r58.checkbox && ctx_r58.multiple);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r58.itemTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r58.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(18, _c15, option_r55, ctx_r58.getOptionIndex(i_r56, scrollerOptions_r50)));\n  }\n}\nfunction Listbox_ng_template_8_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Listbox_ng_template_8_ng_template_2_ng_container_0_Template, 4, 9, \"ng-container\", 6)(1, Listbox_ng_template_8_ng_template_2_ng_container_1_Template, 5, 21, \"ng-container\", 6);\n  }\n  if (rf & 2) {\n    const option_r55 = ctx.$implicit;\n    const ctx_r52 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r52.isOptionGroup(option_r55));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r52.isOptionGroup(option_r55));\n  }\n}\nfunction Listbox_ng_template_8_li_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r89 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r89.emptyFilterMessageText, \" \");\n  }\n}\nfunction Listbox_ng_template_8_li_3_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, null, 48);\n  }\n}\nfunction Listbox_ng_template_8_li_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 47);\n    i0.ɵɵtemplate(1, Listbox_ng_template_8_li_3_ng_container_1_Template, 2, 1, \"ng-container\", 15)(2, Listbox_ng_template_8_li_3_ng_container_2_Template, 2, 0, \"ng-container\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r53 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r53.emptyFilterTemplate && !ctx_r53.emptyTemplate)(\"ngIfElse\", ctx_r53.emptyFilter);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r53.emptyFilterTemplate || ctx_r53.emptyTemplate);\n  }\n}\nfunction Listbox_ng_template_8_li_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r92 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r92.emptyMessage, \" \");\n  }\n}\nfunction Listbox_ng_template_8_li_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, null, 49);\n  }\n}\nfunction Listbox_ng_template_8_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 47);\n    i0.ɵɵtemplate(1, Listbox_ng_template_8_li_4_ng_container_1_Template, 2, 1, \"ng-container\", 15)(2, Listbox_ng_template_8_li_4_ng_container_2_Template, 2, 0, \"ng-container\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r54 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r54.emptyTemplate)(\"ngIfElse\", ctx_r54.empty);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r54.emptyTemplate);\n  }\n}\nfunction Listbox_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r96 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ul\", 38, 39);\n    i0.ɵɵlistener(\"focus\", function Listbox_ng_template_8_Template_ul_focus_0_listener($event) {\n      i0.ɵɵrestoreView(_r96);\n      const ctx_r95 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r95.onListFocus($event));\n    })(\"blur\", function Listbox_ng_template_8_Template_ul_blur_0_listener($event) {\n      i0.ɵɵrestoreView(_r96);\n      const ctx_r97 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r97.onListBlur($event));\n    })(\"keydown\", function Listbox_ng_template_8_Template_ul_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r96);\n      const ctx_r98 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r98.onListKeyDown($event));\n    });\n    i0.ɵɵtemplate(2, Listbox_ng_template_8_ng_template_2_Template, 2, 2, \"ng-template\", 40)(3, Listbox_ng_template_8_li_3_Template, 3, 3, \"li\", 41)(4, Listbox_ng_template_8_li_4_Template, 3, 3, \"li\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const items_r49 = ctx.$implicit;\n    const scrollerOptions_r50 = ctx.options;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(scrollerOptions_r50.contentStyle);\n    i0.ɵɵproperty(\"tabindex\", -1)(\"ngClass\", scrollerOptions_r50.contentStyleClass);\n    i0.ɵɵattribute(\"aria-multiselectable\", true)(\"aria-activedescendant\", ctx_r5.focused ? ctx_r5.focusedOptionId : undefined)(\"aria-label\", ctx_r5.ariaLabel)(\"aria-multiselectable\", ctx_r5.multiple)(\"aria-disabled\", ctx_r5.disabled);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", items_r49);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.hasFilter() && ctx_r5.isEmpty());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.hasFilter() && ctx_r5.isEmpty());\n  }\n}\nfunction Listbox_div_10_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Listbox_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵtemplate(2, Listbox_div_10_ng_container_2_Template, 1, 0, \"ng-container\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r7.footerTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c6, ctx_r7.modelValue(), ctx_r7.visibleOptions()));\n  }\n}\nfunction Listbox_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.emptyMessage, \" \");\n  }\n}\nconst _c16 = [[[\"p-header\"]], [[\"p-footer\"]]];\nconst _c17 = [\"p-header\", \"p-footer\"];\nconst LISTBOX_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => Listbox),\n  multi: true\n};\n/**\n * ListBox is used to select one or more values from a list of items.\n * @group Components\n */\nlet Listbox = /*#__PURE__*/(() => {\n  class Listbox {\n    el;\n    cd;\n    filterService;\n    config;\n    renderer;\n    /**\n     * Unique identifier of the component.\n     * @group Props\n     */\n    id;\n    /**\n     * Text to display when the search is active. Defaults to global value in i18n translation configuration.\n     * @group Props\n     * @defaultValue '{0} results are available'\n     */\n    searchMessage;\n    /**\n     * Text to display when filtering does not return any results. Defaults to global value in i18n translation configuration.\n     * @group Props\n     * @defaultValue 'No selected item'\n     */\n    emptySelectionMessage;\n    /**\n     * Text to be displayed in hidden accessible field when options are selected. Defaults to global value in i18n translation configuration.\n     * @group Props\n     * @defaultValue '{0} items selected'\n     */\n    selectionMessage;\n    /**\n     * Whether to focus on the first visible or selected element when the overlay panel is shown.\n     * @group Props\n     */\n    autoOptionFocus = true;\n    /**\n     * When enabled, the focused option is selected.\n     * @group Props\n     */\n    selectOnFocus;\n    /**\n     * Locale to use in searching. The default locale is the host environment's current locale.\n     * @group Props\n     */\n    searchLocale;\n    /**\n     * When enabled, the hovered option will be focused.\n     * @group Props\n     */\n    focusOnHover;\n    /**\n     * Text to display when filtering.\n     * @group Props\n     */\n    filterMessage;\n    /**\n     * Fields used when filtering the options, defaults to optionLabel.\n     * @group Props\n     */\n    filterFields;\n    /**\n     * Defines if data is loaded and interacted with in lazy manner.\n     * @group Props\n     */\n    lazy = false;\n    /**\n     * Whether the data should be loaded on demand during scroll.\n     * @group Props\n     */\n    virtualScroll;\n    /**\n     * Height of an item in the list for VirtualScrolling.\n     * @group Props\n     */\n    virtualScrollItemSize;\n    /**\n     * Whether to use the scroller feature. The properties of scroller component can be used like an object in it.\n     * @group Props\n     */\n    virtualScrollOptions;\n    /**\n     * Height of the viewport in pixels, a scrollbar is defined if height of list exceeds this value.\n     * @group Props\n     */\n    scrollHeight = '200px';\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    tabindex = 0;\n    /**\n     * When specified, allows selecting multiple values.\n     * @group Props\n     */\n    multiple;\n    /**\n     * Inline style of the container.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the container.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Inline style of the list element.\n     * @group Props\n     */\n    listStyle;\n    /**\n     * Style class of the list element.\n     * @group Props\n     */\n    listStyleClass;\n    /**\n     * When present, it specifies that the element value cannot be changed.\n     * @group Props\n     */\n    readonly;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    disabled;\n    /**\n     * When specified, allows selecting items with checkboxes.\n     * @group Props\n     */\n    checkbox = false;\n    /**\n     * When specified, displays a filter input at header.\n     * @group Props\n     */\n    filter = false;\n    /**\n     * When filtering is enabled, filterBy decides which field or fields (comma separated) to search against.\n     * @group Props\n     */\n    filterBy;\n    /**\n     * Defines how the items are filtered.\n     * @group Props\n     */\n    filterMatchMode = 'contains';\n    /**\n     * Locale to use in filtering. The default locale is the host environment's current locale.\n     * @group Props\n     */\n    filterLocale;\n    /**\n     * Defines how multiple items can be selected, when true metaKey needs to be pressed to select or unselect an item and when set to false selection of each item can be toggled individually. On touch enabled devices, metaKeySelection is turned off automatically.\n     * @group Props\n     */\n    metaKeySelection = true;\n    /**\n     * A property to uniquely identify a value in options.\n     * @group Props\n     */\n    dataKey;\n    /**\n     * Whether header checkbox is shown in multiple mode.\n     * @group Props\n     */\n    showToggleAll = true;\n    /**\n     * Name of the label field of an option.\n     * @group Props\n     */\n    optionLabel;\n    /**\n     * Name of the value field of an option.\n     * @group Props\n     */\n    optionValue;\n    /**\n     * Name of the options field of an option group.\n     * @group Props\n     */\n    optionGroupChildren = 'items';\n    /**\n     * Name of the label field of an option group.\n     * @group Props\n     */\n    optionGroupLabel = 'label';\n    /**\n     * Name of the disabled field of an option.\n     * @group Props\n     */\n    optionDisabled;\n    /**\n     * Defines a string that labels the filter input.\n     * @group Props\n     */\n    ariaFilterLabel;\n    /**\n     * Defines placeholder of the filter input.\n     * @group Props\n     */\n    filterPlaceHolder;\n    /**\n     * Text to display when filtering does not return any results.\n     * @group Props\n     */\n    emptyFilterMessage;\n    /**\n     * Text to display when there is no data. Defaults to global value in i18n translation configuration.\n     * @group Props\n     */\n    emptyMessage;\n    /**\n     * Whether to display options as grouped when nested options are provided.\n     * @group Props\n     */\n    group;\n    /**\n     * An array of selectitems to display as the available options.\n     * @group Props\n     */\n    get options() {\n      return this._options();\n    }\n    set options(val) {\n      this._options.set(val);\n    }\n    /**\n     * When specified, filter displays with this value.\n     * @group Props\n     */\n    get filterValue() {\n      return this._filterValue();\n    }\n    set filterValue(val) {\n      this._filterValue.set(val);\n    }\n    /**\n     * Whether all data is selected.\n     * @group Props\n     */\n    get selectAll() {\n      return this._selectAll;\n    }\n    set selectAll(value) {\n      this._selectAll = value;\n    }\n    /**\n     * Callback to invoke on value change.\n     * @param {ListboxChangeEvent} event - Custom change event.\n     * @group Emits\n     */\n    onChange = new EventEmitter();\n    /**\n     * Callback to invoke when option is clicked.\n     * @param {ListboxClickEvent} event - Custom click event.\n     * @group Emits\n     */\n    onClick = new EventEmitter();\n    /**\n     * Callback to invoke when option is double clicked.\n     * @param {ListboxDoubleClickEvent} event - Custom double click event.\n     * @group Emits\n     */\n    onDblClick = new EventEmitter();\n    /**\n     * Callback to invoke when data is filtered.\n     * @param {ListboxFilterEvent} event - Custom filter event.\n     * @group Emits\n     */\n    onFilter = new EventEmitter();\n    /**\n     * Callback to invoke when component receives focus.\n     * @param {FocusEvent} event - Focus event.\n     * @group Emits\n     */\n    onFocus = new EventEmitter();\n    /**\n     * Callback to invoke when component loses focus.\n     * @param {FocusEvent} event - Blur event.\n     * @group Emits\n     */\n    onBlur = new EventEmitter();\n    /**\n     * Callback to invoke when all data is selected.\n     * @param {ListboxSelectAllChangeEvent} event - Custom select event.\n     * @group Emits\n     */\n    onSelectAllChange = new EventEmitter();\n    headerCheckboxViewChild;\n    filterViewChild;\n    lastHiddenFocusableElement;\n    firstHiddenFocusableElement;\n    scroller;\n    listViewChild;\n    headerFacet;\n    footerFacet;\n    templates;\n    itemTemplate;\n    groupTemplate;\n    headerTemplate;\n    filterTemplate;\n    footerTemplate;\n    emptyFilterTemplate;\n    emptyTemplate;\n    filterIconTemplate;\n    checkIconTemplate;\n    _filterValue = signal(null);\n    _filteredOptions;\n    filterOptions;\n    filtered;\n    value;\n    onModelChange = () => {};\n    onModelTouched = () => {};\n    optionTouched;\n    focus;\n    headerCheckboxFocus;\n    translationSubscription;\n    focused;\n    get containerClass() {\n      return {\n        'p-listbox p-component': true,\n        'p-focus': this.focused,\n        'p-disabled': this.disabled\n      };\n    }\n    get focusedOptionId() {\n      return this.focusedOptionIndex() !== -1 ? `${this.id}_${this.focusedOptionIndex()}` : null;\n    }\n    get filterResultMessageText() {\n      return ObjectUtils.isNotEmpty(this.visibleOptions()) ? this.filterMessageText.replaceAll('{0}', this.visibleOptions().length) : this.emptyFilterMessageText;\n    }\n    get filterMessageText() {\n      return this.filterMessage || this.config.translation.searchMessage || '';\n    }\n    get searchMessageText() {\n      return this.searchMessage || this.config.translation.searchMessage || '';\n    }\n    get emptyFilterMessageText() {\n      return this.emptyFilterMessage || this.config.translation.emptySearchMessage || this.config.translation.emptyFilterMessage || '';\n    }\n    get selectionMessageText() {\n      return this.selectionMessage || this.config.translation.selectionMessage || '';\n    }\n    get emptySelectionMessageText() {\n      return this.emptySelectionMessage || this.config.translation.emptySelectionMessage || '';\n    }\n    get selectedMessageText() {\n      return this.hasSelectedOption() ? this.selectionMessageText.replaceAll('{0}', this.multiple ? this.modelValue().length : '1') : this.emptySelectionMessageText;\n    }\n    get ariaSetSize() {\n      return this.visibleOptions().filter(option => !this.isOptionGroup(option)).length;\n    }\n    get virtualScrollerDisabled() {\n      return !this.virtualScroll;\n    }\n    get searchFields() {\n      return this.filterFields || [this.optionLabel];\n    }\n    get toggleAllAriaLabel() {\n      return this.config.translation.aria ? this.config.translation.aria[this.allSelected() ? 'selectAll' : 'unselectAll'] : undefined;\n    }\n    searchValue;\n    searchTimeout;\n    _selectAll = null;\n    _options = signal(null);\n    startRangeIndex = signal(-1);\n    focusedOptionIndex = signal(-1);\n    modelValue = signal(null);\n    visibleOptions = computed(() => {\n      const options = this.group ? this.flatOptions(this._options()) : this._options() || [];\n      return this._filterValue() ? this.filterService.filter(options, this.searchFields, this._filterValue(), this.filterMatchMode, this.filterLocale) : options;\n    });\n    constructor(el, cd, filterService, config, renderer) {\n      this.el = el;\n      this.cd = cd;\n      this.filterService = filterService;\n      this.config = config;\n      this.renderer = renderer;\n    }\n    ngOnInit() {\n      this.id = this.id || UniqueComponentId();\n      this.translationSubscription = this.config.translationObserver.subscribe(() => {\n        this.cd.markForCheck();\n      });\n      this.autoUpdateModel();\n      if (this.filterBy) {\n        this.filterOptions = {\n          filter: value => this.onFilterChange(value),\n          reset: () => this.resetFilter()\n        };\n      }\n    }\n    ngAfterContentInit() {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'item':\n            this.itemTemplate = item.template;\n            break;\n          case 'group':\n            this.groupTemplate = item.template;\n            break;\n          case 'header':\n            this.headerTemplate = item.template;\n            break;\n          case 'filter':\n            this.filterTemplate = item.template;\n            break;\n          case 'footer':\n            this.footerTemplate = item.template;\n            break;\n          case 'empty':\n            this.emptyTemplate = item.template;\n            break;\n          case 'emptyfilter':\n            this.emptyFilterTemplate = item.template;\n            break;\n          case 'filtericon':\n            this.filterIconTemplate = item.template;\n            break;\n          case 'checkicon':\n            this.checkIconTemplate = item.template;\n            break;\n          default:\n            this.itemTemplate = item.template;\n            break;\n        }\n      });\n    }\n    writeValue(value) {\n      this.value = value;\n      this.modelValue.set(this.value);\n      this.cd.markForCheck();\n    }\n    registerOnChange(fn) {\n      this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n      this.onModelTouched = fn;\n    }\n    setDisabledState(val) {\n      this.disabled = val;\n      this.cd.markForCheck();\n    }\n    flatOptions(options) {\n      return (options || []).reduce((result, option, index) => {\n        result.push({\n          optionGroup: option,\n          group: true,\n          index\n        });\n        const optionGroupChildren = this.getOptionGroupChildren(option);\n        optionGroupChildren && optionGroupChildren.forEach(o => result.push(o));\n        return result;\n      }, []);\n    }\n    autoUpdateModel() {\n      if (this.selectOnFocus && this.autoOptionFocus && !this.hasSelectedOption() && !this.multiple) {\n        const focusedOptionIndex = this.findFirstFocusedOptionIndex();\n        this.focusedOptionIndex.set(focusedOptionIndex);\n        this.onOptionSelect(null, this.visibleOptions()[this.focusedOptionIndex()]);\n      }\n    }\n    /**\n     * Updates the model value.\n     * @group Method\n     */\n    updateModel(value, event) {\n      this.value = value;\n      this.modelValue.set(value);\n      this.onModelChange(value);\n      this.onChange.emit({\n        originalEvent: event,\n        value: this.value\n      });\n    }\n    removeOption(option) {\n      return this.modelValue().filter(val => !ObjectUtils.equals(val, this.getOptionValue(option), this.equalityKey()));\n    }\n    onOptionSelect(event, option, index = -1) {\n      if (this.disabled || this.isOptionDisabled(option)) {\n        return;\n      }\n      event && this.onClick.emit({\n        originalEvent: event,\n        option,\n        value: this.value\n      });\n      this.multiple ? this.onOptionSelectMultiple(event, option) : this.onOptionSelectSingle(event, option);\n      this.optionTouched = false;\n      index !== -1 && this.focusedOptionIndex.set(index);\n    }\n    onOptionSelectMultiple(event, option) {\n      let selected = this.isSelected(option);\n      let value = null;\n      let metaSelection = this.optionTouched ? false : this.metaKeySelection;\n      if (metaSelection) {\n        let metaKey = event.metaKey || event.ctrlKey;\n        if (selected) {\n          value = metaKey ? this.removeOption(option) : [this.getOptionValue(option)];\n        } else {\n          value = metaKey ? this.modelValue() || [] : [];\n          value = [...value, this.getOptionValue(option)];\n        }\n      } else {\n        value = selected ? this.removeOption(option) : [...(this.modelValue() || []), this.getOptionValue(option)];\n      }\n      this.updateModel(value, event);\n    }\n    onOptionSelectSingle(event, option) {\n      let selected = this.isSelected(option);\n      let valueChanged = false;\n      let value = null;\n      let metaSelection = this.optionTouched ? false : this.metaKeySelection;\n      if (metaSelection) {\n        let metaKey = event.metaKey || event.ctrlKey;\n        if (selected) {\n          if (metaKey) {\n            value = null;\n            valueChanged = true;\n          }\n        } else {\n          value = this.getOptionValue(option);\n          valueChanged = true;\n        }\n      } else {\n        value = selected ? null : this.getOptionValue(option);\n        valueChanged = true;\n      }\n      if (valueChanged) {\n        this.updateModel(value, event);\n      }\n    }\n    onOptionSelectRange(event, start = -1, end = -1) {\n      start === -1 && (start = this.findNearestSelectedOptionIndex(end, true));\n      end === -1 && (end = this.findNearestSelectedOptionIndex(start));\n      if (start !== -1 && end !== -1) {\n        const rangeStart = Math.min(start, end);\n        const rangeEnd = Math.max(start, end);\n        const value = this.visibleOptions().slice(rangeStart, rangeEnd + 1).filter(option => this.isValidOption(option)).map(option => this.getOptionValue(option));\n        this.updateModel(value, event);\n      }\n    }\n    onToggleAll(event) {\n      if (this.disabled || this.readonly) {\n        return;\n      }\n      DomHandler.focus(this.headerCheckboxViewChild.nativeElement);\n      if (this.selectAll !== null) {\n        this.onSelectAllChange.emit({\n          originalEvent: event,\n          checked: !this.allSelected()\n        });\n      } else {\n        const value = this.allSelected() ? [] : this.visibleOptions().filter(option => this.isValidOption(option)).map(option => this.getOptionValue(option));\n        this.updateModel(value, event);\n        this.onChange.emit({\n          originalEvent: event,\n          value: this.value\n        });\n      }\n      event.preventDefault();\n      // event.stopPropagation();\n    }\n\n    allSelected() {\n      return this.selectAll !== null ? this.selectAll : ObjectUtils.isNotEmpty(this.visibleOptions()) && this.visibleOptions().every(option => this.isOptionGroup(option) || this.isOptionDisabled(option) || this.isSelected(option));\n    }\n    onOptionTouchEnd() {\n      if (this.disabled) {\n        return;\n      }\n      this.optionTouched = true;\n    }\n    onOptionMouseDown(event, index) {\n      this.changeFocusedOptionIndex(event, index);\n    }\n    onOptionMouseEnter(event, index) {\n      if (this.focusOnHover) {\n        this.changeFocusedOptionIndex(event, index);\n      }\n    }\n    onOptionDoubleClick(event, option) {\n      if (this.disabled || this.isOptionDisabled(option) || this.readonly) {\n        return;\n      }\n      this.onDblClick.emit({\n        originalEvent: event,\n        option: option,\n        value: this.value\n      });\n    }\n    onFirstHiddenFocus(event) {\n      DomHandler.focus(this.listViewChild.nativeElement);\n      const firstFocusableEl = DomHandler.getFirstFocusableElement(this.el.nativeElement, ':not([data-p-hidden-focusable=\"true\"])');\n      this.lastHiddenFocusableElement.nativeElement.tabIndex = ObjectUtils.isEmpty(firstFocusableEl) ? '-1' : undefined;\n      this.firstHiddenFocusableElement.nativeElement.tabIndex = -1;\n    }\n    onLastHiddenFocus(event) {\n      const relatedTarget = event.relatedTarget;\n      if (relatedTarget === this.listViewChild.nativeElement) {\n        const firstFocusableEl = DomHandler.getFirstFocusableElement(this.el.nativeElement, ':not(.p-hidden-focusable)');\n        DomHandler.focus(firstFocusableEl);\n        this.firstHiddenFocusableElement.nativeElement.tabIndex = undefined;\n      } else {\n        DomHandler.focus(this.firstHiddenFocusableElement.nativeElement);\n      }\n      this.lastHiddenFocusableElement.nativeElement.tabIndex = -1;\n    }\n    onFocusout(event) {\n      if (!this.el.nativeElement.contains(event.relatedTarget) && this.lastHiddenFocusableElement && this.firstHiddenFocusableElement) {\n        this.firstHiddenFocusableElement.nativeElement.tabIndex = this.lastHiddenFocusableElement.nativeElement.tabIndex = undefined;\n      }\n    }\n    onListFocus(event) {\n      this.focused = true;\n      const focusedOptionIndex = this.focusedOptionIndex() !== -1 ? this.focusedOptionIndex() : this.autoOptionFocus ? this.findFirstFocusedOptionIndex() : -1;\n      this.focusedOptionIndex.set(focusedOptionIndex);\n      this.onFocus.emit(event);\n    }\n    onListBlur(event) {\n      this.focused = false;\n      this.focusedOptionIndex.set(-1);\n      this.startRangeIndex.set(-1);\n      this.searchValue = '';\n    }\n    onHeaderCheckboxFocus(event) {\n      this.headerCheckboxFocus = true;\n    }\n    onHeaderCheckboxBlur() {\n      this.headerCheckboxFocus = false;\n    }\n    onHeaderCheckboxKeyDown(event) {\n      if (this.disabled) {\n        event.preventDefault();\n        return;\n      }\n      switch (event.code) {\n        case 'Space':\n          this.onToggleAll(event);\n          break;\n        case 'Enter':\n          this.onToggleAll(event);\n          break;\n        case 'Tab':\n          this.onHeaderCheckboxTabKeyDown(event);\n          break;\n        default:\n          break;\n      }\n    }\n    onHeaderCheckboxTabKeyDown(event) {\n      DomHandler.focus(this.listViewChild.nativeElement);\n      event.preventDefault();\n    }\n    onFilterChange(event) {\n      let value = event.target.value?.trim();\n      this._filterValue.set(value);\n      this.focusedOptionIndex.set(-1);\n      this.startRangeIndex.set(-1);\n      this.onFilter.emit({\n        originalEvent: event,\n        filter: this._filterValue()\n      });\n      !this.virtualScrollerDisabled && this.scroller.scrollToIndex(0);\n    }\n    onFilterBlur(event) {\n      this.focusedOptionIndex.set(-1);\n      this.startRangeIndex.set(-1);\n    }\n    onListKeyDown(event) {\n      const metaKey = event.metaKey || event.ctrlKey;\n      switch (event.code) {\n        case 'ArrowDown':\n          this.onArrowDownKey(event);\n          break;\n        case 'ArrowUp':\n          this.onArrowUpKey(event);\n          break;\n        case 'Home':\n          this.onHomeKey(event);\n          break;\n        case 'End':\n          this.onEndKey(event);\n          break;\n        case 'PageDown':\n          this.onPageDownKey(event);\n          break;\n        case 'PageUp':\n          this.onPageUpKey(event);\n          break;\n        case 'Enter':\n        case 'Space':\n          this.onSpaceKey(event);\n          break;\n        case 'Tab':\n          //NOOP\n          break;\n        case 'ShiftLeft':\n        case 'ShiftRight':\n          this.onShiftKey();\n          break;\n        default:\n          if (this.multiple && event.code === 'KeyA' && metaKey) {\n            const value = this.visibleOptions().filter(option => this.isValidOption(option)).map(option => this.getOptionValue(option));\n            this.updateModel(value, event);\n            event.preventDefault();\n            break;\n          }\n          if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n            this.searchOptions(event, event.key);\n            event.preventDefault();\n          }\n          break;\n      }\n    }\n    onFilterKeyDown(event) {\n      switch (event.code) {\n        case 'ArrowDown':\n          this.onArrowDownKey(event);\n          break;\n        case 'ArrowUp':\n          this.onArrowUpKey(event);\n          break;\n        case 'ArrowLeft':\n        case 'ArrowRight':\n          this.onArrowLeftKey(event, true);\n          break;\n        case 'Home':\n          this.onHomeKey(event, true);\n          break;\n        case 'End':\n          this.onEndKey(event, true);\n          break;\n        case 'Enter':\n          this.onEnterKey(event);\n          break;\n        case 'ShiftLeft':\n        case 'ShiftRight':\n          this.onShiftKey();\n          break;\n        default:\n          break;\n      }\n    }\n    onArrowDownKey(event) {\n      const optionIndex = this.focusedOptionIndex() !== -1 ? this.findNextOptionIndex(this.focusedOptionIndex()) : this.findFirstFocusedOptionIndex();\n      if (this.multiple && event.shiftKey) {\n        this.onOptionSelectRange(event, this.startRangeIndex(), optionIndex);\n      }\n      this.changeFocusedOptionIndex(event, optionIndex);\n      event.preventDefault();\n    }\n    onArrowUpKey(event) {\n      const optionIndex = this.focusedOptionIndex() !== -1 ? this.findPrevOptionIndex(this.focusedOptionIndex()) : this.findLastFocusedOptionIndex();\n      if (this.multiple && event.shiftKey) {\n        this.onOptionSelectRange(event, optionIndex, this.startRangeIndex());\n      }\n      this.changeFocusedOptionIndex(event, optionIndex);\n      event.preventDefault();\n    }\n    onArrowLeftKey(event, pressedInInputText = false) {\n      pressedInInputText && this.focusedOptionIndex.set(-1);\n    }\n    onHomeKey(event, pressedInInputText = false) {\n      if (pressedInInputText) {\n        event.currentTarget.setSelectionRange(0, 0);\n        this.focusedOptionIndex.set(-1);\n      } else {\n        let metaKey = event.metaKey || event.ctrlKey;\n        let optionIndex = this.findFirstOptionIndex();\n        if (this.multiple && event.shiftKey && metaKey) {\n          this.onOptionSelectRange(event, optionIndex, this.startRangeIndex());\n        }\n        this.changeFocusedOptionIndex(event, optionIndex);\n      }\n      event.preventDefault();\n    }\n    onEndKey(event, pressedInInputText = false) {\n      if (pressedInInputText) {\n        const target = event.currentTarget;\n        const len = target.value.length;\n        target.setSelectionRange(len, len);\n        this.focusedOptionIndex.set(-1);\n      } else {\n        let metaKey = event.metaKey || event.ctrlKey;\n        let optionIndex = this.findLastOptionIndex();\n        if (this.multiple && event.shiftKey && metaKey) {\n          this.onOptionSelectRange(event, this.startRangeIndex(), optionIndex);\n        }\n        this.changeFocusedOptionIndex(event, optionIndex);\n      }\n      event.preventDefault();\n    }\n    onPageDownKey(event) {\n      this.scrollInView(0);\n      event.preventDefault();\n    }\n    onPageUpKey(event) {\n      this.scrollInView(this.visibleOptions().length - 1);\n      event.preventDefault();\n    }\n    onEnterKey(event) {\n      if (this.focusedOptionIndex() !== -1) {\n        if (this.multiple && event.shiftKey) this.onOptionSelectRange(event, this.focusedOptionIndex());else this.onOptionSelect(event, this.visibleOptions()[this.focusedOptionIndex()]);\n      }\n      event.preventDefault();\n    }\n    onSpaceKey(event) {\n      this.onEnterKey(event);\n    }\n    onShiftKey() {\n      const focusedOptionIndex = this.focusedOptionIndex();\n      this.startRangeIndex.set(focusedOptionIndex);\n    }\n    getOptionGroupChildren(optionGroup) {\n      return this.optionGroupChildren ? ObjectUtils.resolveFieldData(optionGroup, this.optionGroupChildren) : optionGroup.items;\n    }\n    getOptionGroupLabel(optionGroup) {\n      return this.optionGroupLabel ? ObjectUtils.resolveFieldData(optionGroup, this.optionGroupLabel) : optionGroup && optionGroup.label !== undefined ? optionGroup.label : optionGroup;\n    }\n    getOptionLabel(option) {\n      return this.optionLabel ? ObjectUtils.resolveFieldData(option, this.optionLabel) : option.label != undefined ? option.label : option;\n    }\n    getOptionIndex(index, scrollerOptions) {\n      return this.virtualScrollerDisabled ? index : scrollerOptions && scrollerOptions.getItemOptions(index)['index'];\n    }\n    getOptionValue(option) {\n      return this.optionValue ? ObjectUtils.resolveFieldData(option, this.optionValue) : !this.optionLabel && option && option.value !== undefined ? option.value : option;\n    }\n    getAriaPosInset(index) {\n      return (this.optionGroupLabel ? index - this.visibleOptions().slice(0, index).filter(option => this.isOptionGroup(option)).length : index) + 1;\n    }\n    hasSelectedOption() {\n      return ObjectUtils.isNotEmpty(this.modelValue());\n    }\n    isOptionGroup(option) {\n      return this.optionGroupLabel && option.optionGroup && option.group;\n    }\n    changeFocusedOptionIndex(event, index) {\n      if (this.focusedOptionIndex() !== index) {\n        this.focusedOptionIndex.set(index);\n        this.scrollInView();\n        if (this.selectOnFocus && !this.multiple) {\n          this.onOptionSelect(event, this.visibleOptions()[index]);\n        }\n      }\n    }\n    searchOptions(event, char) {\n      this.searchValue = (this.searchValue || '') + char;\n      let optionIndex = -1;\n      let matched = false;\n      if (this.focusedOptionIndex() !== -1) {\n        optionIndex = this.visibleOptions().slice(this.focusedOptionIndex()).findIndex(option => this.isOptionMatched(option));\n        optionIndex = optionIndex === -1 ? this.visibleOptions().slice(0, this.focusedOptionIndex()).findIndex(option => this.isOptionMatched(option)) : optionIndex + this.focusedOptionIndex();\n      } else {\n        optionIndex = this.visibleOptions().findIndex(option => this.isOptionMatched(option));\n      }\n      if (optionIndex !== -1) {\n        matched = true;\n      }\n      if (optionIndex === -1 && this.focusedOptionIndex() === -1) {\n        optionIndex = this.findFirstFocusedOptionIndex();\n      }\n      if (optionIndex !== -1) {\n        this.changeFocusedOptionIndex(event, optionIndex);\n      }\n      if (this.searchTimeout) {\n        clearTimeout(this.searchTimeout);\n      }\n      this.searchTimeout = setTimeout(() => {\n        this.searchValue = '';\n        this.searchTimeout = null;\n      }, 500);\n      return matched;\n    }\n    isOptionMatched(option) {\n      return this.isValidOption(option) && this.getOptionLabel(option).toLocaleLowerCase(this.filterLocale).startsWith(this.searchValue.toLocaleLowerCase(this.filterLocale));\n    }\n    scrollInView(index = -1) {\n      const id = index !== -1 ? `${this.id}_${index}` : this.focusedOptionId;\n      const element = DomHandler.findSingle(this.listViewChild.nativeElement, `li[id=\"${id}\"]`);\n      if (element) {\n        element.scrollIntoView && element.scrollIntoView({\n          block: 'nearest',\n          inline: 'nearest'\n        });\n      } else if (!this.virtualScrollerDisabled) {\n        this.virtualScroll && this.scroller.scrollToIndex(index !== -1 ? index : this.focusedOptionIndex());\n      }\n    }\n    findFirstOptionIndex() {\n      return this.visibleOptions().findIndex(option => this.isValidOption(option));\n    }\n    findLastOptionIndex() {\n      return ObjectUtils.findLastIndex(this.visibleOptions(), option => this.isValidOption(option));\n    }\n    findFirstFocusedOptionIndex() {\n      const selectedIndex = this.findFirstSelectedOptionIndex();\n      return selectedIndex < 0 ? this.findFirstOptionIndex() : selectedIndex;\n    }\n    findLastFocusedOptionIndex() {\n      const selectedIndex = this.findLastSelectedOptionIndex();\n      return selectedIndex < 0 ? this.findLastOptionIndex() : selectedIndex;\n    }\n    findLastSelectedOptionIndex() {\n      return this.hasSelectedOption() ? ObjectUtils.findLastIndex(this.visibleOptions(), option => this.isValidSelectedOption(option)) : -1;\n    }\n    findNextOptionIndex(index) {\n      const matchedOptionIndex = index < this.visibleOptions().length - 1 ? this.visibleOptions().slice(index + 1).findIndex(option => this.isValidOption(option)) : -1;\n      return matchedOptionIndex > -1 ? matchedOptionIndex + index + 1 : index;\n    }\n    findNextSelectedOptionIndex(index) {\n      const matchedOptionIndex = this.hasSelectedOption() && index < this.visibleOptions().length - 1 ? this.visibleOptions().slice(index + 1).findIndex(option => this.isValidSelectedOption(option)) : -1;\n      return matchedOptionIndex > -1 ? matchedOptionIndex + index + 1 : -1;\n    }\n    findPrevSelectedOptionIndex(index) {\n      const matchedOptionIndex = this.hasSelectedOption() && index > 0 ? ObjectUtils.findLastIndex(this.visibleOptions().slice(0, index), option => this.isValidSelectedOption(option)) : -1;\n      return matchedOptionIndex > -1 ? matchedOptionIndex : -1;\n    }\n    findFirstSelectedOptionIndex() {\n      return this.hasSelectedOption() ? this.visibleOptions().findIndex(option => this.isValidSelectedOption(option)) : -1;\n    }\n    findPrevOptionIndex(index) {\n      const matchedOptionIndex = index > 0 ? ObjectUtils.findLastIndex(this.visibleOptions().slice(0, index), option => this.isValidOption(option)) : -1;\n      return matchedOptionIndex > -1 ? matchedOptionIndex : index;\n    }\n    findNearestSelectedOptionIndex(index, firstCheckUp = false) {\n      let matchedOptionIndex = -1;\n      if (this.hasSelectedOption()) {\n        if (firstCheckUp) {\n          matchedOptionIndex = this.findPrevSelectedOptionIndex(index);\n          matchedOptionIndex = matchedOptionIndex === -1 ? this.findNextSelectedOptionIndex(index) : matchedOptionIndex;\n        } else {\n          matchedOptionIndex = this.findNextSelectedOptionIndex(index);\n          matchedOptionIndex = matchedOptionIndex === -1 ? this.findPrevSelectedOptionIndex(index) : matchedOptionIndex;\n        }\n      }\n      return matchedOptionIndex > -1 ? matchedOptionIndex : index;\n    }\n    equalityKey() {\n      return this.optionValue ? null : this.dataKey;\n    }\n    isValidSelectedOption(option) {\n      return this.isValidOption(option) && this.isSelected(option);\n    }\n    isOptionDisabled(option) {\n      return this.optionDisabled ? ObjectUtils.resolveFieldData(option, this.optionDisabled) : false;\n    }\n    isSelected(option) {\n      const optionValue = this.getOptionValue(option);\n      if (this.multiple) return (this.modelValue() || []).some(value => ObjectUtils.equals(value, optionValue, this.equalityKey()));else return ObjectUtils.equals(this.modelValue(), optionValue, this.equalityKey());\n    }\n    isValidOption(option) {\n      return option && !(this.isOptionDisabled(option) || this.isOptionGroup(option));\n    }\n    isEmpty() {\n      return !this._options() || this._options() && this._options().length === 0;\n    }\n    hasFilter() {\n      return this._filterValue() && this._filterValue().trim().length > 0;\n    }\n    resetFilter() {\n      if (this.filterViewChild && this.filterViewChild.nativeElement) {\n        this.filterViewChild.nativeElement.value = '';\n      }\n      this._filterValue.set(null);\n    }\n    ngOnDestroy() {\n      if (this.translationSubscription) {\n        this.translationSubscription.unsubscribe();\n      }\n    }\n    static ɵfac = function Listbox_Factory(t) {\n      return new (t || Listbox)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.FilterService), i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i0.Renderer2));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Listbox,\n      selectors: [[\"p-listbox\"]],\n      contentQueries: function Listbox_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, Header, 5);\n          i0.ɵɵcontentQuery(dirIndex, Footer, 5);\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerFacet = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerFacet = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function Listbox_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n          i0.ɵɵviewQuery(_c3, 5);\n          i0.ɵɵviewQuery(_c4, 5);\n          i0.ɵɵviewQuery(_c5, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerCheckboxViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.filterViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.lastHiddenFocusableElement = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.firstHiddenFocusableElement = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scroller = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listViewChild = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        id: \"id\",\n        searchMessage: \"searchMessage\",\n        emptySelectionMessage: \"emptySelectionMessage\",\n        selectionMessage: \"selectionMessage\",\n        autoOptionFocus: \"autoOptionFocus\",\n        selectOnFocus: \"selectOnFocus\",\n        searchLocale: \"searchLocale\",\n        focusOnHover: \"focusOnHover\",\n        filterMessage: \"filterMessage\",\n        filterFields: \"filterFields\",\n        lazy: \"lazy\",\n        virtualScroll: \"virtualScroll\",\n        virtualScrollItemSize: \"virtualScrollItemSize\",\n        virtualScrollOptions: \"virtualScrollOptions\",\n        scrollHeight: \"scrollHeight\",\n        tabindex: \"tabindex\",\n        multiple: \"multiple\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        listStyle: \"listStyle\",\n        listStyleClass: \"listStyleClass\",\n        readonly: \"readonly\",\n        disabled: \"disabled\",\n        checkbox: \"checkbox\",\n        filter: \"filter\",\n        filterBy: \"filterBy\",\n        filterMatchMode: \"filterMatchMode\",\n        filterLocale: \"filterLocale\",\n        metaKeySelection: \"metaKeySelection\",\n        dataKey: \"dataKey\",\n        showToggleAll: \"showToggleAll\",\n        optionLabel: \"optionLabel\",\n        optionValue: \"optionValue\",\n        optionGroupChildren: \"optionGroupChildren\",\n        optionGroupLabel: \"optionGroupLabel\",\n        optionDisabled: \"optionDisabled\",\n        ariaFilterLabel: \"ariaFilterLabel\",\n        filterPlaceHolder: \"filterPlaceHolder\",\n        emptyFilterMessage: \"emptyFilterMessage\",\n        emptyMessage: \"emptyMessage\",\n        group: \"group\",\n        options: \"options\",\n        filterValue: \"filterValue\",\n        selectAll: \"selectAll\"\n      },\n      outputs: {\n        onChange: \"onChange\",\n        onClick: \"onClick\",\n        onDblClick: \"onDblClick\",\n        onFilter: \"onFilter\",\n        onFocus: \"onFocus\",\n        onBlur: \"onBlur\",\n        onSelectAllChange: \"onSelectAllChange\"\n      },\n      features: [i0.ɵɵProvidersFeature([LISTBOX_VALUE_ACCESSOR])],\n      ngContentSelectors: _c17,\n      decls: 16,\n      vars: 24,\n      consts: [[3, \"ngClass\", \"ngStyle\", \"focusout\"], [\"role\", \"presentation\", 1, \"p-hidden-accessible\", \"p-hidden-focusable\", 3, \"tabindex\", \"focus\"], [\"firstHiddenFocusableElement\", \"\"], [\"class\", \"p-listbox-header\", 4, \"ngIf\"], [3, \"ngClass\", \"ngStyle\"], [3, \"items\", \"style\", \"itemSize\", \"autoSize\", \"tabindex\", \"lazy\", \"options\", \"onLazyLoad\", 4, \"ngIf\"], [4, \"ngIf\"], [\"buildInItems\", \"\"], [\"class\", \"p-listbox-footer\", 4, \"ngIf\"], [\"role\", \"status\", \"aria-live\", \"polite\", \"class\", \"p-hidden-accessible\", 4, \"ngIf\"], [\"role\", \"status\", \"aria-live\", \"polite\", 1, \"p-hidden-accessible\"], [\"lastHiddenFocusableElement\", \"\"], [1, \"p-listbox-header\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"class\", \"p-checkbox p-component\", 3, \"ngClass\", \"click\", \"keydown\", 4, \"ngIf\"], [4, \"ngIf\", \"ngIfElse\"], [\"builtInFilterElement\", \"\"], [1, \"p-checkbox\", \"p-component\", 3, \"ngClass\", \"click\", \"keydown\"], [1, \"p-hidden-accessible\"], [\"type\", \"checkbox\", \"readonly\", \"readonly\", 3, \"disabled\", \"focus\", \"blur\"], [\"headerchkbox\", \"\"], [\"role\", \"checkbox\", 1, \"p-checkbox-box\", 3, \"ngClass\"], [3, \"styleClass\", 4, \"ngIf\"], [\"class\", \"p-checkbox-icon\", 4, \"ngIf\"], [3, \"styleClass\"], [1, \"p-checkbox-icon\"], [4, \"ngTemplateOutlet\"], [\"class\", \"p-listbox-filter-container\", 4, \"ngIf\"], [\"role\", \"status\", \"attr.aria-live\", \"polite\", 1, \"p-hidden-accessible\"], [1, \"p-listbox-filter-container\"], [\"type\", \"text\", \"role\", \"searchbox\", 1, \"p-listbox-filter\", \"p-inputtext\", \"p-component\", 3, \"value\", \"disabled\", \"tabindex\", \"input\", \"keydown\", \"blur\"], [\"filterInput\", \"\"], [\"class\", \"p-listbox-filter-icon\", 4, \"ngIf\"], [1, \"p-listbox-filter-icon\"], [3, \"items\", \"itemSize\", \"autoSize\", \"tabindex\", \"lazy\", \"options\", \"onLazyLoad\"], [\"scroller\", \"\"], [\"pTemplate\", \"content\"], [\"pTemplate\", \"loader\"], [\"role\", \"listbox\", 1, \"p-listbox-list\", 3, \"tabindex\", \"ngClass\", \"focus\", \"blur\", \"keydown\"], [\"list\", \"\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"class\", \"p-listbox-empty-message\", \"role\", \"option\", 4, \"ngIf\"], [\"role\", \"option\", 1, \"p-listbox-item-group\", 3, \"ngStyle\"], [\"pRipple\", \"\", \"role\", \"option\", 1, \"p-listbox-item\", 3, \"ngStyle\", \"ngClass\", \"ariaPosInset\", \"click\", \"dblclick\", \"mousedown\", \"mouseenter\", \"touchend\"], [\"class\", \"p-checkbox p-component\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-checkbox\", \"p-component\", 3, \"ngClass\"], [1, \"p-checkbox-box\", 3, \"ngClass\"], [\"role\", \"option\", 1, \"p-listbox-empty-message\"], [\"emptyFilter\", \"\"], [\"empty\", \"\"], [1, \"p-listbox-footer\"]],\n      template: function Listbox_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c16);\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵlistener(\"focusout\", function Listbox_Template_div_focusout_0_listener($event) {\n            return ctx.onFocusout($event);\n          });\n          i0.ɵɵelementStart(1, \"span\", 1, 2);\n          i0.ɵɵlistener(\"focus\", function Listbox_Template_span_focus_1_listener($event) {\n            return ctx.onFirstHiddenFocus($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, Listbox_div_3_Template, 3, 5, \"div\", 3)(4, Listbox_div_4_Template, 5, 3, \"div\", 3);\n          i0.ɵɵelementStart(5, \"div\", 4);\n          i0.ɵɵtemplate(6, Listbox_p_scroller_6_Template, 4, 11, \"p-scroller\", 5)(7, Listbox_ng_container_7_Template, 2, 6, \"ng-container\", 6)(8, Listbox_ng_template_8_Template, 5, 12, \"ng-template\", null, 7, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, Listbox_div_10_Template, 3, 5, \"div\", 8)(11, Listbox_span_11_Template, 2, 1, \"span\", 9);\n          i0.ɵɵelementStart(12, \"span\", 10);\n          i0.ɵɵtext(13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"span\", 1, 11);\n          i0.ɵɵlistener(\"focus\", function Listbox_Template_span_focus_14_listener($event) {\n            return ctx.onLastHiddenFocus($event);\n          });\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngClass\", ctx.containerClass)(\"ngStyle\", ctx.style);\n          i0.ɵɵattribute(\"id\", ctx.id);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"tabindex\", !ctx.disabled ? ctx.tabindex : -1);\n          i0.ɵɵattribute(\"aria-hidden\", true)(\"data-p-hidden-focusable\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.headerFacet || ctx.headerTemplate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.checkbox && ctx.multiple && ctx.showToggleAll || ctx.filter);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.listStyleClass);\n          i0.ɵɵstyleProp(\"max-height\", ctx.virtualScroll ? \"auto\" : ctx.scrollHeight || \"auto\");\n          i0.ɵɵproperty(\"ngClass\", \"p-listbox-list-wrapper\")(\"ngStyle\", ctx.listStyle);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.virtualScroll);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.virtualScroll);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.footerFacet || ctx.footerTemplate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isEmpty());\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.selectedMessageText, \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"tabindex\", !ctx.disabled ? ctx.tabindex : -1);\n          i0.ɵɵattribute(\"aria-hidden\", true)(\"data-p-hidden-focusable\", true);\n        }\n      },\n      dependencies: () => [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i1.PrimeTemplate, i3.Ripple, i4.Scroller, SearchIcon, CheckIcon],\n      styles: [\"@layer primeng{.p-listbox-list-wrapper{overflow:auto}.p-listbox-list{list-style-type:none;margin:0;padding:0}.p-listbox-item{cursor:pointer;position:relative;overflow:hidden;display:flex;align-items:center;-webkit-user-select:none;user-select:none}.p-listbox-header{display:flex;align-items:center}.p-listbox-filter-container{position:relative;flex:1 1 auto}.p-listbox-filter-icon{position:absolute;top:50%;margin-top:-.5rem}.p-listbox-filter{width:100%}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return Listbox;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ListboxModule = /*#__PURE__*/(() => {\n  class ListboxModule {\n    static ɵfac = function ListboxModule_Factory(t) {\n      return new (t || ListboxModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ListboxModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, SharedModule, RippleModule, ScrollerModule, SearchIcon, CheckIcon, SharedModule, ScrollerModule]\n    });\n  }\n  return ListboxModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { LISTBOX_VALUE_ACCESSOR, Listbox, ListboxModule };\n//# sourceMappingURL=primeng-listbox.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}