{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport let LoadingService = /*#__PURE__*/(() => {\n  class LoadingService {\n    constructor() {\n      this.loadingSubject = new BehaviorSubject(false);\n      this.loadingCounter = 0;\n      this.loading$ = this.loadingSubject.asObservable();\n    }\n    show() {\n      this.loadingCounter++;\n      if (this.loadingCounter === 1) {\n        this.loadingSubject.next(true);\n      }\n    }\n    hide() {\n      if (this.loadingCounter > 0) {\n        this.loadingCounter--;\n      }\n      if (this.loadingCounter === 0) {\n        this.loadingSubject.next(false);\n      }\n    }\n    // Μέθοδος για force hide (για emergency cases)\n    forceHide() {\n      this.loadingCounter = 0;\n      this.loadingSubject.next(false);\n    }\n    // Getter για debugging\n    get activeRequests() {\n      return this.loadingCounter;\n    }\n    static #_ = this.ɵfac = function LoadingService_Factory(t) {\n      return new (t || LoadingService)();\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: LoadingService,\n      factory: LoadingService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return LoadingService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}