{"ast": null, "code": "import { tap } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let AuthService = /*#__PURE__*/(() => {\n  class AuthService {\n    constructor(http) {\n      this.http = http;\n      this.apiLoginUrl = environment.solarApi + 'api/auth/authenticate'; // Endpoint για login\n      this.apiRegisterUrl = environment.solarApi + 'api/user/register'; // Endpoint για register\n      this.tokenKey = 'auth_token'; // Κλειδί για αποθήκευση του token\n    }\n\n    login(username, password) {\n      console.log(\"API URL\" + this.apiLoginUrl);\n      return this.http.post(this.apiLoginUrl, {\n        username,\n        password\n      }).pipe(tap(response => {\n        console.log(response);\n        if (response.token) {\n          localStorage.setItem(this.tokenKey, response.token);\n          console.log('Token αποθηκεύτηκε:', response.token); // ✅ Έλεγχος αν αποθηκεύεται\n        }\n      }));\n    }\n\n    register(username, password, email, firstName, lastName) {\n      console.log(\"API URL\" + this.apiRegisterUrl);\n      return this.http.post(this.apiRegisterUrl, {\n        username,\n        password,\n        email,\n        firstName,\n        lastName\n      }).pipe(tap(response => {\n        console.log(response);\n        if (response.token) {}\n      }));\n    }\n    logout() {\n      localStorage.removeItem(this.tokenKey);\n    }\n    getToken() {\n      return localStorage.getItem(this.tokenKey);\n    }\n    isLoggedIn() {\n      return !!this.getToken();\n    }\n    static #_ = this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return AuthService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}