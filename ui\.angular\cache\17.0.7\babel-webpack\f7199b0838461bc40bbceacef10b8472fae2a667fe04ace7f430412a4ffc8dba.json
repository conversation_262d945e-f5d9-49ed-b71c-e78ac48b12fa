{"ast": null, "code": "import { style, animate, transition, trigger } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, forwardRef, Component, ViewEncapsulation, Inject, Input, Output, ViewChild, signal, effect, PLATFORM_ID, ChangeDetectionStrategy, ContentChildren, NgModule } from '@angular/core';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i5 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { AngleRightIcon } from 'primeng/icons/angleright';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i4 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ObjectUtils, UniqueComponentId, ZIndexUtils } from 'primeng/utils';\nconst _c0 = [\"sublist\"];\nfunction TieredMenuSub_ng_template_2_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 5);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(ctx_r4.getItemProp(processedItem_r2, \"style\"));\n    i0.ɵɵproperty(\"ngClass\", ctx_r4.getSeparatorItemClass(processedItem_r2));\n    i0.ɵɵattribute(\"id\", ctx_r4.getItemId(processedItem_r2))(\"data-pc-section\", \"separator\");\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 18);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r13.getItemProp(processedItem_r2, \"icon\"))(\"ngStyle\", ctx_r13.getItemProp(processedItem_r2, \"iconStyle\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\")(\"aria-hidden\", true)(\"tabindex\", -1);\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.getItemLabel(processedItem_r2), \" \");\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 20);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r15.getItemLabel(processedItem_r2), i0.ɵɵsanitizeHtml);\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r17 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r17.getItemProp(processedItem_r2, \"badgeStyleClass\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r17.getItemProp(processedItem_r2, \"badge\"));\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_AngleRightIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleRightIcon\", 24);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_ng_template_0_Template(rf, ctx) {}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_AngleRightIcon_1_Template, 1, 3, \"AngleRightIcon\", 22)(2, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_Template, 1, 2, null, 23);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r18.tieredMenu.submenuIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r18.tieredMenu.submenuIconTemplate);\n  }\n}\nconst _c1 = a1 => ({\n  \"p-menuitem-link\": true,\n  \"p-disabled\": a1\n});\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 13);\n    i0.ɵɵtemplate(1, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_span_1_Template, 1, 5, \"span\", 14)(2, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_span_2_Template, 2, 2, \"span\", 15)(3, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_template_3_Template, 1, 2, \"ng-template\", null, 16, i0.ɵɵtemplateRefExtractor)(5, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_span_5_Template, 2, 2, \"span\", 17)(6, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_Template, 3, 2, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const _r16 = i0.ɵɵreference(4);\n    const processedItem_r2 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"target\", ctx_r11.getItemProp(processedItem_r2, \"target\"))(\"ngClass\", i0.ɵɵpureFunction1(12, _c1, ctx_r11.getItemProp(processedItem_r2, \"disabled\")));\n    i0.ɵɵattribute(\"href\", ctx_r11.getItemProp(processedItem_r2, \"url\"), i0.ɵɵsanitizeUrl)(\"aria-hidden\", true)(\"data-automationid\", ctx_r11.getItemProp(processedItem_r2, \"automationId\"))(\"data-pc-section\", \"action\")(\"tabindex\", -1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.getItemProp(processedItem_r2, \"icon\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.getItemProp(processedItem_r2, \"escape\"))(\"ngIfElse\", _r16);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.getItemProp(processedItem_r2, \"badge\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.isItemGroup(processedItem_r2));\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 18);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r27 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r27.getItemProp(processedItem_r2, \"icon\"))(\"ngStyle\", ctx_r27.getItemProp(processedItem_r2, \"iconStyle\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\")(\"aria-hidden\", true)(\"tabindex\", -1);\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r28 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r28.getItemLabel(processedItem_r2), \" \");\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 20);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r29 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r29.getItemLabel(processedItem_r2), i0.ɵɵsanitizeHtml);\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r31 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r31.getItemProp(processedItem_r2, \"badgeStyleClass\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r31.getItemProp(processedItem_r2, \"badge\"));\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_AngleRightIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleRightIcon\", 24);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_ng_template_0_Template(rf, ctx) {}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_AngleRightIcon_1_Template, 1, 3, \"AngleRightIcon\", 22)(2, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_Template, 1, 2, null, 23);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r32.tieredMenu.submenuIconTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r32.tieredMenu.submenuIconTemplate);\n  }\n}\nconst _c2 = () => ({\n  exact: false\n});\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 25);\n    i0.ɵɵtemplate(1, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_span_1_Template, 1, 5, \"span\", 14)(2, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_span_2_Template, 2, 2, \"span\", 15)(3, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_template_3_Template, 1, 2, \"ng-template\", null, 16, i0.ɵɵtemplateRefExtractor)(5, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_span_5_Template, 2, 2, \"span\", 17)(6, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_Template, 3, 2, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const _r30 = i0.ɵɵreference(4);\n    const processedItem_r2 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", ctx_r12.getItemProp(processedItem_r2, \"routerLink\"))(\"queryParams\", ctx_r12.getItemProp(processedItem_r2, \"queryParams\"))(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", ctx_r12.getItemProp(processedItem_r2, \"routerLinkActiveOptions\") || i0.ɵɵpureFunction0(21, _c2))(\"target\", ctx_r12.getItemProp(processedItem_r2, \"target\"))(\"ngClass\", i0.ɵɵpureFunction1(22, _c1, ctx_r12.getItemProp(processedItem_r2, \"disabled\")))(\"fragment\", ctx_r12.getItemProp(processedItem_r2, \"fragment\"))(\"queryParamsHandling\", ctx_r12.getItemProp(processedItem_r2, \"queryParamsHandling\"))(\"preserveFragment\", ctx_r12.getItemProp(processedItem_r2, \"preserveFragment\"))(\"skipLocationChange\", ctx_r12.getItemProp(processedItem_r2, \"skipLocationChange\"))(\"replaceUrl\", ctx_r12.getItemProp(processedItem_r2, \"replaceUrl\"))(\"state\", ctx_r12.getItemProp(processedItem_r2, \"state\"));\n    i0.ɵɵattribute(\"data-automationid\", ctx_r12.getItemProp(processedItem_r2, \"automationId\"))(\"tabindex\", -1)(\"aria-hidden\", true)(\"data-pc-section\", \"action\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.getItemProp(processedItem_r2, \"icon\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.getItemProp(processedItem_r2, \"escape\"))(\"ngIfElse\", _r30);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.getItemProp(processedItem_r2, \"badge\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.isItemGroup(processedItem_r2));\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_Template, 7, 14, \"a\", 11)(2, TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_Template, 7, 24, \"a\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.getItemProp(processedItem_r2, \"routerLink\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.getItemProp(processedItem_r2, \"routerLink\"));\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_4_1_ng_template_0_Template(rf, ctx) {}\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_4_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TieredMenuSub_ng_template_2_li_1_ng_container_4_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nconst _c3 = (a0, a1) => ({\n  $implicit: a0,\n  hasSubmenu: a1\n});\nfunction TieredMenuSub_ng_template_2_li_1_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TieredMenuSub_ng_template_2_li_1_ng_container_4_1_Template, 1, 0, null, 26);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r9.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c3, processedItem_r2.item, ctx_r9.getItemProp(processedItem_r2, \"items\")));\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_p_tieredMenuSub_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-tieredMenuSub\", 27);\n    i0.ɵɵlistener(\"itemClick\", function TieredMenuSub_ng_template_2_li_1_p_tieredMenuSub_5_Template_p_tieredMenuSub_itemClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r45 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r45.itemClick.emit($event));\n    })(\"itemMouseEnter\", function TieredMenuSub_ng_template_2_li_1_p_tieredMenuSub_5_Template_p_tieredMenuSub_itemMouseEnter_0_listener($event) {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r47 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r47.onItemMouseEnter($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"items\", processedItem_r2.items)(\"itemTemplate\", ctx_r10.itemTemplate)(\"autoDisplay\", ctx_r10.autoDisplay)(\"menuId\", ctx_r10.menuId)(\"activeItemPath\", ctx_r10.activeItemPath)(\"focusedItemId\", ctx_r10.focusedItemId)(\"level\", ctx_r10.level + 1);\n  }\n}\nfunction TieredMenuSub_ng_template_2_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r51 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 6, 7)(2, \"div\", 8);\n    i0.ɵɵlistener(\"click\", function TieredMenuSub_ng_template_2_li_1_Template_div_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r51);\n      const processedItem_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r49 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r49.onItemClick($event, processedItem_r2));\n    })(\"mouseenter\", function TieredMenuSub_ng_template_2_li_1_Template_div_mouseenter_2_listener($event) {\n      i0.ɵɵrestoreView(_r51);\n      const processedItem_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r52 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r52.onItemMouseEnter({\n        $event: $event,\n        processedItem: processedItem_r2\n      }));\n    });\n    i0.ɵɵtemplate(3, TieredMenuSub_ng_template_2_li_1_ng_container_3_Template, 3, 2, \"ng-container\", 9)(4, TieredMenuSub_ng_template_2_li_1_ng_container_4_Template, 2, 5, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, TieredMenuSub_ng_template_2_li_1_p_tieredMenuSub_5_Template, 1, 7, \"p-tieredMenuSub\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r54 = i0.ɵɵnextContext();\n    const processedItem_r2 = ctx_r54.$implicit;\n    const index_r3 = ctx_r54.index;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r5.getItemProp(processedItem_r2, \"styleClass\"));\n    i0.ɵɵproperty(\"ngStyle\", ctx_r5.getItemProp(processedItem_r2, \"style\"))(\"ngClass\", ctx_r5.getItemClass(processedItem_r2))(\"tooltipOptions\", ctx_r5.getItemProp(processedItem_r2, \"tooltipOptions\"));\n    i0.ɵɵattribute(\"id\", ctx_r5.getItemId(processedItem_r2))(\"data-pc-section\", \"menuitem\")(\"data-p-highlight\", ctx_r5.isItemActive(processedItem_r2))(\"data-p-focused\", ctx_r5.isItemFocused(processedItem_r2))(\"data-p-disabled\", ctx_r5.isItemDisabled(processedItem_r2))(\"aria-label\", ctx_r5.getItemLabel(processedItem_r2))(\"aria-disabled\", ctx_r5.isItemDisabled(processedItem_r2) || undefined)(\"aria-haspopup\", ctx_r5.isItemGroup(processedItem_r2) && !ctx_r5.getItemProp(processedItem_r2, \"to\") ? \"menu\" : undefined)(\"aria-expanded\", ctx_r5.isItemGroup(processedItem_r2) ? ctx_r5.isItemActive(processedItem_r2) : undefined)(\"aria-level\", ctx_r5.level + 1)(\"aria-setsize\", ctx_r5.getAriaSetSize())(\"aria-posinset\", ctx_r5.getAriaPosInset(index_r3));\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"content\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.itemTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.itemTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.isItemVisible(processedItem_r2) && ctx_r5.isItemGroup(processedItem_r2));\n  }\n}\nfunction TieredMenuSub_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TieredMenuSub_ng_template_2_li_0_Template, 1, 5, \"li\", 3)(1, TieredMenuSub_ng_template_2_li_1_Template, 6, 21, \"li\", 4);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isItemVisible(processedItem_r2) && ctx_r1.getItemProp(processedItem_r2, \"separator\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isItemVisible(processedItem_r2) && !ctx_r1.getItemProp(processedItem_r2, \"separator\"));\n  }\n}\nconst _c4 = (a0, a1) => ({\n  \"p-submenu-list\": a0,\n  \"p-tieredmenu-root-list\": a1\n});\nconst _c5 = [\"rootmenu\"];\nconst _c6 = [\"container\"];\nconst _c7 = a1 => ({\n  \"p-tieredmenu p-component\": true,\n  \"p-tieredmenu-overlay\": a1\n});\nconst _c8 = (a0, a1) => ({\n  showTransitionParams: a0,\n  hideTransitionParams: a1\n});\nconst _c9 = a1 => ({\n  value: \"visible\",\n  params: a1\n});\nfunction TieredMenu_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1, 2);\n    i0.ɵɵlistener(\"click\", function TieredMenu_div_0_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onOverlayClick($event));\n    })(\"@overlayAnimation.start\", function TieredMenu_div_0_Template_div_animation_overlayAnimation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onOverlayAnimationStart($event));\n    })(\"@overlayAnimation.done\", function TieredMenu_div_0_Template_div_animation_overlayAnimation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onOverlayAnimationEnd($event));\n    });\n    i0.ɵɵelementStart(2, \"p-tieredMenuSub\", 3, 4);\n    i0.ɵɵlistener(\"itemClick\", function TieredMenu_div_0_Template_p_tieredMenuSub_itemClick_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onItemClick($event));\n    })(\"menuFocus\", function TieredMenu_div_0_Template_p_tieredMenuSub_menuFocus_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.onMenuFocus($event));\n    })(\"menuBlur\", function TieredMenu_div_0_Template_p_tieredMenuSub_menuBlur_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onMenuBlur($event));\n    })(\"menuKeydown\", function TieredMenu_div_0_Template_p_tieredMenuSub_menuKeydown_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onKeyDown($event));\n    })(\"itemMouseEnter\", function TieredMenu_div_0_Template_p_tieredMenuSub_itemMouseEnter_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.onItemMouseEnter($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.styleClass);\n    i0.ɵɵproperty(\"id\", ctx_r0.id)(\"ngClass\", i0.ɵɵpureFunction1(22, _c7, ctx_r0.popup))(\"ngStyle\", ctx_r0.style)(\"@overlayAnimation\", i0.ɵɵpureFunction1(27, _c9, i0.ɵɵpureFunction2(24, _c8, ctx_r0.showTransitionOptions, ctx_r0.hideTransitionOptions)))(\"@.disabled\", ctx_r0.popup !== true);\n    i0.ɵɵattribute(\"data-pc-section\", \"root\")(\"data-pc-name\", \"tieredmenu\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"root\", true)(\"items\", ctx_r0.processedItems)(\"itemTemplate\", ctx_r0.itemTemplate)(\"menuId\", ctx_r0.id)(\"tabindex\", !ctx_r0.disabled ? ctx_r0.tabindex : -1)(\"ariaLabel\", ctx_r0.ariaLabel)(\"ariaLabelledBy\", ctx_r0.ariaLabelledBy)(\"baseZIndex\", ctx_r0.baseZIndex)(\"autoZIndex\", ctx_r0.autoZIndex)(\"autoDisplay\", ctx_r0.autoDisplay)(\"popup\", ctx_r0.popup)(\"focusedItemId\", ctx_r0.focused ? ctx_r0.focusedItemId : undefined)(\"activeItemPath\", ctx_r0.activeItemPath());\n  }\n}\nclass TieredMenuSub {\n  el;\n  renderer;\n  cd;\n  tieredMenu;\n  items;\n  itemTemplate;\n  root = false;\n  autoDisplay;\n  autoZIndex = true;\n  baseZIndex = 0;\n  popup;\n  menuId;\n  ariaLabel;\n  ariaLabelledBy;\n  level = 0;\n  focusedItemId;\n  activeItemPath;\n  tabindex = 0;\n  itemClick = new EventEmitter();\n  itemMouseEnter = new EventEmitter();\n  menuFocus = new EventEmitter();\n  menuBlur = new EventEmitter();\n  menuKeydown = new EventEmitter();\n  sublistViewChild;\n  constructor(el, renderer, cd, tieredMenu) {\n    this.el = el;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.tieredMenu = tieredMenu;\n  }\n  positionSubmenu() {\n    let sublist = this.sublistViewChild && this.sublistViewChild.nativeElement;\n    if (sublist) {\n      const parentItem = sublist.parentElement.parentElement;\n      const containerOffset = DomHandler.getOffset(parentItem);\n      const viewport = DomHandler.getViewport();\n      const sublistWidth = sublist.offsetParent ? sublist.offsetWidth : DomHandler.getHiddenElementOuterWidth(sublist);\n      const itemOuterWidth = DomHandler.getOuterWidth(parentItem.children[0]);\n      if (parseInt(containerOffset.left, 10) + itemOuterWidth + sublistWidth > viewport.width - DomHandler.calculateScrollbarWidth()) {\n        DomHandler.addClass(sublist, 'p-submenu-list-flipped');\n      }\n    }\n  }\n  getItemProp(processedItem, name, params = null) {\n    return processedItem && processedItem.item ? ObjectUtils.getItemValue(processedItem.item[name], params) : undefined;\n  }\n  getItemId(processedItem) {\n    return processedItem.item?.id ?? `${this.menuId}_${processedItem.key}`;\n  }\n  getItemKey(processedItem) {\n    return this.getItemId(processedItem);\n  }\n  getItemClass(processedItem) {\n    return {\n      ...this.getItemProp(processedItem, 'class'),\n      'p-menuitem': true,\n      'p-highlight': this.isItemActive(processedItem),\n      'p-menuitem-active': this.isItemActive(processedItem),\n      'p-focus': this.isItemFocused(processedItem),\n      'p-disabled': this.isItemDisabled(processedItem)\n    };\n  }\n  getItemLabel(processedItem) {\n    return this.getItemProp(processedItem, 'label');\n  }\n  getSeparatorItemClass(processedItem) {\n    return {\n      ...this.getItemProp(processedItem, 'class'),\n      'p-menuitem-separator': true\n    };\n  }\n  getAriaSetSize() {\n    return this.items.filter(processedItem => this.isItemVisible(processedItem) && !this.getItemProp(processedItem, 'separator')).length;\n  }\n  getAriaPosInset(index) {\n    return index - this.items.slice(0, index).filter(processedItem => this.isItemVisible(processedItem) && this.getItemProp(processedItem, 'separator')).length + 1;\n  }\n  isItemVisible(processedItem) {\n    return this.getItemProp(processedItem, 'visible') !== false;\n  }\n  isItemActive(processedItem) {\n    if (this.activeItemPath) {\n      return this.activeItemPath.some(path => path.key === processedItem.key);\n    }\n  }\n  isItemDisabled(processedItem) {\n    return this.getItemProp(processedItem, 'disabled');\n  }\n  isItemFocused(processedItem) {\n    return this.focusedItemId === this.getItemId(processedItem);\n  }\n  isItemGroup(processedItem) {\n    return ObjectUtils.isNotEmpty(processedItem.items);\n  }\n  onItemMouseEnter(param) {\n    if (this.autoDisplay) {\n      const {\n        event,\n        processedItem\n      } = param;\n      this.itemMouseEnter.emit({\n        originalEvent: event,\n        processedItem\n      });\n    }\n  }\n  onItemClick(event, processedItem) {\n    this.getItemProp(processedItem, 'command', {\n      originalEvent: event,\n      item: processedItem.item\n    });\n    this.itemClick.emit({\n      originalEvent: event,\n      processedItem,\n      isFocus: true\n    });\n  }\n  static ɵfac = function TieredMenuSub_Factory(t) {\n    return new (t || TieredMenuSub)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(forwardRef(() => TieredMenu)));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: TieredMenuSub,\n    selectors: [[\"p-tieredMenuSub\"]],\n    viewQuery: function TieredMenuSub_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sublistViewChild = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      items: \"items\",\n      itemTemplate: \"itemTemplate\",\n      root: \"root\",\n      autoDisplay: \"autoDisplay\",\n      autoZIndex: \"autoZIndex\",\n      baseZIndex: \"baseZIndex\",\n      popup: \"popup\",\n      menuId: \"menuId\",\n      ariaLabel: \"ariaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\",\n      level: \"level\",\n      focusedItemId: \"focusedItemId\",\n      activeItemPath: \"activeItemPath\",\n      tabindex: \"tabindex\"\n    },\n    outputs: {\n      itemClick: \"itemClick\",\n      itemMouseEnter: \"itemMouseEnter\",\n      menuFocus: \"menuFocus\",\n      menuBlur: \"menuBlur\",\n      menuKeydown: \"menuKeydown\"\n    },\n    decls: 3,\n    vars: 12,\n    consts: [[\"role\", \"menu\", 3, \"ngClass\", \"id\", \"tabindex\", \"keydown\", \"focus\", \"blur\"], [\"sublist\", \"\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"role\", \"separator\", 3, \"style\", \"ngClass\", 4, \"ngIf\"], [\"role\", \"menuitem\", \"pTooltip\", \"\", 3, \"ngStyle\", \"ngClass\", \"class\", \"tooltipOptions\", 4, \"ngIf\"], [\"role\", \"separator\", 3, \"ngClass\"], [\"role\", \"menuitem\", \"pTooltip\", \"\", 3, \"ngStyle\", \"ngClass\", \"tooltipOptions\"], [\"listItem\", \"\"], [1, \"p-menuitem-content\", 3, \"click\", \"mouseenter\"], [4, \"ngIf\"], [3, \"items\", \"itemTemplate\", \"autoDisplay\", \"menuId\", \"activeItemPath\", \"focusedItemId\", \"level\", \"itemClick\", \"itemMouseEnter\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"target\", \"ngClass\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"target\", \"ngClass\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-menuitem-text\", 4, \"ngIf\", \"ngIfElse\"], [\"htmlLabel\", \"\"], [\"class\", \"p-menuitem-badge\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-menuitem-text\"], [1, \"p-menuitem-text\", 3, \"innerHTML\"], [1, \"p-menuitem-badge\", 3, \"ngClass\"], [3, \"styleClass\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [3, \"styleClass\"], [\"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"items\", \"itemTemplate\", \"autoDisplay\", \"menuId\", \"activeItemPath\", \"focusedItemId\", \"level\", \"itemClick\", \"itemMouseEnter\"]],\n    template: function TieredMenuSub_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"ul\", 0, 1);\n        i0.ɵɵlistener(\"keydown\", function TieredMenuSub_Template_ul_keydown_0_listener($event) {\n          return ctx.menuKeydown.emit($event);\n        })(\"focus\", function TieredMenuSub_Template_ul_focus_0_listener($event) {\n          return ctx.menuFocus.emit($event);\n        })(\"blur\", function TieredMenuSub_Template_ul_blur_0_listener($event) {\n          return ctx.menuBlur.emit($event);\n        });\n        i0.ɵɵtemplate(2, TieredMenuSub_ng_template_2_Template, 2, 2, \"ng-template\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(9, _c4, !ctx.root, ctx.root))(\"id\", ctx.menuId + \"_list\")(\"tabindex\", ctx.tabindex);\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel)(\"aria-labelledBy\", ctx.ariaLabelledBy)(\"aria-activedescendant\", ctx.focusedItemId)(\"aria-orientation\", \"vertical\")(\"data-pc-section\", \"menu\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.items);\n      }\n    },\n    dependencies: () => [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.RouterLink, i2.RouterLinkActive, i3.Ripple, i4.Tooltip, AngleRightIcon, TieredMenuSub],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TieredMenuSub, [{\n    type: Component,\n    args: [{\n      selector: 'p-tieredMenuSub',\n      template: `\n        <ul\n            #sublist\n            role=\"menu\"\n            [ngClass]=\"{ 'p-submenu-list': !root, 'p-tieredmenu-root-list': root }\"\n            [id]=\"menuId + '_list'\"\n            [tabindex]=\"tabindex\"\n            [attr.aria-label]=\"ariaLabel\"\n            [attr.aria-labelledBy]=\"ariaLabelledBy\"\n            [attr.aria-activedescendant]=\"focusedItemId\"\n            [attr.aria-orientation]=\"'vertical'\"\n            [attr.data-pc-section]=\"'menu'\"\n            (keydown)=\"menuKeydown.emit($event)\"\n            (focus)=\"menuFocus.emit($event)\"\n            (blur)=\"menuBlur.emit($event)\"\n        >\n            <ng-template ngFor let-processedItem [ngForOf]=\"items\" let-index=\"index\">\n                <li\n                    *ngIf=\"isItemVisible(processedItem) && getItemProp(processedItem, 'separator')\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [style]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getSeparatorItemClass(processedItem)\"\n                    role=\"separator\"\n                    [attr.data-pc-section]=\"'separator'\"\n                ></li>\n                <li\n                    #listItem\n                    *ngIf=\"isItemVisible(processedItem) && !getItemProp(processedItem, 'separator')\"\n                    role=\"menuitem\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [attr.data-pc-section]=\"'menuitem'\"\n                    [attr.data-p-highlight]=\"isItemActive(processedItem)\"\n                    [attr.data-p-focused]=\"isItemFocused(processedItem)\"\n                    [attr.data-p-disabled]=\"isItemDisabled(processedItem)\"\n                    [attr.aria-label]=\"getItemLabel(processedItem)\"\n                    [attr.aria-disabled]=\"isItemDisabled(processedItem) || undefined\"\n                    [attr.aria-haspopup]=\"isItemGroup(processedItem) && !getItemProp(processedItem, 'to') ? 'menu' : undefined\"\n                    [attr.aria-expanded]=\"isItemGroup(processedItem) ? isItemActive(processedItem) : undefined\"\n                    [attr.aria-level]=\"level + 1\"\n                    [attr.aria-setsize]=\"getAriaSetSize()\"\n                    [attr.aria-posinset]=\"getAriaPosInset(index)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getItemClass(processedItem)\"\n                    [class]=\"getItemProp(processedItem, 'styleClass')\"\n                    pTooltip\n                    [tooltipOptions]=\"getItemProp(processedItem, 'tooltipOptions')\"\n                >\n                    <div [attr.data-pc-section]=\"'content'\" class=\"p-menuitem-content\" (click)=\"onItemClick($event, processedItem)\" (mouseenter)=\"onItemMouseEnter({$event, processedItem})\">\n                        <ng-container *ngIf=\"!itemTemplate\">\n                            <a\n                                *ngIf=\"!getItemProp(processedItem, 'routerLink')\"\n                                [attr.href]=\"getItemProp(processedItem, 'url')\"\n                                [attr.aria-hidden]=\"true\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [attr.tabindex]=\"-1\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-menuitem-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.aria-hidden]=\"true\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menuitem-text\" [attr.data-pc-section]=\"'label'\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span>\n                                </ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <AngleRightIcon *ngIf=\"!tieredMenu.submenuIconTemplate\" [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                    <ng-template *ngTemplateOutlet=\"tieredMenu.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                            <a\n                                *ngIf=\"getItemProp(processedItem, 'routerLink')\"\n                                [routerLink]=\"getItemProp(processedItem, 'routerLink')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.tabindex]=\"-1\"\n                                [attr.aria-hidden]=\"true\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [queryParams]=\"getItemProp(processedItem, 'queryParams')\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"getItemProp(processedItem, 'routerLinkActiveOptions') || { exact: false }\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [fragment]=\"getItemProp(processedItem, 'fragment')\"\n                                [queryParamsHandling]=\"getItemProp(processedItem, 'queryParamsHandling')\"\n                                [preserveFragment]=\"getItemProp(processedItem, 'preserveFragment')\"\n                                [skipLocationChange]=\"getItemProp(processedItem, 'skipLocationChange')\"\n                                [replaceUrl]=\"getItemProp(processedItem, 'replaceUrl')\"\n                                [state]=\"getItemProp(processedItem, 'state')\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-menuitem-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.aria-hidden]=\"true\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menuitem-text\" [attr.data-pc-section]=\"'label'\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span>\n                                </ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <AngleRightIcon *ngIf=\"!tieredMenu.submenuIconTemplate\" [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                    <ng-template *ngTemplateOutlet=\"tieredMenu.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                        </ng-container>\n                        <ng-container *ngIf=\"itemTemplate\">\n                            <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: processedItem.item, hasSubmenu: getItemProp(processedItem, 'items') }\"></ng-template>\n                        </ng-container>\n                    </div>\n\n                    <p-tieredMenuSub\n                        *ngIf=\"isItemVisible(processedItem) && isItemGroup(processedItem)\"\n                        [items]=\"processedItem.items\"\n                        [itemTemplate]=\"itemTemplate\"\n                        [autoDisplay]=\"autoDisplay\"\n                        [menuId]=\"menuId\"\n                        [activeItemPath]=\"activeItemPath\"\n                        [focusedItemId]=\"focusedItemId\"\n                        [level]=\"level + 1\"\n                        (itemClick)=\"itemClick.emit($event)\"\n                        (itemMouseEnter)=\"onItemMouseEnter($event)\"\n                    ></p-tieredMenuSub>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      }\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: TieredMenu,\n    decorators: [{\n      type: Inject,\n      args: [forwardRef(() => TieredMenu)]\n    }]\n  }], {\n    items: [{\n      type: Input\n    }],\n    itemTemplate: [{\n      type: Input\n    }],\n    root: [{\n      type: Input\n    }],\n    autoDisplay: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input\n    }],\n    baseZIndex: [{\n      type: Input\n    }],\n    popup: [{\n      type: Input\n    }],\n    menuId: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    level: [{\n      type: Input\n    }],\n    focusedItemId: [{\n      type: Input\n    }],\n    activeItemPath: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input\n    }],\n    itemClick: [{\n      type: Output\n    }],\n    itemMouseEnter: [{\n      type: Output\n    }],\n    menuFocus: [{\n      type: Output\n    }],\n    menuBlur: [{\n      type: Output\n    }],\n    menuKeydown: [{\n      type: Output\n    }],\n    sublistViewChild: [{\n      type: ViewChild,\n      args: ['sublist', {\n        static: true\n      }]\n    }]\n  });\n})();\n/**\n * TieredMenu displays submenus in nested overlays.\n * @group Components\n */\nclass TieredMenu {\n  document;\n  platformId;\n  el;\n  renderer;\n  cd;\n  config;\n  overlayService;\n  /**\n   * An array of menuitems.\n   * @group Props\n   */\n  set model(value) {\n    this._model = value;\n    this._processedItems = this.createProcessedItems(this._model || []);\n  }\n  get model() {\n    return this._model;\n  }\n  /**\n   * Defines if menu would displayed as a popup.\n   * @group Props\n   */\n  popup;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Target element to attach the overlay, valid values are \"body\" or a local ng-template variable of another element.\n   * @group Props\n   */\n  appendTo;\n  /**\n   * Whether to automatically manage layering.\n   * @group Props\n   */\n  autoZIndex = true;\n  /**\n   * Base zIndex value to use in layering.\n   * @group Props\n   */\n  baseZIndex = 0;\n  /**\n   * Whether to show a root submenu on mouse over.\n   * @defaultValue true\n   * @group Props\n   */\n  autoDisplay = true;\n  /**\n   * Transition options of the show animation.\n   * @group Props\n   */\n  showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n  /**\n   * Transition options of the hide animation.\n   * @group Props\n   */\n  hideTransitionOptions = '.1s linear';\n  /**\n   * Current id state as a string.\n   * @group Props\n   */\n  id;\n  /**\n   * Defines a string value that labels an interactive element.\n   * @group Props\n   */\n  ariaLabel;\n  /**\n   * Identifier of the underlying input element.\n   * @group Props\n   */\n  ariaLabelledBy;\n  /**\n   * When present, it specifies that the component should be disabled.\n   * @group Props\n   */\n  disabled = false;\n  /**\n   * Index of the element in tabbing order.\n   * @group Props\n   */\n  tabindex = 0;\n  /**\n   * Callback to invoke when overlay menu is shown.\n   * @group Emits\n   */\n  onShow = new EventEmitter();\n  /**\n   * Callback to invoke when overlay menu is hidden.\n   * @group Emits\n   */\n  onHide = new EventEmitter();\n  templates;\n  rootmenu;\n  containerViewChild;\n  submenuIconTemplate;\n  itemTemplate;\n  container;\n  outsideClickListener;\n  resizeListener;\n  scrollHandler;\n  target;\n  relatedTarget;\n  visible;\n  relativeAlign;\n  window;\n  dirty = false;\n  focused = false;\n  activeItemPath = signal([]);\n  number = signal(0);\n  focusedItemInfo = signal({\n    index: -1,\n    level: 0,\n    parentKey: '',\n    item: null\n  });\n  searchValue = '';\n  searchTimeout;\n  _processedItems;\n  _model;\n  get visibleItems() {\n    const processedItem = this.activeItemPath().find(p => p.key === this.focusedItemInfo().parentKey);\n    return processedItem ? processedItem.items : this.processedItems;\n  }\n  get processedItems() {\n    if (!this._processedItems || !this._processedItems.length) {\n      this._processedItems = this.createProcessedItems(this.model || []);\n    }\n    return this._processedItems;\n  }\n  get focusedItemId() {\n    const focusedItemInfo = this.focusedItemInfo();\n    return focusedItemInfo.item?.id ? focusedItemInfo.item.id : focusedItemInfo.index !== -1 ? `${this.id}${ObjectUtils.isNotEmpty(focusedItemInfo.parentKey) ? '_' + focusedItemInfo.parentKey : ''}_${focusedItemInfo.index}` : null;\n  }\n  constructor(document, platformId, el, renderer, cd, config, overlayService) {\n    this.document = document;\n    this.platformId = platformId;\n    this.el = el;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.config = config;\n    this.overlayService = overlayService;\n    this.window = this.document.defaultView;\n    effect(() => {\n      const path = this.activeItemPath();\n      if (ObjectUtils.isNotEmpty(path)) {\n        this.bindOutsideClickListener();\n        this.bindResizeListener();\n      } else {\n        this.unbindOutsideClickListener();\n        this.unbindResizeListener();\n      }\n    });\n  }\n  ngOnInit() {\n    this.id = this.id || UniqueComponentId();\n  }\n  ngAfterContentInit() {\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'submenuicon':\n          this.submenuIconTemplate = item.template;\n          break;\n        case 'item':\n          this.itemTemplate = item.template;\n          break;\n        default:\n          this.itemTemplate = item.template;\n          break;\n      }\n    });\n  }\n  createProcessedItems(items, level = 0, parent = {}, parentKey = '') {\n    const processedItems = [];\n    items && items.forEach((item, index) => {\n      const key = (parentKey !== '' ? parentKey + '_' : '') + index;\n      const newItem = {\n        item,\n        index,\n        level,\n        key,\n        parent,\n        parentKey\n      };\n      newItem['items'] = this.createProcessedItems(item.items, level + 1, newItem, key);\n      processedItems.push(newItem);\n    });\n    return processedItems;\n  }\n  getItemProp(item, name) {\n    return item ? ObjectUtils.getItemValue(item[name]) : undefined;\n  }\n  getProccessedItemLabel(processedItem) {\n    return processedItem ? this.getItemLabel(processedItem.item) : undefined;\n  }\n  getItemLabel(item) {\n    return this.getItemProp(item, 'label');\n  }\n  isProcessedItemGroup(processedItem) {\n    return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n  }\n  isSelected(processedItem) {\n    return this.activeItemPath().some(p => p.key === processedItem.key);\n  }\n  isValidSelectedItem(processedItem) {\n    return this.isValidItem(processedItem) && this.isSelected(processedItem);\n  }\n  isValidItem(processedItem) {\n    return !!processedItem && !this.isItemDisabled(processedItem.item) && !this.isItemSeparator(processedItem.item);\n  }\n  isItemDisabled(item) {\n    return this.getItemProp(item, 'disabled');\n  }\n  isItemSeparator(item) {\n    return this.getItemProp(item, 'separator');\n  }\n  isItemMatched(processedItem) {\n    return this.isValidItem(processedItem) && this.getProccessedItemLabel(processedItem).toLocaleLowerCase().startsWith(this.searchValue.toLocaleLowerCase());\n  }\n  isProccessedItemGroup(processedItem) {\n    return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n  }\n  onOverlayClick(event) {\n    if (this.popup) {\n      this.overlayService.add({\n        originalEvent: event,\n        target: this.el.nativeElement\n      });\n    }\n  }\n  onItemClick(event) {\n    const {\n      originalEvent,\n      processedItem\n    } = event;\n    const grouped = this.isProcessedItemGroup(processedItem);\n    const root = ObjectUtils.isEmpty(processedItem.parent);\n    const selected = this.isSelected(processedItem);\n    if (selected) {\n      const {\n        index,\n        key,\n        level,\n        parentKey,\n        item\n      } = processedItem;\n      this.activeItemPath.set(this.activeItemPath().filter(p => key !== p.key && key.startsWith(p.key)));\n      this.focusedItemInfo.set({\n        index,\n        level,\n        parentKey,\n        item\n      });\n      this.dirty = true;\n      DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n    } else {\n      if (grouped) {\n        this.onItemChange(event);\n      } else {\n        const rootProcessedItem = root ? processedItem : this.activeItemPath().find(p => p.parentKey === '');\n        this.hide(originalEvent);\n        this.changeFocusedItemIndex(originalEvent, rootProcessedItem ? rootProcessedItem.index : -1);\n        DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n      }\n    }\n  }\n  onItemMouseEnter(event) {\n    if (!DomHandler.isTouchDevice()) {\n      if (this.dirty) {\n        this.onItemChange(event);\n      }\n    } else {\n      this.onItemChange({\n        event,\n        processedItem: event.processedItem,\n        focus: this.autoDisplay\n      });\n    }\n  }\n  onKeyDown(event) {\n    const metaKey = event.metaKey || event.ctrlKey;\n    switch (event.code) {\n      case 'ArrowDown':\n        this.onArrowDownKey(event);\n        break;\n      case 'ArrowUp':\n        this.onArrowUpKey(event);\n        break;\n      case 'ArrowLeft':\n        this.onArrowLeftKey(event);\n        break;\n      case 'ArrowRight':\n        this.onArrowRightKey(event);\n        break;\n      case 'Home':\n        this.onHomeKey(event);\n        break;\n      case 'End':\n        this.onEndKey(event);\n        break;\n      case 'Space':\n        this.onSpaceKey(event);\n        break;\n      case 'Enter':\n        this.onEnterKey(event);\n        break;\n      case 'Escape':\n        this.onEscapeKey(event);\n        break;\n      case 'Tab':\n        this.onTabKey(event);\n        break;\n      case 'PageDown':\n      case 'PageUp':\n      case 'Backspace':\n      case 'ShiftLeft':\n      case 'ShiftRight':\n        //NOOP\n        break;\n      default:\n        if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n          this.searchItems(event, event.key);\n        }\n        break;\n    }\n  }\n  onArrowDownKey(event) {\n    const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n    this.changeFocusedItemIndex(event, itemIndex);\n    event.preventDefault();\n  }\n  onArrowRightKey(event) {\n    const processedItem = this.visibleItems[this.focusedItemInfo().index];\n    const grouped = this.isProccessedItemGroup(processedItem);\n    const item = processedItem.item;\n    if (grouped) {\n      this.onItemChange({\n        originalEvent: event,\n        processedItem\n      });\n      this.focusedItemInfo.set({\n        index: -1,\n        parentKey: processedItem.key,\n        item\n      });\n      this.searchValue = '';\n      this.onArrowDownKey(event);\n    }\n    event.preventDefault();\n  }\n  onArrowUpKey(event) {\n    if (event.altKey) {\n      if (this.focusedItemInfo().index !== -1) {\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const grouped = this.isProccessedItemGroup(processedItem);\n        !grouped && this.onItemChange({\n          originalEvent: event,\n          processedItem\n        });\n      }\n      this.popup && this.hide(event, true);\n      event.preventDefault();\n    } else {\n      const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n      this.changeFocusedItemIndex(event, itemIndex);\n      event.preventDefault();\n    }\n  }\n  onArrowLeftKey(event) {\n    const processedItem = this.visibleItems[this.focusedItemInfo().index];\n    const parentItem = this.activeItemPath().find(p => p.key === processedItem.parentKey);\n    const root = ObjectUtils.isEmpty(processedItem.parent);\n    if (!root) {\n      this.focusedItemInfo.set({\n        index: -1,\n        parentKey: parentItem ? parentItem.parentKey : '',\n        item: processedItem.item\n      });\n      this.searchValue = '';\n      this.onArrowDownKey(event);\n    }\n    const activeItemPath = this.activeItemPath().filter(p => p.parentKey !== this.focusedItemInfo().parentKey);\n    this.activeItemPath.set(activeItemPath);\n    event.preventDefault();\n  }\n  onHomeKey(event) {\n    this.changeFocusedItemIndex(event, this.findFirstItemIndex());\n    event.preventDefault();\n  }\n  onEndKey(event) {\n    this.changeFocusedItemIndex(event, this.findLastItemIndex());\n    event.preventDefault();\n  }\n  onSpaceKey(event) {\n    this.onEnterKey(event);\n  }\n  onEscapeKey(event) {\n    this.hide(event, true);\n    this.focusedItemInfo().index = this.findFirstFocusedItemIndex();\n    event.preventDefault();\n  }\n  onTabKey(event) {\n    if (this.focusedItemInfo().index !== -1) {\n      const processedItem = this.visibleItems[this.focusedItemInfo().index];\n      const grouped = this.isProccessedItemGroup(processedItem);\n      !grouped && this.onItemChange({\n        originalEvent: event,\n        processedItem\n      });\n    }\n    this.hide();\n  }\n  onEnterKey(event) {\n    if (this.focusedItemInfo().index !== -1) {\n      const element = DomHandler.findSingle(this.rootmenu.el.nativeElement, `li[id=\"${`${this.focusedItemId}`}\"]`);\n      const anchorElement = element && DomHandler.findSingle(element, 'a[data-pc-section=\"action\"]');\n      anchorElement ? anchorElement.click() : element && element.click();\n      const processedItem = this.visibleItems[this.focusedItemInfo().index];\n      if (!this.popup) {\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const grouped = this.isProccessedItemGroup(processedItem);\n        !grouped && (this.focusedItemInfo().index = this.findFirstFocusedItemIndex());\n      }\n    }\n    event.preventDefault();\n  }\n  onItemChange(event) {\n    const {\n      processedItem,\n      isFocus\n    } = event;\n    if (ObjectUtils.isEmpty(processedItem)) return;\n    const {\n      index,\n      key,\n      level,\n      parentKey,\n      items,\n      item\n    } = processedItem;\n    const grouped = ObjectUtils.isNotEmpty(items);\n    const activeItemPath = this.activeItemPath().filter(p => p.parentKey !== parentKey && p.parentKey !== key);\n    grouped && activeItemPath.push(processedItem);\n    this.focusedItemInfo.set({\n      index,\n      level,\n      parentKey,\n      item\n    });\n    this.activeItemPath.set(activeItemPath);\n    grouped && (this.dirty = true);\n    isFocus && DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n  }\n  onMenuFocus(event) {\n    this.focused = true;\n    const focusedItemInfo = this.focusedItemInfo().index !== -1 ? this.focusedItemInfo() : {\n      index: this.findFirstFocusedItemIndex(),\n      level: 0,\n      parentKey: '',\n      item: this.visibleItems[this.findFirstFocusedItemIndex()]?.item\n    };\n    this.focusedItemInfo.set(focusedItemInfo);\n  }\n  onMenuBlur(event) {\n    this.focused = false;\n    this.focusedItemInfo.set({\n      index: -1,\n      level: 0,\n      parentKey: '',\n      item: null\n    });\n    this.searchValue = '';\n    this.dirty = false;\n  }\n  onOverlayAnimationStart(event) {\n    switch (event.toState) {\n      case 'visible':\n        if (this.popup) {\n          this.container = event.element;\n          this.moveOnTop();\n          this.onShow.emit({});\n          this.appendOverlay();\n          this.alignOverlay();\n          this.bindOutsideClickListener();\n          this.bindResizeListener();\n          this.bindScrollListener();\n          DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n          this.scrollInView();\n        }\n        break;\n      case 'void':\n        this.onOverlayHide();\n        this.onHide.emit({});\n        break;\n    }\n  }\n  alignOverlay() {\n    if (this.relativeAlign) DomHandler.relativePosition(this.container, this.target);else DomHandler.absolutePosition(this.container, this.target);\n  }\n  onOverlayAnimationEnd(event) {\n    switch (event.toState) {\n      case 'void':\n        ZIndexUtils.clear(event.element);\n        break;\n    }\n  }\n  appendOverlay() {\n    if (this.appendTo) {\n      if (this.appendTo === 'body') this.renderer.appendChild(this.document.body, this.container);else DomHandler.appendChild(this.container, this.appendTo);\n    }\n  }\n  restoreOverlayAppend() {\n    if (this.container && this.appendTo) {\n      this.renderer.appendChild(this.el.nativeElement, this.container);\n    }\n  }\n  moveOnTop() {\n    if (this.autoZIndex) {\n      ZIndexUtils.set('menu', this.container, this.baseZIndex + this.config.zIndex.menu);\n    }\n  }\n  /**\n   * Hides the popup menu.\n   * @group Method\n   */\n  hide(event, isFocus) {\n    if (this.popup) {\n      this.onHide.emit({});\n      this.visible = false;\n    }\n    this.activeItemPath.set([]);\n    this.focusedItemInfo.set({\n      index: -1,\n      level: 0,\n      parentKey: ''\n    });\n    isFocus && DomHandler.focus(this.relatedTarget || this.target || this.rootmenu.sublistViewChild.nativeElement);\n    this.dirty = false;\n  }\n  /**\n   * Toggles the visibility of the popup menu.\n   * @param {Event} event - Browser event.\n   * @group Method\n   */\n  toggle(event) {\n    this.visible ? this.hide(event, true) : this.show(event);\n  }\n  /**\n   * Displays the popup menu.\n   * @param {Event} even - Browser event.\n   * @group Method\n   */\n  show(event, isFocus) {\n    if (this.popup) {\n      this.visible = true;\n      this.target = this.target || event.currentTarget;\n      this.relatedTarget = event.relatedTarget || null;\n      this.relativeAlign = event?.relativeAlign || null;\n    }\n    this.focusedItemInfo.set({\n      index: this.findFirstFocusedItemIndex(),\n      level: 0,\n      parentKey: ''\n    });\n    isFocus && DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n    this.cd.markForCheck();\n  }\n  searchItems(event, char) {\n    this.searchValue = (this.searchValue || '') + char;\n    let itemIndex = -1;\n    let matched = false;\n    if (this.focusedItemInfo().index !== -1) {\n      itemIndex = this.visibleItems.slice(this.focusedItemInfo().index).findIndex(processedItem => this.isItemMatched(processedItem));\n      itemIndex = itemIndex === -1 ? this.visibleItems.slice(0, this.focusedItemInfo().index).findIndex(processedItem => this.isItemMatched(processedItem)) : itemIndex + this.focusedItemInfo().index;\n    } else {\n      itemIndex = this.visibleItems.findIndex(processedItem => this.isItemMatched(processedItem));\n    }\n    if (itemIndex !== -1) {\n      matched = true;\n    }\n    if (itemIndex === -1 && this.focusedItemInfo().index === -1) {\n      itemIndex = this.findFirstFocusedItemIndex();\n    }\n    if (itemIndex !== -1) {\n      this.changeFocusedItemIndex(event, itemIndex);\n    }\n    if (this.searchTimeout) {\n      clearTimeout(this.searchTimeout);\n    }\n    this.searchTimeout = setTimeout(() => {\n      this.searchValue = '';\n      this.searchTimeout = null;\n    }, 500);\n    return matched;\n  }\n  findLastFocusedItemIndex() {\n    const selectedIndex = this.findSelectedItemIndex();\n    return selectedIndex < 0 ? this.findLastItemIndex() : selectedIndex;\n  }\n  findLastItemIndex() {\n    return ObjectUtils.findLastIndex(this.visibleItems, processedItem => this.isValidItem(processedItem));\n  }\n  findPrevItemIndex(index) {\n    const matchedItemIndex = index > 0 ? ObjectUtils.findLastIndex(this.visibleItems.slice(0, index), processedItem => this.isValidItem(processedItem)) : -1;\n    return matchedItemIndex > -1 ? matchedItemIndex : index;\n  }\n  findNextItemIndex(index) {\n    const matchedItemIndex = index < this.visibleItems.length - 1 ? this.visibleItems.slice(index + 1).findIndex(processedItem => this.isValidItem(processedItem)) : -1;\n    return matchedItemIndex > -1 ? matchedItemIndex + index + 1 : index;\n  }\n  findFirstFocusedItemIndex() {\n    const selectedIndex = this.findSelectedItemIndex();\n    return selectedIndex < 0 ? this.findFirstItemIndex() : selectedIndex;\n  }\n  findFirstItemIndex() {\n    return this.visibleItems.findIndex(processedItem => this.isValidItem(processedItem));\n  }\n  findSelectedItemIndex() {\n    return this.visibleItems.findIndex(processedItem => this.isValidSelectedItem(processedItem));\n  }\n  changeFocusedItemIndex(event, index) {\n    if (this.focusedItemInfo().index !== index) {\n      const focusedItemInfo = this.focusedItemInfo();\n      this.focusedItemInfo.set({\n        ...focusedItemInfo,\n        item: this.visibleItems[index].item,\n        index\n      });\n      this.scrollInView();\n    }\n  }\n  scrollInView(index = -1) {\n    const id = index !== -1 ? `${this.id}_${index}` : this.focusedItemId;\n    const element = DomHandler.findSingle(this.rootmenu.el.nativeElement, `li[id=\"${id}\"]`);\n    if (element) {\n      element.scrollIntoView && element.scrollIntoView({\n        block: 'nearest',\n        inline: 'nearest'\n      });\n    }\n  }\n  bindScrollListener() {\n    if (!this.scrollHandler) {\n      this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, event => {\n        if (this.visible) {\n          this.hide(event, true);\n        }\n      });\n    }\n    this.scrollHandler.bindScrollListener();\n  }\n  unbindScrollListener() {\n    if (this.scrollHandler) {\n      this.scrollHandler.unbindScrollListener();\n      this.scrollHandler = null;\n    }\n  }\n  bindResizeListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.resizeListener) {\n        this.resizeListener = this.renderer.listen(this.document.defaultView, 'resize', event => {\n          if (!DomHandler.isTouchDevice()) {\n            this.hide(event, true);\n          }\n        });\n      }\n    }\n  }\n  bindOutsideClickListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.outsideClickListener) {\n        this.outsideClickListener = this.renderer.listen(this.document, 'click', event => {\n          const isOutsideContainer = this.containerViewChild && !this.containerViewChild.nativeElement.contains(event.target);\n          const isOutsideTarget = this.popup ? !(this.target && (this.target === event.target || this.target.contains(event.target))) : true;\n          if (isOutsideContainer && isOutsideTarget) {\n            this.hide();\n          }\n        });\n      }\n    }\n  }\n  unbindOutsideClickListener() {\n    if (this.outsideClickListener) {\n      document.removeEventListener('click', this.outsideClickListener);\n      this.outsideClickListener = null;\n    }\n  }\n  unbindResizeListener() {\n    if (this.resizeListener) {\n      this.resizeListener();\n      this.resizeListener = null;\n    }\n  }\n  onOverlayHide() {\n    this.unbindOutsideClickListener();\n    this.unbindResizeListener();\n    this.unbindScrollListener();\n    if (!this.cd.destroyed) {\n      this.target = null;\n    }\n  }\n  ngOnDestroy() {\n    if (this.popup) {\n      if (this.scrollHandler) {\n        this.scrollHandler.destroy();\n        this.scrollHandler = null;\n      }\n      if (this.container && this.autoZIndex) {\n        ZIndexUtils.clear(this.container);\n      }\n      this.restoreOverlayAppend();\n      this.onOverlayHide();\n    }\n  }\n  static ɵfac = function TieredMenu_Factory(t) {\n    return new (t || TieredMenu)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.PrimeNGConfig), i0.ɵɵdirectiveInject(i5.OverlayService));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: TieredMenu,\n    selectors: [[\"p-tieredMenu\"]],\n    contentQueries: function TieredMenu_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function TieredMenu_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c5, 5);\n        i0.ɵɵviewQuery(_c6, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.rootmenu = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      model: \"model\",\n      popup: \"popup\",\n      style: \"style\",\n      styleClass: \"styleClass\",\n      appendTo: \"appendTo\",\n      autoZIndex: \"autoZIndex\",\n      baseZIndex: \"baseZIndex\",\n      autoDisplay: \"autoDisplay\",\n      showTransitionOptions: \"showTransitionOptions\",\n      hideTransitionOptions: \"hideTransitionOptions\",\n      id: \"id\",\n      ariaLabel: \"ariaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\",\n      disabled: \"disabled\",\n      tabindex: \"tabindex\"\n    },\n    outputs: {\n      onShow: \"onShow\",\n      onHide: \"onHide\"\n    },\n    decls: 1,\n    vars: 1,\n    consts: [[3, \"id\", \"ngClass\", \"class\", \"ngStyle\", \"click\", 4, \"ngIf\"], [3, \"id\", \"ngClass\", \"ngStyle\", \"click\"], [\"container\", \"\"], [3, \"root\", \"items\", \"itemTemplate\", \"menuId\", \"tabindex\", \"ariaLabel\", \"ariaLabelledBy\", \"baseZIndex\", \"autoZIndex\", \"autoDisplay\", \"popup\", \"focusedItemId\", \"activeItemPath\", \"itemClick\", \"menuFocus\", \"menuBlur\", \"menuKeydown\", \"itemMouseEnter\"], [\"rootmenu\", \"\"]],\n    template: function TieredMenu_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, TieredMenu_div_0_Template, 4, 29, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", !ctx.popup || ctx.visible);\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgIf, i1.NgStyle, TieredMenuSub],\n    styles: [\"@layer primeng{.p-tieredmenu-overlay{position:absolute;top:0;left:0}.p-tieredmenu ul{margin:0;padding:0;list-style:none}.p-tieredmenu .p-submenu-list{position:absolute;min-width:100%;z-index:1;display:none}.p-tieredmenu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-tieredmenu .p-menuitem-text{line-height:1}.p-tieredmenu .p-menuitem{position:relative}.p-tieredmenu .p-menuitem-link .p-submenu-icon:not(svg){margin-left:auto}.p-tieredmenu .p-menuitem-link .p-icon-wrapper{margin-left:auto}.p-tieredmenu .p-menuitem-active>p-tieredmenusub>.p-submenu-list{display:block;left:100%;top:0}.p-tieredmenu .p-menuitem-active>p-tieredmenusub>.p-submenu-list.p-submenu-list-flipped{left:-100%}}\\n\"],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('overlayAnimation', [transition(':enter', [style({\n        opacity: 0,\n        transform: 'scaleY(0.8)'\n      }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({\n        opacity: 0\n      }))])])]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TieredMenu, [{\n    type: Component,\n    args: [{\n      selector: 'p-tieredMenu',\n      template: `\n        <div\n            #container\n            [attr.data-pc-section]=\"'root'\"\n            [attr.data-pc-name]=\"'tieredmenu'\"\n            [id]=\"id\"\n            [ngClass]=\"{ 'p-tieredmenu p-component': true, 'p-tieredmenu-overlay': popup }\"\n            [class]=\"styleClass\"\n            [ngStyle]=\"style\"\n            (click)=\"onOverlayClick($event)\"\n            [@overlayAnimation]=\"{ value: 'visible', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions } }\"\n            [@.disabled]=\"popup !== true\"\n            (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\"\n            (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\"\n            *ngIf=\"!popup || visible\"\n        >\n            <p-tieredMenuSub\n                #rootmenu\n                [root]=\"true\"\n                [items]=\"processedItems\"\n                [itemTemplate]=\"itemTemplate\"\n                [menuId]=\"id\"\n                [tabindex]=\"!disabled ? tabindex : -1\"\n                [ariaLabel]=\"ariaLabel\"\n                [ariaLabelledBy]=\"ariaLabelledBy\"\n                [baseZIndex]=\"baseZIndex\"\n                [autoZIndex]=\"autoZIndex\"\n                [autoDisplay]=\"autoDisplay\"\n                [popup]=\"popup\"\n                [focusedItemId]=\"focused ? focusedItemId : undefined\"\n                [activeItemPath]=\"activeItemPath()\"\n                (itemClick)=\"onItemClick($event)\"\n                (menuFocus)=\"onMenuFocus($event)\"\n                (menuBlur)=\"onMenuBlur($event)\"\n                (menuKeydown)=\"onKeyDown($event)\"\n                (itemMouseEnter)=\"onItemMouseEnter($event)\"\n            ></p-tieredMenuSub>\n        </div>\n    `,\n      animations: [trigger('overlayAnimation', [transition(':enter', [style({\n        opacity: 0,\n        transform: 'scaleY(0.8)'\n      }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({\n        opacity: 0\n      }))])])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\"@layer primeng{.p-tieredmenu-overlay{position:absolute;top:0;left:0}.p-tieredmenu ul{margin:0;padding:0;list-style:none}.p-tieredmenu .p-submenu-list{position:absolute;min-width:100%;z-index:1;display:none}.p-tieredmenu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-tieredmenu .p-menuitem-text{line-height:1}.p-tieredmenu .p-menuitem{position:relative}.p-tieredmenu .p-menuitem-link .p-submenu-icon:not(svg){margin-left:auto}.p-tieredmenu .p-menuitem-link .p-icon-wrapper{margin-left:auto}.p-tieredmenu .p-menuitem-active>p-tieredmenusub>.p-submenu-list{display:block;left:100%;top:0}.p-tieredmenu .p-menuitem-active>p-tieredmenusub>.p-submenu-list.p-submenu-list-flipped{left:-100%}}\\n\"]\n    }]\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i5.PrimeNGConfig\n  }, {\n    type: i5.OverlayService\n  }], {\n    model: [{\n      type: Input\n    }],\n    popup: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input\n    }],\n    baseZIndex: [{\n      type: Input\n    }],\n    autoDisplay: [{\n      type: Input\n    }],\n    showTransitionOptions: [{\n      type: Input\n    }],\n    hideTransitionOptions: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input\n    }],\n    onShow: [{\n      type: Output\n    }],\n    onHide: [{\n      type: Output\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    rootmenu: [{\n      type: ViewChild,\n      args: ['rootmenu']\n    }],\n    containerViewChild: [{\n      type: ViewChild,\n      args: ['container']\n    }]\n  });\n})();\nclass TieredMenuModule {\n  static ɵfac = function TieredMenuModule_Factory(t) {\n    return new (t || TieredMenuModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: TieredMenuModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule, RippleModule, TooltipModule, AngleRightIcon, SharedModule, RouterModule, TooltipModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TieredMenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, RouterModule, RippleModule, TooltipModule, AngleRightIcon, SharedModule],\n      exports: [TieredMenu, RouterModule, TooltipModule, SharedModule],\n      declarations: [TieredMenu, TieredMenuSub]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TieredMenu, TieredMenuModule, TieredMenuSub };", "map": {"version": 3, "names": ["style", "animate", "transition", "trigger", "i1", "isPlatformBrowser", "DOCUMENT", "CommonModule", "i0", "EventEmitter", "forwardRef", "Component", "ViewEncapsulation", "Inject", "Input", "Output", "ViewChild", "signal", "effect", "PLATFORM_ID", "ChangeDetectionStrategy", "ContentChildren", "NgModule", "i2", "RouterModule", "i5", "PrimeTemplate", "SharedModule", "<PERSON><PERSON><PERSON><PERSON>", "ConnectedOverlayScrollHandler", "AngleRightIcon", "i3", "RippleModule", "i4", "TooltipModule", "ObjectUtils", "UniqueComponentId", "ZIndexUtils", "_c0", "TieredMenuSub_ng_template_2_li_0_Template", "rf", "ctx", "ɵɵelement", "processedItem_r2", "ɵɵnextContext", "$implicit", "ctx_r4", "ɵɵstyleMap", "getItemProp", "ɵɵproperty", "getSeparatorItemClass", "ɵɵattribute", "getItemId", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_span_1_Template", "ctx_r13", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_span_2_Template", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ctx_r14", "ɵɵadvance", "ɵɵtextInterpolate1", "getItemLabel", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_template_3_Template", "ctx_r15", "ɵɵsanitizeHtml", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_span_5_Template", "ctx_r17", "ɵɵtextInterpolate", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_AngleRightIcon_1_Template", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_ng_template_0_Template", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_Template", "ɵɵtemplate", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "ctx_r18", "tieredMenu", "submenuIconTemplate", "_c1", "a1", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_1_Template", "ɵɵtemplateRefExtractor", "_r16", "ɵɵreference", "ctx_r11", "ɵɵpureFunction1", "ɵɵsanitizeUrl", "isItemGroup", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_span_1_Template", "ctx_r27", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_span_2_Template", "ctx_r28", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_template_3_Template", "ctx_r29", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_span_5_Template", "ctx_r31", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_AngleRightIcon_1_Template", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_ng_template_0_Template", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_Template", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_Template", "ctx_r32", "_c2", "exact", "TieredMenuSub_ng_template_2_li_1_ng_container_3_a_2_Template", "_r30", "ctx_r12", "ɵɵpureFunction0", "TieredMenuSub_ng_template_2_li_1_ng_container_3_Template", "ctx_r8", "TieredMenuSub_ng_template_2_li_1_ng_container_4_1_ng_template_0_Template", "TieredMenuSub_ng_template_2_li_1_ng_container_4_1_Template", "_c3", "a0", "hasSubmenu", "TieredMenuSub_ng_template_2_li_1_ng_container_4_Template", "ctx_r9", "itemTemplate", "ɵɵpureFunction2", "item", "TieredMenuSub_ng_template_2_li_1_p_tieredMenuSub_5_Template", "_r46", "ɵɵgetCurrentView", "ɵɵlistener", "TieredMenuSub_ng_template_2_li_1_p_tieredMenuSub_5_Template_p_tieredMenuSub_itemClick_0_listener", "$event", "ɵɵrestoreView", "ctx_r45", "ɵɵresetView", "itemClick", "emit", "TieredMenuSub_ng_template_2_li_1_p_tieredMenuSub_5_Template_p_tieredMenuSub_itemMouseEnter_0_listener", "ctx_r47", "onItemMouseEnter", "ctx_r10", "items", "autoDisplay", "menuId", "activeItemPath", "focusedItemId", "level", "TieredMenuSub_ng_template_2_li_1_Template", "_r51", "TieredMenuSub_ng_template_2_li_1_Template_div_click_2_listener", "ctx_r49", "onItemClick", "TieredMenuSub_ng_template_2_li_1_Template_div_mouseenter_2_listener", "ctx_r52", "processedItem", "ctx_r54", "index_r3", "index", "ctx_r5", "ɵɵclassMap", "getItemClass", "isItemActive", "isItemFocused", "isItemDisabled", "undefined", "getAriaSetSize", "getAriaPosInset", "isItemVisible", "TieredMenuSub_ng_template_2_Template", "ctx_r1", "_c4", "_c5", "_c6", "_c7", "_c8", "showTransitionParams", "hideTransitionParams", "_c9", "value", "params", "TieredMenu_div_0_Template", "_r4", "TieredMenu_div_0_Template_div_click_0_listener", "ctx_r3", "onOverlayClick", "TieredMenu_div_0_Template_div_animation_overlayAnimation_start_0_listener", "onOverlayAnimationStart", "TieredMenu_div_0_Template_div_animation_overlayAnimation_done_0_listener", "ctx_r6", "onOverlayAnimationEnd", "TieredMenu_div_0_Template_p_tieredMenuSub_itemClick_2_listener", "ctx_r7", "TieredMenu_div_0_Template_p_tieredMenuSub_menuFocus_2_listener", "onMenuFocus", "TieredMenu_div_0_Template_p_tieredMenuSub_menuBlur_2_listener", "onMenuBlur", "TieredMenu_div_0_Template_p_tieredMenuSub_menuKeydown_2_listener", "onKeyDown", "TieredMenu_div_0_Template_p_tieredMenuSub_itemMouseEnter_2_listener", "ctx_r0", "styleClass", "id", "popup", "showTransitionOptions", "hideTransitionOptions", "processedItems", "disabled", "tabindex", "aria<PERSON><PERSON><PERSON>", "ariaLabelledBy", "baseZIndex", "autoZIndex", "focused", "TieredMenuSub", "el", "renderer", "cd", "root", "itemMouseEnter", "menuFocus", "menuBlur", "menuKeydown", "sublist<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "positionSubmenu", "sublist", "nativeElement", "parentItem", "parentElement", "containerOffset", "getOffset", "viewport", "getViewport", "sublist<PERSON><PERSON><PERSON>", "offsetParent", "offsetWidth", "getHiddenElementOuterWidth", "itemOuterWidth", "getOuterWidth", "children", "parseInt", "left", "width", "calculateScrollbarWidth", "addClass", "name", "getItemValue", "key", "getItemKey", "filter", "length", "slice", "some", "path", "isNotEmpty", "param", "event", "originalEvent", "isFocus", "ɵfac", "TieredMenuSub_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "Renderer2", "ChangeDetectorRef", "TieredMenu", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "TieredMenuSub_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "inputs", "outputs", "decls", "vars", "consts", "template", "TieredMenuSub_Template", "TieredMenuSub_Template_ul_keydown_0_listener", "TieredMenuSub_Template_ul_focus_0_listener", "TieredMenuSub_Template_ul_blur_0_listener", "dependencies", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "RouterLink", "RouterLinkActive", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "encapsulation", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "None", "host", "class", "decorators", "static", "document", "platformId", "config", "overlayService", "model", "_model", "_processedItems", "createProcessedItems", "appendTo", "onShow", "onHide", "templates", "rootmenu", "containerViewChild", "container", "outsideClickListener", "resizeListener", "<PERSON><PERSON><PERSON><PERSON>", "target", "relatedTarget", "visible", "relativeAlign", "window", "dirty", "number", "focusedItemInfo", "parent<PERSON><PERSON>", "searchValue", "searchTimeout", "visibleItems", "find", "p", "defaultView", "bindOutsideClickListener", "bindResizeListener", "unbindOutsideClickListener", "unbindResizeListener", "ngOnInit", "ngAfterContentInit", "for<PERSON>ach", "getType", "parent", "newItem", "push", "getProccessedItemLabel", "isProcessedItemGroup", "isSelected", "isValidSelectedItem", "isValidItem", "isItemSeparator", "isItemMatched", "toLocaleLowerCase", "startsWith", "isProccessedItemGroup", "add", "grouped", "isEmpty", "selected", "set", "focus", "onItemChange", "rootProcessedItem", "hide", "changeFocusedItemIndex", "isTouchDevice", "metaKey", "ctrl<PERSON>ey", "code", "onArrowDownKey", "onArrowUpKey", "onArrowLeftKey", "onArrowRightKey", "onHomeKey", "onEndKey", "onSpaceKey", "onEnterKey", "onEscapeKey", "onTabKey", "isPrintableCharacter", "searchItems", "itemIndex", "findNextItemIndex", "findFirstFocusedItemIndex", "preventDefault", "altKey", "findPrevItemIndex", "findLastFocusedItemIndex", "findFirstItemIndex", "findLastItemIndex", "element", "findSingle", "anchorElement", "click", "toState", "moveOnTop", "appendOverlay", "alignOverlay", "bindScrollListener", "scrollInView", "onOverlayHide", "relativePosition", "absolutePosition", "clear", "append<PERSON><PERSON><PERSON>", "body", "restoreOverlayAppend", "zIndex", "menu", "toggle", "show", "currentTarget", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "char", "matched", "findIndex", "clearTimeout", "setTimeout", "selectedIndex", "findSelectedItemIndex", "findLastIndex", "matchedItemIndex", "scrollIntoView", "block", "inline", "unbindScrollListener", "listen", "isOutsideContainer", "contains", "isOutsideTarget", "removeEventListener", "destroyed", "ngOnDestroy", "destroy", "TieredMenu_Factory", "PrimeNGConfig", "OverlayService", "contentQueries", "TieredMenu_ContentQueries", "dirIndex", "ɵɵcontentQuery", "TieredMenu_Query", "TieredMenu_Template", "styles", "data", "animation", "opacity", "transform", "changeDetection", "animations", "OnPush", "Document", "TieredMenuModule", "TieredMenuModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["D:/Greg/solarkapital/ui/node_modules/primeng/fesm2022/primeng-tieredmenu.mjs"], "sourcesContent": ["import { style, animate, transition, trigger } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, forwardRef, Component, ViewEncapsulation, Inject, Input, Output, ViewChild, signal, effect, PLATFORM_ID, ChangeDetectionStrategy, ContentChildren, NgModule } from '@angular/core';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i5 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { AngleRightIcon } from 'primeng/icons/angleright';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i4 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ObjectUtils, UniqueComponentId, ZIndexUtils } from 'primeng/utils';\n\nclass TieredMenuSub {\n    el;\n    renderer;\n    cd;\n    tieredMenu;\n    items;\n    itemTemplate;\n    root = false;\n    autoDisplay;\n    autoZIndex = true;\n    baseZIndex = 0;\n    popup;\n    menuId;\n    ariaLabel;\n    ariaLabelledBy;\n    level = 0;\n    focusedItemId;\n    activeItemPath;\n    tabindex = 0;\n    itemClick = new EventEmitter();\n    itemMouseEnter = new EventEmitter();\n    menuFocus = new EventEmitter();\n    menuBlur = new EventEmitter();\n    menuKeydown = new EventEmitter();\n    sublistViewChild;\n    constructor(el, renderer, cd, tieredMenu) {\n        this.el = el;\n        this.renderer = renderer;\n        this.cd = cd;\n        this.tieredMenu = tieredMenu;\n    }\n    positionSubmenu() {\n        let sublist = this.sublistViewChild && this.sublistViewChild.nativeElement;\n        if (sublist) {\n            const parentItem = sublist.parentElement.parentElement;\n            const containerOffset = DomHandler.getOffset(parentItem);\n            const viewport = DomHandler.getViewport();\n            const sublistWidth = sublist.offsetParent ? sublist.offsetWidth : DomHandler.getHiddenElementOuterWidth(sublist);\n            const itemOuterWidth = DomHandler.getOuterWidth(parentItem.children[0]);\n            if (parseInt(containerOffset.left, 10) + itemOuterWidth + sublistWidth > viewport.width - DomHandler.calculateScrollbarWidth()) {\n                DomHandler.addClass(sublist, 'p-submenu-list-flipped');\n            }\n        }\n    }\n    getItemProp(processedItem, name, params = null) {\n        return processedItem && processedItem.item ? ObjectUtils.getItemValue(processedItem.item[name], params) : undefined;\n    }\n    getItemId(processedItem) {\n        return processedItem.item?.id ?? `${this.menuId}_${processedItem.key}`;\n    }\n    getItemKey(processedItem) {\n        return this.getItemId(processedItem);\n    }\n    getItemClass(processedItem) {\n        return {\n            ...this.getItemProp(processedItem, 'class'),\n            'p-menuitem': true,\n            'p-highlight': this.isItemActive(processedItem),\n            'p-menuitem-active': this.isItemActive(processedItem),\n            'p-focus': this.isItemFocused(processedItem),\n            'p-disabled': this.isItemDisabled(processedItem)\n        };\n    }\n    getItemLabel(processedItem) {\n        return this.getItemProp(processedItem, 'label');\n    }\n    getSeparatorItemClass(processedItem) {\n        return {\n            ...this.getItemProp(processedItem, 'class'),\n            'p-menuitem-separator': true\n        };\n    }\n    getAriaSetSize() {\n        return this.items.filter((processedItem) => this.isItemVisible(processedItem) && !this.getItemProp(processedItem, 'separator')).length;\n    }\n    getAriaPosInset(index) {\n        return index - this.items.slice(0, index).filter((processedItem) => this.isItemVisible(processedItem) && this.getItemProp(processedItem, 'separator')).length + 1;\n    }\n    isItemVisible(processedItem) {\n        return this.getItemProp(processedItem, 'visible') !== false;\n    }\n    isItemActive(processedItem) {\n        if (this.activeItemPath) {\n            return this.activeItemPath.some((path) => path.key === processedItem.key);\n        }\n    }\n    isItemDisabled(processedItem) {\n        return this.getItemProp(processedItem, 'disabled');\n    }\n    isItemFocused(processedItem) {\n        return this.focusedItemId === this.getItemId(processedItem);\n    }\n    isItemGroup(processedItem) {\n        return ObjectUtils.isNotEmpty(processedItem.items);\n    }\n    onItemMouseEnter(param) {\n        if (this.autoDisplay) {\n            const { event, processedItem } = param;\n            this.itemMouseEnter.emit({ originalEvent: event, processedItem });\n        }\n    }\n    onItemClick(event, processedItem) {\n        this.getItemProp(processedItem, 'command', { originalEvent: event, item: processedItem.item });\n        this.itemClick.emit({ originalEvent: event, processedItem, isFocus: true });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: TieredMenuSub, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: forwardRef(() => TieredMenu) }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.5\", type: TieredMenuSub, selector: \"p-tieredMenuSub\", inputs: { items: \"items\", itemTemplate: \"itemTemplate\", root: \"root\", autoDisplay: \"autoDisplay\", autoZIndex: \"autoZIndex\", baseZIndex: \"baseZIndex\", popup: \"popup\", menuId: \"menuId\", ariaLabel: \"ariaLabel\", ariaLabelledBy: \"ariaLabelledBy\", level: \"level\", focusedItemId: \"focusedItemId\", activeItemPath: \"activeItemPath\", tabindex: \"tabindex\" }, outputs: { itemClick: \"itemClick\", itemMouseEnter: \"itemMouseEnter\", menuFocus: \"menuFocus\", menuBlur: \"menuBlur\", menuKeydown: \"menuKeydown\" }, host: { classAttribute: \"p-element\" }, viewQueries: [{ propertyName: \"sublistViewChild\", first: true, predicate: [\"sublist\"], descendants: true, static: true }], ngImport: i0, template: `\n        <ul\n            #sublist\n            role=\"menu\"\n            [ngClass]=\"{ 'p-submenu-list': !root, 'p-tieredmenu-root-list': root }\"\n            [id]=\"menuId + '_list'\"\n            [tabindex]=\"tabindex\"\n            [attr.aria-label]=\"ariaLabel\"\n            [attr.aria-labelledBy]=\"ariaLabelledBy\"\n            [attr.aria-activedescendant]=\"focusedItemId\"\n            [attr.aria-orientation]=\"'vertical'\"\n            [attr.data-pc-section]=\"'menu'\"\n            (keydown)=\"menuKeydown.emit($event)\"\n            (focus)=\"menuFocus.emit($event)\"\n            (blur)=\"menuBlur.emit($event)\"\n        >\n            <ng-template ngFor let-processedItem [ngForOf]=\"items\" let-index=\"index\">\n                <li\n                    *ngIf=\"isItemVisible(processedItem) && getItemProp(processedItem, 'separator')\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [style]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getSeparatorItemClass(processedItem)\"\n                    role=\"separator\"\n                    [attr.data-pc-section]=\"'separator'\"\n                ></li>\n                <li\n                    #listItem\n                    *ngIf=\"isItemVisible(processedItem) && !getItemProp(processedItem, 'separator')\"\n                    role=\"menuitem\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [attr.data-pc-section]=\"'menuitem'\"\n                    [attr.data-p-highlight]=\"isItemActive(processedItem)\"\n                    [attr.data-p-focused]=\"isItemFocused(processedItem)\"\n                    [attr.data-p-disabled]=\"isItemDisabled(processedItem)\"\n                    [attr.aria-label]=\"getItemLabel(processedItem)\"\n                    [attr.aria-disabled]=\"isItemDisabled(processedItem) || undefined\"\n                    [attr.aria-haspopup]=\"isItemGroup(processedItem) && !getItemProp(processedItem, 'to') ? 'menu' : undefined\"\n                    [attr.aria-expanded]=\"isItemGroup(processedItem) ? isItemActive(processedItem) : undefined\"\n                    [attr.aria-level]=\"level + 1\"\n                    [attr.aria-setsize]=\"getAriaSetSize()\"\n                    [attr.aria-posinset]=\"getAriaPosInset(index)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getItemClass(processedItem)\"\n                    [class]=\"getItemProp(processedItem, 'styleClass')\"\n                    pTooltip\n                    [tooltipOptions]=\"getItemProp(processedItem, 'tooltipOptions')\"\n                >\n                    <div [attr.data-pc-section]=\"'content'\" class=\"p-menuitem-content\" (click)=\"onItemClick($event, processedItem)\" (mouseenter)=\"onItemMouseEnter({$event, processedItem})\">\n                        <ng-container *ngIf=\"!itemTemplate\">\n                            <a\n                                *ngIf=\"!getItemProp(processedItem, 'routerLink')\"\n                                [attr.href]=\"getItemProp(processedItem, 'url')\"\n                                [attr.aria-hidden]=\"true\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [attr.tabindex]=\"-1\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-menuitem-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.aria-hidden]=\"true\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menuitem-text\" [attr.data-pc-section]=\"'label'\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span>\n                                </ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <AngleRightIcon *ngIf=\"!tieredMenu.submenuIconTemplate\" [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                    <ng-template *ngTemplateOutlet=\"tieredMenu.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                            <a\n                                *ngIf=\"getItemProp(processedItem, 'routerLink')\"\n                                [routerLink]=\"getItemProp(processedItem, 'routerLink')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.tabindex]=\"-1\"\n                                [attr.aria-hidden]=\"true\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [queryParams]=\"getItemProp(processedItem, 'queryParams')\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"getItemProp(processedItem, 'routerLinkActiveOptions') || { exact: false }\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [fragment]=\"getItemProp(processedItem, 'fragment')\"\n                                [queryParamsHandling]=\"getItemProp(processedItem, 'queryParamsHandling')\"\n                                [preserveFragment]=\"getItemProp(processedItem, 'preserveFragment')\"\n                                [skipLocationChange]=\"getItemProp(processedItem, 'skipLocationChange')\"\n                                [replaceUrl]=\"getItemProp(processedItem, 'replaceUrl')\"\n                                [state]=\"getItemProp(processedItem, 'state')\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-menuitem-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.aria-hidden]=\"true\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menuitem-text\" [attr.data-pc-section]=\"'label'\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span>\n                                </ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <AngleRightIcon *ngIf=\"!tieredMenu.submenuIconTemplate\" [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                    <ng-template *ngTemplateOutlet=\"tieredMenu.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                        </ng-container>\n                        <ng-container *ngIf=\"itemTemplate\">\n                            <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: processedItem.item, hasSubmenu: getItemProp(processedItem, 'items') }\"></ng-template>\n                        </ng-container>\n                    </div>\n\n                    <p-tieredMenuSub\n                        *ngIf=\"isItemVisible(processedItem) && isItemGroup(processedItem)\"\n                        [items]=\"processedItem.items\"\n                        [itemTemplate]=\"itemTemplate\"\n                        [autoDisplay]=\"autoDisplay\"\n                        [menuId]=\"menuId\"\n                        [activeItemPath]=\"activeItemPath\"\n                        [focusedItemId]=\"focusedItemId\"\n                        [level]=\"level + 1\"\n                        (itemClick)=\"itemClick.emit($event)\"\n                        (itemMouseEnter)=\"onItemMouseEnter($event)\"\n                    ></p-tieredMenuSub>\n                </li>\n            </ng-template>\n        </ul>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i0.forwardRef(() => i1.NgClass), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgForOf), selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgIf), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgTemplateOutlet), selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgStyle), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.RouterLink), selector: \"[routerLink]\", inputs: [\"target\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"state\", \"relativeTo\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"routerLink\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.RouterLinkActive), selector: \"[routerLinkActive]\", inputs: [\"routerLinkActiveOptions\", \"ariaCurrentWhenActive\", \"routerLinkActive\"], outputs: [\"isActiveChange\"], exportAs: [\"routerLinkActive\"] }, { kind: \"directive\", type: i0.forwardRef(() => i3.Ripple), selector: \"[pRipple]\" }, { kind: \"directive\", type: i0.forwardRef(() => i4.Tooltip), selector: \"[pTooltip]\", inputs: [\"tooltipPosition\", \"tooltipEvent\", \"appendTo\", \"positionStyle\", \"tooltipStyleClass\", \"tooltipZIndex\", \"escape\", \"showDelay\", \"hideDelay\", \"life\", \"positionTop\", \"positionLeft\", \"autoHide\", \"fitContent\", \"hideOnEscape\", \"pTooltip\", \"tooltipDisabled\", \"tooltipOptions\"] }, { kind: \"component\", type: i0.forwardRef(() => AngleRightIcon), selector: \"AngleRightIcon\" }, { kind: \"component\", type: i0.forwardRef(() => TieredMenuSub), selector: \"p-tieredMenuSub\", inputs: [\"items\", \"itemTemplate\", \"root\", \"autoDisplay\", \"autoZIndex\", \"baseZIndex\", \"popup\", \"menuId\", \"ariaLabel\", \"ariaLabelledBy\", \"level\", \"focusedItemId\", \"activeItemPath\", \"tabindex\"], outputs: [\"itemClick\", \"itemMouseEnter\", \"menuFocus\", \"menuBlur\", \"menuKeydown\"] }], encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: TieredMenuSub, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-tieredMenuSub',\n                    template: `\n        <ul\n            #sublist\n            role=\"menu\"\n            [ngClass]=\"{ 'p-submenu-list': !root, 'p-tieredmenu-root-list': root }\"\n            [id]=\"menuId + '_list'\"\n            [tabindex]=\"tabindex\"\n            [attr.aria-label]=\"ariaLabel\"\n            [attr.aria-labelledBy]=\"ariaLabelledBy\"\n            [attr.aria-activedescendant]=\"focusedItemId\"\n            [attr.aria-orientation]=\"'vertical'\"\n            [attr.data-pc-section]=\"'menu'\"\n            (keydown)=\"menuKeydown.emit($event)\"\n            (focus)=\"menuFocus.emit($event)\"\n            (blur)=\"menuBlur.emit($event)\"\n        >\n            <ng-template ngFor let-processedItem [ngForOf]=\"items\" let-index=\"index\">\n                <li\n                    *ngIf=\"isItemVisible(processedItem) && getItemProp(processedItem, 'separator')\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [style]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getSeparatorItemClass(processedItem)\"\n                    role=\"separator\"\n                    [attr.data-pc-section]=\"'separator'\"\n                ></li>\n                <li\n                    #listItem\n                    *ngIf=\"isItemVisible(processedItem) && !getItemProp(processedItem, 'separator')\"\n                    role=\"menuitem\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [attr.data-pc-section]=\"'menuitem'\"\n                    [attr.data-p-highlight]=\"isItemActive(processedItem)\"\n                    [attr.data-p-focused]=\"isItemFocused(processedItem)\"\n                    [attr.data-p-disabled]=\"isItemDisabled(processedItem)\"\n                    [attr.aria-label]=\"getItemLabel(processedItem)\"\n                    [attr.aria-disabled]=\"isItemDisabled(processedItem) || undefined\"\n                    [attr.aria-haspopup]=\"isItemGroup(processedItem) && !getItemProp(processedItem, 'to') ? 'menu' : undefined\"\n                    [attr.aria-expanded]=\"isItemGroup(processedItem) ? isItemActive(processedItem) : undefined\"\n                    [attr.aria-level]=\"level + 1\"\n                    [attr.aria-setsize]=\"getAriaSetSize()\"\n                    [attr.aria-posinset]=\"getAriaPosInset(index)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getItemClass(processedItem)\"\n                    [class]=\"getItemProp(processedItem, 'styleClass')\"\n                    pTooltip\n                    [tooltipOptions]=\"getItemProp(processedItem, 'tooltipOptions')\"\n                >\n                    <div [attr.data-pc-section]=\"'content'\" class=\"p-menuitem-content\" (click)=\"onItemClick($event, processedItem)\" (mouseenter)=\"onItemMouseEnter({$event, processedItem})\">\n                        <ng-container *ngIf=\"!itemTemplate\">\n                            <a\n                                *ngIf=\"!getItemProp(processedItem, 'routerLink')\"\n                                [attr.href]=\"getItemProp(processedItem, 'url')\"\n                                [attr.aria-hidden]=\"true\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [attr.tabindex]=\"-1\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-menuitem-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.aria-hidden]=\"true\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menuitem-text\" [attr.data-pc-section]=\"'label'\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span>\n                                </ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <AngleRightIcon *ngIf=\"!tieredMenu.submenuIconTemplate\" [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                    <ng-template *ngTemplateOutlet=\"tieredMenu.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                            <a\n                                *ngIf=\"getItemProp(processedItem, 'routerLink')\"\n                                [routerLink]=\"getItemProp(processedItem, 'routerLink')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.tabindex]=\"-1\"\n                                [attr.aria-hidden]=\"true\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [queryParams]=\"getItemProp(processedItem, 'queryParams')\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"getItemProp(processedItem, 'routerLinkActiveOptions') || { exact: false }\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [fragment]=\"getItemProp(processedItem, 'fragment')\"\n                                [queryParamsHandling]=\"getItemProp(processedItem, 'queryParamsHandling')\"\n                                [preserveFragment]=\"getItemProp(processedItem, 'preserveFragment')\"\n                                [skipLocationChange]=\"getItemProp(processedItem, 'skipLocationChange')\"\n                                [replaceUrl]=\"getItemProp(processedItem, 'replaceUrl')\"\n                                [state]=\"getItemProp(processedItem, 'state')\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-menuitem-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.aria-hidden]=\"true\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menuitem-text\" [attr.data-pc-section]=\"'label'\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span>\n                                </ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <AngleRightIcon *ngIf=\"!tieredMenu.submenuIconTemplate\" [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                    <ng-template *ngTemplateOutlet=\"tieredMenu.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                        </ng-container>\n                        <ng-container *ngIf=\"itemTemplate\">\n                            <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: processedItem.item, hasSubmenu: getItemProp(processedItem, 'items') }\"></ng-template>\n                        </ng-container>\n                    </div>\n\n                    <p-tieredMenuSub\n                        *ngIf=\"isItemVisible(processedItem) && isItemGroup(processedItem)\"\n                        [items]=\"processedItem.items\"\n                        [itemTemplate]=\"itemTemplate\"\n                        [autoDisplay]=\"autoDisplay\"\n                        [menuId]=\"menuId\"\n                        [activeItemPath]=\"activeItemPath\"\n                        [focusedItemId]=\"focusedItemId\"\n                        [level]=\"level + 1\"\n                        (itemClick)=\"itemClick.emit($event)\"\n                        (itemMouseEnter)=\"onItemMouseEnter($event)\"\n                    ></p-tieredMenuSub>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n                    encapsulation: ViewEncapsulation.None,\n                    host: {\n                        class: 'p-element'\n                    }\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: TieredMenu, decorators: [{\n                    type: Inject,\n                    args: [forwardRef(() => TieredMenu)]\n                }] }], propDecorators: { items: [{\n                type: Input\n            }], itemTemplate: [{\n                type: Input\n            }], root: [{\n                type: Input\n            }], autoDisplay: [{\n                type: Input\n            }], autoZIndex: [{\n                type: Input\n            }], baseZIndex: [{\n                type: Input\n            }], popup: [{\n                type: Input\n            }], menuId: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input\n            }], ariaLabelledBy: [{\n                type: Input\n            }], level: [{\n                type: Input\n            }], focusedItemId: [{\n                type: Input\n            }], activeItemPath: [{\n                type: Input\n            }], tabindex: [{\n                type: Input\n            }], itemClick: [{\n                type: Output\n            }], itemMouseEnter: [{\n                type: Output\n            }], menuFocus: [{\n                type: Output\n            }], menuBlur: [{\n                type: Output\n            }], menuKeydown: [{\n                type: Output\n            }], sublistViewChild: [{\n                type: ViewChild,\n                args: ['sublist', { static: true }]\n            }] } });\n/**\n * TieredMenu displays submenus in nested overlays.\n * @group Components\n */\nclass TieredMenu {\n    document;\n    platformId;\n    el;\n    renderer;\n    cd;\n    config;\n    overlayService;\n    /**\n     * An array of menuitems.\n     * @group Props\n     */\n    set model(value) {\n        this._model = value;\n        this._processedItems = this.createProcessedItems(this._model || []);\n    }\n    get model() {\n        return this._model;\n    }\n    /**\n     * Defines if menu would displayed as a popup.\n     * @group Props\n     */\n    popup;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Target element to attach the overlay, valid values are \"body\" or a local ng-template variable of another element.\n     * @group Props\n     */\n    appendTo;\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    autoZIndex = true;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    baseZIndex = 0;\n    /**\n     * Whether to show a root submenu on mouse over.\n     * @defaultValue true\n     * @group Props\n     */\n    autoDisplay = true;\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     */\n    showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     */\n    hideTransitionOptions = '.1s linear';\n    /**\n     * Current id state as a string.\n     * @group Props\n     */\n    id;\n    /**\n     * Defines a string value that labels an interactive element.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Identifier of the underlying input element.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * When present, it specifies that the component should be disabled.\n     * @group Props\n     */\n    disabled = false;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    tabindex = 0;\n    /**\n     * Callback to invoke when overlay menu is shown.\n     * @group Emits\n     */\n    onShow = new EventEmitter();\n    /**\n     * Callback to invoke when overlay menu is hidden.\n     * @group Emits\n     */\n    onHide = new EventEmitter();\n    templates;\n    rootmenu;\n    containerViewChild;\n    submenuIconTemplate;\n    itemTemplate;\n    container;\n    outsideClickListener;\n    resizeListener;\n    scrollHandler;\n    target;\n    relatedTarget;\n    visible;\n    relativeAlign;\n    window;\n    dirty = false;\n    focused = false;\n    activeItemPath = signal([]);\n    number = signal(0);\n    focusedItemInfo = signal({ index: -1, level: 0, parentKey: '', item: null });\n    searchValue = '';\n    searchTimeout;\n    _processedItems;\n    _model;\n    get visibleItems() {\n        const processedItem = this.activeItemPath().find((p) => p.key === this.focusedItemInfo().parentKey);\n        return processedItem ? processedItem.items : this.processedItems;\n    }\n    get processedItems() {\n        if (!this._processedItems || !this._processedItems.length) {\n            this._processedItems = this.createProcessedItems(this.model || []);\n        }\n        return this._processedItems;\n    }\n    get focusedItemId() {\n        const focusedItemInfo = this.focusedItemInfo();\n        return focusedItemInfo.item?.id ? focusedItemInfo.item.id : focusedItemInfo.index !== -1 ? `${this.id}${ObjectUtils.isNotEmpty(focusedItemInfo.parentKey) ? '_' + focusedItemInfo.parentKey : ''}_${focusedItemInfo.index}` : null;\n    }\n    constructor(document, platformId, el, renderer, cd, config, overlayService) {\n        this.document = document;\n        this.platformId = platformId;\n        this.el = el;\n        this.renderer = renderer;\n        this.cd = cd;\n        this.config = config;\n        this.overlayService = overlayService;\n        this.window = this.document.defaultView;\n        effect(() => {\n            const path = this.activeItemPath();\n            if (ObjectUtils.isNotEmpty(path)) {\n                this.bindOutsideClickListener();\n                this.bindResizeListener();\n            }\n            else {\n                this.unbindOutsideClickListener();\n                this.unbindResizeListener();\n            }\n        });\n    }\n    ngOnInit() {\n        this.id = this.id || UniqueComponentId();\n    }\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'submenuicon':\n                    this.submenuIconTemplate = item.template;\n                    break;\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    createProcessedItems(items, level = 0, parent = {}, parentKey = '') {\n        const processedItems = [];\n        items &&\n            items.forEach((item, index) => {\n                const key = (parentKey !== '' ? parentKey + '_' : '') + index;\n                const newItem = {\n                    item,\n                    index,\n                    level,\n                    key,\n                    parent,\n                    parentKey\n                };\n                newItem['items'] = this.createProcessedItems(item.items, level + 1, newItem, key);\n                processedItems.push(newItem);\n            });\n        return processedItems;\n    }\n    getItemProp(item, name) {\n        return item ? ObjectUtils.getItemValue(item[name]) : undefined;\n    }\n    getProccessedItemLabel(processedItem) {\n        return processedItem ? this.getItemLabel(processedItem.item) : undefined;\n    }\n    getItemLabel(item) {\n        return this.getItemProp(item, 'label');\n    }\n    isProcessedItemGroup(processedItem) {\n        return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n    }\n    isSelected(processedItem) {\n        return this.activeItemPath().some((p) => p.key === processedItem.key);\n    }\n    isValidSelectedItem(processedItem) {\n        return this.isValidItem(processedItem) && this.isSelected(processedItem);\n    }\n    isValidItem(processedItem) {\n        return !!processedItem && !this.isItemDisabled(processedItem.item) && !this.isItemSeparator(processedItem.item);\n    }\n    isItemDisabled(item) {\n        return this.getItemProp(item, 'disabled');\n    }\n    isItemSeparator(item) {\n        return this.getItemProp(item, 'separator');\n    }\n    isItemMatched(processedItem) {\n        return this.isValidItem(processedItem) && this.getProccessedItemLabel(processedItem).toLocaleLowerCase().startsWith(this.searchValue.toLocaleLowerCase());\n    }\n    isProccessedItemGroup(processedItem) {\n        return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n    }\n    onOverlayClick(event) {\n        if (this.popup) {\n            this.overlayService.add({\n                originalEvent: event,\n                target: this.el.nativeElement\n            });\n        }\n    }\n    onItemClick(event) {\n        const { originalEvent, processedItem } = event;\n        const grouped = this.isProcessedItemGroup(processedItem);\n        const root = ObjectUtils.isEmpty(processedItem.parent);\n        const selected = this.isSelected(processedItem);\n        if (selected) {\n            const { index, key, level, parentKey, item } = processedItem;\n            this.activeItemPath.set(this.activeItemPath().filter((p) => key !== p.key && key.startsWith(p.key)));\n            this.focusedItemInfo.set({ index, level, parentKey, item });\n            this.dirty = true;\n            DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n        }\n        else {\n            if (grouped) {\n                this.onItemChange(event);\n            }\n            else {\n                const rootProcessedItem = root ? processedItem : this.activeItemPath().find((p) => p.parentKey === '');\n                this.hide(originalEvent);\n                this.changeFocusedItemIndex(originalEvent, rootProcessedItem ? rootProcessedItem.index : -1);\n                DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n            }\n        }\n    }\n    onItemMouseEnter(event) {\n        if (!DomHandler.isTouchDevice()) {\n            if (this.dirty) {\n                this.onItemChange(event);\n            }\n        }\n        else {\n            this.onItemChange({ event, processedItem: event.processedItem, focus: this.autoDisplay });\n        }\n    }\n    onKeyDown(event) {\n        const metaKey = event.metaKey || event.ctrlKey;\n        switch (event.code) {\n            case 'ArrowDown':\n                this.onArrowDownKey(event);\n                break;\n            case 'ArrowUp':\n                this.onArrowUpKey(event);\n                break;\n            case 'ArrowLeft':\n                this.onArrowLeftKey(event);\n                break;\n            case 'ArrowRight':\n                this.onArrowRightKey(event);\n                break;\n            case 'Home':\n                this.onHomeKey(event);\n                break;\n            case 'End':\n                this.onEndKey(event);\n                break;\n            case 'Space':\n                this.onSpaceKey(event);\n                break;\n            case 'Enter':\n                this.onEnterKey(event);\n                break;\n            case 'Escape':\n                this.onEscapeKey(event);\n                break;\n            case 'Tab':\n                this.onTabKey(event);\n                break;\n            case 'PageDown':\n            case 'PageUp':\n            case 'Backspace':\n            case 'ShiftLeft':\n            case 'ShiftRight':\n                //NOOP\n                break;\n            default:\n                if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n                    this.searchItems(event, event.key);\n                }\n                break;\n        }\n    }\n    onArrowDownKey(event) {\n        const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n        this.changeFocusedItemIndex(event, itemIndex);\n        event.preventDefault();\n    }\n    onArrowRightKey(event) {\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const grouped = this.isProccessedItemGroup(processedItem);\n        const item = processedItem.item;\n        if (grouped) {\n            this.onItemChange({ originalEvent: event, processedItem });\n            this.focusedItemInfo.set({ index: -1, parentKey: processedItem.key, item });\n            this.searchValue = '';\n            this.onArrowDownKey(event);\n        }\n        event.preventDefault();\n    }\n    onArrowUpKey(event) {\n        if (event.altKey) {\n            if (this.focusedItemInfo().index !== -1) {\n                const processedItem = this.visibleItems[this.focusedItemInfo().index];\n                const grouped = this.isProccessedItemGroup(processedItem);\n                !grouped && this.onItemChange({ originalEvent: event, processedItem });\n            }\n            this.popup && this.hide(event, true);\n            event.preventDefault();\n        }\n        else {\n            const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n            this.changeFocusedItemIndex(event, itemIndex);\n            event.preventDefault();\n        }\n    }\n    onArrowLeftKey(event) {\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const parentItem = this.activeItemPath().find((p) => p.key === processedItem.parentKey);\n        const root = ObjectUtils.isEmpty(processedItem.parent);\n        if (!root) {\n            this.focusedItemInfo.set({ index: -1, parentKey: parentItem ? parentItem.parentKey : '', item: processedItem.item });\n            this.searchValue = '';\n            this.onArrowDownKey(event);\n        }\n        const activeItemPath = this.activeItemPath().filter((p) => p.parentKey !== this.focusedItemInfo().parentKey);\n        this.activeItemPath.set(activeItemPath);\n        event.preventDefault();\n    }\n    onHomeKey(event) {\n        this.changeFocusedItemIndex(event, this.findFirstItemIndex());\n        event.preventDefault();\n    }\n    onEndKey(event) {\n        this.changeFocusedItemIndex(event, this.findLastItemIndex());\n        event.preventDefault();\n    }\n    onSpaceKey(event) {\n        this.onEnterKey(event);\n    }\n    onEscapeKey(event) {\n        this.hide(event, true);\n        this.focusedItemInfo().index = this.findFirstFocusedItemIndex();\n        event.preventDefault();\n    }\n    onTabKey(event) {\n        if (this.focusedItemInfo().index !== -1) {\n            const processedItem = this.visibleItems[this.focusedItemInfo().index];\n            const grouped = this.isProccessedItemGroup(processedItem);\n            !grouped && this.onItemChange({ originalEvent: event, processedItem });\n        }\n        this.hide();\n    }\n    onEnterKey(event) {\n        if (this.focusedItemInfo().index !== -1) {\n            const element = DomHandler.findSingle(this.rootmenu.el.nativeElement, `li[id=\"${`${this.focusedItemId}`}\"]`);\n            const anchorElement = element && DomHandler.findSingle(element, 'a[data-pc-section=\"action\"]');\n            anchorElement ? anchorElement.click() : element && element.click();\n            const processedItem = this.visibleItems[this.focusedItemInfo().index];\n            if (!this.popup) {\n                const processedItem = this.visibleItems[this.focusedItemInfo().index];\n                const grouped = this.isProccessedItemGroup(processedItem);\n                !grouped && (this.focusedItemInfo().index = this.findFirstFocusedItemIndex());\n            }\n        }\n        event.preventDefault();\n    }\n    onItemChange(event) {\n        const { processedItem, isFocus } = event;\n        if (ObjectUtils.isEmpty(processedItem))\n            return;\n        const { index, key, level, parentKey, items, item } = processedItem;\n        const grouped = ObjectUtils.isNotEmpty(items);\n        const activeItemPath = this.activeItemPath().filter((p) => p.parentKey !== parentKey && p.parentKey !== key);\n        grouped && activeItemPath.push(processedItem);\n        this.focusedItemInfo.set({ index, level, parentKey, item });\n        this.activeItemPath.set(activeItemPath);\n        grouped && (this.dirty = true);\n        isFocus && DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n    }\n    onMenuFocus(event) {\n        this.focused = true;\n        const focusedItemInfo = this.focusedItemInfo().index !== -1 ? this.focusedItemInfo() : { index: this.findFirstFocusedItemIndex(), level: 0, parentKey: '', item: this.visibleItems[this.findFirstFocusedItemIndex()]?.item };\n        this.focusedItemInfo.set(focusedItemInfo);\n    }\n    onMenuBlur(event) {\n        this.focused = false;\n        this.focusedItemInfo.set({ index: -1, level: 0, parentKey: '', item: null });\n        this.searchValue = '';\n        this.dirty = false;\n    }\n    onOverlayAnimationStart(event) {\n        switch (event.toState) {\n            case 'visible':\n                if (this.popup) {\n                    this.container = event.element;\n                    this.moveOnTop();\n                    this.onShow.emit({});\n                    this.appendOverlay();\n                    this.alignOverlay();\n                    this.bindOutsideClickListener();\n                    this.bindResizeListener();\n                    this.bindScrollListener();\n                    DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n                    this.scrollInView();\n                }\n                break;\n            case 'void':\n                this.onOverlayHide();\n                this.onHide.emit({});\n                break;\n        }\n    }\n    alignOverlay() {\n        if (this.relativeAlign)\n            DomHandler.relativePosition(this.container, this.target);\n        else\n            DomHandler.absolutePosition(this.container, this.target);\n    }\n    onOverlayAnimationEnd(event) {\n        switch (event.toState) {\n            case 'void':\n                ZIndexUtils.clear(event.element);\n                break;\n        }\n    }\n    appendOverlay() {\n        if (this.appendTo) {\n            if (this.appendTo === 'body')\n                this.renderer.appendChild(this.document.body, this.container);\n            else\n                DomHandler.appendChild(this.container, this.appendTo);\n        }\n    }\n    restoreOverlayAppend() {\n        if (this.container && this.appendTo) {\n            this.renderer.appendChild(this.el.nativeElement, this.container);\n        }\n    }\n    moveOnTop() {\n        if (this.autoZIndex) {\n            ZIndexUtils.set('menu', this.container, this.baseZIndex + this.config.zIndex.menu);\n        }\n    }\n    /**\n     * Hides the popup menu.\n     * @group Method\n     */\n    hide(event, isFocus) {\n        if (this.popup) {\n            this.onHide.emit({});\n            this.visible = false;\n        }\n        this.activeItemPath.set([]);\n        this.focusedItemInfo.set({ index: -1, level: 0, parentKey: '' });\n        isFocus && DomHandler.focus(this.relatedTarget || this.target || this.rootmenu.sublistViewChild.nativeElement);\n        this.dirty = false;\n    }\n    /**\n     * Toggles the visibility of the popup menu.\n     * @param {Event} event - Browser event.\n     * @group Method\n     */\n    toggle(event) {\n        this.visible ? this.hide(event, true) : this.show(event);\n    }\n    /**\n     * Displays the popup menu.\n     * @param {Event} even - Browser event.\n     * @group Method\n     */\n    show(event, isFocus) {\n        if (this.popup) {\n            this.visible = true;\n            this.target = this.target || event.currentTarget;\n            this.relatedTarget = event.relatedTarget || null;\n            this.relativeAlign = event?.relativeAlign || null;\n        }\n        this.focusedItemInfo.set({ index: this.findFirstFocusedItemIndex(), level: 0, parentKey: '' });\n        isFocus && DomHandler.focus(this.rootmenu.sublistViewChild.nativeElement);\n        this.cd.markForCheck();\n    }\n    searchItems(event, char) {\n        this.searchValue = (this.searchValue || '') + char;\n        let itemIndex = -1;\n        let matched = false;\n        if (this.focusedItemInfo().index !== -1) {\n            itemIndex = this.visibleItems.slice(this.focusedItemInfo().index).findIndex((processedItem) => this.isItemMatched(processedItem));\n            itemIndex = itemIndex === -1 ? this.visibleItems.slice(0, this.focusedItemInfo().index).findIndex((processedItem) => this.isItemMatched(processedItem)) : itemIndex + this.focusedItemInfo().index;\n        }\n        else {\n            itemIndex = this.visibleItems.findIndex((processedItem) => this.isItemMatched(processedItem));\n        }\n        if (itemIndex !== -1) {\n            matched = true;\n        }\n        if (itemIndex === -1 && this.focusedItemInfo().index === -1) {\n            itemIndex = this.findFirstFocusedItemIndex();\n        }\n        if (itemIndex !== -1) {\n            this.changeFocusedItemIndex(event, itemIndex);\n        }\n        if (this.searchTimeout) {\n            clearTimeout(this.searchTimeout);\n        }\n        this.searchTimeout = setTimeout(() => {\n            this.searchValue = '';\n            this.searchTimeout = null;\n        }, 500);\n        return matched;\n    }\n    findLastFocusedItemIndex() {\n        const selectedIndex = this.findSelectedItemIndex();\n        return selectedIndex < 0 ? this.findLastItemIndex() : selectedIndex;\n    }\n    findLastItemIndex() {\n        return ObjectUtils.findLastIndex(this.visibleItems, (processedItem) => this.isValidItem(processedItem));\n    }\n    findPrevItemIndex(index) {\n        const matchedItemIndex = index > 0 ? ObjectUtils.findLastIndex(this.visibleItems.slice(0, index), (processedItem) => this.isValidItem(processedItem)) : -1;\n        return matchedItemIndex > -1 ? matchedItemIndex : index;\n    }\n    findNextItemIndex(index) {\n        const matchedItemIndex = index < this.visibleItems.length - 1 ? this.visibleItems.slice(index + 1).findIndex((processedItem) => this.isValidItem(processedItem)) : -1;\n        return matchedItemIndex > -1 ? matchedItemIndex + index + 1 : index;\n    }\n    findFirstFocusedItemIndex() {\n        const selectedIndex = this.findSelectedItemIndex();\n        return selectedIndex < 0 ? this.findFirstItemIndex() : selectedIndex;\n    }\n    findFirstItemIndex() {\n        return this.visibleItems.findIndex((processedItem) => this.isValidItem(processedItem));\n    }\n    findSelectedItemIndex() {\n        return this.visibleItems.findIndex((processedItem) => this.isValidSelectedItem(processedItem));\n    }\n    changeFocusedItemIndex(event, index) {\n        if (this.focusedItemInfo().index !== index) {\n            const focusedItemInfo = this.focusedItemInfo();\n            this.focusedItemInfo.set({ ...focusedItemInfo, item: this.visibleItems[index].item, index });\n            this.scrollInView();\n        }\n    }\n    scrollInView(index = -1) {\n        const id = index !== -1 ? `${this.id}_${index}` : this.focusedItemId;\n        const element = DomHandler.findSingle(this.rootmenu.el.nativeElement, `li[id=\"${id}\"]`);\n        if (element) {\n            element.scrollIntoView && element.scrollIntoView({ block: 'nearest', inline: 'nearest' });\n        }\n    }\n    bindScrollListener() {\n        if (!this.scrollHandler) {\n            this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, (event) => {\n                if (this.visible) {\n                    this.hide(event, true);\n                }\n            });\n        }\n        this.scrollHandler.bindScrollListener();\n    }\n    unbindScrollListener() {\n        if (this.scrollHandler) {\n            this.scrollHandler.unbindScrollListener();\n            this.scrollHandler = null;\n        }\n    }\n    bindResizeListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.resizeListener) {\n                this.resizeListener = this.renderer.listen(this.document.defaultView, 'resize', (event) => {\n                    if (!DomHandler.isTouchDevice()) {\n                        this.hide(event, true);\n                    }\n                });\n            }\n        }\n    }\n    bindOutsideClickListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.outsideClickListener) {\n                this.outsideClickListener = this.renderer.listen(this.document, 'click', (event) => {\n                    const isOutsideContainer = this.containerViewChild && !this.containerViewChild.nativeElement.contains(event.target);\n                    const isOutsideTarget = this.popup ? !(this.target && (this.target === event.target || this.target.contains(event.target))) : true;\n                    if (isOutsideContainer && isOutsideTarget) {\n                        this.hide();\n                    }\n                });\n            }\n        }\n    }\n    unbindOutsideClickListener() {\n        if (this.outsideClickListener) {\n            document.removeEventListener('click', this.outsideClickListener);\n            this.outsideClickListener = null;\n        }\n    }\n    unbindResizeListener() {\n        if (this.resizeListener) {\n            this.resizeListener();\n            this.resizeListener = null;\n        }\n    }\n    onOverlayHide() {\n        this.unbindOutsideClickListener();\n        this.unbindResizeListener();\n        this.unbindScrollListener();\n        if (!this.cd.destroyed) {\n            this.target = null;\n        }\n    }\n    ngOnDestroy() {\n        if (this.popup) {\n            if (this.scrollHandler) {\n                this.scrollHandler.destroy();\n                this.scrollHandler = null;\n            }\n            if (this.container && this.autoZIndex) {\n                ZIndexUtils.clear(this.container);\n            }\n            this.restoreOverlayAppend();\n            this.onOverlayHide();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: TieredMenu, deps: [{ token: DOCUMENT }, { token: PLATFORM_ID }, { token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: i5.PrimeNGConfig }, { token: i5.OverlayService }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.5\", type: TieredMenu, selector: \"p-tieredMenu\", inputs: { model: \"model\", popup: \"popup\", style: \"style\", styleClass: \"styleClass\", appendTo: \"appendTo\", autoZIndex: \"autoZIndex\", baseZIndex: \"baseZIndex\", autoDisplay: \"autoDisplay\", showTransitionOptions: \"showTransitionOptions\", hideTransitionOptions: \"hideTransitionOptions\", id: \"id\", ariaLabel: \"ariaLabel\", ariaLabelledBy: \"ariaLabelledBy\", disabled: \"disabled\", tabindex: \"tabindex\" }, outputs: { onShow: \"onShow\", onHide: \"onHide\" }, host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"rootmenu\", first: true, predicate: [\"rootmenu\"], descendants: true }, { propertyName: \"containerViewChild\", first: true, predicate: [\"container\"], descendants: true }], ngImport: i0, template: `\n        <div\n            #container\n            [attr.data-pc-section]=\"'root'\"\n            [attr.data-pc-name]=\"'tieredmenu'\"\n            [id]=\"id\"\n            [ngClass]=\"{ 'p-tieredmenu p-component': true, 'p-tieredmenu-overlay': popup }\"\n            [class]=\"styleClass\"\n            [ngStyle]=\"style\"\n            (click)=\"onOverlayClick($event)\"\n            [@overlayAnimation]=\"{ value: 'visible', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions } }\"\n            [@.disabled]=\"popup !== true\"\n            (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\"\n            (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\"\n            *ngIf=\"!popup || visible\"\n        >\n            <p-tieredMenuSub\n                #rootmenu\n                [root]=\"true\"\n                [items]=\"processedItems\"\n                [itemTemplate]=\"itemTemplate\"\n                [menuId]=\"id\"\n                [tabindex]=\"!disabled ? tabindex : -1\"\n                [ariaLabel]=\"ariaLabel\"\n                [ariaLabelledBy]=\"ariaLabelledBy\"\n                [baseZIndex]=\"baseZIndex\"\n                [autoZIndex]=\"autoZIndex\"\n                [autoDisplay]=\"autoDisplay\"\n                [popup]=\"popup\"\n                [focusedItemId]=\"focused ? focusedItemId : undefined\"\n                [activeItemPath]=\"activeItemPath()\"\n                (itemClick)=\"onItemClick($event)\"\n                (menuFocus)=\"onMenuFocus($event)\"\n                (menuBlur)=\"onMenuBlur($event)\"\n                (menuKeydown)=\"onKeyDown($event)\"\n                (itemMouseEnter)=\"onItemMouseEnter($event)\"\n            ></p-tieredMenuSub>\n        </div>\n    `, isInline: true, styles: [\"@layer primeng{.p-tieredmenu-overlay{position:absolute;top:0;left:0}.p-tieredmenu ul{margin:0;padding:0;list-style:none}.p-tieredmenu .p-submenu-list{position:absolute;min-width:100%;z-index:1;display:none}.p-tieredmenu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-tieredmenu .p-menuitem-text{line-height:1}.p-tieredmenu .p-menuitem{position:relative}.p-tieredmenu .p-menuitem-link .p-submenu-icon:not(svg){margin-left:auto}.p-tieredmenu .p-menuitem-link .p-icon-wrapper{margin-left:auto}.p-tieredmenu .p-menuitem-active>p-tieredmenusub>.p-submenu-list{display:block;left:100%;top:0}.p-tieredmenu .p-menuitem-active>p-tieredmenusub>.p-submenu-list.p-submenu-list-flipped{left:-100%}}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"component\", type: TieredMenuSub, selector: \"p-tieredMenuSub\", inputs: [\"items\", \"itemTemplate\", \"root\", \"autoDisplay\", \"autoZIndex\", \"baseZIndex\", \"popup\", \"menuId\", \"ariaLabel\", \"ariaLabelledBy\", \"level\", \"focusedItemId\", \"activeItemPath\", \"tabindex\"], outputs: [\"itemClick\", \"itemMouseEnter\", \"menuFocus\", \"menuBlur\", \"menuKeydown\"] }], animations: [trigger('overlayAnimation', [transition(':enter', [style({ opacity: 0, transform: 'scaleY(0.8)' }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({ opacity: 0 }))])])], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: TieredMenu, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-tieredMenu', template: `\n        <div\n            #container\n            [attr.data-pc-section]=\"'root'\"\n            [attr.data-pc-name]=\"'tieredmenu'\"\n            [id]=\"id\"\n            [ngClass]=\"{ 'p-tieredmenu p-component': true, 'p-tieredmenu-overlay': popup }\"\n            [class]=\"styleClass\"\n            [ngStyle]=\"style\"\n            (click)=\"onOverlayClick($event)\"\n            [@overlayAnimation]=\"{ value: 'visible', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions } }\"\n            [@.disabled]=\"popup !== true\"\n            (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\"\n            (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\"\n            *ngIf=\"!popup || visible\"\n        >\n            <p-tieredMenuSub\n                #rootmenu\n                [root]=\"true\"\n                [items]=\"processedItems\"\n                [itemTemplate]=\"itemTemplate\"\n                [menuId]=\"id\"\n                [tabindex]=\"!disabled ? tabindex : -1\"\n                [ariaLabel]=\"ariaLabel\"\n                [ariaLabelledBy]=\"ariaLabelledBy\"\n                [baseZIndex]=\"baseZIndex\"\n                [autoZIndex]=\"autoZIndex\"\n                [autoDisplay]=\"autoDisplay\"\n                [popup]=\"popup\"\n                [focusedItemId]=\"focused ? focusedItemId : undefined\"\n                [activeItemPath]=\"activeItemPath()\"\n                (itemClick)=\"onItemClick($event)\"\n                (menuFocus)=\"onMenuFocus($event)\"\n                (menuBlur)=\"onMenuBlur($event)\"\n                (menuKeydown)=\"onKeyDown($event)\"\n                (itemMouseEnter)=\"onItemMouseEnter($event)\"\n            ></p-tieredMenuSub>\n        </div>\n    `, animations: [trigger('overlayAnimation', [transition(':enter', [style({ opacity: 0, transform: 'scaleY(0.8)' }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({ opacity: 0 }))])])], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        class: 'p-element'\n                    }, styles: [\"@layer primeng{.p-tieredmenu-overlay{position:absolute;top:0;left:0}.p-tieredmenu ul{margin:0;padding:0;list-style:none}.p-tieredmenu .p-submenu-list{position:absolute;min-width:100%;z-index:1;display:none}.p-tieredmenu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-tieredmenu .p-menuitem-text{line-height:1}.p-tieredmenu .p-menuitem{position:relative}.p-tieredmenu .p-menuitem-link .p-submenu-icon:not(svg){margin-left:auto}.p-tieredmenu .p-menuitem-link .p-icon-wrapper{margin-left:auto}.p-tieredmenu .p-menuitem-active>p-tieredmenusub>.p-submenu-list{display:block;left:100%;top:0}.p-tieredmenu .p-menuitem-active>p-tieredmenusub>.p-submenu-list.p-submenu-list-flipped{left:-100%}}\\n\"] }]\n        }], ctorParameters: () => [{ type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: i5.PrimeNGConfig }, { type: i5.OverlayService }], propDecorators: { model: [{\n                type: Input\n            }], popup: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], appendTo: [{\n                type: Input\n            }], autoZIndex: [{\n                type: Input\n            }], baseZIndex: [{\n                type: Input\n            }], autoDisplay: [{\n                type: Input\n            }], showTransitionOptions: [{\n                type: Input\n            }], hideTransitionOptions: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input\n            }], ariaLabelledBy: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], tabindex: [{\n                type: Input\n            }], onShow: [{\n                type: Output\n            }], onHide: [{\n                type: Output\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }], rootmenu: [{\n                type: ViewChild,\n                args: ['rootmenu']\n            }], containerViewChild: [{\n                type: ViewChild,\n                args: ['container']\n            }] } });\nclass TieredMenuModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: TieredMenuModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.5\", ngImport: i0, type: TieredMenuModule, declarations: [TieredMenu, TieredMenuSub], imports: [CommonModule, RouterModule, RippleModule, TooltipModule, AngleRightIcon, SharedModule], exports: [TieredMenu, RouterModule, TooltipModule, SharedModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: TieredMenuModule, imports: [CommonModule, RouterModule, RippleModule, TooltipModule, AngleRightIcon, SharedModule, RouterModule, TooltipModule, SharedModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: TieredMenuModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, RouterModule, RippleModule, TooltipModule, AngleRightIcon, SharedModule],\n                    exports: [TieredMenu, RouterModule, TooltipModule, SharedModule],\n                    declarations: [TieredMenu, TieredMenuSub]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TieredMenu, TieredMenuModule, TieredMenuSub };\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,OAAO,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AACzE,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,iBAAiB,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AAC3E,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,UAAU,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,MAAM,EAAEC,MAAM,EAAEC,WAAW,EAAEC,uBAAuB,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AACzM,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,aAAa;AACjC,SAASC,aAAa,EAAEC,YAAY,QAAQ,aAAa;AACzD,SAASC,UAAU,EAAEC,6BAA6B,QAAQ,aAAa;AACvE,SAASC,cAAc,QAAQ,0BAA0B;AACzD,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,WAAW,EAAEC,iBAAiB,EAAEC,WAAW,QAAQ,eAAe;AAAC,MAAAC,GAAA;AAAA,SAAAC,0CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA2GiBhC,EAAE,CAAAkC,SAAA,WAyB1E,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,gBAAA,GAzBuEnC,EAAE,CAAAoC,aAAA,GAAAC,SAAA;IAAA,MAAAC,MAAA,GAAFtC,EAAE,CAAAoC,aAAA;IAAFpC,EAAE,CAAAuC,UAAA,CAAAD,MAAA,CAAAE,WAAA,CAAAL,gBAAA,UAqB/B,CAAC;IArB4BnC,EAAE,CAAAyC,UAAA,YAAAH,MAAA,CAAAI,qBAAA,CAAAP,gBAAA,CAsB5B,CAAC;IAtByBnC,EAAE,CAAA2C,WAAA,OAAAL,MAAA,CAAAM,SAAA,CAAAT,gBAAA,CAoBxC,CAAC,+BAAD,CAAC;EAAA;AAAA;AAAA,SAAAU,oEAAAb,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApBqChC,EAAE,CAAAkC,SAAA,cAsEzD,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,gBAAA,GAtEsDnC,EAAE,CAAAoC,aAAA,IAAAC,SAAA;IAAA,MAAAS,OAAA,GAAF9C,EAAE,CAAAoC,aAAA;IAAFpC,EAAE,CAAAyC,UAAA,YAAAK,OAAA,CAAAN,WAAA,CAAAL,gBAAA,SAgEd,CAAC,YAAAW,OAAA,CAAAN,WAAA,CAAAL,gBAAA,cAAD,CAAC;IAhEWnC,EAAE,CAAA2C,WAAA,0BAkE7B,CAAC,oBAAD,CAAC,eAAD,CAAC;EAAA;AAAA;AAAA,SAAAI,oEAAAf,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlE0BhC,EAAE,CAAAgD,cAAA,cAuE4D,CAAC;IAvE/DhD,EAAE,CAAAiD,MAAA,EAyEhE,CAAC;IAzE6DjD,EAAE,CAAAkD,YAAA,CAyEzD,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAG,gBAAA,GAzEsDnC,EAAE,CAAAoC,aAAA,IAAAC,SAAA;IAAA,MAAAc,OAAA,GAAFnD,EAAE,CAAAoC,aAAA;IAAFpC,EAAE,CAAA2C,WAAA,2BAuE2D,CAAC;IAvE9D3C,EAAE,CAAAoD,SAAA,EAyEhE,CAAC;IAzE6DpD,EAAE,CAAAqD,kBAAA,MAAAF,OAAA,CAAAG,YAAA,CAAAnB,gBAAA,MAyEhE,CAAC;EAAA;AAAA;AAAA,SAAAoB,2EAAAvB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzE6DhC,EAAE,CAAAkC,SAAA,cA2EoD,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,gBAAA,GA3EvDnC,EAAE,CAAAoC,aAAA,IAAAC,SAAA;IAAA,MAAAmB,OAAA,GAAFxD,EAAE,CAAAoC,aAAA;IAAFpC,EAAE,CAAAyC,UAAA,cAAAe,OAAA,CAAAF,YAAA,CAAAnB,gBAAA,GAAFnC,EAAE,CAAAyD,cA2EW,CAAC;IA3EdzD,EAAE,CAAA2C,WAAA,2BA2E4C,CAAC;EAAA;AAAA;AAAA,SAAAe,oEAAA1B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3E/ChC,EAAE,CAAAgD,cAAA,cA6EqE,CAAC;IA7ExEhD,EAAE,CAAAiD,MAAA,EA6E8G,CAAC;IA7EjHjD,EAAE,CAAAkD,YAAA,CA6EqH,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAG,gBAAA,GA7ExHnC,EAAE,CAAAoC,aAAA,IAAAC,SAAA;IAAA,MAAAsB,OAAA,GAAF3D,EAAE,CAAAoC,aAAA;IAAFpC,EAAE,CAAAyC,UAAA,YAAAkB,OAAA,CAAAnB,WAAA,CAAAL,gBAAA,oBA6EoE,CAAC;IA7EvEnC,EAAE,CAAAoD,SAAA,EA6E8G,CAAC;IA7EjHpD,EAAE,CAAA4D,iBAAA,CAAAD,OAAA,CAAAnB,WAAA,CAAAL,gBAAA,UA6E8G,CAAC;EAAA;AAAA;AAAA,SAAA0B,6FAAA7B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7EjHhC,EAAE,CAAAkC,SAAA,wBAgF+F,CAAC;EAAA;EAAA,IAAAF,EAAA;IAhFlGhC,EAAE,CAAAyC,UAAA,+BAgF2B,CAAC;IAhF9BzC,EAAE,CAAA2C,WAAA,iCAgFkE,CAAC,oBAAD,CAAC;EAAA;AAAA;AAAA,SAAAmB,4FAAA9B,EAAA,EAAAC,GAAA;AAAA,SAAA8B,8EAAA/B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhFrEhC,EAAE,CAAAgE,UAAA,IAAAF,2FAAA,qBAiFmF,CAAC;EAAA;EAAA,IAAA9B,EAAA;IAjFtFhC,EAAE,CAAAyC,UAAA,iCAiF0C,CAAC,oBAAD,CAAC;EAAA;AAAA;AAAA,SAAAwB,4EAAAjC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjF7ChC,EAAE,CAAAkE,uBAAA,EA+Ef,CAAC;IA/EYlE,EAAE,CAAAgE,UAAA,IAAAH,4FAAA,4BAgF+F,CAAC,IAAAE,6EAAA,gBAAD,CAAC;IAhFlG/D,EAAE,CAAAmE,qBAAA,CAkFjD,CAAC;EAAA;EAAA,IAAAnC,EAAA;IAAA,MAAAoC,OAAA,GAlF8CpE,EAAE,CAAAoC,aAAA;IAAFpC,EAAE,CAAAoD,SAAA,EAgFN,CAAC;IAhFGpD,EAAE,CAAAyC,UAAA,UAAA2B,OAAA,CAAAC,UAAA,CAAAC,mBAgFN,CAAC;IAhFGtE,EAAE,CAAAoD,SAAA,EAiFE,CAAC;IAjFLpD,EAAE,CAAAyC,UAAA,qBAAA2B,OAAA,CAAAC,UAAA,CAAAC,mBAiFE,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA,GAAAC,EAAA;EAAA;EAAA,cAAAA;AAAA;AAAA,SAAAC,6DAAAzC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjFLhC,EAAE,CAAAgD,cAAA,WA4DnE,CAAC;IA5DgEhD,EAAE,CAAAgE,UAAA,IAAAnB,mEAAA,kBAsEzD,CAAC,IAAAE,mEAAA,kBAAD,CAAC,IAAAQ,0EAAA,iCAtEsDvD,EAAE,CAAA0E,sBAsEzD,CAAC,IAAAhB,mEAAA,kBAAD,CAAC,IAAAO,2EAAA,yBAAD,CAAC;IAtEsDjE,EAAE,CAAAkD,YAAA,CAmFhE,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAA2C,IAAA,GAnF6D3E,EAAE,CAAA4E,WAAA;IAAA,MAAAzC,gBAAA,GAAFnC,EAAE,CAAAoC,aAAA,IAAAC,SAAA;IAAA,MAAAwC,OAAA,GAAF7E,EAAE,CAAAoC,aAAA;IAAFpC,EAAE,CAAAyC,UAAA,WAAAoC,OAAA,CAAArC,WAAA,CAAAL,gBAAA,WAwDjB,CAAC,YAxDcnC,EAAE,CAAA8E,eAAA,KAAAP,GAAA,EAAAM,OAAA,CAAArC,WAAA,CAAAL,gBAAA,cAwDjB,CAAC;IAxDcnC,EAAE,CAAA2C,WAAA,SAAAkC,OAAA,CAAArC,WAAA,CAAAL,gBAAA,UAAFnC,EAAE,CAAA+E,aAoDjB,CAAC,oBAAD,CAAC,sBAAAF,OAAA,CAAArC,WAAA,CAAAL,gBAAA,iBAAD,CAAC,4BAAD,CAAC,eAAD,CAAC;IApDcnC,EAAE,CAAAoD,SAAA,EA8DnB,CAAC;IA9DgBpD,EAAE,CAAAyC,UAAA,SAAAoC,OAAA,CAAArC,WAAA,CAAAL,gBAAA,SA8DnB,CAAC;IA9DgBnC,EAAE,CAAAoD,SAAA,EAuEb,CAAC;IAvEUpD,EAAE,CAAAyC,UAAA,SAAAoC,OAAA,CAAArC,WAAA,CAAAL,gBAAA,WAuEb,CAAC,aAAAwC,IAAD,CAAC;IAvEU3E,EAAE,CAAAoD,SAAA,EA6ES,CAAC;IA7EZpD,EAAE,CAAAyC,UAAA,SAAAoC,OAAA,CAAArC,WAAA,CAAAL,gBAAA,UA6ES,CAAC;IA7EZnC,EAAE,CAAAoD,SAAA,EA+EjB,CAAC;IA/EcpD,EAAE,CAAAyC,UAAA,SAAAoC,OAAA,CAAAG,WAAA,CAAA7C,gBAAA,CA+EjB,CAAC;EAAA;AAAA;AAAA,SAAA8C,oEAAAjD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/EchC,EAAE,CAAAkC,SAAA,cAiHzD,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,gBAAA,GAjHsDnC,EAAE,CAAAoC,aAAA,IAAAC,SAAA;IAAA,MAAA6C,OAAA,GAAFlF,EAAE,CAAAoC,aAAA;IAAFpC,EAAE,CAAAyC,UAAA,YAAAyC,OAAA,CAAA1C,WAAA,CAAAL,gBAAA,SA2Gd,CAAC,YAAA+C,OAAA,CAAA1C,WAAA,CAAAL,gBAAA,cAAD,CAAC;IA3GWnC,EAAE,CAAA2C,WAAA,0BA6G7B,CAAC,oBAAD,CAAC,eAAD,CAAC;EAAA;AAAA;AAAA,SAAAwC,oEAAAnD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7G0BhC,EAAE,CAAAgD,cAAA,cAkH4D,CAAC;IAlH/DhD,EAAE,CAAAiD,MAAA,EAoHhE,CAAC;IApH6DjD,EAAE,CAAAkD,YAAA,CAoHzD,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAG,gBAAA,GApHsDnC,EAAE,CAAAoC,aAAA,IAAAC,SAAA;IAAA,MAAA+C,OAAA,GAAFpF,EAAE,CAAAoC,aAAA;IAAFpC,EAAE,CAAA2C,WAAA,2BAkH2D,CAAC;IAlH9D3C,EAAE,CAAAoD,SAAA,EAoHhE,CAAC;IApH6DpD,EAAE,CAAAqD,kBAAA,MAAA+B,OAAA,CAAA9B,YAAA,CAAAnB,gBAAA,MAoHhE,CAAC;EAAA;AAAA;AAAA,SAAAkD,2EAAArD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApH6DhC,EAAE,CAAAkC,SAAA,cAsHoD,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,gBAAA,GAtHvDnC,EAAE,CAAAoC,aAAA,IAAAC,SAAA;IAAA,MAAAiD,OAAA,GAAFtF,EAAE,CAAAoC,aAAA;IAAFpC,EAAE,CAAAyC,UAAA,cAAA6C,OAAA,CAAAhC,YAAA,CAAAnB,gBAAA,GAAFnC,EAAE,CAAAyD,cAsHW,CAAC;IAtHdzD,EAAE,CAAA2C,WAAA,2BAsH4C,CAAC;EAAA;AAAA;AAAA,SAAA4C,oEAAAvD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtH/ChC,EAAE,CAAAgD,cAAA,cAwHqE,CAAC;IAxHxEhD,EAAE,CAAAiD,MAAA,EAwH8G,CAAC;IAxHjHjD,EAAE,CAAAkD,YAAA,CAwHqH,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAG,gBAAA,GAxHxHnC,EAAE,CAAAoC,aAAA,IAAAC,SAAA;IAAA,MAAAmD,OAAA,GAAFxF,EAAE,CAAAoC,aAAA;IAAFpC,EAAE,CAAAyC,UAAA,YAAA+C,OAAA,CAAAhD,WAAA,CAAAL,gBAAA,oBAwHoE,CAAC;IAxHvEnC,EAAE,CAAAoD,SAAA,EAwH8G,CAAC;IAxHjHpD,EAAE,CAAA4D,iBAAA,CAAA4B,OAAA,CAAAhD,WAAA,CAAAL,gBAAA,UAwH8G,CAAC;EAAA;AAAA;AAAA,SAAAsD,6FAAAzD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxHjHhC,EAAE,CAAAkC,SAAA,wBA2H+F,CAAC;EAAA;EAAA,IAAAF,EAAA;IA3HlGhC,EAAE,CAAAyC,UAAA,+BA2H2B,CAAC;IA3H9BzC,EAAE,CAAA2C,WAAA,iCA2HkE,CAAC,oBAAD,CAAC;EAAA;AAAA;AAAA,SAAA+C,4FAAA1D,EAAA,EAAAC,GAAA;AAAA,SAAA0D,8EAAA3D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3HrEhC,EAAE,CAAAgE,UAAA,IAAA0B,2FAAA,qBA4HmF,CAAC;EAAA;EAAA,IAAA1D,EAAA;IA5HtFhC,EAAE,CAAAyC,UAAA,iCA4H0C,CAAC,oBAAD,CAAC;EAAA;AAAA;AAAA,SAAAmD,4EAAA5D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5H7ChC,EAAE,CAAAkE,uBAAA,EA0Hf,CAAC;IA1HYlE,EAAE,CAAAgE,UAAA,IAAAyB,4FAAA,4BA2H+F,CAAC,IAAAE,6EAAA,gBAAD,CAAC;IA3HlG3F,EAAE,CAAAmE,qBAAA,CA6HjD,CAAC;EAAA;EAAA,IAAAnC,EAAA;IAAA,MAAA6D,OAAA,GA7H8C7F,EAAE,CAAAoC,aAAA;IAAFpC,EAAE,CAAAoD,SAAA,EA2HN,CAAC;IA3HGpD,EAAE,CAAAyC,UAAA,UAAAoD,OAAA,CAAAxB,UAAA,CAAAC,mBA2HN,CAAC;IA3HGtE,EAAE,CAAAoD,SAAA,EA4HE,CAAC;IA5HLpD,EAAE,CAAAyC,UAAA,qBAAAoD,OAAA,CAAAxB,UAAA,CAAAC,mBA4HE,CAAC;EAAA;AAAA;AAAA,MAAAwB,GAAA,GAAAA,CAAA;EAAAC,KAAA;AAAA;AAAA,SAAAC,6DAAAhE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5HLhC,EAAE,CAAAgD,cAAA,WAuGnE,CAAC;IAvGgEhD,EAAE,CAAAgE,UAAA,IAAAiB,mEAAA,kBAiHzD,CAAC,IAAAE,mEAAA,kBAAD,CAAC,IAAAE,0EAAA,iCAjHsDrF,EAAE,CAAA0E,sBAiHzD,CAAC,IAAAa,mEAAA,kBAAD,CAAC,IAAAK,2EAAA,yBAAD,CAAC;IAjHsD5F,EAAE,CAAAkD,YAAA,CA8HhE,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAiE,IAAA,GA9H6DjG,EAAE,CAAA4E,WAAA;IAAA,MAAAzC,gBAAA,GAAFnC,EAAE,CAAAoC,aAAA,IAAAC,SAAA;IAAA,MAAA6D,OAAA,GAAFlG,EAAE,CAAAoC,aAAA;IAAFpC,EAAE,CAAAyC,UAAA,eAAAyD,OAAA,CAAA1D,WAAA,CAAAL,gBAAA,eAsFT,CAAC,gBAAA+D,OAAA,CAAA1D,WAAA,CAAAL,gBAAA,gBAAD,CAAC,6CAAD,CAAC,4BAAA+D,OAAA,CAAA1D,WAAA,CAAAL,gBAAA,gCAtFMnC,EAAE,CAAAmG,eAAA,KAAAL,GAAA,CAsFT,CAAC,WAAAI,OAAA,CAAA1D,WAAA,CAAAL,gBAAA,WAAD,CAAC,YAtFMnC,EAAE,CAAA8E,eAAA,KAAAP,GAAA,EAAA2B,OAAA,CAAA1D,WAAA,CAAAL,gBAAA,cAsFT,CAAC,aAAA+D,OAAA,CAAA1D,WAAA,CAAAL,gBAAA,aAAD,CAAC,wBAAA+D,OAAA,CAAA1D,WAAA,CAAAL,gBAAA,wBAAD,CAAC,qBAAA+D,OAAA,CAAA1D,WAAA,CAAAL,gBAAA,qBAAD,CAAC,uBAAA+D,OAAA,CAAA1D,WAAA,CAAAL,gBAAA,uBAAD,CAAC,eAAA+D,OAAA,CAAA1D,WAAA,CAAAL,gBAAA,eAAD,CAAC,UAAA+D,OAAA,CAAA1D,WAAA,CAAAL,gBAAA,UAAD,CAAC;IAtFMnC,EAAE,CAAA2C,WAAA,sBAAAuD,OAAA,CAAA1D,WAAA,CAAAL,gBAAA,iBAuFK,CAAC,eAAD,CAAC,oBAAD,CAAC,4BAAD,CAAC;IAvFRnC,EAAE,CAAAoD,SAAA,EAyGnB,CAAC;IAzGgBpD,EAAE,CAAAyC,UAAA,SAAAyD,OAAA,CAAA1D,WAAA,CAAAL,gBAAA,SAyGnB,CAAC;IAzGgBnC,EAAE,CAAAoD,SAAA,EAkHb,CAAC;IAlHUpD,EAAE,CAAAyC,UAAA,SAAAyD,OAAA,CAAA1D,WAAA,CAAAL,gBAAA,WAkHb,CAAC,aAAA8D,IAAD,CAAC;IAlHUjG,EAAE,CAAAoD,SAAA,EAwHS,CAAC;IAxHZpD,EAAE,CAAAyC,UAAA,SAAAyD,OAAA,CAAA1D,WAAA,CAAAL,gBAAA,UAwHS,CAAC;IAxHZnC,EAAE,CAAAoD,SAAA,EA0HjB,CAAC;IA1HcpD,EAAE,CAAAyC,UAAA,SAAAyD,OAAA,CAAAlB,WAAA,CAAA7C,gBAAA,CA0HjB,CAAC;EAAA;AAAA;AAAA,SAAAiE,yDAAApE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1HchC,EAAE,CAAAkE,uBAAA,EAiDpC,CAAC;IAjDiClE,EAAE,CAAAgE,UAAA,IAAAS,4DAAA,gBAmFhE,CAAC,IAAAuB,4DAAA,gBAAD,CAAC;IAnF6DhG,EAAE,CAAAmE,qBAAA,CA+HzD,CAAC;EAAA;EAAA,IAAAnC,EAAA;IAAA,MAAAG,gBAAA,GA/HsDnC,EAAE,CAAAoC,aAAA,IAAAC,SAAA;IAAA,MAAAgE,MAAA,GAAFrG,EAAE,CAAAoC,aAAA;IAAFpC,EAAE,CAAAoD,SAAA,EAmDhB,CAAC;IAnDapD,EAAE,CAAAyC,UAAA,UAAA4D,MAAA,CAAA7D,WAAA,CAAAL,gBAAA,eAmDhB,CAAC;IAnDanC,EAAE,CAAAoD,SAAA,EAqFjB,CAAC;IArFcpD,EAAE,CAAAyC,UAAA,SAAA4D,MAAA,CAAA7D,WAAA,CAAAL,gBAAA,eAqFjB,CAAC;EAAA;AAAA;AAAA,SAAAmE,yEAAAtE,EAAA,EAAAC,GAAA;AAAA,SAAAsE,2DAAAvE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArFchC,EAAE,CAAAgE,UAAA,IAAAsC,wEAAA,qBAiIqF,CAAC;EAAA;AAAA;AAAA,MAAAE,GAAA,GAAAA,CAAAC,EAAA,EAAAjC,EAAA;EAAAnC,SAAA,EAAAoE,EAAA;EAAAC,UAAA,EAAAlC;AAAA;AAAA,SAAAmC,yDAAA3E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjIxFhC,EAAE,CAAAkE,uBAAA,EAgIrC,CAAC;IAhIkClE,EAAE,CAAAgE,UAAA,IAAAuC,0DAAA,gBAiIqF,CAAC;IAjIxFvG,EAAE,CAAAmE,qBAAA,CAkIzD,CAAC;EAAA;EAAA,IAAAnC,EAAA;IAAA,MAAAG,gBAAA,GAlIsDnC,EAAE,CAAAoC,aAAA,IAAAC,SAAA;IAAA,MAAAuE,MAAA,GAAF5G,EAAE,CAAAoC,aAAA;IAAFpC,EAAE,CAAAoD,SAAA,EAiItB,CAAC;IAjImBpD,EAAE,CAAAyC,UAAA,qBAAAmE,MAAA,CAAAC,YAiItB,CAAC,4BAjImB7G,EAAE,CAAA8G,eAAA,IAAAN,GAAA,EAAArE,gBAAA,CAAA4E,IAAA,EAAAH,MAAA,CAAApE,WAAA,CAAAL,gBAAA,WAiItB,CAAC;EAAA;AAAA;AAAA,SAAA6E,4DAAAhF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAiF,IAAA,GAjImBjH,EAAE,CAAAkH,gBAAA;IAAFlH,EAAE,CAAAgD,cAAA,yBAgJ3E,CAAC;IAhJwEhD,EAAE,CAAAmH,UAAA,uBAAAC,iGAAAC,MAAA;MAAFrH,EAAE,CAAAsH,aAAA,CAAAL,IAAA;MAAA,MAAAM,OAAA,GAAFvH,EAAE,CAAAoC,aAAA;MAAA,OAAFpC,EAAE,CAAAwH,WAAA,CA8I1DD,OAAA,CAAAE,SAAA,CAAAC,IAAA,CAAAL,MAAqB,EAAC;IAAA,EAAC,4BAAAM,sGAAAN,MAAA;MA9IiCrH,EAAE,CAAAsH,aAAA,CAAAL,IAAA;MAAA,MAAAW,OAAA,GAAF5H,EAAE,CAAAoC,aAAA;MAAA,OAAFpC,EAAE,CAAAwH,WAAA,CA+IrDI,OAAA,CAAAC,gBAAA,CAAAR,MAAuB,EAAC;IAAA,CADP,CAAC;IA9IiCrH,EAAE,CAAAkD,YAAA,CAgJzD,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAG,gBAAA,GAhJsDnC,EAAE,CAAAoC,aAAA,IAAAC,SAAA;IAAA,MAAAyF,OAAA,GAAF9H,EAAE,CAAAoC,aAAA;IAAFpC,EAAE,CAAAyC,UAAA,UAAAN,gBAAA,CAAA4F,KAuI3C,CAAC,iBAAAD,OAAA,CAAAjB,YAAD,CAAC,gBAAAiB,OAAA,CAAAE,WAAD,CAAC,WAAAF,OAAA,CAAAG,MAAD,CAAC,mBAAAH,OAAA,CAAAI,cAAD,CAAC,kBAAAJ,OAAA,CAAAK,aAAD,CAAC,UAAAL,OAAA,CAAAM,KAAA,IAAD,CAAC;EAAA;AAAA;AAAA,SAAAC,0CAAArG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAsG,IAAA,GAvIwCtI,EAAE,CAAAkH,gBAAA;IAAFlH,EAAE,CAAAgD,cAAA,cA+C/E,CAAC,YAAD,CAAC;IA/C4EhD,EAAE,CAAAmH,UAAA,mBAAAoB,+DAAAlB,MAAA;MAAFrH,EAAE,CAAAsH,aAAA,CAAAgB,IAAA;MAAA,MAAAnG,gBAAA,GAAFnC,EAAE,CAAAoC,aAAA,GAAAC,SAAA;MAAA,MAAAmG,OAAA,GAAFxI,EAAE,CAAAoC,aAAA;MAAA,OAAFpC,EAAE,CAAAwH,WAAA,CAgDCgB,OAAA,CAAAC,WAAA,CAAApB,MAAA,EAAAlF,gBAAiC,EAAC;IAAA,EAAC,wBAAAuG,oEAAArB,MAAA;MAhDtCrH,EAAE,CAAAsH,aAAA,CAAAgB,IAAA;MAAA,MAAAnG,gBAAA,GAAFnC,EAAE,CAAAoC,aAAA,GAAAC,SAAA;MAAA,MAAAsG,OAAA,GAAF3I,EAAE,CAAAoC,aAAA;MAAA,OAAFpC,EAAE,CAAAwH,WAAA,CAgDmDmB,OAAA,CAAAd,gBAAA;QAAAR,MAAA,EAAAA,MAAA;QAAAuB,aAAA,EAAAzG;MAAA,CAAwC,EAAC;IAAA,CAAzD,CAAC;IAhDtCnC,EAAE,CAAAgE,UAAA,IAAAoC,wDAAA,yBA+HzD,CAAC,IAAAO,wDAAA,yBAAD,CAAC;IA/HsD3G,EAAE,CAAAkD,YAAA,CAmItE,CAAC;IAnImElD,EAAE,CAAAgE,UAAA,IAAAgD,2DAAA,6BAgJzD,CAAC;IAhJsDhH,EAAE,CAAAkD,YAAA,CAiJ3E,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAA6G,OAAA,GAjJwE7I,EAAE,CAAAoC,aAAA;IAAA,MAAAD,gBAAA,GAAA0G,OAAA,CAAAxG,SAAA;IAAA,MAAAyG,QAAA,GAAAD,OAAA,CAAAE,KAAA;IAAA,MAAAC,MAAA,GAAFhJ,EAAE,CAAAoC,aAAA;IAAFpC,EAAE,CAAAiJ,UAAA,CAAAD,MAAA,CAAAxG,WAAA,CAAAL,gBAAA,eA4C1B,CAAC;IA5CuBnC,EAAE,CAAAyC,UAAA,YAAAuG,MAAA,CAAAxG,WAAA,CAAAL,gBAAA,UA0C7B,CAAC,YAAA6G,MAAA,CAAAE,YAAA,CAAA/G,gBAAA,CAAD,CAAC,mBAAA6G,MAAA,CAAAxG,WAAA,CAAAL,gBAAA,mBAAD,CAAC;IA1C0BnC,EAAE,CAAA2C,WAAA,OAAAqG,MAAA,CAAApG,SAAA,CAAAT,gBAAA,CA8BxC,CAAC,8BAAD,CAAC,qBAAA6G,MAAA,CAAAG,YAAA,CAAAhH,gBAAA,CAAD,CAAC,mBAAA6G,MAAA,CAAAI,aAAA,CAAAjH,gBAAA,CAAD,CAAC,oBAAA6G,MAAA,CAAAK,cAAA,CAAAlH,gBAAA,CAAD,CAAC,eAAA6G,MAAA,CAAA1F,YAAA,CAAAnB,gBAAA,CAAD,CAAC,kBAAA6G,MAAA,CAAAK,cAAA,CAAAlH,gBAAA,KAAAmH,SAAD,CAAC,kBAAAN,MAAA,CAAAhE,WAAA,CAAA7C,gBAAA,MAAA6G,MAAA,CAAAxG,WAAA,CAAAL,gBAAA,mBAAAmH,SAAD,CAAC,kBAAAN,MAAA,CAAAhE,WAAA,CAAA7C,gBAAA,IAAA6G,MAAA,CAAAG,YAAA,CAAAhH,gBAAA,IAAAmH,SAAD,CAAC,eAAAN,MAAA,CAAAZ,KAAA,IAAD,CAAC,iBAAAY,MAAA,CAAAO,cAAA,EAAD,CAAC,kBAAAP,MAAA,CAAAQ,eAAA,CAAAV,QAAA,CAAD,CAAC;IA9BqC9I,EAAE,CAAAoD,SAAA,EAgDrC,CAAC;IAhDkCpD,EAAE,CAAA2C,WAAA,6BAgDrC,CAAC;IAhDkC3C,EAAE,CAAAoD,SAAA,EAiDtC,CAAC;IAjDmCpD,EAAE,CAAAyC,UAAA,UAAAuG,MAAA,CAAAnC,YAiDtC,CAAC;IAjDmC7G,EAAE,CAAAoD,SAAA,EAgIvC,CAAC;IAhIoCpD,EAAE,CAAAyC,UAAA,SAAAuG,MAAA,CAAAnC,YAgIvC,CAAC;IAhIoC7G,EAAE,CAAAoD,SAAA,EAsIP,CAAC;IAtIIpD,EAAE,CAAAyC,UAAA,SAAAuG,MAAA,CAAAS,aAAA,CAAAtH,gBAAA,KAAA6G,MAAA,CAAAhE,WAAA,CAAA7C,gBAAA,CAsIP,CAAC;EAAA;AAAA;AAAA,SAAAuH,qCAAA1H,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtIIhC,EAAE,CAAAgE,UAAA,IAAAjC,yCAAA,eAyB1E,CAAC,IAAAsG,yCAAA,gBAAD,CAAC;EAAA;EAAA,IAAArG,EAAA;IAAA,MAAAG,gBAAA,GAAAF,GAAA,CAAAI,SAAA;IAAA,MAAAsH,MAAA,GAzBuE3J,EAAE,CAAAoC,aAAA;IAAFpC,EAAE,CAAAyC,UAAA,SAAAkH,MAAA,CAAAF,aAAA,CAAAtH,gBAAA,KAAAwH,MAAA,CAAAnH,WAAA,CAAAL,gBAAA,cAmBE,CAAC;IAnBLnC,EAAE,CAAAoD,SAAA,EA4BG,CAAC;IA5BNpD,EAAE,CAAAyC,UAAA,SAAAkH,MAAA,CAAAF,aAAA,CAAAtH,gBAAA,MAAAwH,MAAA,CAAAnH,WAAA,CAAAL,gBAAA,cA4BG,CAAC;EAAA;AAAA;AAAA,MAAAyH,GAAA,GAAAA,CAAAnD,EAAA,EAAAjC,EAAA;EAAA,kBAAAiC,EAAA;EAAA,0BAAAjC;AAAA;AAAA,MAAAqF,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAvF,EAAA;EAAA;EAAA,wBAAAA;AAAA;AAAA,MAAAwF,GAAA,GAAAA,CAAAvD,EAAA,EAAAjC,EAAA;EAAAyF,oBAAA,EAAAxD,EAAA;EAAAyD,oBAAA,EAAA1F;AAAA;AAAA,MAAA2F,GAAA,GAAA3F,EAAA;EAAA4F,KAAA;EAAAC,MAAA,EAAA7F;AAAA;AAAA,SAAA8F,0BAAAtI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAuI,GAAA,GA5BNvK,EAAE,CAAAkH,gBAAA;IAAFlH,EAAE,CAAAgD,cAAA,eAogCvF,CAAC;IApgCoFhD,EAAE,CAAAmH,UAAA,mBAAAqD,+CAAAnD,MAAA;MAAFrH,EAAE,CAAAsH,aAAA,CAAAiD,GAAA;MAAA,MAAAE,MAAA,GAAFzK,EAAE,CAAAoC,aAAA;MAAA,OAAFpC,EAAE,CAAAwH,WAAA,CA8/B1EiD,MAAA,CAAAC,cAAA,CAAArD,MAAqB,EAAC;IAAA,EAAC,qCAAAsD,0EAAAtD,MAAA;MA9/BiDrH,EAAE,CAAAsH,aAAA,CAAAiD,GAAA;MAAA,MAAAvB,MAAA,GAAFhJ,EAAE,CAAAoC,aAAA;MAAA,OAAFpC,EAAE,CAAAwH,WAAA,CAigCxDwB,MAAA,CAAA4B,uBAAA,CAAAvD,MAA8B,EAAC;IAAA,CAH3B,CAAC,oCAAAwD,yEAAAxD,MAAA;MA9/BiDrH,EAAE,CAAAsH,aAAA,CAAAiD,GAAA;MAAA,MAAAO,MAAA,GAAF9K,EAAE,CAAAoC,aAAA;MAAA,OAAFpC,EAAE,CAAAwH,WAAA,CAkgCzDsD,MAAA,CAAAC,qBAAA,CAAA1D,MAA4B,EAAC;IAAA,CAJxB,CAAC;IA9/BiDrH,EAAE,CAAAgD,cAAA,2BAyhCnF,CAAC;IAzhCgFhD,EAAE,CAAAmH,UAAA,uBAAA6D,+DAAA3D,MAAA;MAAFrH,EAAE,CAAAsH,aAAA,CAAAiD,GAAA;MAAA,MAAAU,MAAA,GAAFjL,EAAE,CAAAoC,aAAA;MAAA,OAAFpC,EAAE,CAAAwH,WAAA,CAohClEyD,MAAA,CAAAxC,WAAA,CAAApB,MAAkB,EAAC;IAAA,EAAC,uBAAA6D,+DAAA7D,MAAA;MAphC4CrH,EAAE,CAAAsH,aAAA,CAAAiD,GAAA;MAAA,MAAAlE,MAAA,GAAFrG,EAAE,CAAAoC,aAAA;MAAA,OAAFpC,EAAE,CAAAwH,WAAA,CAqhClEnB,MAAA,CAAA8E,WAAA,CAAA9D,MAAkB,EAAC;IAAA,CADA,CAAC,sBAAA+D,8DAAA/D,MAAA;MAphC4CrH,EAAE,CAAAsH,aAAA,CAAAiD,GAAA;MAAA,MAAA3D,MAAA,GAAF5G,EAAE,CAAAoC,aAAA;MAAA,OAAFpC,EAAE,CAAAwH,WAAA,CAshCnEZ,MAAA,CAAAyE,UAAA,CAAAhE,MAAiB,EAAC;IAAA,CAFE,CAAC,yBAAAiE,iEAAAjE,MAAA;MAphC4CrH,EAAE,CAAAsH,aAAA,CAAAiD,GAAA;MAAA,MAAAzC,OAAA,GAAF9H,EAAE,CAAAoC,aAAA;MAAA,OAAFpC,EAAE,CAAAwH,WAAA,CAuhChEM,OAAA,CAAAyD,SAAA,CAAAlE,MAAgB,EAAC;IAAA,CAHA,CAAC,4BAAAmE,oEAAAnE,MAAA;MAphC4CrH,EAAE,CAAAsH,aAAA,CAAAiD,GAAA;MAAA,MAAA1F,OAAA,GAAF7E,EAAE,CAAAoC,aAAA;MAAA,OAAFpC,EAAE,CAAAwH,WAAA,CAwhC7D3C,OAAA,CAAAgD,gBAAA,CAAAR,MAAuB,EAAC;IAAA,CAJV,CAAC;IAphC4CrH,EAAE,CAAAkD,YAAA,CAyhCjE,CAAC,CAAD,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAyJ,MAAA,GAzhC8DzL,EAAE,CAAAoC,aAAA;IAAFpC,EAAE,CAAAiJ,UAAA,CAAAwC,MAAA,CAAAC,UA4/BhE,CAAC;IA5/B6D1L,EAAE,CAAAyC,UAAA,OAAAgJ,MAAA,CAAAE,EA0/B3E,CAAC,YA1/BwE3L,EAAE,CAAA8E,eAAA,KAAAiF,GAAA,EAAA0B,MAAA,CAAAG,KAAA,CA0/B3E,CAAC,YAAAH,MAAA,CAAAjM,KAAD,CAAC,sBA1/BwEQ,EAAE,CAAA8E,eAAA,KAAAqF,GAAA,EAAFnK,EAAE,CAAA8G,eAAA,KAAAkD,GAAA,EAAAyB,MAAA,CAAAI,qBAAA,EAAAJ,MAAA,CAAAK,qBAAA,EA0/B3E,CAAC,eAAAL,MAAA,CAAAG,KAAA,SAAD,CAAC;IA1/BwE5L,EAAE,CAAA2C,WAAA,0BAw/BrD,CAAC,6BAAD,CAAC;IAx/BkD3C,EAAE,CAAAoD,SAAA,EAugCnE,CAAC;IAvgCgEpD,EAAE,CAAAyC,UAAA,aAugCnE,CAAC,UAAAgJ,MAAA,CAAAM,cAAD,CAAC,iBAAAN,MAAA,CAAA5E,YAAD,CAAC,WAAA4E,MAAA,CAAAE,EAAD,CAAC,cAAAF,MAAA,CAAAO,QAAA,GAAAP,MAAA,CAAAQ,QAAA,KAAD,CAAC,cAAAR,MAAA,CAAAS,SAAD,CAAC,mBAAAT,MAAA,CAAAU,cAAD,CAAC,eAAAV,MAAA,CAAAW,UAAD,CAAC,eAAAX,MAAA,CAAAY,UAAD,CAAC,gBAAAZ,MAAA,CAAAzD,WAAD,CAAC,UAAAyD,MAAA,CAAAG,KAAD,CAAC,kBAAAH,MAAA,CAAAa,OAAA,GAAAb,MAAA,CAAAtD,aAAA,GAAAmB,SAAD,CAAC,mBAAAmC,MAAA,CAAAvD,cAAA,EAAD,CAAC;EAAA;AAAA;AAhnC7B,MAAMqE,aAAa,CAAC;EAChBC,EAAE;EACFC,QAAQ;EACRC,EAAE;EACFrI,UAAU;EACV0D,KAAK;EACLlB,YAAY;EACZ8F,IAAI,GAAG,KAAK;EACZ3E,WAAW;EACXqE,UAAU,GAAG,IAAI;EACjBD,UAAU,GAAG,CAAC;EACdR,KAAK;EACL3D,MAAM;EACNiE,SAAS;EACTC,cAAc;EACd/D,KAAK,GAAG,CAAC;EACTD,aAAa;EACbD,cAAc;EACd+D,QAAQ,GAAG,CAAC;EACZxE,SAAS,GAAG,IAAIxH,YAAY,CAAC,CAAC;EAC9B2M,cAAc,GAAG,IAAI3M,YAAY,CAAC,CAAC;EACnC4M,SAAS,GAAG,IAAI5M,YAAY,CAAC,CAAC;EAC9B6M,QAAQ,GAAG,IAAI7M,YAAY,CAAC,CAAC;EAC7B8M,WAAW,GAAG,IAAI9M,YAAY,CAAC,CAAC;EAChC+M,gBAAgB;EAChBC,WAAWA,CAACT,EAAE,EAAEC,QAAQ,EAAEC,EAAE,EAAErI,UAAU,EAAE;IACtC,IAAI,CAACmI,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACrI,UAAU,GAAGA,UAAU;EAChC;EACA6I,eAAeA,CAAA,EAAG;IACd,IAAIC,OAAO,GAAG,IAAI,CAACH,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACI,aAAa;IAC1E,IAAID,OAAO,EAAE;MACT,MAAME,UAAU,GAAGF,OAAO,CAACG,aAAa,CAACA,aAAa;MACtD,MAAMC,eAAe,GAAGnM,UAAU,CAACoM,SAAS,CAACH,UAAU,CAAC;MACxD,MAAMI,QAAQ,GAAGrM,UAAU,CAACsM,WAAW,CAAC,CAAC;MACzC,MAAMC,YAAY,GAAGR,OAAO,CAACS,YAAY,GAAGT,OAAO,CAACU,WAAW,GAAGzM,UAAU,CAAC0M,0BAA0B,CAACX,OAAO,CAAC;MAChH,MAAMY,cAAc,GAAG3M,UAAU,CAAC4M,aAAa,CAACX,UAAU,CAACY,QAAQ,CAAC,CAAC,CAAC,CAAC;MACvE,IAAIC,QAAQ,CAACX,eAAe,CAACY,IAAI,EAAE,EAAE,CAAC,GAAGJ,cAAc,GAAGJ,YAAY,GAAGF,QAAQ,CAACW,KAAK,GAAGhN,UAAU,CAACiN,uBAAuB,CAAC,CAAC,EAAE;QAC5HjN,UAAU,CAACkN,QAAQ,CAACnB,OAAO,EAAE,wBAAwB,CAAC;MAC1D;IACJ;EACJ;EACA3K,WAAWA,CAACoG,aAAa,EAAE2F,IAAI,EAAElE,MAAM,GAAG,IAAI,EAAE;IAC5C,OAAOzB,aAAa,IAAIA,aAAa,CAAC7B,IAAI,GAAGpF,WAAW,CAAC6M,YAAY,CAAC5F,aAAa,CAAC7B,IAAI,CAACwH,IAAI,CAAC,EAAElE,MAAM,CAAC,GAAGf,SAAS;EACvH;EACA1G,SAASA,CAACgG,aAAa,EAAE;IACrB,OAAOA,aAAa,CAAC7B,IAAI,EAAE4E,EAAE,IAAK,GAAE,IAAI,CAAC1D,MAAO,IAAGW,aAAa,CAAC6F,GAAI,EAAC;EAC1E;EACAC,UAAUA,CAAC9F,aAAa,EAAE;IACtB,OAAO,IAAI,CAAChG,SAAS,CAACgG,aAAa,CAAC;EACxC;EACAM,YAAYA,CAACN,aAAa,EAAE;IACxB,OAAO;MACH,GAAG,IAAI,CAACpG,WAAW,CAACoG,aAAa,EAAE,OAAO,CAAC;MAC3C,YAAY,EAAE,IAAI;MAClB,aAAa,EAAE,IAAI,CAACO,YAAY,CAACP,aAAa,CAAC;MAC/C,mBAAmB,EAAE,IAAI,CAACO,YAAY,CAACP,aAAa,CAAC;MACrD,SAAS,EAAE,IAAI,CAACQ,aAAa,CAACR,aAAa,CAAC;MAC5C,YAAY,EAAE,IAAI,CAACS,cAAc,CAACT,aAAa;IACnD,CAAC;EACL;EACAtF,YAAYA,CAACsF,aAAa,EAAE;IACxB,OAAO,IAAI,CAACpG,WAAW,CAACoG,aAAa,EAAE,OAAO,CAAC;EACnD;EACAlG,qBAAqBA,CAACkG,aAAa,EAAE;IACjC,OAAO;MACH,GAAG,IAAI,CAACpG,WAAW,CAACoG,aAAa,EAAE,OAAO,CAAC;MAC3C,sBAAsB,EAAE;IAC5B,CAAC;EACL;EACAW,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACxB,KAAK,CAAC4G,MAAM,CAAE/F,aAAa,IAAK,IAAI,CAACa,aAAa,CAACb,aAAa,CAAC,IAAI,CAAC,IAAI,CAACpG,WAAW,CAACoG,aAAa,EAAE,WAAW,CAAC,CAAC,CAACgG,MAAM;EAC1I;EACApF,eAAeA,CAACT,KAAK,EAAE;IACnB,OAAOA,KAAK,GAAG,IAAI,CAAChB,KAAK,CAAC8G,KAAK,CAAC,CAAC,EAAE9F,KAAK,CAAC,CAAC4F,MAAM,CAAE/F,aAAa,IAAK,IAAI,CAACa,aAAa,CAACb,aAAa,CAAC,IAAI,IAAI,CAACpG,WAAW,CAACoG,aAAa,EAAE,WAAW,CAAC,CAAC,CAACgG,MAAM,GAAG,CAAC;EACrK;EACAnF,aAAaA,CAACb,aAAa,EAAE;IACzB,OAAO,IAAI,CAACpG,WAAW,CAACoG,aAAa,EAAE,SAAS,CAAC,KAAK,KAAK;EAC/D;EACAO,YAAYA,CAACP,aAAa,EAAE;IACxB,IAAI,IAAI,CAACV,cAAc,EAAE;MACrB,OAAO,IAAI,CAACA,cAAc,CAAC4G,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACN,GAAG,KAAK7F,aAAa,CAAC6F,GAAG,CAAC;IAC7E;EACJ;EACApF,cAAcA,CAACT,aAAa,EAAE;IAC1B,OAAO,IAAI,CAACpG,WAAW,CAACoG,aAAa,EAAE,UAAU,CAAC;EACtD;EACAQ,aAAaA,CAACR,aAAa,EAAE;IACzB,OAAO,IAAI,CAACT,aAAa,KAAK,IAAI,CAACvF,SAAS,CAACgG,aAAa,CAAC;EAC/D;EACA5D,WAAWA,CAAC4D,aAAa,EAAE;IACvB,OAAOjH,WAAW,CAACqN,UAAU,CAACpG,aAAa,CAACb,KAAK,CAAC;EACtD;EACAF,gBAAgBA,CAACoH,KAAK,EAAE;IACpB,IAAI,IAAI,CAACjH,WAAW,EAAE;MAClB,MAAM;QAAEkH,KAAK;QAAEtG;MAAc,CAAC,GAAGqG,KAAK;MACtC,IAAI,CAACrC,cAAc,CAAClF,IAAI,CAAC;QAAEyH,aAAa,EAAED,KAAK;QAAEtG;MAAc,CAAC,CAAC;IACrE;EACJ;EACAH,WAAWA,CAACyG,KAAK,EAAEtG,aAAa,EAAE;IAC9B,IAAI,CAACpG,WAAW,CAACoG,aAAa,EAAE,SAAS,EAAE;MAAEuG,aAAa,EAAED,KAAK;MAAEnI,IAAI,EAAE6B,aAAa,CAAC7B;IAAK,CAAC,CAAC;IAC9F,IAAI,CAACU,SAAS,CAACC,IAAI,CAAC;MAAEyH,aAAa,EAAED,KAAK;MAAEtG,aAAa;MAAEwG,OAAO,EAAE;IAAK,CAAC,CAAC;EAC/E;EACA,OAAOC,IAAI,YAAAC,sBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFhD,aAAa,EAAvBvM,EAAE,CAAAwP,iBAAA,CAAuCxP,EAAE,CAACyP,UAAU,GAAtDzP,EAAE,CAAAwP,iBAAA,CAAiExP,EAAE,CAAC0P,SAAS,GAA/E1P,EAAE,CAAAwP,iBAAA,CAA0FxP,EAAE,CAAC2P,iBAAiB,GAAhH3P,EAAE,CAAAwP,iBAAA,CAA2HtP,UAAU,CAAC,MAAM0P,UAAU,CAAC;EAAA;EAClP,OAAOC,IAAI,kBAD8E7P,EAAE,CAAA8P,iBAAA;IAAAC,IAAA,EACJxD,aAAa;IAAAyD,SAAA;IAAAC,SAAA,WAAAC,oBAAAlO,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADXhC,EAAE,CAAAmQ,WAAA,CAAArO,GAAA;MAAA;MAAA,IAAAE,EAAA;QAAA,IAAAoO,EAAA;QAAFpQ,EAAE,CAAAqQ,cAAA,CAAAD,EAAA,GAAFpQ,EAAE,CAAAsQ,WAAA,QAAArO,GAAA,CAAA+K,gBAAA,GAAAoD,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAC,SAAA;IAAAC,MAAA;MAAA1I,KAAA;MAAAlB,YAAA;MAAA8F,IAAA;MAAA3E,WAAA;MAAAqE,UAAA;MAAAD,UAAA;MAAAR,KAAA;MAAA3D,MAAA;MAAAiE,SAAA;MAAAC,cAAA;MAAA/D,KAAA;MAAAD,aAAA;MAAAD,cAAA;MAAA+D,QAAA;IAAA;IAAAyE,OAAA;MAAAjJ,SAAA;MAAAmF,cAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,WAAA;IAAA;IAAA4D,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uBAAA/O,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFhC,EAAE,CAAAgD,cAAA,cAgBvF,CAAC;QAhBoFhD,EAAE,CAAAmH,UAAA,qBAAA6J,6CAAA3J,MAAA;UAAA,OAaxEpF,GAAA,CAAA8K,WAAA,CAAArF,IAAA,CAAAL,MAAuB,CAAC;QAAA,EAAC,mBAAA4J,2CAAA5J,MAAA;UAAA,OAC3BpF,GAAA,CAAA4K,SAAA,CAAAnF,IAAA,CAAAL,MAAqB,CAAC;QAAA,CADI,CAAC,kBAAA6J,0CAAA7J,MAAA;UAAA,OAE5BpF,GAAA,CAAA6K,QAAA,CAAApF,IAAA,CAAAL,MAAoB,CAAC;QAAA,CAFM,CAAC;QAb6CrH,EAAE,CAAAgE,UAAA,IAAA0F,oCAAA,wBAkJtE,CAAC;QAlJmE1J,EAAE,CAAAkD,YAAA,CAmJnF,CAAC;MAAA;MAAA,IAAAlB,EAAA;QAnJgFhC,EAAE,CAAAyC,UAAA,YAAFzC,EAAE,CAAA8G,eAAA,IAAA8C,GAAA,GAAA3H,GAAA,CAAA0K,IAAA,EAAA1K,GAAA,CAAA0K,IAAA,CAKb,CAAC,OAAA1K,GAAA,CAAAgG,MAAA,UAAD,CAAC,aAAAhG,GAAA,CAAAgK,QAAD,CAAC;QALUjM,EAAE,CAAA2C,WAAA,eAAAV,GAAA,CAAAiK,SAQvD,CAAC,oBAAAjK,GAAA,CAAAkK,cAAD,CAAC,0BAAAlK,GAAA,CAAAkG,aAAD,CAAC,+BAAD,CAAC,0BAAD,CAAC;QARoDnI,EAAE,CAAAoD,SAAA,EAiB9B,CAAC;QAjB2BpD,EAAE,CAAAyC,UAAA,YAAAR,GAAA,CAAA8F,KAiB9B,CAAC;MAAA;IAAA;IAAAoJ,YAAA,EAAAA,CAAA,MAmImBvR,EAAE,CAACwR,OAAO,EAAyGxR,EAAE,CAACyR,OAAO,EAAwIzR,EAAE,CAAC0R,IAAI,EAAkH1R,EAAE,CAAC2R,gBAAgB,EAAyK3R,EAAE,CAAC4R,OAAO,EAAgGzQ,EAAE,CAAC0Q,UAAU,EAAiP1Q,EAAE,CAAC2Q,gBAAgB,EAAmOnQ,EAAE,CAACoQ,MAAM,EAA2ElQ,EAAE,CAACmQ,OAAO,EAAkWtQ,cAAc,EAAgFiL,aAAa;IAAAsF,aAAA;EAAA;AACxxD;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAtJ6F9R,EAAE,CAAA+R,iBAAA,CAsJJxF,aAAa,EAAc,CAAC;IAC3GwD,IAAI,EAAE5P,SAAS;IACf6R,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BnB,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MACee,aAAa,EAAEzR,iBAAiB,CAAC8R,IAAI;MACrCC,IAAI,EAAE;QACFC,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAErC,IAAI,EAAE/P,EAAE,CAACyP;EAAW,CAAC,EAAE;IAAEM,IAAI,EAAE/P,EAAE,CAAC0P;EAAU,CAAC,EAAE;IAAEK,IAAI,EAAE/P,EAAE,CAAC2P;EAAkB,CAAC,EAAE;IAAEI,IAAI,EAAEH,UAAU;IAAEyC,UAAU,EAAE,CAAC;MACjItC,IAAI,EAAE1P,MAAM;MACZ2R,IAAI,EAAE,CAAC9R,UAAU,CAAC,MAAM0P,UAAU,CAAC;IACvC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE7H,KAAK,EAAE,CAAC;MACjCgI,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAEuG,YAAY,EAAE,CAAC;MACfkJ,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAEqM,IAAI,EAAE,CAAC;MACPoD,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAE0H,WAAW,EAAE,CAAC;MACd+H,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAE+L,UAAU,EAAE,CAAC;MACb0D,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAE8L,UAAU,EAAE,CAAC;MACb2D,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAEsL,KAAK,EAAE,CAAC;MACRmE,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAE2H,MAAM,EAAE,CAAC;MACT8H,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAE4L,SAAS,EAAE,CAAC;MACZ6D,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAE6L,cAAc,EAAE,CAAC;MACjB4D,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAE8H,KAAK,EAAE,CAAC;MACR2H,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAE6H,aAAa,EAAE,CAAC;MAChB4H,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAE4H,cAAc,EAAE,CAAC;MACjB6H,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAE2L,QAAQ,EAAE,CAAC;MACX8D,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAEmH,SAAS,EAAE,CAAC;MACZsI,IAAI,EAAExP;IACV,CAAC,CAAC;IAAEqM,cAAc,EAAE,CAAC;MACjBmD,IAAI,EAAExP;IACV,CAAC,CAAC;IAAEsM,SAAS,EAAE,CAAC;MACZkD,IAAI,EAAExP;IACV,CAAC,CAAC;IAAEuM,QAAQ,EAAE,CAAC;MACXiD,IAAI,EAAExP;IACV,CAAC,CAAC;IAAEwM,WAAW,EAAE,CAAC;MACdgD,IAAI,EAAExP;IACV,CAAC,CAAC;IAAEyM,gBAAgB,EAAE,CAAC;MACnB+C,IAAI,EAAEvP,SAAS;MACfwR,IAAI,EAAE,CAAC,SAAS,EAAE;QAAEM,MAAM,EAAE;MAAK,CAAC;IACtC,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,MAAM1C,UAAU,CAAC;EACb2C,QAAQ;EACRC,UAAU;EACVhG,EAAE;EACFC,QAAQ;EACRC,EAAE;EACF+F,MAAM;EACNC,cAAc;EACd;AACJ;AACA;AACA;EACI,IAAIC,KAAKA,CAACvI,KAAK,EAAE;IACb,IAAI,CAACwI,MAAM,GAAGxI,KAAK;IACnB,IAAI,CAACyI,eAAe,GAAG,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACF,MAAM,IAAI,EAAE,CAAC;EACvE;EACA,IAAID,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EACA;AACJ;AACA;AACA;EACIhH,KAAK;EACL;AACJ;AACA;AACA;EACIpM,KAAK;EACL;AACJ;AACA;AACA;EACIkM,UAAU;EACV;AACJ;AACA;AACA;EACIqH,QAAQ;EACR;AACJ;AACA;AACA;EACI1G,UAAU,GAAG,IAAI;EACjB;AACJ;AACA;AACA;EACID,UAAU,GAAG,CAAC;EACd;AACJ;AACA;AACA;AACA;EACIpE,WAAW,GAAG,IAAI;EAClB;AACJ;AACA;AACA;EACI6D,qBAAqB,GAAG,iCAAiC;EACzD;AACJ;AACA;AACA;EACIC,qBAAqB,GAAG,YAAY;EACpC;AACJ;AACA;AACA;EACIH,EAAE;EACF;AACJ;AACA;AACA;EACIO,SAAS;EACT;AACJ;AACA;AACA;EACIC,cAAc;EACd;AACJ;AACA;AACA;EACIH,QAAQ,GAAG,KAAK;EAChB;AACJ;AACA;AACA;EACIC,QAAQ,GAAG,CAAC;EACZ;AACJ;AACA;AACA;EACI+G,MAAM,GAAG,IAAI/S,YAAY,CAAC,CAAC;EAC3B;AACJ;AACA;AACA;EACIgT,MAAM,GAAG,IAAIhT,YAAY,CAAC,CAAC;EAC3BiT,SAAS;EACTC,QAAQ;EACRC,kBAAkB;EAClB9O,mBAAmB;EACnBuC,YAAY;EACZwM,SAAS;EACTC,oBAAoB;EACpBC,cAAc;EACdC,aAAa;EACbC,MAAM;EACNC,aAAa;EACbC,OAAO;EACPC,aAAa;EACbC,MAAM;EACNC,KAAK,GAAG,KAAK;EACbxH,OAAO,GAAG,KAAK;EACfpE,cAAc,GAAGzH,MAAM,CAAC,EAAE,CAAC;EAC3BsT,MAAM,GAAGtT,MAAM,CAAC,CAAC,CAAC;EAClBuT,eAAe,GAAGvT,MAAM,CAAC;IAAEsI,KAAK,EAAE,CAAC,CAAC;IAAEX,KAAK,EAAE,CAAC;IAAE6L,SAAS,EAAE,EAAE;IAAElN,IAAI,EAAE;EAAK,CAAC,CAAC;EAC5EmN,WAAW,GAAG,EAAE;EAChBC,aAAa;EACbtB,eAAe;EACfD,MAAM;EACN,IAAIwB,YAAYA,CAAA,EAAG;IACf,MAAMxL,aAAa,GAAG,IAAI,CAACV,cAAc,CAAC,CAAC,CAACmM,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC7F,GAAG,KAAK,IAAI,CAACuF,eAAe,CAAC,CAAC,CAACC,SAAS,CAAC;IACnG,OAAOrL,aAAa,GAAGA,aAAa,CAACb,KAAK,GAAG,IAAI,CAACgE,cAAc;EACpE;EACA,IAAIA,cAAcA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAAC8G,eAAe,IAAI,CAAC,IAAI,CAACA,eAAe,CAACjE,MAAM,EAAE;MACvD,IAAI,CAACiE,eAAe,GAAG,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACH,KAAK,IAAI,EAAE,CAAC;IACtE;IACA,OAAO,IAAI,CAACE,eAAe;EAC/B;EACA,IAAI1K,aAAaA,CAAA,EAAG;IAChB,MAAM6L,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC,CAAC;IAC9C,OAAOA,eAAe,CAACjN,IAAI,EAAE4E,EAAE,GAAGqI,eAAe,CAACjN,IAAI,CAAC4E,EAAE,GAAGqI,eAAe,CAACjL,KAAK,KAAK,CAAC,CAAC,GAAI,GAAE,IAAI,CAAC4C,EAAG,GAAEhK,WAAW,CAACqN,UAAU,CAACgF,eAAe,CAACC,SAAS,CAAC,GAAG,GAAG,GAAGD,eAAe,CAACC,SAAS,GAAG,EAAG,IAAGD,eAAe,CAACjL,KAAM,EAAC,GAAG,IAAI;EACtO;EACAkE,WAAWA,CAACsF,QAAQ,EAAEC,UAAU,EAAEhG,EAAE,EAAEC,QAAQ,EAAEC,EAAE,EAAE+F,MAAM,EAAEC,cAAc,EAAE;IACxE,IAAI,CAACH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAChG,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAAC+F,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACmB,MAAM,GAAG,IAAI,CAACtB,QAAQ,CAACgC,WAAW;IACvC7T,MAAM,CAAC,MAAM;MACT,MAAMqO,IAAI,GAAG,IAAI,CAAC7G,cAAc,CAAC,CAAC;MAClC,IAAIvG,WAAW,CAACqN,UAAU,CAACD,IAAI,CAAC,EAAE;QAC9B,IAAI,CAACyF,wBAAwB,CAAC,CAAC;QAC/B,IAAI,CAACC,kBAAkB,CAAC,CAAC;MAC7B,CAAC,MACI;QACD,IAAI,CAACC,0BAA0B,CAAC,CAAC;QACjC,IAAI,CAACC,oBAAoB,CAAC,CAAC;MAC/B;IACJ,CAAC,CAAC;EACN;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACjJ,EAAE,GAAG,IAAI,CAACA,EAAE,IAAI/J,iBAAiB,CAAC,CAAC;EAC5C;EACAiT,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC3B,SAAS,EAAE4B,OAAO,CAAE/N,IAAI,IAAK;MAC9B,QAAQA,IAAI,CAACgO,OAAO,CAAC,CAAC;QAClB,KAAK,aAAa;UACd,IAAI,CAACzQ,mBAAmB,GAAGyC,IAAI,CAAC+J,QAAQ;UACxC;QACJ,KAAK,MAAM;UACP,IAAI,CAACjK,YAAY,GAAGE,IAAI,CAAC+J,QAAQ;UACjC;QACJ;UACI,IAAI,CAACjK,YAAY,GAAGE,IAAI,CAAC+J,QAAQ;UACjC;MACR;IACJ,CAAC,CAAC;EACN;EACAgC,oBAAoBA,CAAC/K,KAAK,EAAEK,KAAK,GAAG,CAAC,EAAE4M,MAAM,GAAG,CAAC,CAAC,EAAEf,SAAS,GAAG,EAAE,EAAE;IAChE,MAAMlI,cAAc,GAAG,EAAE;IACzBhE,KAAK,IACDA,KAAK,CAAC+M,OAAO,CAAC,CAAC/N,IAAI,EAAEgC,KAAK,KAAK;MAC3B,MAAM0F,GAAG,GAAG,CAACwF,SAAS,KAAK,EAAE,GAAGA,SAAS,GAAG,GAAG,GAAG,EAAE,IAAIlL,KAAK;MAC7D,MAAMkM,OAAO,GAAG;QACZlO,IAAI;QACJgC,KAAK;QACLX,KAAK;QACLqG,GAAG;QACHuG,MAAM;QACNf;MACJ,CAAC;MACDgB,OAAO,CAAC,OAAO,CAAC,GAAG,IAAI,CAACnC,oBAAoB,CAAC/L,IAAI,CAACgB,KAAK,EAAEK,KAAK,GAAG,CAAC,EAAE6M,OAAO,EAAExG,GAAG,CAAC;MACjF1C,cAAc,CAACmJ,IAAI,CAACD,OAAO,CAAC;IAChC,CAAC,CAAC;IACN,OAAOlJ,cAAc;EACzB;EACAvJ,WAAWA,CAACuE,IAAI,EAAEwH,IAAI,EAAE;IACpB,OAAOxH,IAAI,GAAGpF,WAAW,CAAC6M,YAAY,CAACzH,IAAI,CAACwH,IAAI,CAAC,CAAC,GAAGjF,SAAS;EAClE;EACA6L,sBAAsBA,CAACvM,aAAa,EAAE;IAClC,OAAOA,aAAa,GAAG,IAAI,CAACtF,YAAY,CAACsF,aAAa,CAAC7B,IAAI,CAAC,GAAGuC,SAAS;EAC5E;EACAhG,YAAYA,CAACyD,IAAI,EAAE;IACf,OAAO,IAAI,CAACvE,WAAW,CAACuE,IAAI,EAAE,OAAO,CAAC;EAC1C;EACAqO,oBAAoBA,CAACxM,aAAa,EAAE;IAChC,OAAOA,aAAa,IAAIjH,WAAW,CAACqN,UAAU,CAACpG,aAAa,CAACb,KAAK,CAAC;EACvE;EACAsN,UAAUA,CAACzM,aAAa,EAAE;IACtB,OAAO,IAAI,CAACV,cAAc,CAAC,CAAC,CAAC4G,IAAI,CAAEwF,CAAC,IAAKA,CAAC,CAAC7F,GAAG,KAAK7F,aAAa,CAAC6F,GAAG,CAAC;EACzE;EACA6G,mBAAmBA,CAAC1M,aAAa,EAAE;IAC/B,OAAO,IAAI,CAAC2M,WAAW,CAAC3M,aAAa,CAAC,IAAI,IAAI,CAACyM,UAAU,CAACzM,aAAa,CAAC;EAC5E;EACA2M,WAAWA,CAAC3M,aAAa,EAAE;IACvB,OAAO,CAAC,CAACA,aAAa,IAAI,CAAC,IAAI,CAACS,cAAc,CAACT,aAAa,CAAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,CAACyO,eAAe,CAAC5M,aAAa,CAAC7B,IAAI,CAAC;EACnH;EACAsC,cAAcA,CAACtC,IAAI,EAAE;IACjB,OAAO,IAAI,CAACvE,WAAW,CAACuE,IAAI,EAAE,UAAU,CAAC;EAC7C;EACAyO,eAAeA,CAACzO,IAAI,EAAE;IAClB,OAAO,IAAI,CAACvE,WAAW,CAACuE,IAAI,EAAE,WAAW,CAAC;EAC9C;EACA0O,aAAaA,CAAC7M,aAAa,EAAE;IACzB,OAAO,IAAI,CAAC2M,WAAW,CAAC3M,aAAa,CAAC,IAAI,IAAI,CAACuM,sBAAsB,CAACvM,aAAa,CAAC,CAAC8M,iBAAiB,CAAC,CAAC,CAACC,UAAU,CAAC,IAAI,CAACzB,WAAW,CAACwB,iBAAiB,CAAC,CAAC,CAAC;EAC7J;EACAE,qBAAqBA,CAAChN,aAAa,EAAE;IACjC,OAAOA,aAAa,IAAIjH,WAAW,CAACqN,UAAU,CAACpG,aAAa,CAACb,KAAK,CAAC;EACvE;EACA2C,cAAcA,CAACwE,KAAK,EAAE;IAClB,IAAI,IAAI,CAACtD,KAAK,EAAE;MACZ,IAAI,CAAC8G,cAAc,CAACmD,GAAG,CAAC;QACpB1G,aAAa,EAAED,KAAK;QACpBuE,MAAM,EAAE,IAAI,CAACjH,EAAE,CAACY;MACpB,CAAC,CAAC;IACN;EACJ;EACA3E,WAAWA,CAACyG,KAAK,EAAE;IACf,MAAM;MAAEC,aAAa;MAAEvG;IAAc,CAAC,GAAGsG,KAAK;IAC9C,MAAM4G,OAAO,GAAG,IAAI,CAACV,oBAAoB,CAACxM,aAAa,CAAC;IACxD,MAAM+D,IAAI,GAAGhL,WAAW,CAACoU,OAAO,CAACnN,aAAa,CAACoM,MAAM,CAAC;IACtD,MAAMgB,QAAQ,GAAG,IAAI,CAACX,UAAU,CAACzM,aAAa,CAAC;IAC/C,IAAIoN,QAAQ,EAAE;MACV,MAAM;QAAEjN,KAAK;QAAE0F,GAAG;QAAErG,KAAK;QAAE6L,SAAS;QAAElN;MAAK,CAAC,GAAG6B,aAAa;MAC5D,IAAI,CAACV,cAAc,CAAC+N,GAAG,CAAC,IAAI,CAAC/N,cAAc,CAAC,CAAC,CAACyG,MAAM,CAAE2F,CAAC,IAAK7F,GAAG,KAAK6F,CAAC,CAAC7F,GAAG,IAAIA,GAAG,CAACkH,UAAU,CAACrB,CAAC,CAAC7F,GAAG,CAAC,CAAC,CAAC;MACpG,IAAI,CAACuF,eAAe,CAACiC,GAAG,CAAC;QAAElN,KAAK;QAAEX,KAAK;QAAE6L,SAAS;QAAElN;MAAK,CAAC,CAAC;MAC3D,IAAI,CAAC+M,KAAK,GAAG,IAAI;MACjB1S,UAAU,CAAC8U,KAAK,CAAC,IAAI,CAAC/C,QAAQ,CAACnG,gBAAgB,CAACI,aAAa,CAAC;IAClE,CAAC,MACI;MACD,IAAI0I,OAAO,EAAE;QACT,IAAI,CAACK,YAAY,CAACjH,KAAK,CAAC;MAC5B,CAAC,MACI;QACD,MAAMkH,iBAAiB,GAAGzJ,IAAI,GAAG/D,aAAa,GAAG,IAAI,CAACV,cAAc,CAAC,CAAC,CAACmM,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACL,SAAS,KAAK,EAAE,CAAC;QACtG,IAAI,CAACoC,IAAI,CAAClH,aAAa,CAAC;QACxB,IAAI,CAACmH,sBAAsB,CAACnH,aAAa,EAAEiH,iBAAiB,GAAGA,iBAAiB,CAACrN,KAAK,GAAG,CAAC,CAAC,CAAC;QAC5F3H,UAAU,CAAC8U,KAAK,CAAC,IAAI,CAAC/C,QAAQ,CAACnG,gBAAgB,CAACI,aAAa,CAAC;MAClE;IACJ;EACJ;EACAvF,gBAAgBA,CAACqH,KAAK,EAAE;IACpB,IAAI,CAAC9N,UAAU,CAACmV,aAAa,CAAC,CAAC,EAAE;MAC7B,IAAI,IAAI,CAACzC,KAAK,EAAE;QACZ,IAAI,CAACqC,YAAY,CAACjH,KAAK,CAAC;MAC5B;IACJ,CAAC,MACI;MACD,IAAI,CAACiH,YAAY,CAAC;QAAEjH,KAAK;QAAEtG,aAAa,EAAEsG,KAAK,CAACtG,aAAa;QAAEsN,KAAK,EAAE,IAAI,CAAClO;MAAY,CAAC,CAAC;IAC7F;EACJ;EACAuD,SAASA,CAAC2D,KAAK,EAAE;IACb,MAAMsH,OAAO,GAAGtH,KAAK,CAACsH,OAAO,IAAItH,KAAK,CAACuH,OAAO;IAC9C,QAAQvH,KAAK,CAACwH,IAAI;MACd,KAAK,WAAW;QACZ,IAAI,CAACC,cAAc,CAACzH,KAAK,CAAC;QAC1B;MACJ,KAAK,SAAS;QACV,IAAI,CAAC0H,YAAY,CAAC1H,KAAK,CAAC;QACxB;MACJ,KAAK,WAAW;QACZ,IAAI,CAAC2H,cAAc,CAAC3H,KAAK,CAAC;QAC1B;MACJ,KAAK,YAAY;QACb,IAAI,CAAC4H,eAAe,CAAC5H,KAAK,CAAC;QAC3B;MACJ,KAAK,MAAM;QACP,IAAI,CAAC6H,SAAS,CAAC7H,KAAK,CAAC;QACrB;MACJ,KAAK,KAAK;QACN,IAAI,CAAC8H,QAAQ,CAAC9H,KAAK,CAAC;QACpB;MACJ,KAAK,OAAO;QACR,IAAI,CAAC+H,UAAU,CAAC/H,KAAK,CAAC;QACtB;MACJ,KAAK,OAAO;QACR,IAAI,CAACgI,UAAU,CAAChI,KAAK,CAAC;QACtB;MACJ,KAAK,QAAQ;QACT,IAAI,CAACiI,WAAW,CAACjI,KAAK,CAAC;QACvB;MACJ,KAAK,KAAK;QACN,IAAI,CAACkI,QAAQ,CAAClI,KAAK,CAAC;QACpB;MACJ,KAAK,UAAU;MACf,KAAK,QAAQ;MACb,KAAK,WAAW;MAChB,KAAK,WAAW;MAChB,KAAK,YAAY;QACb;QACA;MACJ;QACI,IAAI,CAACsH,OAAO,IAAI7U,WAAW,CAAC0V,oBAAoB,CAACnI,KAAK,CAACT,GAAG,CAAC,EAAE;UACzD,IAAI,CAAC6I,WAAW,CAACpI,KAAK,EAAEA,KAAK,CAACT,GAAG,CAAC;QACtC;QACA;IACR;EACJ;EACAkI,cAAcA,CAACzH,KAAK,EAAE;IAClB,MAAMqI,SAAS,GAAG,IAAI,CAACvD,eAAe,CAAC,CAAC,CAACjL,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAACyO,iBAAiB,CAAC,IAAI,CAACxD,eAAe,CAAC,CAAC,CAACjL,KAAK,CAAC,GAAG,IAAI,CAAC0O,yBAAyB,CAAC,CAAC;IAC/I,IAAI,CAACnB,sBAAsB,CAACpH,KAAK,EAAEqI,SAAS,CAAC;IAC7CrI,KAAK,CAACwI,cAAc,CAAC,CAAC;EAC1B;EACAZ,eAAeA,CAAC5H,KAAK,EAAE;IACnB,MAAMtG,aAAa,GAAG,IAAI,CAACwL,YAAY,CAAC,IAAI,CAACJ,eAAe,CAAC,CAAC,CAACjL,KAAK,CAAC;IACrE,MAAM+M,OAAO,GAAG,IAAI,CAACF,qBAAqB,CAAChN,aAAa,CAAC;IACzD,MAAM7B,IAAI,GAAG6B,aAAa,CAAC7B,IAAI;IAC/B,IAAI+O,OAAO,EAAE;MACT,IAAI,CAACK,YAAY,CAAC;QAAEhH,aAAa,EAAED,KAAK;QAAEtG;MAAc,CAAC,CAAC;MAC1D,IAAI,CAACoL,eAAe,CAACiC,GAAG,CAAC;QAAElN,KAAK,EAAE,CAAC,CAAC;QAAEkL,SAAS,EAAErL,aAAa,CAAC6F,GAAG;QAAE1H;MAAK,CAAC,CAAC;MAC3E,IAAI,CAACmN,WAAW,GAAG,EAAE;MACrB,IAAI,CAACyC,cAAc,CAACzH,KAAK,CAAC;IAC9B;IACAA,KAAK,CAACwI,cAAc,CAAC,CAAC;EAC1B;EACAd,YAAYA,CAAC1H,KAAK,EAAE;IAChB,IAAIA,KAAK,CAACyI,MAAM,EAAE;MACd,IAAI,IAAI,CAAC3D,eAAe,CAAC,CAAC,CAACjL,KAAK,KAAK,CAAC,CAAC,EAAE;QACrC,MAAMH,aAAa,GAAG,IAAI,CAACwL,YAAY,CAAC,IAAI,CAACJ,eAAe,CAAC,CAAC,CAACjL,KAAK,CAAC;QACrE,MAAM+M,OAAO,GAAG,IAAI,CAACF,qBAAqB,CAAChN,aAAa,CAAC;QACzD,CAACkN,OAAO,IAAI,IAAI,CAACK,YAAY,CAAC;UAAEhH,aAAa,EAAED,KAAK;UAAEtG;QAAc,CAAC,CAAC;MAC1E;MACA,IAAI,CAACgD,KAAK,IAAI,IAAI,CAACyK,IAAI,CAACnH,KAAK,EAAE,IAAI,CAAC;MACpCA,KAAK,CAACwI,cAAc,CAAC,CAAC;IAC1B,CAAC,MACI;MACD,MAAMH,SAAS,GAAG,IAAI,CAACvD,eAAe,CAAC,CAAC,CAACjL,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC6O,iBAAiB,CAAC,IAAI,CAAC5D,eAAe,CAAC,CAAC,CAACjL,KAAK,CAAC,GAAG,IAAI,CAAC8O,wBAAwB,CAAC,CAAC;MAC9I,IAAI,CAACvB,sBAAsB,CAACpH,KAAK,EAAEqI,SAAS,CAAC;MAC7CrI,KAAK,CAACwI,cAAc,CAAC,CAAC;IAC1B;EACJ;EACAb,cAAcA,CAAC3H,KAAK,EAAE;IAClB,MAAMtG,aAAa,GAAG,IAAI,CAACwL,YAAY,CAAC,IAAI,CAACJ,eAAe,CAAC,CAAC,CAACjL,KAAK,CAAC;IACrE,MAAMsE,UAAU,GAAG,IAAI,CAACnF,cAAc,CAAC,CAAC,CAACmM,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAAC7F,GAAG,KAAK7F,aAAa,CAACqL,SAAS,CAAC;IACvF,MAAMtH,IAAI,GAAGhL,WAAW,CAACoU,OAAO,CAACnN,aAAa,CAACoM,MAAM,CAAC;IACtD,IAAI,CAACrI,IAAI,EAAE;MACP,IAAI,CAACqH,eAAe,CAACiC,GAAG,CAAC;QAAElN,KAAK,EAAE,CAAC,CAAC;QAAEkL,SAAS,EAAE5G,UAAU,GAAGA,UAAU,CAAC4G,SAAS,GAAG,EAAE;QAAElN,IAAI,EAAE6B,aAAa,CAAC7B;MAAK,CAAC,CAAC;MACpH,IAAI,CAACmN,WAAW,GAAG,EAAE;MACrB,IAAI,CAACyC,cAAc,CAACzH,KAAK,CAAC;IAC9B;IACA,MAAMhH,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC,CAACyG,MAAM,CAAE2F,CAAC,IAAKA,CAAC,CAACL,SAAS,KAAK,IAAI,CAACD,eAAe,CAAC,CAAC,CAACC,SAAS,CAAC;IAC5G,IAAI,CAAC/L,cAAc,CAAC+N,GAAG,CAAC/N,cAAc,CAAC;IACvCgH,KAAK,CAACwI,cAAc,CAAC,CAAC;EAC1B;EACAX,SAASA,CAAC7H,KAAK,EAAE;IACb,IAAI,CAACoH,sBAAsB,CAACpH,KAAK,EAAE,IAAI,CAAC4I,kBAAkB,CAAC,CAAC,CAAC;IAC7D5I,KAAK,CAACwI,cAAc,CAAC,CAAC;EAC1B;EACAV,QAAQA,CAAC9H,KAAK,EAAE;IACZ,IAAI,CAACoH,sBAAsB,CAACpH,KAAK,EAAE,IAAI,CAAC6I,iBAAiB,CAAC,CAAC,CAAC;IAC5D7I,KAAK,CAACwI,cAAc,CAAC,CAAC;EAC1B;EACAT,UAAUA,CAAC/H,KAAK,EAAE;IACd,IAAI,CAACgI,UAAU,CAAChI,KAAK,CAAC;EAC1B;EACAiI,WAAWA,CAACjI,KAAK,EAAE;IACf,IAAI,CAACmH,IAAI,CAACnH,KAAK,EAAE,IAAI,CAAC;IACtB,IAAI,CAAC8E,eAAe,CAAC,CAAC,CAACjL,KAAK,GAAG,IAAI,CAAC0O,yBAAyB,CAAC,CAAC;IAC/DvI,KAAK,CAACwI,cAAc,CAAC,CAAC;EAC1B;EACAN,QAAQA,CAAClI,KAAK,EAAE;IACZ,IAAI,IAAI,CAAC8E,eAAe,CAAC,CAAC,CAACjL,KAAK,KAAK,CAAC,CAAC,EAAE;MACrC,MAAMH,aAAa,GAAG,IAAI,CAACwL,YAAY,CAAC,IAAI,CAACJ,eAAe,CAAC,CAAC,CAACjL,KAAK,CAAC;MACrE,MAAM+M,OAAO,GAAG,IAAI,CAACF,qBAAqB,CAAChN,aAAa,CAAC;MACzD,CAACkN,OAAO,IAAI,IAAI,CAACK,YAAY,CAAC;QAAEhH,aAAa,EAAED,KAAK;QAAEtG;MAAc,CAAC,CAAC;IAC1E;IACA,IAAI,CAACyN,IAAI,CAAC,CAAC;EACf;EACAa,UAAUA,CAAChI,KAAK,EAAE;IACd,IAAI,IAAI,CAAC8E,eAAe,CAAC,CAAC,CAACjL,KAAK,KAAK,CAAC,CAAC,EAAE;MACrC,MAAMiP,OAAO,GAAG5W,UAAU,CAAC6W,UAAU,CAAC,IAAI,CAAC9E,QAAQ,CAAC3G,EAAE,CAACY,aAAa,EAAG,UAAU,GAAE,IAAI,CAACjF,aAAc,EAAE,IAAG,CAAC;MAC5G,MAAM+P,aAAa,GAAGF,OAAO,IAAI5W,UAAU,CAAC6W,UAAU,CAACD,OAAO,EAAE,6BAA6B,CAAC;MAC9FE,aAAa,GAAGA,aAAa,CAACC,KAAK,CAAC,CAAC,GAAGH,OAAO,IAAIA,OAAO,CAACG,KAAK,CAAC,CAAC;MAClE,MAAMvP,aAAa,GAAG,IAAI,CAACwL,YAAY,CAAC,IAAI,CAACJ,eAAe,CAAC,CAAC,CAACjL,KAAK,CAAC;MACrE,IAAI,CAAC,IAAI,CAAC6C,KAAK,EAAE;QACb,MAAMhD,aAAa,GAAG,IAAI,CAACwL,YAAY,CAAC,IAAI,CAACJ,eAAe,CAAC,CAAC,CAACjL,KAAK,CAAC;QACrE,MAAM+M,OAAO,GAAG,IAAI,CAACF,qBAAqB,CAAChN,aAAa,CAAC;QACzD,CAACkN,OAAO,KAAK,IAAI,CAAC9B,eAAe,CAAC,CAAC,CAACjL,KAAK,GAAG,IAAI,CAAC0O,yBAAyB,CAAC,CAAC,CAAC;MACjF;IACJ;IACAvI,KAAK,CAACwI,cAAc,CAAC,CAAC;EAC1B;EACAvB,YAAYA,CAACjH,KAAK,EAAE;IAChB,MAAM;MAAEtG,aAAa;MAAEwG;IAAQ,CAAC,GAAGF,KAAK;IACxC,IAAIvN,WAAW,CAACoU,OAAO,CAACnN,aAAa,CAAC,EAClC;IACJ,MAAM;MAAEG,KAAK;MAAE0F,GAAG;MAAErG,KAAK;MAAE6L,SAAS;MAAElM,KAAK;MAAEhB;IAAK,CAAC,GAAG6B,aAAa;IACnE,MAAMkN,OAAO,GAAGnU,WAAW,CAACqN,UAAU,CAACjH,KAAK,CAAC;IAC7C,MAAMG,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC,CAACyG,MAAM,CAAE2F,CAAC,IAAKA,CAAC,CAACL,SAAS,KAAKA,SAAS,IAAIK,CAAC,CAACL,SAAS,KAAKxF,GAAG,CAAC;IAC5GqH,OAAO,IAAI5N,cAAc,CAACgN,IAAI,CAACtM,aAAa,CAAC;IAC7C,IAAI,CAACoL,eAAe,CAACiC,GAAG,CAAC;MAAElN,KAAK;MAAEX,KAAK;MAAE6L,SAAS;MAAElN;IAAK,CAAC,CAAC;IAC3D,IAAI,CAACmB,cAAc,CAAC+N,GAAG,CAAC/N,cAAc,CAAC;IACvC4N,OAAO,KAAK,IAAI,CAAChC,KAAK,GAAG,IAAI,CAAC;IAC9B1E,OAAO,IAAIhO,UAAU,CAAC8U,KAAK,CAAC,IAAI,CAAC/C,QAAQ,CAACnG,gBAAgB,CAACI,aAAa,CAAC;EAC7E;EACAjC,WAAWA,CAAC+D,KAAK,EAAE;IACf,IAAI,CAAC5C,OAAO,GAAG,IAAI;IACnB,MAAM0H,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC,CAAC,CAACjL,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAACiL,eAAe,CAAC,CAAC,GAAG;MAAEjL,KAAK,EAAE,IAAI,CAAC0O,yBAAyB,CAAC,CAAC;MAAErP,KAAK,EAAE,CAAC;MAAE6L,SAAS,EAAE,EAAE;MAAElN,IAAI,EAAE,IAAI,CAACqN,YAAY,CAAC,IAAI,CAACqD,yBAAyB,CAAC,CAAC,CAAC,EAAE1Q;IAAK,CAAC;IAC5N,IAAI,CAACiN,eAAe,CAACiC,GAAG,CAACjC,eAAe,CAAC;EAC7C;EACA3I,UAAUA,CAAC6D,KAAK,EAAE;IACd,IAAI,CAAC5C,OAAO,GAAG,KAAK;IACpB,IAAI,CAAC0H,eAAe,CAACiC,GAAG,CAAC;MAAElN,KAAK,EAAE,CAAC,CAAC;MAAEX,KAAK,EAAE,CAAC;MAAE6L,SAAS,EAAE,EAAE;MAAElN,IAAI,EAAE;IAAK,CAAC,CAAC;IAC5E,IAAI,CAACmN,WAAW,GAAG,EAAE;IACrB,IAAI,CAACJ,KAAK,GAAG,KAAK;EACtB;EACAlJ,uBAAuBA,CAACsE,KAAK,EAAE;IAC3B,QAAQA,KAAK,CAACkJ,OAAO;MACjB,KAAK,SAAS;QACV,IAAI,IAAI,CAACxM,KAAK,EAAE;UACZ,IAAI,CAACyH,SAAS,GAAGnE,KAAK,CAAC8I,OAAO;UAC9B,IAAI,CAACK,SAAS,CAAC,CAAC;UAChB,IAAI,CAACrF,MAAM,CAACtL,IAAI,CAAC,CAAC,CAAC,CAAC;UACpB,IAAI,CAAC4Q,aAAa,CAAC,CAAC;UACpB,IAAI,CAACC,YAAY,CAAC,CAAC;UACnB,IAAI,CAAC/D,wBAAwB,CAAC,CAAC;UAC/B,IAAI,CAACC,kBAAkB,CAAC,CAAC;UACzB,IAAI,CAAC+D,kBAAkB,CAAC,CAAC;UACzBpX,UAAU,CAAC8U,KAAK,CAAC,IAAI,CAAC/C,QAAQ,CAACnG,gBAAgB,CAACI,aAAa,CAAC;UAC9D,IAAI,CAACqL,YAAY,CAAC,CAAC;QACvB;QACA;MACJ,KAAK,MAAM;QACP,IAAI,CAACC,aAAa,CAAC,CAAC;QACpB,IAAI,CAACzF,MAAM,CAACvL,IAAI,CAAC,CAAC,CAAC,CAAC;QACpB;IACR;EACJ;EACA6Q,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAAC3E,aAAa,EAClBxS,UAAU,CAACuX,gBAAgB,CAAC,IAAI,CAACtF,SAAS,EAAE,IAAI,CAACI,MAAM,CAAC,CAAC,KAEzDrS,UAAU,CAACwX,gBAAgB,CAAC,IAAI,CAACvF,SAAS,EAAE,IAAI,CAACI,MAAM,CAAC;EAChE;EACA1I,qBAAqBA,CAACmE,KAAK,EAAE;IACzB,QAAQA,KAAK,CAACkJ,OAAO;MACjB,KAAK,MAAM;QACPvW,WAAW,CAACgX,KAAK,CAAC3J,KAAK,CAAC8I,OAAO,CAAC;QAChC;IACR;EACJ;EACAM,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACvF,QAAQ,EAAE;MACf,IAAI,IAAI,CAACA,QAAQ,KAAK,MAAM,EACxB,IAAI,CAACtG,QAAQ,CAACqM,WAAW,CAAC,IAAI,CAACvG,QAAQ,CAACwG,IAAI,EAAE,IAAI,CAAC1F,SAAS,CAAC,CAAC,KAE9DjS,UAAU,CAAC0X,WAAW,CAAC,IAAI,CAACzF,SAAS,EAAE,IAAI,CAACN,QAAQ,CAAC;IAC7D;EACJ;EACAiG,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAC3F,SAAS,IAAI,IAAI,CAACN,QAAQ,EAAE;MACjC,IAAI,CAACtG,QAAQ,CAACqM,WAAW,CAAC,IAAI,CAACtM,EAAE,CAACY,aAAa,EAAE,IAAI,CAACiG,SAAS,CAAC;IACpE;EACJ;EACAgF,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAAChM,UAAU,EAAE;MACjBxK,WAAW,CAACoU,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC5C,SAAS,EAAE,IAAI,CAACjH,UAAU,GAAG,IAAI,CAACqG,MAAM,CAACwG,MAAM,CAACC,IAAI,CAAC;IACtF;EACJ;EACA;AACJ;AACA;AACA;EACI7C,IAAIA,CAACnH,KAAK,EAAEE,OAAO,EAAE;IACjB,IAAI,IAAI,CAACxD,KAAK,EAAE;MACZ,IAAI,CAACqH,MAAM,CAACvL,IAAI,CAAC,CAAC,CAAC,CAAC;MACpB,IAAI,CAACiM,OAAO,GAAG,KAAK;IACxB;IACA,IAAI,CAACzL,cAAc,CAAC+N,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAI,CAACjC,eAAe,CAACiC,GAAG,CAAC;MAAElN,KAAK,EAAE,CAAC,CAAC;MAAEX,KAAK,EAAE,CAAC;MAAE6L,SAAS,EAAE;IAAG,CAAC,CAAC;IAChE7E,OAAO,IAAIhO,UAAU,CAAC8U,KAAK,CAAC,IAAI,CAACxC,aAAa,IAAI,IAAI,CAACD,MAAM,IAAI,IAAI,CAACN,QAAQ,CAACnG,gBAAgB,CAACI,aAAa,CAAC;IAC9G,IAAI,CAAC0G,KAAK,GAAG,KAAK;EACtB;EACA;AACJ;AACA;AACA;AACA;EACIqF,MAAMA,CAACjK,KAAK,EAAE;IACV,IAAI,CAACyE,OAAO,GAAG,IAAI,CAAC0C,IAAI,CAACnH,KAAK,EAAE,IAAI,CAAC,GAAG,IAAI,CAACkK,IAAI,CAAClK,KAAK,CAAC;EAC5D;EACA;AACJ;AACA;AACA;AACA;EACIkK,IAAIA,CAAClK,KAAK,EAAEE,OAAO,EAAE;IACjB,IAAI,IAAI,CAACxD,KAAK,EAAE;MACZ,IAAI,CAAC+H,OAAO,GAAG,IAAI;MACnB,IAAI,CAACF,MAAM,GAAG,IAAI,CAACA,MAAM,IAAIvE,KAAK,CAACmK,aAAa;MAChD,IAAI,CAAC3F,aAAa,GAAGxE,KAAK,CAACwE,aAAa,IAAI,IAAI;MAChD,IAAI,CAACE,aAAa,GAAG1E,KAAK,EAAE0E,aAAa,IAAI,IAAI;IACrD;IACA,IAAI,CAACI,eAAe,CAACiC,GAAG,CAAC;MAAElN,KAAK,EAAE,IAAI,CAAC0O,yBAAyB,CAAC,CAAC;MAAErP,KAAK,EAAE,CAAC;MAAE6L,SAAS,EAAE;IAAG,CAAC,CAAC;IAC9F7E,OAAO,IAAIhO,UAAU,CAAC8U,KAAK,CAAC,IAAI,CAAC/C,QAAQ,CAACnG,gBAAgB,CAACI,aAAa,CAAC;IACzE,IAAI,CAACV,EAAE,CAAC4M,YAAY,CAAC,CAAC;EAC1B;EACAhC,WAAWA,CAACpI,KAAK,EAAEqK,IAAI,EAAE;IACrB,IAAI,CAACrF,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW,IAAI,EAAE,IAAIqF,IAAI;IAClD,IAAIhC,SAAS,GAAG,CAAC,CAAC;IAClB,IAAIiC,OAAO,GAAG,KAAK;IACnB,IAAI,IAAI,CAACxF,eAAe,CAAC,CAAC,CAACjL,KAAK,KAAK,CAAC,CAAC,EAAE;MACrCwO,SAAS,GAAG,IAAI,CAACnD,YAAY,CAACvF,KAAK,CAAC,IAAI,CAACmF,eAAe,CAAC,CAAC,CAACjL,KAAK,CAAC,CAAC0Q,SAAS,CAAE7Q,aAAa,IAAK,IAAI,CAAC6M,aAAa,CAAC7M,aAAa,CAAC,CAAC;MACjI2O,SAAS,GAAGA,SAAS,KAAK,CAAC,CAAC,GAAG,IAAI,CAACnD,YAAY,CAACvF,KAAK,CAAC,CAAC,EAAE,IAAI,CAACmF,eAAe,CAAC,CAAC,CAACjL,KAAK,CAAC,CAAC0Q,SAAS,CAAE7Q,aAAa,IAAK,IAAI,CAAC6M,aAAa,CAAC7M,aAAa,CAAC,CAAC,GAAG2O,SAAS,GAAG,IAAI,CAACvD,eAAe,CAAC,CAAC,CAACjL,KAAK;IACtM,CAAC,MACI;MACDwO,SAAS,GAAG,IAAI,CAACnD,YAAY,CAACqF,SAAS,CAAE7Q,aAAa,IAAK,IAAI,CAAC6M,aAAa,CAAC7M,aAAa,CAAC,CAAC;IACjG;IACA,IAAI2O,SAAS,KAAK,CAAC,CAAC,EAAE;MAClBiC,OAAO,GAAG,IAAI;IAClB;IACA,IAAIjC,SAAS,KAAK,CAAC,CAAC,IAAI,IAAI,CAACvD,eAAe,CAAC,CAAC,CAACjL,KAAK,KAAK,CAAC,CAAC,EAAE;MACzDwO,SAAS,GAAG,IAAI,CAACE,yBAAyB,CAAC,CAAC;IAChD;IACA,IAAIF,SAAS,KAAK,CAAC,CAAC,EAAE;MAClB,IAAI,CAACjB,sBAAsB,CAACpH,KAAK,EAAEqI,SAAS,CAAC;IACjD;IACA,IAAI,IAAI,CAACpD,aAAa,EAAE;MACpBuF,YAAY,CAAC,IAAI,CAACvF,aAAa,CAAC;IACpC;IACA,IAAI,CAACA,aAAa,GAAGwF,UAAU,CAAC,MAAM;MAClC,IAAI,CAACzF,WAAW,GAAG,EAAE;MACrB,IAAI,CAACC,aAAa,GAAG,IAAI;IAC7B,CAAC,EAAE,GAAG,CAAC;IACP,OAAOqF,OAAO;EAClB;EACA3B,wBAAwBA,CAAA,EAAG;IACvB,MAAM+B,aAAa,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAClD,OAAOD,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC7B,iBAAiB,CAAC,CAAC,GAAG6B,aAAa;EACvE;EACA7B,iBAAiBA,CAAA,EAAG;IAChB,OAAOpW,WAAW,CAACmY,aAAa,CAAC,IAAI,CAAC1F,YAAY,EAAGxL,aAAa,IAAK,IAAI,CAAC2M,WAAW,CAAC3M,aAAa,CAAC,CAAC;EAC3G;EACAgP,iBAAiBA,CAAC7O,KAAK,EAAE;IACrB,MAAMgR,gBAAgB,GAAGhR,KAAK,GAAG,CAAC,GAAGpH,WAAW,CAACmY,aAAa,CAAC,IAAI,CAAC1F,YAAY,CAACvF,KAAK,CAAC,CAAC,EAAE9F,KAAK,CAAC,EAAGH,aAAa,IAAK,IAAI,CAAC2M,WAAW,CAAC3M,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC;IAC1J,OAAOmR,gBAAgB,GAAG,CAAC,CAAC,GAAGA,gBAAgB,GAAGhR,KAAK;EAC3D;EACAyO,iBAAiBA,CAACzO,KAAK,EAAE;IACrB,MAAMgR,gBAAgB,GAAGhR,KAAK,GAAG,IAAI,CAACqL,YAAY,CAACxF,MAAM,GAAG,CAAC,GAAG,IAAI,CAACwF,YAAY,CAACvF,KAAK,CAAC9F,KAAK,GAAG,CAAC,CAAC,CAAC0Q,SAAS,CAAE7Q,aAAa,IAAK,IAAI,CAAC2M,WAAW,CAAC3M,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC;IACrK,OAAOmR,gBAAgB,GAAG,CAAC,CAAC,GAAGA,gBAAgB,GAAGhR,KAAK,GAAG,CAAC,GAAGA,KAAK;EACvE;EACA0O,yBAAyBA,CAAA,EAAG;IACxB,MAAMmC,aAAa,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAClD,OAAOD,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC9B,kBAAkB,CAAC,CAAC,GAAG8B,aAAa;EACxE;EACA9B,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC1D,YAAY,CAACqF,SAAS,CAAE7Q,aAAa,IAAK,IAAI,CAAC2M,WAAW,CAAC3M,aAAa,CAAC,CAAC;EAC1F;EACAiR,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACzF,YAAY,CAACqF,SAAS,CAAE7Q,aAAa,IAAK,IAAI,CAAC0M,mBAAmB,CAAC1M,aAAa,CAAC,CAAC;EAClG;EACA0N,sBAAsBA,CAACpH,KAAK,EAAEnG,KAAK,EAAE;IACjC,IAAI,IAAI,CAACiL,eAAe,CAAC,CAAC,CAACjL,KAAK,KAAKA,KAAK,EAAE;MACxC,MAAMiL,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC,CAAC;MAC9C,IAAI,CAACA,eAAe,CAACiC,GAAG,CAAC;QAAE,GAAGjC,eAAe;QAAEjN,IAAI,EAAE,IAAI,CAACqN,YAAY,CAACrL,KAAK,CAAC,CAAChC,IAAI;QAAEgC;MAAM,CAAC,CAAC;MAC5F,IAAI,CAAC0P,YAAY,CAAC,CAAC;IACvB;EACJ;EACAA,YAAYA,CAAC1P,KAAK,GAAG,CAAC,CAAC,EAAE;IACrB,MAAM4C,EAAE,GAAG5C,KAAK,KAAK,CAAC,CAAC,GAAI,GAAE,IAAI,CAAC4C,EAAG,IAAG5C,KAAM,EAAC,GAAG,IAAI,CAACZ,aAAa;IACpE,MAAM6P,OAAO,GAAG5W,UAAU,CAAC6W,UAAU,CAAC,IAAI,CAAC9E,QAAQ,CAAC3G,EAAE,CAACY,aAAa,EAAG,UAASzB,EAAG,IAAG,CAAC;IACvF,IAAIqM,OAAO,EAAE;MACTA,OAAO,CAACgC,cAAc,IAAIhC,OAAO,CAACgC,cAAc,CAAC;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC;IAC7F;EACJ;EACA1B,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAAChF,aAAa,EAAE;MACrB,IAAI,CAACA,aAAa,GAAG,IAAInS,6BAA6B,CAAC,IAAI,CAACoS,MAAM,EAAGvE,KAAK,IAAK;QAC3E,IAAI,IAAI,CAACyE,OAAO,EAAE;UACd,IAAI,CAAC0C,IAAI,CAACnH,KAAK,EAAE,IAAI,CAAC;QAC1B;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAACsE,aAAa,CAACgF,kBAAkB,CAAC,CAAC;EAC3C;EACA2B,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAC3G,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAAC2G,oBAAoB,CAAC,CAAC;MACzC,IAAI,CAAC3G,aAAa,GAAG,IAAI;IAC7B;EACJ;EACAiB,kBAAkBA,CAAA,EAAG;IACjB,IAAI5U,iBAAiB,CAAC,IAAI,CAAC2S,UAAU,CAAC,EAAE;MACpC,IAAI,CAAC,IAAI,CAACe,cAAc,EAAE;QACtB,IAAI,CAACA,cAAc,GAAG,IAAI,CAAC9G,QAAQ,CAAC2N,MAAM,CAAC,IAAI,CAAC7H,QAAQ,CAACgC,WAAW,EAAE,QAAQ,EAAGrF,KAAK,IAAK;UACvF,IAAI,CAAC9N,UAAU,CAACmV,aAAa,CAAC,CAAC,EAAE;YAC7B,IAAI,CAACF,IAAI,CAACnH,KAAK,EAAE,IAAI,CAAC;UAC1B;QACJ,CAAC,CAAC;MACN;IACJ;EACJ;EACAsF,wBAAwBA,CAAA,EAAG;IACvB,IAAI3U,iBAAiB,CAAC,IAAI,CAAC2S,UAAU,CAAC,EAAE;MACpC,IAAI,CAAC,IAAI,CAACc,oBAAoB,EAAE;QAC5B,IAAI,CAACA,oBAAoB,GAAG,IAAI,CAAC7G,QAAQ,CAAC2N,MAAM,CAAC,IAAI,CAAC7H,QAAQ,EAAE,OAAO,EAAGrD,KAAK,IAAK;UAChF,MAAMmL,kBAAkB,GAAG,IAAI,CAACjH,kBAAkB,IAAI,CAAC,IAAI,CAACA,kBAAkB,CAAChG,aAAa,CAACkN,QAAQ,CAACpL,KAAK,CAACuE,MAAM,CAAC;UACnH,MAAM8G,eAAe,GAAG,IAAI,CAAC3O,KAAK,GAAG,EAAE,IAAI,CAAC6H,MAAM,KAAK,IAAI,CAACA,MAAM,KAAKvE,KAAK,CAACuE,MAAM,IAAI,IAAI,CAACA,MAAM,CAAC6G,QAAQ,CAACpL,KAAK,CAACuE,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI;UAClI,IAAI4G,kBAAkB,IAAIE,eAAe,EAAE;YACvC,IAAI,CAAClE,IAAI,CAAC,CAAC;UACf;QACJ,CAAC,CAAC;MACN;IACJ;EACJ;EACA3B,0BAA0BA,CAAA,EAAG;IACzB,IAAI,IAAI,CAACpB,oBAAoB,EAAE;MAC3Bf,QAAQ,CAACiI,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAClH,oBAAoB,CAAC;MAChE,IAAI,CAACA,oBAAoB,GAAG,IAAI;IACpC;EACJ;EACAqB,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACpB,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,CAAC,CAAC;MACrB,IAAI,CAACA,cAAc,GAAG,IAAI;IAC9B;EACJ;EACAmF,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAChE,0BAA0B,CAAC,CAAC;IACjC,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACwF,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAAC,IAAI,CAACzN,EAAE,CAAC+N,SAAS,EAAE;MACpB,IAAI,CAAChH,MAAM,GAAG,IAAI;IACtB;EACJ;EACAiH,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC9O,KAAK,EAAE;MACZ,IAAI,IAAI,CAAC4H,aAAa,EAAE;QACpB,IAAI,CAACA,aAAa,CAACmH,OAAO,CAAC,CAAC;QAC5B,IAAI,CAACnH,aAAa,GAAG,IAAI;MAC7B;MACA,IAAI,IAAI,CAACH,SAAS,IAAI,IAAI,CAAChH,UAAU,EAAE;QACnCxK,WAAW,CAACgX,KAAK,CAAC,IAAI,CAACxF,SAAS,CAAC;MACrC;MACA,IAAI,CAAC2F,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACN,aAAa,CAAC,CAAC;IACxB;EACJ;EACA,OAAOrJ,IAAI,YAAAuL,mBAAArL,CAAA;IAAA,YAAAA,CAAA,IAAwFK,UAAU,EAp/BpB5P,EAAE,CAAAwP,iBAAA,CAo/BoC1P,QAAQ,GAp/B9CE,EAAE,CAAAwP,iBAAA,CAo/ByD7O,WAAW,GAp/BtEX,EAAE,CAAAwP,iBAAA,CAo/BiFxP,EAAE,CAACyP,UAAU,GAp/BhGzP,EAAE,CAAAwP,iBAAA,CAo/B2GxP,EAAE,CAAC0P,SAAS,GAp/BzH1P,EAAE,CAAAwP,iBAAA,CAo/BoIxP,EAAE,CAAC2P,iBAAiB,GAp/B1J3P,EAAE,CAAAwP,iBAAA,CAo/BqKvO,EAAE,CAAC4Z,aAAa,GAp/BvL7a,EAAE,CAAAwP,iBAAA,CAo/BkMvO,EAAE,CAAC6Z,cAAc;EAAA;EAC9S,OAAOjL,IAAI,kBAr/B8E7P,EAAE,CAAA8P,iBAAA;IAAAC,IAAA,EAq/BJH,UAAU;IAAAI,SAAA;IAAA+K,cAAA,WAAAC,0BAAAhZ,EAAA,EAAAC,GAAA,EAAAgZ,QAAA;MAAA,IAAAjZ,EAAA;QAr/BRhC,EAAE,CAAAkb,cAAA,CAAAD,QAAA,EAq/BwjB/Z,aAAa;MAAA;MAAA,IAAAc,EAAA;QAAA,IAAAoO,EAAA;QAr/BvkBpQ,EAAE,CAAAqQ,cAAA,CAAAD,EAAA,GAAFpQ,EAAE,CAAAsQ,WAAA,QAAArO,GAAA,CAAAiR,SAAA,GAAA9C,EAAA;MAAA;IAAA;IAAAH,SAAA,WAAAkL,iBAAAnZ,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFhC,EAAE,CAAAmQ,WAAA,CAAAtG,GAAA;QAAF7J,EAAE,CAAAmQ,WAAA,CAAArG,GAAA;MAAA;MAAA,IAAA9H,EAAA;QAAA,IAAAoO,EAAA;QAAFpQ,EAAE,CAAAqQ,cAAA,CAAAD,EAAA,GAAFpQ,EAAE,CAAAsQ,WAAA,QAAArO,GAAA,CAAAkR,QAAA,GAAA/C,EAAA,CAAAG,KAAA;QAAFvQ,EAAE,CAAAqQ,cAAA,CAAAD,EAAA,GAAFpQ,EAAE,CAAAsQ,WAAA,QAAArO,GAAA,CAAAmR,kBAAA,GAAAhD,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAC,SAAA;IAAAC,MAAA;MAAAkC,KAAA;MAAA/G,KAAA;MAAApM,KAAA;MAAAkM,UAAA;MAAAqH,QAAA;MAAA1G,UAAA;MAAAD,UAAA;MAAApE,WAAA;MAAA6D,qBAAA;MAAAC,qBAAA;MAAAH,EAAA;MAAAO,SAAA;MAAAC,cAAA;MAAAH,QAAA;MAAAC,QAAA;IAAA;IAAAyE,OAAA;MAAAsC,MAAA;MAAAC,MAAA;IAAA;IAAAtC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAsK,oBAAApZ,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFhC,EAAE,CAAAgE,UAAA,IAAAsG,yBAAA,iBA0hClF,CAAC;MAAA;MAAA,IAAAtI,EAAA;QA1hC+EhC,EAAE,CAAAyC,UAAA,UAAAR,GAAA,CAAA2J,KAAA,IAAA3J,GAAA,CAAA0R,OAmgC5D,CAAC;MAAA;IAAA;IAAAxC,YAAA,GAwBqyBvR,EAAE,CAACwR,OAAO,EAAoFxR,EAAE,CAAC0R,IAAI,EAA6F1R,EAAE,CAAC4R,OAAO,EAA2EjF,aAAa;IAAA8O,MAAA;IAAAxJ,aAAA;IAAAyJ,IAAA;MAAAC,SAAA,EAAgU,CAAC5b,OAAO,CAAC,kBAAkB,EAAE,CAACD,UAAU,CAAC,QAAQ,EAAE,CAACF,KAAK,CAAC;QAAEgc,OAAO,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAc,CAAC,CAAC,EAAEhc,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC,EAAEC,UAAU,CAAC,QAAQ,EAAE,CAACD,OAAO,CAAC,0BAA0B,EAAED,KAAK,CAAC;QAAEgc,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAAC;IAAAE,eAAA;EAAA;AAC9oD;AACA;EAAA,QAAA5J,SAAA,oBAAAA,SAAA,KA7hC6F9R,EAAE,CAAA+R,iBAAA,CA6hCJnC,UAAU,EAAc,CAAC;IACxGG,IAAI,EAAE5P,SAAS;IACf6R,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,cAAc;MAAEnB,QAAQ,EAAG;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MAAE6K,UAAU,EAAE,CAAChc,OAAO,CAAC,kBAAkB,EAAE,CAACD,UAAU,CAAC,QAAQ,EAAE,CAACF,KAAK,CAAC;QAAEgc,OAAO,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAc,CAAC,CAAC,EAAEhc,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC,EAAEC,UAAU,CAAC,QAAQ,EAAE,CAACD,OAAO,CAAC,0BAA0B,EAAED,KAAK,CAAC;QAAEgc,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAAEE,eAAe,EAAE9a,uBAAuB,CAACgb,MAAM;MAAE/J,aAAa,EAAEzR,iBAAiB,CAAC8R,IAAI;MAAEC,IAAI,EAAE;QAC5TC,KAAK,EAAE;MACX,CAAC;MAAEiJ,MAAM,EAAE,CAAC,4vBAA4vB;IAAE,CAAC;EACvxB,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEtL,IAAI,EAAE8L,QAAQ;IAAExJ,UAAU,EAAE,CAAC;MAC9CtC,IAAI,EAAE1P,MAAM;MACZ2R,IAAI,EAAE,CAAClS,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEiQ,IAAI,EAAEzG,SAAS;IAAE+I,UAAU,EAAE,CAAC;MAClCtC,IAAI,EAAE1P,MAAM;MACZ2R,IAAI,EAAE,CAACrR,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAEoP,IAAI,EAAE/P,EAAE,CAACyP;EAAW,CAAC,EAAE;IAAEM,IAAI,EAAE/P,EAAE,CAAC0P;EAAU,CAAC,EAAE;IAAEK,IAAI,EAAE/P,EAAE,CAAC2P;EAAkB,CAAC,EAAE;IAAEI,IAAI,EAAE9O,EAAE,CAAC4Z;EAAc,CAAC,EAAE;IAAE9K,IAAI,EAAE9O,EAAE,CAAC6Z;EAAe,CAAC,CAAC,EAAkB;IAAEnI,KAAK,EAAE,CAAC;MAC3K5C,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAEsL,KAAK,EAAE,CAAC;MACRmE,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAEd,KAAK,EAAE,CAAC;MACRuQ,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAEoL,UAAU,EAAE,CAAC;MACbqE,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAEyS,QAAQ,EAAE,CAAC;MACXhD,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAE+L,UAAU,EAAE,CAAC;MACb0D,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAE8L,UAAU,EAAE,CAAC;MACb2D,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAE0H,WAAW,EAAE,CAAC;MACd+H,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAEuL,qBAAqB,EAAE,CAAC;MACxBkE,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAEwL,qBAAqB,EAAE,CAAC;MACxBiE,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAEqL,EAAE,EAAE,CAAC;MACLoE,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAE4L,SAAS,EAAE,CAAC;MACZ6D,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAE6L,cAAc,EAAE,CAAC;MACjB4D,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAE0L,QAAQ,EAAE,CAAC;MACX+D,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAE2L,QAAQ,EAAE,CAAC;MACX8D,IAAI,EAAEzP;IACV,CAAC,CAAC;IAAE0S,MAAM,EAAE,CAAC;MACTjD,IAAI,EAAExP;IACV,CAAC,CAAC;IAAE0S,MAAM,EAAE,CAAC;MACTlD,IAAI,EAAExP;IACV,CAAC,CAAC;IAAE2S,SAAS,EAAE,CAAC;MACZnD,IAAI,EAAElP,eAAe;MACrBmR,IAAI,EAAE,CAAC9Q,aAAa;IACxB,CAAC,CAAC;IAAEiS,QAAQ,EAAE,CAAC;MACXpD,IAAI,EAAEvP,SAAS;MACfwR,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC,CAAC;IAAEoB,kBAAkB,EAAE,CAAC;MACrBrD,IAAI,EAAEvP,SAAS;MACfwR,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM8J,gBAAgB,CAAC;EACnB,OAAOzM,IAAI,YAAA0M,yBAAAxM,CAAA;IAAA,YAAAA,CAAA,IAAwFuM,gBAAgB;EAAA;EACnH,OAAOE,IAAI,kBA5nC8Ehc,EAAE,CAAAic,gBAAA;IAAAlM,IAAA,EA4nCS+L;EAAgB;EACpH,OAAOI,IAAI,kBA7nC8Elc,EAAE,CAAAmc,gBAAA;IAAAC,OAAA,GA6nCqCrc,YAAY,EAAEiB,YAAY,EAAEQ,YAAY,EAAEE,aAAa,EAAEJ,cAAc,EAAEH,YAAY,EAAEH,YAAY,EAAEU,aAAa,EAAEP,YAAY;EAAA;AACpQ;AACA;EAAA,QAAA2Q,SAAA,oBAAAA,SAAA,KA/nC6F9R,EAAE,CAAA+R,iBAAA,CA+nCJ+J,gBAAgB,EAAc,CAAC;IAC9G/L,IAAI,EAAEjP,QAAQ;IACdkR,IAAI,EAAE,CAAC;MACCoK,OAAO,EAAE,CAACrc,YAAY,EAAEiB,YAAY,EAAEQ,YAAY,EAAEE,aAAa,EAAEJ,cAAc,EAAEH,YAAY,CAAC;MAChGkb,OAAO,EAAE,CAACzM,UAAU,EAAE5O,YAAY,EAAEU,aAAa,EAAEP,YAAY,CAAC;MAChEmb,YAAY,EAAE,CAAC1M,UAAU,EAAErD,aAAa;IAC5C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASqD,UAAU,EAAEkM,gBAAgB,EAAEvP,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}