{"ast": null, "code": "import { debounceTime } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/layout/service/app.layout.service\";\nimport * as i2 from \"../../service/stations.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"primeng/dataview\";\nimport * as i8 from \"primeng/knob\";\nimport * as i9 from \"primeng/inputtext\";\nimport * as i10 from \"primeng/dropdown\";\nimport * as i11 from \"@fortawesome/angular-fontawesome\";\nimport * as i12 from \"@coreui/angular-chartjs\";\nfunction IndexComponent_ng_template_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"p-dropdown\", 16);\n    i0.ɵɵlistener(\"onChange\", function IndexComponent_ng_template_25_Template_p_dropdown_onChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onSortChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"p-dropdown\", 17);\n    i0.ɵɵlistener(\"onChange\", function IndexComponent_ng_template_25_Template_p_dropdown_onChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onSortChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 18);\n    i0.ɵɵelement(4, \"i\", 19);\n    i0.ɵɵelementStart(5, \"input\", 20);\n    i0.ɵɵlistener(\"input\", function IndexComponent_ng_template_25_Template_input_input_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r6 = i0.ɵɵnextContext();\n      const _r0 = i0.ɵɵreference(24);\n      return i0.ɵɵresetView(ctx_r6.onFilter(_r0, $event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"options\", ctx_r1.sortOptionsCountry);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"options\", ctx_r1.sortOptionsStatus);\n  }\n}\nconst _c0 = a1 => [\"/uikit/station\", a1];\nfunction IndexComponent_ng_template_26_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 22)(2, \"div\", 0)(3, \"div\", 23)(4, \"div\", 24)(5, \"a\", 25);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 26)(8, \"div\", 27);\n    i0.ɵɵelement(9, \"fa-icon\", 7);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 28);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 26);\n    i0.ɵɵelement(14, \"c-chart\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 23)(16, \"div\", 0)(17, \"div\", 30)(18, \"p\", 27);\n    i0.ɵɵtext(19, \"Inverter\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\", 27)(21, \"p-knob\", 31);\n    i0.ɵɵlistener(\"ngModelChange\", function IndexComponent_ng_template_26_div_0_Template_p_knob_ngModelChange_21_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const station_r9 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(station_r9.inverter = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 30)(23, \"p\", 27);\n    i0.ɵɵtext(24, \"MMPT\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"p\", 27)(26, \"p-knob\", 31);\n    i0.ɵɵlistener(\"ngModelChange\", function IndexComponent_ng_template_26_div_0_Template_p_knob_ngModelChange_26_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const station_r9 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(station_r9.mmpt = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"div\", 30)(28, \"p\", 27);\n    i0.ɵɵtext(29, \"String\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"p\", 27)(31, \"p-knob\", 31);\n    i0.ɵɵlistener(\"ngModelChange\", function IndexComponent_ng_template_26_div_0_Template_p_knob_ngModelChange_31_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const station_r9 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(station_r9.string = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"div\", 30)(33, \"p\", 27);\n    i0.ɵɵtext(34, \"PVN\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"p\", 27)(36, \"p-knob\", 31);\n    i0.ɵɵlistener(\"ngModelChange\", function IndexComponent_ng_template_26_div_0_Template_p_knob_ngModelChange_36_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const station_r9 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(station_r9.pvn = $event);\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(37, \"div\", 32)(38, \"p\", 27);\n    i0.ɵɵelement(39, \"i\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"p\", 27)(41, \"span\", 34);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(43, \"div\", 32)(44, \"div\", 35);\n    i0.ɵɵelement(45, \"fa-icon\", 36);\n    i0.ɵɵtext(46);\n    i0.ɵɵelementStart(47, \"span\");\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const station_r9 = ctx.$implicit;\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(\"station-box-status-\" + station_r9.status.toLowerCase());\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(37, _c0, station_r9.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(station_r9.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", station_r9.power, \"kW\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Irradiance: \", station_r9.irradiance, \"kWh/m2\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"data\", ctx_r8.data[1])(\"options\", ctx_r8.lineOptions);\n    i0.ɵɵadvance(7);\n    i0.ɵɵpropertyInterpolate(\"valueTemplate\", station_r9.inverter);\n    i0.ɵɵproperty(\"ngModel\", station_r9.inverter)(\"step\", 1)(\"min\", 0)(\"max\", 5)(\"size\", 50);\n    i0.ɵɵadvance(5);\n    i0.ɵɵpropertyInterpolate(\"valueTemplate\", station_r9.mmpt);\n    i0.ɵɵproperty(\"ngModel\", station_r9.mmpt)(\"step\", 1)(\"min\", 0)(\"max\", 5)(\"size\", 50);\n    i0.ɵɵadvance(5);\n    i0.ɵɵpropertyInterpolate(\"valueTemplate\", station_r9.string);\n    i0.ɵɵproperty(\"ngModel\", station_r9.string)(\"step\", 1)(\"min\", 0)(\"max\", 5)(\"size\", 50);\n    i0.ɵɵadvance(5);\n    i0.ɵɵpropertyInterpolate(\"valueTemplate\", station_r9.pvn);\n    i0.ɵɵproperty(\"ngModel\", station_r9.pvn)(\"step\", 1)(\"min\", 0)(\"max\", 5)(\"size\", 50);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", station_r9.temperature, \" \\u2103\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", station_r9.updateTime, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(\"station-badge status-\" + station_r9.status.toLowerCase());\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(station_r9.status.toLowerCase());\n  }\n}\nfunction IndexComponent_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, IndexComponent_ng_template_26_div_0_Template, 49, 39, \"div\", 21);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.stations);\n  }\n}\nconst _c1 = () => ({\n  width: \"2.5rem\",\n  height: \"2.5rem\"\n});\nexport class IndexComponent {\n  constructor(layoutService, stationsService) {\n    this.layoutService = layoutService;\n    this.stationsService = stationsService;\n    this.stations = [];\n    this.sortOptionsCountry = [];\n    this.sortOptionsStatus = [];\n    this.sortOrder = 0;\n    this.sortField = '';\n    this.sourceCities = [];\n    this.targetCities = [];\n    this.orderCities = [];\n    this.subscription = this.layoutService.configUpdate$.pipe(debounceTime(25)).subscribe(config => {\n      //   this.initChart();\n    });\n  }\n  ngOnInit() {\n    this.stationsService.getStations().then(data => this.stations = data);\n    this.sourceCities = [{\n      name: 'San Francisco',\n      code: 'SF'\n    }, {\n      name: 'London',\n      code: 'LDN'\n    }, {\n      name: 'Paris',\n      code: 'PRS'\n    }, {\n      name: 'Istanbul',\n      code: 'IST'\n    }, {\n      name: 'Berlin',\n      code: 'BRL'\n    }, {\n      name: 'Barcelona',\n      code: 'BRC'\n    }, {\n      name: 'Rome',\n      code: 'RM'\n    }];\n    this.targetCities = [];\n    this.orderCities = [{\n      name: 'San Francisco',\n      code: 'SF'\n    }, {\n      name: 'London',\n      code: 'LDN'\n    }, {\n      name: 'Paris',\n      code: 'PRS'\n    }, {\n      name: 'Istanbul',\n      code: 'IST'\n    }, {\n      name: 'Berlin',\n      code: 'BRL'\n    }, {\n      name: 'Barcelona',\n      code: 'BRC'\n    }, {\n      name: 'Rome',\n      code: 'RM'\n    }];\n    // this.sortOptionsCountry = [\n    //     { label: 'Price High to Low', value: '!price' },\n    //     { label: 'Price Low to High', value: 'price' }\n    // ];\n    this.barOptions = {\n      maintainAspectRatio: false,\n      plugins: {\n        legend: {\n          display: false\n        }\n      },\n      scales: {\n        x: {\n          display: false\n        },\n        y: {\n          display: false\n        }\n      }\n    };\n    this.lineOptions = {\n      maintainAspectRatio: false,\n      elements: {\n        line: {\n          tension: 0.4\n        },\n        point: {\n          radius: 0\n        }\n      },\n      plugins: {\n        legend: {\n          display: false\n        }\n      },\n      scales: {\n        x: {\n          display: false\n        },\n        y: {\n          display: false\n        }\n      }\n    };\n    this.data = [{\n      labels: ['M', 'T', 'W', 'T', 'F', 'S', 'S', 'M', 'T', 'W', 'T', 'F', 'S', 'S', 'M'],\n      datasets: [{\n        backgroundColor: '#321fdb',\n        borderColor: 'transparent',\n        borderWidth: 1,\n        data: [41, 78, 51, 66, 74, 42, 89, 97, 87, 84, 78, 88, 67, 45, 47]\n      }]\n    }, {\n      labels: ['M', 'T', 'W', 'T', 'F', 'S', 'S', 'M', 'T', 'W', 'T', 'F', 'S', 'S', 'M'],\n      datasets: [{\n        backgroundColor: 'transparent',\n        borderColor: '#321fdb',\n        borderWidth: 2,\n        data: [41, 78, 51, 66, 74, 42, 89, 97, 87, 84, 78, 88, 67, 45, 47],\n        pointBackgroundColor: '#321fdb'\n      }]\n    }];\n  }\n  onSortChange(event) {\n    const value = event.value;\n    if (value.indexOf('!') === 0) {\n      this.sortOrder = -1;\n      this.sortField = value.substring(1, value.length);\n    } else {\n      this.sortOrder = 1;\n      this.sortField = value;\n    }\n  }\n  onFilter(dv, event) {\n    dv.filter(event.target.value);\n  }\n  initMap() {\n    this.mapSrc = \"https://www.digitaltrends.com/wp-content/uploads/2020/02/custom-map.jpg?fit=720%2C479&p=1\";\n  }\n  // initChart() {\n  //     const documentStyle = getComputedStyle(document.documentElement);\n  //     const textColor = documentStyle.getPropertyValue('--text-color');\n  //     const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n  //     const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\n  //     this.chartData = {\n  //         labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\n  //         datasets: [\n  //             {\n  //                 label: 'First Dataset',\n  //                 data: [65, 59, 80, 81, 56, 55, 40],\n  //                 fill: false,\n  //                 backgroundColor: documentStyle.getPropertyValue('--bluegray-700'),\n  //                 borderColor: documentStyle.getPropertyValue('--bluegray-700'),\n  //                 tension: .4\n  //             },\n  //             {\n  //                 label: 'Second Dataset',\n  //                 data: [28, 48, 40, 19, 86, 27, 90],\n  //                 fill: false,\n  //                 backgroundColor: documentStyle.getPropertyValue('--green-600'),\n  //                 borderColor: documentStyle.getPropertyValue('--green-600'),\n  //                 tension: .4\n  //             }\n  //         ]\n  //     };\n  //     this.chartOptions = {\n  //         plugins: {\n  //             legend: {\n  //                 labels: {\n  //                     color: textColor\n  //                 }\n  //             }\n  //         },\n  //         scales: {\n  //             x: {\n  //                 ticks: {\n  //                     color: textColorSecondary\n  //                 },\n  //                 grid: {\n  //                     color: surfaceBorder,\n  //                     drawBorder: false\n  //                 }\n  //             },\n  //             y: {\n  //                 ticks: {\n  //                     color: textColorSecondary\n  //                 },\n  //                 grid: {\n  //                     color: surfaceBorder,\n  //                     drawBorder: false\n  //                 }\n  //             }\n  //         }\n  //     };\n  // }\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n  static #_ = this.ɵfac = function IndexComponent_Factory(t) {\n    return new (t || IndexComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.StationsService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: IndexComponent,\n    selectors: [[\"ng-component\"]],\n    decls: 27,\n    vars: 9,\n    consts: [[1, \"grid\"], [1, \"col-12\", \"lg:col-6\", \"xl:col-6\"], [1, \"card\", \"mb-0\"], [1, \"flex\", \"justify-content-between\", \"mb-3\"], [1, \"block\", \"text-500\", \"font-medium\", \"font-bold\", \"mb-3\"], [1, \"text-900\", \"font-medium\", \"text-xl\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-blue-100\", \"border-round\", 3, \"ngStyle\"], [\"icon\", \"plug\"], [1, \"pi\", \"pi-sun\", \"text-blue-500\", \"text-xl\"], [1, \"col-12\", \"lg:col-12\", \"xl:col-12\"], [1, \"card\"], [\"filterBy\", \"name\", \"layout\", \"list\", 3, \"value\", \"paginator\", \"rows\", \"sortField\", \"sortOrder\"], [\"dv\", \"\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"listItem\"], [1, \"flex\", \"flex-column\", \"md:flex-row\", \"md:justify-content-between\", \"gap-2\"], [\"placeholder\", \"Country\", 3, \"options\", \"onChange\"], [\"placeholder\", \"Status\", 3, \"options\", \"onChange\"], [1, \"p-input-icon-left\"], [1, \"pi\", \"pi-search\"], [\"type\", \"search\", \"pInputText\", \"\", \"placeholder\", \"Search\", 3, \"input\"], [3, \"class\", 4, \"ngFor\", \"ngForOf\"], [1, \"\"], [1, \"col-12\", \"md:col-6\", \"lg:col-3\"], [1, \"font-bold\", \"text-xl\"], [3, \"routerLink\"], [1, \"col-12\", \"md:col-6\", \"lg:col-2\"], [1, \"text-center\"], [1, \"text-center\", \"text-sm\"], [\"type\", \"line\", \"height\", \"40\", \"width\", \"80\", 1, \"mx-auto\", 3, \"data\", \"options\"], [1, \"col-3\", \"md:col-3\", \"lg:col-3\"], [3, \"ngModel\", \"valueTemplate\", \"step\", \"min\", \"max\", \"size\", \"ngModelChange\"], [1, \"col-1\"], [1, \"pi\", \"pi-cloud\", \"text-2xl\"], [1, \"text-2xl\", \"font-semibold\", \"mb-2\", \"align-self-center\", \"md:align-self-end\"], [1, \"font-bold\", \"flex-auto\", \"text-xl\", \"align-items-right\", \"text-center\"], [\"icon\", \"clock\"]],\n    template: function IndexComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\")(5, \"span\", 4);\n        i0.ɵɵtext(6, \"Active Energy Systems\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"div\", 5);\n        i0.ɵɵtext(8, \"44\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(9, \"div\", 6);\n        i0.ɵɵelement(10, \"fa-icon\", 7);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(11, \"div\", 1)(12, \"div\", 2)(13, \"div\", 3)(14, \"div\")(15, \"span\", 4);\n        i0.ɵɵtext(16, \"Combined Power Permormance\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"div\", 5);\n        i0.ɵɵtext(18, \"33% (4.124kW) \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(19, \"div\", 6);\n        i0.ɵɵelement(20, \"i\", 8);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(21, \"div\", 9)(22, \"div\", 10)(23, \"p-dataView\", 11, 12);\n        i0.ɵɵtemplate(25, IndexComponent_ng_template_25_Template, 6, 2, \"ng-template\", 13)(26, IndexComponent_ng_template_26_Template, 1, 1, \"ng-template\", 14);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(7, _c1));\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(8, _c1));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"value\", ctx.stations)(\"paginator\", true)(\"rows\", 9)(\"sortField\", ctx.sortField)(\"sortOrder\", ctx.sortOrder);\n      }\n    },\n    dependencies: [i3.NgForOf, i3.NgStyle, i4.NgControlStatus, i4.NgModel, i5.RouterLink, i6.PrimeTemplate, i7.DataView, i8.Knob, i9.InputText, i10.Dropdown, i11.FaIconComponent, i12.ChartjsComponent],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["debounceTime", "i0", "ɵɵelementStart", "ɵɵlistener", "IndexComponent_ng_template_25_Template_p_dropdown_onChange_1_listener", "$event", "ɵɵrestoreView", "_r4", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "onSortChange", "ɵɵelementEnd", "IndexComponent_ng_template_25_Template_p_dropdown_onChange_2_listener", "ctx_r5", "ɵɵelement", "IndexComponent_ng_template_25_Template_input_input_5_listener", "ctx_r6", "_r0", "ɵɵreference", "onFilter", "ɵɵadvance", "ɵɵproperty", "ctx_r1", "sortOptionsCountry", "sortOptionsStatus", "ɵɵtext", "IndexComponent_ng_template_26_div_0_Template_p_knob_ngModelChange_21_listener", "restoredCtx", "_r11", "station_r9", "$implicit", "inverter", "IndexComponent_ng_template_26_div_0_Template_p_knob_ngModelChange_26_listener", "mmpt", "IndexComponent_ng_template_26_div_0_Template_p_knob_ngModelChange_31_listener", "string", "IndexComponent_ng_template_26_div_0_Template_p_knob_ngModelChange_36_listener", "pvn", "ɵɵclassMap", "status", "toLowerCase", "ɵɵpureFunction1", "_c0", "id", "ɵɵtextInterpolate", "name", "ɵɵtextInterpolate1", "power", "irradiance", "ctx_r8", "data", "lineOptions", "ɵɵpropertyInterpolate", "temperature", "updateTime", "ɵɵtemplate", "IndexComponent_ng_template_26_div_0_Template", "ctx_r2", "stations", "IndexComponent", "constructor", "layoutService", "stationsService", "sortOrder", "sortField", "sourceCities", "targetCities", "orderCities", "subscription", "configUpdate$", "pipe", "subscribe", "config", "ngOnInit", "getStations", "then", "code", "barOptions", "maintainAspectRatio", "plugins", "legend", "display", "scales", "x", "y", "elements", "line", "tension", "point", "radius", "labels", "datasets", "backgroundColor", "borderColor", "borderWidth", "pointBackgroundColor", "event", "value", "indexOf", "substring", "length", "dv", "filter", "target", "initMap", "mapSrc", "ngOnDestroy", "unsubscribe", "_", "ɵɵdirectiveInject", "i1", "LayoutService", "i2", "StationsService", "_2", "selectors", "decls", "vars", "consts", "template", "IndexComponent_Template", "rf", "ctx", "IndexComponent_ng_template_25_Template", "IndexComponent_ng_template_26_Template", "ɵɵpureFunction0", "_c1"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\index\\index.component.ts", "D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\index\\index.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { Product } from '../../api/product';\r\nimport { ProductService } from '../../service/product.service';\r\nimport { Subscription, debounceTime } from 'rxjs';\r\nimport { LayoutService } from 'src/app/layout/service/app.layout.service';\r\nimport { SelectItem } from 'primeng/api';\r\nimport { DataView } from 'primeng/dataview';\r\nimport { StationsService } from '../../service/stations.service';\r\nimport { Station } from '../../api/station';\r\n\r\n@Component({\r\n    templateUrl: './index.component.html',\r\n})\r\nexport class IndexComponent implements OnInit, OnDestroy {\r\n\r\n    items!: MenuItem[];\r\n\r\n    stations: Station[] =[];\r\n\r\n    chartData: any;\r\n\r\n    chartOptions: any;\r\n\r\n    subscription!: Subscription;\r\n\r\n    sortOptionsCountry: SelectItem[] = [];\r\n\r\n    sortOptionsStatus: SelectItem[] = [];\r\n\r\n    sortOrder: number = 0;\r\n\r\n    sortField: string = '';\r\n\r\n    sourceCities: any[] = [];\r\n\r\n    targetCities: any[] = [];\r\n\r\n    orderCities: any[] = [];\r\n\r\n    mapSrc:string;\r\n\r\n    barOptions:any;\r\n    lineOptions:any;\r\n    data: any;\r\n    \r\n\r\n    constructor(public layoutService: LayoutService, \r\n        private stationsService: StationsService) {\r\n        this.subscription = this.layoutService.configUpdate$\r\n        .pipe(debounceTime(25))\r\n        .subscribe((config) => {\r\n         //   this.initChart();\r\n        });\r\n    }\r\n\r\n    ngOnInit() {\r\n        this.stationsService.getStations().then(data => this.stations = data);\r\n\r\n        this.sourceCities = [\r\n            { name: 'San Francisco', code: 'SF' },\r\n            { name: 'London', code: 'LDN' },\r\n            { name: 'Paris', code: 'PRS' },\r\n            { name: 'Istanbul', code: 'IST' },\r\n            { name: 'Berlin', code: 'BRL' },\r\n            { name: 'Barcelona', code: 'BRC' },\r\n            { name: 'Rome', code: 'RM' }];\r\n\r\n        this.targetCities = [];\r\n\r\n        this.orderCities = [\r\n            { name: 'San Francisco', code: 'SF' },\r\n            { name: 'London', code: 'LDN' },\r\n            { name: 'Paris', code: 'PRS' },\r\n            { name: 'Istanbul', code: 'IST' },\r\n            { name: 'Berlin', code: 'BRL' },\r\n            { name: 'Barcelona', code: 'BRC' },\r\n            { name: 'Rome', code: 'RM' }];\r\n\r\n        // this.sortOptionsCountry = [\r\n        //     { label: 'Price High to Low', value: '!price' },\r\n        //     { label: 'Price Low to High', value: 'price' }\r\n        // ];\r\n\r\n\r\n        this.barOptions = {\r\n            maintainAspectRatio: false,\r\n            plugins: {\r\n              legend: {\r\n                display: false\r\n              }\r\n            },\r\n            scales: {\r\n              x: {\r\n                display: false\r\n              },\r\n              y: {\r\n                display: false\r\n              }\r\n            }\r\n          };\r\n        \r\n          this.lineOptions = {\r\n            maintainAspectRatio: false,\r\n            elements: {\r\n              line: {\r\n                tension: 0.4\r\n              },\r\n              point: {\r\n                radius: 0\r\n              }\r\n            },\r\n            plugins: {\r\n              legend: {\r\n                display: false\r\n              }\r\n            },\r\n            scales: {\r\n              x: {\r\n                display: false\r\n              },\r\n              y: {\r\n                display: false\r\n              }\r\n            }\r\n          };\r\n        \r\n          this.data = [\r\n            {\r\n              labels: ['M', 'T', 'W', 'T', 'F', 'S', 'S', 'M', 'T', 'W', 'T', 'F', 'S', 'S', 'M'],\r\n              datasets: [\r\n                {\r\n                  backgroundColor: '#321fdb',\r\n                  borderColor: 'transparent',\r\n                  borderWidth: 1,\r\n                  data: [41, 78, 51, 66, 74, 42, 89, 97, 87, 84, 78, 88, 67, 45, 47]\r\n                }\r\n              ]\r\n            }, {\r\n              labels: ['M', 'T', 'W', 'T', 'F', 'S', 'S', 'M', 'T', 'W', 'T', 'F', 'S', 'S', 'M'],\r\n              datasets: [\r\n                {\r\n                  backgroundColor: 'transparent',\r\n                  borderColor: '#321fdb',\r\n                  borderWidth: 2,\r\n                  data: [41, 78, 51, 66, 74, 42, 89, 97, 87, 84, 78, 88, 67, 45, 47],\r\n                  pointBackgroundColor: '#321fdb'\r\n                }\r\n              ]\r\n            }\r\n          ];\r\n\r\n\r\n\r\n    }\r\n\r\n    onSortChange(event: any) {\r\n        const value = event.value;\r\n\r\n        if (value.indexOf('!') === 0) {\r\n            this.sortOrder = -1;\r\n            this.sortField = value.substring(1, value.length);\r\n        } else {\r\n            this.sortOrder = 1;\r\n            this.sortField = value;\r\n        }\r\n    }\r\n\r\n    onFilter(dv: DataView, event: Event) {\r\n        dv.filter((event.target as HTMLInputElement).value);\r\n    }\r\n\r\n\r\n    initMap(){\r\n        this.mapSrc = \"https://www.digitaltrends.com/wp-content/uploads/2020/02/custom-map.jpg?fit=720%2C479&p=1\"\r\n    }\r\n\r\n    // initChart() {\r\n    //     const documentStyle = getComputedStyle(document.documentElement);\r\n    //     const textColor = documentStyle.getPropertyValue('--text-color');\r\n    //     const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\r\n    //     const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\r\n\r\n    //     this.chartData = {\r\n    //         labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\r\n    //         datasets: [\r\n    //             {\r\n    //                 label: 'First Dataset',\r\n    //                 data: [65, 59, 80, 81, 56, 55, 40],\r\n    //                 fill: false,\r\n    //                 backgroundColor: documentStyle.getPropertyValue('--bluegray-700'),\r\n    //                 borderColor: documentStyle.getPropertyValue('--bluegray-700'),\r\n    //                 tension: .4\r\n    //             },\r\n    //             {\r\n    //                 label: 'Second Dataset',\r\n    //                 data: [28, 48, 40, 19, 86, 27, 90],\r\n    //                 fill: false,\r\n    //                 backgroundColor: documentStyle.getPropertyValue('--green-600'),\r\n    //                 borderColor: documentStyle.getPropertyValue('--green-600'),\r\n    //                 tension: .4\r\n    //             }\r\n    //         ]\r\n    //     };\r\n\r\n    //     this.chartOptions = {\r\n    //         plugins: {\r\n    //             legend: {\r\n    //                 labels: {\r\n    //                     color: textColor\r\n    //                 }\r\n    //             }\r\n    //         },\r\n    //         scales: {\r\n    //             x: {\r\n    //                 ticks: {\r\n    //                     color: textColorSecondary\r\n    //                 },\r\n    //                 grid: {\r\n    //                     color: surfaceBorder,\r\n    //                     drawBorder: false\r\n    //                 }\r\n    //             },\r\n    //             y: {\r\n    //                 ticks: {\r\n    //                     color: textColorSecondary\r\n    //                 },\r\n    //                 grid: {\r\n    //                     color: surfaceBorder,\r\n    //                     drawBorder: false\r\n    //                 }\r\n    //             }\r\n    //         }\r\n    //     };\r\n    // }\r\n\r\n    ngOnDestroy() {\r\n        if (this.subscription) {\r\n            this.subscription.unsubscribe();\r\n        }\r\n    }\r\n}\r\n", "<div class=\"grid\">\r\n    <div class=\"col-12 lg:col-6 xl:col-6\">\r\n        <div class=\"card mb-0\">\r\n            <div class=\"flex justify-content-between mb-3\">\r\n                <div>\r\n                    <span class=\"block text-500 font-medium font-bold mb-3\">Active Energy Systems</span>\r\n                    <div class=\"text-900 font-medium text-xl\">44</div>\r\n                </div>\r\n                <div class=\"flex align-items-center justify-content-center bg-blue-100 border-round\" [ngStyle]=\"{width: '2.5rem', height: '2.5rem'}\">\r\n                    <fa-icon icon=\"plug\"></fa-icon> \r\n                </div>\r\n            </div>\r\n            <!-- <span class=\"text-green-500 font-medium\">24 new </span>\r\n            <span class=\"text-500\">since last visit</span> -->\r\n        </div>\r\n    </div>\r\n    <div class=\"col-12 lg:col-6 xl:col-6\">\r\n        <div class=\"card mb-0\">\r\n            <div class=\"flex justify-content-between mb-3\">\r\n                <div>\r\n                    <span class=\"block text-500 font-medium font-bold mb-3\">Combined Power Permormance</span>\r\n                    <div class=\"text-900 font-medium text-xl\">33% (4.124kW) </div>\r\n                </div>\r\n                <div class=\"flex align-items-center justify-content-center bg-blue-100 border-round\" [ngStyle]=\"{width: '2.5rem', height: '2.5rem'}\">\r\n                    <i class=\"pi pi-sun text-blue-500 text-xl\"></i>\r\n                </div>\r\n            </div>\r\n            <!-- <span class=\"text-green-500 font-medium\">24 new </span>\r\n            <span class=\"text-500\">since last visit</span> -->\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"col-12 lg:col-12 xl:col-12\">\r\n\t\t<div class=\"card\">\r\n\t\t\t<p-dataView #dv [value]=\"stations\" [paginator]=\"true\" [rows]=\"9\" filterBy=\"name\" [sortField]=\"sortField\" [sortOrder]=\"sortOrder\" layout=\"list\">\r\n\t\t\t\t<ng-template pTemplate=\"header\">\r\n\t\t\t\t\t<div class=\"flex flex-column md:flex-row md:justify-content-between gap-2\">\r\n\t\t\t\t\t\t<p-dropdown [options]=\"sortOptionsCountry\" placeholder=\"Country\" (onChange)=\"onSortChange($event)\"></p-dropdown>\r\n\t\t\t\t\t\t<p-dropdown [options]=\"sortOptionsStatus\" placeholder=\"Status\" (onChange)=\"onSortChange($event)\"></p-dropdown>\r\n\t\t\t\t\t\t<span class=\"p-input-icon-left\">\r\n                            <i class=\"pi pi-search\"></i>\r\n                            <input type=\"search\" pInputText placeholder=\"Search\" (input)=\"onFilter(dv, $event)\">\r\n                        </span>\t\r\n\t\t\t\t\t\t<!-- <p-dataViewLayoutOptions></p-dataViewLayoutOptions> -->\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</ng-template>\r\n\r\n\t\t\t\t<ng-template let-products pTemplate=\"listItem\">\r\n\t\t\t\t\t<div *ngFor=\"let station of stations\" [class]=\"'station-box-status-' + station.status.toLowerCase()\">\r\n\t\t\t\t\t\t<div class=\"\">\r\n\t\t\t\t\t\t\t<div class=\"grid\">\r\n\t\t\t\t\t\t\t\t<div class=\"col-12 md:col-6 lg:col-3\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"font-bold text-xl\"><a [routerLink]=\"['/uikit/station',station.id]\">{{station.name}}</a></div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"col-12 md:col-6 lg:col-2\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"text-center\"><fa-icon icon=\"plug\"></fa-icon> {{station.power}}kW</div>\t\r\n\t\t\t\t\t\t\t\t\t<div class=\"text-center text-sm\"><!--<fa-icon icon=\"plug\"></fa-icon>--> Irradiance: {{station.irradiance}}kWh/m2</div>\t\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"col-12 md:col-6 lg:col-2\">\r\n\t\t\t\t\t\t\t\t\t<c-chart [data]=\"data[1]\" [options]=\"lineOptions\" type=\"line\" class=\"mx-auto\" height=\"40\" width=\"80\" />\r\n\t\t\t\t\t\t\t\t\t<!-- <div class=\"\">Inverter: {{station.inverter}} | MMPT: {{station.mmpt}} | String: {{station.string}} | PVN: {{station.pvn}}</div> -->\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"col-12 md:col-6 lg:col-3\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"grid\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"col-3 md:col-3 lg:col-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\">Inverter</p>\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\"><p-knob [(ngModel)]=\"station.inverter\" valueTemplate=\"{{station.inverter}}\" [step]=\"1\" [min]=\"0\" [max]=\"5\" [size]=\"50\"></p-knob></p>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"col-3 md:col-3 lg:col-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\">MMPT</p>\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\"><p-knob [(ngModel)]=\"station.mmpt\" valueTemplate=\"{{station.mmpt}}\" [step]=\"1\" [min]=\"0\" [max]=\"5\" [size]=\"50\"></p-knob></p>\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"col-3 md:col-3 lg:col-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\">String</p>\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\"><p-knob [(ngModel)]=\"station.string\" valueTemplate=\"{{station.string}}\" [step]=\"1\" [min]=\"0\" [max]=\"5\" [size]=\"50\"></p-knob></p>\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"col-3 md:col-3 lg:col-3\">\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\">PVN</p>\r\n\t\t\t\t\t\t\t\t\t\t\t<p class=\"text-center\"><p-knob [(ngModel)]=\"station.pvn\" valueTemplate=\"{{station.pvn}}\" [step]=\"1\" [min]=\"0\" [max]=\"5\" [size]=\"50\"></p-knob></p>\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\t\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"col-1\">\r\n\t\t\t\t\t\t\t\t\t<p class=\"text-center\"><i class=\"pi pi-cloud text-2xl\"></i></p>\r\n\t\t\t\t\t\t\t\t\t<p class=\"text-center\"><span class=\"text-2xl font-semibold mb-2 align-self-center md:align-self-end\"> {{station.temperature}} &#x2103;</span></p>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<div class=\"col-1\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"font-bold flex-auto text-xl align-items-right text-center\"><fa-icon icon=\"clock\"></fa-icon> {{station.updateTime}} <span [class]=\"'station-badge status-' + station.status.toLowerCase()\">{{station.status.toLowerCase()}}</span></div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<!-- <div class=\"card\">\r\n\t\t\t\t\t\t\t<div class=\"flex flex-column md:flex-row align-items-center p-3 w-full\">\r\n\t\t\t\t\t\t\t\t<div class=\"flex-1 flex flex-column md:text-left\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"flex align-items-center\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"font-bold text-xl\"><a [routerLink]=\"['/uikit/station',station.id]\">{{station.name}}</a></div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"font-bold flex-auto text-xl align-items-right text-right\">{{station.updateTime}} <span [class]=\"'station-badge status-' + station.status.toLowerCase()\">{{station.status.toLowerCase()}}</span></div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<hr>\r\n\t\t\t\t\t\t\t\t\t<div class=\"mb-2\">{{station.location}}</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"flex align-items-center mt-2\">\r\n\t\t\t\t\t\t\t\t\t\t<p-knob [(ngModel)]=\"station.percentage\" valueTemplate=\"{value}%\" [step]=\"10\" [min]=\"0\" [max]=\"100\" [size]=\"70\"></p-knob>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"align-items-right\">\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"mt-2 text-xl\">Active Performance</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-2 text-sm\">Power: {{station.power}}kW | Irradiance: {{station.irradiance}}kWh/m2</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"mt-2\">Inverter: {{station.inverter}} | MMPT: {{station.mmpt}} | String: {{station.string}} | PVN: {{station.pvn}}</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"flex flex-row md:flex-column justify-content-between w-full md:w-auto align-items-center md:align-items-end mt-5 md:mt-0\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"pi pi-cloud text-2xl\"></i>\r\n\t\t\t\t\t\t\t\t\t\t<span class=\"text-2xl font-semibold mb-2 align-self-center md:align-self-end\">{{station.temperature}} &#x2103;</span>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div> -->\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</ng-template>\r\n\r\n\t\t\t\t<!-- <ng-template let-products pTemplate=\"listItem\">\r\n\t\t\t\t\t<div class=\"col-12\" *ngFor=\"let station of stations\">\r\n\t\t\t\t\t\t<div class=\"card\">\r\n\t\t\t\t\t\t\t<div class=\"flex flex-column md:flex-row align-items-center p-3 w-full\">\r\n\t\t\t\t\t\t\t\t<div class=\"flex-1 flex flex-column md:text-left\">\r\n\t\t\t\t\t\t\t\t\t<div class=\"flex align-items-center\">\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"font-bold text-xl\"><a [routerLink]=\"['/uikit/station',station.id]\">{{station.name}}</a></div>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"font-bold flex-auto text-xl align-items-right text-right\">{{station.updateTime}} <span [class]=\"'station-badge status-' + station.status.toLowerCase()\">{{station.status.toLowerCase()}}</span></div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<hr>\r\n\t\t\t\t\t\t\t\t\t<div class=\"mb-2\">{{station.location}}</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"flex align-items-center mt-2\">\r\n\t\t\t\t\t\t\t\t\t\t<p-knob [(ngModel)]=\"station.percentage\" valueTemplate=\"{value}%\" [step]=\"10\" [min]=\"0\" [max]=\"100\" [size]=\"70\"></p-knob>\r\n\t\t\t\t\t\t\t\t\t\t<div class=\"align-items-right\">\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"mt-2 text-xl\">Active Performance</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"mb-2 text-sm\">Power: {{station.power}}kW | Irradiance: {{station.irradiance}}kWh/m2</div>\r\n\t\t\t\t\t\t\t\t\t\t\t<div class=\"mt-2\">Inverter: {{station.inverter}} | MMPT: {{station.mmpt}} | String: {{station.string}} | PVN: {{station.pvn}}</div>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t<div class=\"flex flex-row md:flex-column justify-content-between w-full md:w-auto align-items-center md:align-items-end mt-5 md:mt-0\">\r\n\t\t\t\t\t\t\t\t\t\t<i class=\"pi pi-cloud text-2xl\"></i>\r\n\t\t\t\t\t\t\t\t\t\t<span class=\"text-2xl font-semibold mb-2 align-self-center md:align-self-end\">{{station.temperature}} &#x2103;</span>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</ng-template> -->\r\n\r\n\t\t\t\t<!-- <ng-template let-products pTemplate=\"gridItem\">\r\n\t\t\t\t\t<div class=\"grid grid-nogutter\">\r\n\t\t\t\t\t<div class=\"col-12 md:col-4\" *ngFor=\"let station of stations\">\r\n\t\t\t\t\t\t<div class=\"card m-3 border-1 surface-border\">\r\n\t\t\t\t\t\t\t<div class=\"flex flex-wrap gap-2 align-items-center justify-content-between mb-2\">\r\n\t\t\t\t\t\t\t\t<div class=\"flex align-items-center\">\r\n\t\t\t\t\t\t\t\t\t<i class=\"pi pi-tag mr-2\"></i>\r\n\t\t\t\t\t\t\t\t\t<span class=\"font-semibold\">{{station.name}}</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"flex flex-column align-items-center text-center mb-3\">\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t<div class=\"text-2xl font-bold\">{{station.name}}</div>\r\n\t\t\t\t\t\t\t\t<div class=\"mb-3\">{{station.location}}</div>\r\n\t\t\t\t\t\t\t\t<p-rating [ngModel]=\"station.temperature\" [readonly]=\"true\" [cancel]=\"false\"></p-rating>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"flex align-items-center justify-content-between\">\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</ng-template> -->\r\n\t\t\t</p-dataView>\r\n\t\t</div>\r\n\t</div>\r\n\r\n    <!-- <div class=\"col-12 lg:col-6 xl:col-6\">\r\n\t\t<div class=\"card\">\r\n\t\t\t<img src=\"https://www.digitaltrends.com/wp-content/uploads/2020/02/custom-map.jpg?fit=720%2C479&p=1\" >\r\n\t\t</div>\r\n\t</div> -->\r\n</div>"], "mappings": "AAIA,SAAuBA,YAAY,QAAQ,MAAM;;;;;;;;;;;;;;;;;ICgC5CC,EAAA,CAAAC,cAAA,cAA2E;IACTD,EAAA,CAAAE,UAAA,sBAAAC,sEAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAYR,EAAA,CAAAS,WAAA,CAAAF,MAAA,CAAAG,YAAA,CAAAN,MAAA,CAAoB;IAAA,EAAC;IAACJ,EAAA,CAAAW,YAAA,EAAa;IAChHX,EAAA,CAAAC,cAAA,qBAAiG;IAAlCD,EAAA,CAAAE,UAAA,sBAAAU,sEAAAR,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAO,MAAA,GAAAb,EAAA,CAAAQ,aAAA;MAAA,OAAYR,EAAA,CAAAS,WAAA,CAAAI,MAAA,CAAAH,YAAA,CAAAN,MAAA,CAAoB;IAAA,EAAC;IAACJ,EAAA,CAAAW,YAAA,EAAa;IAC9GX,EAAA,CAAAC,cAAA,eAAgC;IACVD,EAAA,CAAAc,SAAA,YAA4B;IAC5Bd,EAAA,CAAAC,cAAA,gBAAoF;IAA/BD,EAAA,CAAAE,UAAA,mBAAAa,8DAAAX,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAU,MAAA,GAAAhB,EAAA,CAAAQ,aAAA;MAAA,MAAAS,GAAA,GAAAjB,EAAA,CAAAkB,WAAA;MAAA,OAASlB,EAAA,CAAAS,WAAA,CAAAO,MAAA,CAAAG,QAAA,CAAAF,GAAA,EAAAb,MAAA,CAAoB;IAAA,EAAC;IAAnFJ,EAAA,CAAAW,YAAA,EAAoF;;;;IAJ9FX,EAAA,CAAAoB,SAAA,GAA8B;IAA9BpB,EAAA,CAAAqB,UAAA,YAAAC,MAAA,CAAAC,kBAAA,CAA8B;IAC9BvB,EAAA,CAAAoB,SAAA,GAA6B;IAA7BpB,EAAA,CAAAqB,UAAA,YAAAC,MAAA,CAAAE,iBAAA,CAA6B;;;;;;;IAU1CxB,EAAA,CAAAC,cAAA,UAAqG;IAIlBD,EAAA,CAAAyB,MAAA,GAAgB;IAAAzB,EAAA,CAAAW,YAAA,EAAI;IAEpGX,EAAA,CAAAC,cAAA,cAAsC;IACZD,EAAA,CAAAc,SAAA,iBAA+B;IAACd,EAAA,CAAAyB,MAAA,IAAmB;IAAAzB,EAAA,CAAAW,YAAA,EAAM;IAClFX,EAAA,CAAAC,cAAA,eAAiC;IAAuCD,EAAA,CAAAyB,MAAA,IAAwC;IAAAzB,EAAA,CAAAW,YAAA,EAAM;IAEvHX,EAAA,CAAAC,cAAA,eAAsC;IACrCD,EAAA,CAAAc,SAAA,mBAAuG;IAExGd,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAAsC;IAGZD,EAAA,CAAAyB,MAAA,gBAAQ;IAAAzB,EAAA,CAAAW,YAAA,EAAI;IACnCX,EAAA,CAAAC,cAAA,aAAuB;IAAQD,EAAA,CAAAE,UAAA,2BAAAwB,8EAAAtB,MAAA;MAAA,MAAAuB,WAAA,GAAA3B,EAAA,CAAAK,aAAA,CAAAuB,IAAA;MAAA,MAAAC,UAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,OAAa9B,EAAA,CAAAS,WAAA,CAAAoB,UAAA,CAAAE,QAAA,GAAA3B,MAAA,CAAwB;IAAA,EAAP;IAAiFJ,EAAA,CAAAW,YAAA,EAAS;IAExJX,EAAA,CAAAC,cAAA,eAAqC;IACbD,EAAA,CAAAyB,MAAA,YAAI;IAAAzB,EAAA,CAAAW,YAAA,EAAI;IAC/BX,EAAA,CAAAC,cAAA,aAAuB;IAAQD,EAAA,CAAAE,UAAA,2BAAA8B,8EAAA5B,MAAA;MAAA,MAAAuB,WAAA,GAAA3B,EAAA,CAAAK,aAAA,CAAAuB,IAAA;MAAA,MAAAC,UAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,OAAa9B,EAAA,CAAAS,WAAA,CAAAoB,UAAA,CAAAI,IAAA,GAAA7B,MAAA,CAAoB;IAAA,EAAP;IAA6EJ,EAAA,CAAAW,YAAA,EAAS;IAGhJX,EAAA,CAAAC,cAAA,eAAqC;IACbD,EAAA,CAAAyB,MAAA,cAAM;IAAAzB,EAAA,CAAAW,YAAA,EAAI;IACjCX,EAAA,CAAAC,cAAA,aAAuB;IAAQD,EAAA,CAAAE,UAAA,2BAAAgC,8EAAA9B,MAAA;MAAA,MAAAuB,WAAA,GAAA3B,EAAA,CAAAK,aAAA,CAAAuB,IAAA;MAAA,MAAAC,UAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,OAAa9B,EAAA,CAAAS,WAAA,CAAAoB,UAAA,CAAAM,MAAA,GAAA/B,MAAA,CAAsB;IAAA,EAAP;IAA+EJ,EAAA,CAAAW,YAAA,EAAS;IAGpJX,EAAA,CAAAC,cAAA,eAAqC;IACbD,EAAA,CAAAyB,MAAA,WAAG;IAAAzB,EAAA,CAAAW,YAAA,EAAI;IAC9BX,EAAA,CAAAC,cAAA,aAAuB;IAAQD,EAAA,CAAAE,UAAA,2BAAAkC,8EAAAhC,MAAA;MAAA,MAAAuB,WAAA,GAAA3B,EAAA,CAAAK,aAAA,CAAAuB,IAAA;MAAA,MAAAC,UAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,OAAa9B,EAAA,CAAAS,WAAA,CAAAoB,UAAA,CAAAQ,GAAA,GAAAjC,MAAA,CAAmB;IAAA,EAAP;IAA4EJ,EAAA,CAAAW,YAAA,EAAS;IAKhJX,EAAA,CAAAC,cAAA,eAAmB;IACKD,EAAA,CAAAc,SAAA,aAAoC;IAAAd,EAAA,CAAAW,YAAA,EAAI;IAC/DX,EAAA,CAAAC,cAAA,aAAuB;IAA+ED,EAAA,CAAAyB,MAAA,IAAgC;IAAAzB,EAAA,CAAAW,YAAA,EAAO;IAE9IX,EAAA,CAAAC,cAAA,eAAmB;IACqDD,EAAA,CAAAc,SAAA,mBAAgC;IAACd,EAAA,CAAAyB,MAAA,IAAuB;IAAAzB,EAAA,CAAAC,cAAA,YAAuE;IAAAD,EAAA,CAAAyB,MAAA,IAAgC;IAAAzB,EAAA,CAAAW,YAAA,EAAO;;;;;IA1C3MX,EAAA,CAAAsC,UAAA,yBAAAT,UAAA,CAAAU,MAAA,CAAAC,WAAA,GAA8D;IAI9DxC,EAAA,CAAAoB,SAAA,GAA4C;IAA5CpB,EAAA,CAAAqB,UAAA,eAAArB,EAAA,CAAAyC,eAAA,KAAAC,GAAA,EAAAb,UAAA,CAAAc,EAAA,EAA4C;IAAC3C,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAA4C,iBAAA,CAAAf,UAAA,CAAAgB,IAAA,CAAgB;IAGtC7C,EAAA,CAAAoB,SAAA,GAAmB;IAAnBpB,EAAA,CAAA8C,kBAAA,MAAAjB,UAAA,CAAAkB,KAAA,OAAmB;IACJ/C,EAAA,CAAAoB,SAAA,GAAwC;IAAxCpB,EAAA,CAAA8C,kBAAA,kBAAAjB,UAAA,CAAAmB,UAAA,WAAwC;IAGvGhD,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAAqB,UAAA,SAAA4B,MAAA,CAAAC,IAAA,IAAgB,YAAAD,MAAA,CAAAE,WAAA;IAOuCnD,EAAA,CAAAoB,SAAA,GAAoC;IAApCpB,EAAA,CAAAoD,qBAAA,kBAAAvB,UAAA,CAAAE,QAAA,CAAoC;IAAnE/B,EAAA,CAAAqB,UAAA,YAAAQ,UAAA,CAAAE,QAAA,CAA8B;IAIH/B,EAAA,CAAAoB,SAAA,GAAgC;IAAhCpB,EAAA,CAAAoD,qBAAA,kBAAAvB,UAAA,CAAAI,IAAA,CAAgC;IAA3DjC,EAAA,CAAAqB,UAAA,YAAAQ,UAAA,CAAAI,IAAA,CAA0B;IAKGjC,EAAA,CAAAoB,SAAA,GAAkC;IAAlCpB,EAAA,CAAAoD,qBAAA,kBAAAvB,UAAA,CAAAM,MAAA,CAAkC;IAA/DnC,EAAA,CAAAqB,UAAA,YAAAQ,UAAA,CAAAM,MAAA,CAA4B;IAKFnC,EAAA,CAAAoB,SAAA,GAA+B;IAA/BpB,EAAA,CAAAoD,qBAAA,kBAAAvB,UAAA,CAAAQ,GAAA,CAA+B;IAAzDrC,EAAA,CAAAqB,UAAA,YAAAQ,UAAA,CAAAQ,GAAA,CAAyB;IAO4CrC,EAAA,CAAAoB,SAAA,GAAgC;IAAhCpB,EAAA,CAAA8C,kBAAA,MAAAjB,UAAA,CAAAwB,WAAA,YAAgC;IAG9BrD,EAAA,CAAAoB,SAAA,GAAuB;IAAvBpB,EAAA,CAAA8C,kBAAA,MAAAjB,UAAA,CAAAyB,UAAA,MAAuB;IAAMtD,EAAA,CAAAoB,SAAA,GAAgE;IAAhEpB,EAAA,CAAAsC,UAAA,2BAAAT,UAAA,CAAAU,MAAA,CAAAC,WAAA,GAAgE;IAACxC,EAAA,CAAAoB,SAAA,GAAgC;IAAhCpB,EAAA,CAAA4C,iBAAA,CAAAf,UAAA,CAAAU,MAAA,CAAAC,WAAA,GAAgC;;;;;IA1C1OxC,EAAA,CAAAuD,UAAA,IAAAC,4CAAA,oBAuEM;;;;IAvEmBxD,EAAA,CAAAqB,UAAA,YAAAoC,MAAA,CAAAC,QAAA,CAAW;;;;;;;ADlCzC,OAAM,MAAOC,cAAc;EAiCvBC,YAAmBC,aAA4B,EACnCC,eAAgC;IADzB,KAAAD,aAAa,GAAbA,aAAa;IACpB,KAAAC,eAAe,GAAfA,eAAe;IA9B3B,KAAAJ,QAAQ,GAAa,EAAE;IAQvB,KAAAnC,kBAAkB,GAAiB,EAAE;IAErC,KAAAC,iBAAiB,GAAiB,EAAE;IAEpC,KAAAuC,SAAS,GAAW,CAAC;IAErB,KAAAC,SAAS,GAAW,EAAE;IAEtB,KAAAC,YAAY,GAAU,EAAE;IAExB,KAAAC,YAAY,GAAU,EAAE;IAExB,KAAAC,WAAW,GAAU,EAAE;IAWnB,IAAI,CAACC,YAAY,GAAG,IAAI,CAACP,aAAa,CAACQ,aAAa,CACnDC,IAAI,CAACvE,YAAY,CAAC,EAAE,CAAC,CAAC,CACtBwE,SAAS,CAAEC,MAAM,IAAI;MACrB;IAAA,CACA,CAAC;EACN;EAEAC,QAAQA,CAAA;IACJ,IAAI,CAACX,eAAe,CAACY,WAAW,EAAE,CAACC,IAAI,CAACzB,IAAI,IAAI,IAAI,CAACQ,QAAQ,GAAGR,IAAI,CAAC;IAErE,IAAI,CAACe,YAAY,GAAG,CAChB;MAAEpB,IAAI,EAAE,eAAe;MAAE+B,IAAI,EAAE;IAAI,CAAE,EACrC;MAAE/B,IAAI,EAAE,QAAQ;MAAE+B,IAAI,EAAE;IAAK,CAAE,EAC/B;MAAE/B,IAAI,EAAE,OAAO;MAAE+B,IAAI,EAAE;IAAK,CAAE,EAC9B;MAAE/B,IAAI,EAAE,UAAU;MAAE+B,IAAI,EAAE;IAAK,CAAE,EACjC;MAAE/B,IAAI,EAAE,QAAQ;MAAE+B,IAAI,EAAE;IAAK,CAAE,EAC/B;MAAE/B,IAAI,EAAE,WAAW;MAAE+B,IAAI,EAAE;IAAK,CAAE,EAClC;MAAE/B,IAAI,EAAE,MAAM;MAAE+B,IAAI,EAAE;IAAI,CAAE,CAAC;IAEjC,IAAI,CAACV,YAAY,GAAG,EAAE;IAEtB,IAAI,CAACC,WAAW,GAAG,CACf;MAAEtB,IAAI,EAAE,eAAe;MAAE+B,IAAI,EAAE;IAAI,CAAE,EACrC;MAAE/B,IAAI,EAAE,QAAQ;MAAE+B,IAAI,EAAE;IAAK,CAAE,EAC/B;MAAE/B,IAAI,EAAE,OAAO;MAAE+B,IAAI,EAAE;IAAK,CAAE,EAC9B;MAAE/B,IAAI,EAAE,UAAU;MAAE+B,IAAI,EAAE;IAAK,CAAE,EACjC;MAAE/B,IAAI,EAAE,QAAQ;MAAE+B,IAAI,EAAE;IAAK,CAAE,EAC/B;MAAE/B,IAAI,EAAE,WAAW;MAAE+B,IAAI,EAAE;IAAK,CAAE,EAClC;MAAE/B,IAAI,EAAE,MAAM;MAAE+B,IAAI,EAAE;IAAI,CAAE,CAAC;IAEjC;IACA;IACA;IACA;IAGA,IAAI,CAACC,UAAU,GAAG;MACdC,mBAAmB,EAAE,KAAK;MAC1BC,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE;;OAEZ;MACDC,MAAM,EAAE;QACNC,CAAC,EAAE;UACDF,OAAO,EAAE;SACV;QACDG,CAAC,EAAE;UACDH,OAAO,EAAE;;;KAGd;IAED,IAAI,CAAC9B,WAAW,GAAG;MACjB2B,mBAAmB,EAAE,KAAK;MAC1BO,QAAQ,EAAE;QACRC,IAAI,EAAE;UACJC,OAAO,EAAE;SACV;QACDC,KAAK,EAAE;UACLC,MAAM,EAAE;;OAEX;MACDV,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE;;OAEZ;MACDC,MAAM,EAAE;QACNC,CAAC,EAAE;UACDF,OAAO,EAAE;SACV;QACDG,CAAC,EAAE;UACDH,OAAO,EAAE;;;KAGd;IAED,IAAI,CAAC/B,IAAI,GAAG,CACV;MACEwC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MACnFC,QAAQ,EAAE,CACR;QACEC,eAAe,EAAE,SAAS;QAC1BC,WAAW,EAAE,aAAa;QAC1BC,WAAW,EAAE,CAAC;QACd5C,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;OAClE;KAEJ,EAAE;MACDwC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MACnFC,QAAQ,EAAE,CACR;QACEC,eAAe,EAAE,aAAa;QAC9BC,WAAW,EAAE,SAAS;QACtBC,WAAW,EAAE,CAAC;QACd5C,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QAClE6C,oBAAoB,EAAE;OACvB;KAEJ,CACF;EAIP;EAEArF,YAAYA,CAACsF,KAAU;IACnB,MAAMC,KAAK,GAAGD,KAAK,CAACC,KAAK;IAEzB,IAAIA,KAAK,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;MAC1B,IAAI,CAACnC,SAAS,GAAG,CAAC,CAAC;MACnB,IAAI,CAACC,SAAS,GAAGiC,KAAK,CAACE,SAAS,CAAC,CAAC,EAAEF,KAAK,CAACG,MAAM,CAAC;KACpD,MAAM;MACH,IAAI,CAACrC,SAAS,GAAG,CAAC;MAClB,IAAI,CAACC,SAAS,GAAGiC,KAAK;;EAE9B;EAEA9E,QAAQA,CAACkF,EAAY,EAAEL,KAAY;IAC/BK,EAAE,CAACC,MAAM,CAAEN,KAAK,CAACO,MAA2B,CAACN,KAAK,CAAC;EACvD;EAGAO,OAAOA,CAAA;IACH,IAAI,CAACC,MAAM,GAAG,2FAA2F;EAC7G;EAEA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEAC,WAAWA,CAAA;IACP,IAAI,IAAI,CAACtC,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAACuC,WAAW,EAAE;;EAEvC;EAAC,QAAAC,CAAA,G;qBAlOQjD,cAAc,EAAA3D,EAAA,CAAA6G,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA/G,EAAA,CAAA6G,iBAAA,CAAAG,EAAA,CAAAC,eAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAdvD,cAAc;IAAAwD,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCd3BzH,EAAA,CAAAC,cAAA,aAAkB;QAK0DD,EAAA,CAAAyB,MAAA,4BAAqB;QAAAzB,EAAA,CAAAW,YAAA,EAAO;QACpFX,EAAA,CAAAC,cAAA,aAA0C;QAAAD,EAAA,CAAAyB,MAAA,SAAE;QAAAzB,EAAA,CAAAW,YAAA,EAAM;QAEtDX,EAAA,CAAAC,cAAA,aAAqI;QACjID,EAAA,CAAAc,SAAA,kBAA+B;QACnCd,EAAA,CAAAW,YAAA,EAAM;QAMlBX,EAAA,CAAAC,cAAA,cAAsC;QAIkCD,EAAA,CAAAyB,MAAA,kCAA0B;QAAAzB,EAAA,CAAAW,YAAA,EAAO;QACzFX,EAAA,CAAAC,cAAA,cAA0C;QAAAD,EAAA,CAAAyB,MAAA,sBAAc;QAAAzB,EAAA,CAAAW,YAAA,EAAM;QAElEX,EAAA,CAAAC,cAAA,cAAqI;QACjID,EAAA,CAAAc,SAAA,YAA+C;QACnDd,EAAA,CAAAW,YAAA,EAAM;QAOlBX,EAAA,CAAAC,cAAA,cAAwC;QAGxCD,EAAA,CAAAuD,UAAA,KAAAoE,sCAAA,0BAUc,KAAAC,sCAAA;QAmIf5H,EAAA,CAAAW,YAAA,EAAa;;;QAxKqFX,EAAA,CAAAoB,SAAA,GAA+C;QAA/CpB,EAAA,CAAAqB,UAAA,YAAArB,EAAA,CAAA6H,eAAA,IAAAC,GAAA,EAA+C;QAe/C9H,EAAA,CAAAoB,SAAA,IAA+C;QAA/CpB,EAAA,CAAAqB,UAAA,YAAArB,EAAA,CAAA6H,eAAA,IAAAC,GAAA,EAA+C;QAWjI9H,EAAA,CAAAoB,SAAA,GAAkB;QAAlBpB,EAAA,CAAAqB,UAAA,UAAAqG,GAAA,CAAAhE,QAAA,CAAkB,4CAAAgE,GAAA,CAAA1D,SAAA,eAAA0D,GAAA,CAAA3D,SAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}