{"ast": null, "code": "import { MessageService } from 'primeng/api';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/fileupload\";\nfunction FileDemoComponent_ng_template_6_ul_0_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r3 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", file_r3.name, \" - \", file_r3.size, \" bytes\");\n  }\n}\nfunction FileDemoComponent_ng_template_6_ul_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\");\n    i0.ɵɵtemplate(1, FileDemoComponent_ng_template_6_ul_0_li_1_Template, 2, 2, \"li\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.uploadedFiles);\n  }\n}\nfunction FileDemoComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, FileDemoComponent_ng_template_6_ul_0_Template, 2, 1, \"ul\", 6);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.uploadedFiles.length);\n  }\n}\nexport let FileDemoComponent = /*#__PURE__*/(() => {\n  class FileDemoComponent {\n    constructor(messageService) {\n      this.messageService = messageService;\n      this.uploadedFiles = [];\n    }\n    onUpload(event) {\n      for (const file of event.files) {\n        this.uploadedFiles.push(file);\n      }\n      this.messageService.add({\n        severity: 'info',\n        summary: 'Success',\n        detail: 'File Uploaded'\n      });\n    }\n    onBasicUpload() {\n      this.messageService.add({\n        severity: 'info',\n        summary: 'Success',\n        detail: 'File Uploaded with Basic Mode'\n      });\n    }\n    static #_ = this.ɵfac = function FileDemoComponent_Factory(t) {\n      return new (t || FileDemoComponent)(i0.ɵɵdirectiveInject(i1.MessageService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FileDemoComponent,\n      selectors: [[\"ng-component\"]],\n      features: [i0.ɵɵProvidersFeature([MessageService])],\n      decls: 10,\n      vars: 3,\n      consts: [[1, \"grid\"], [1, \"col-12\"], [1, \"card\"], [\"name\", \"demo[]\", \"url\", \"./upload.php\", \"accept\", \"image/*\", 3, \"multiple\", \"maxFileSize\", \"onUpload\"], [\"pTemplate\", \"content\"], [\"mode\", \"basic\", \"name\", \"demo[]\", \"url\", \"./upload.php\", \"accept\", \"image/*\", 3, \"maxFileSize\", \"onUpload\"], [4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"]],\n      template: function FileDemoComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h5\");\n          i0.ɵɵtext(4, \"Advanced\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p-fileUpload\", 3);\n          i0.ɵɵlistener(\"onUpload\", function FileDemoComponent_Template_p_fileUpload_onUpload_5_listener($event) {\n            return ctx.onUpload($event);\n          });\n          i0.ɵɵtemplate(6, FileDemoComponent_ng_template_6_Template, 1, 1, \"ng-template\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"h5\");\n          i0.ɵɵtext(8, \"Basic\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p-fileUpload\", 5);\n          i0.ɵɵlistener(\"onUpload\", function FileDemoComponent_Template_p_fileUpload_onUpload_9_listener() {\n            return ctx.onBasicUpload();\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"multiple\", true)(\"maxFileSize\", 1000000);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"maxFileSize\", 1000000);\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf, i3.FileUpload, i1.PrimeTemplate],\n      encapsulation: 2\n    });\n  }\n  return FileDemoComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}