{"ast": null, "code": "import { debounceTime } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/layout/service/app.layout.service\";\nimport * as i2 from \"../../service/stations.service\";\nimport * as i3 from \"../../service/providers.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"../../service/cache.service\";\nimport * as i8 from \"../../service/date-utils.service\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"primeng/tooltip\";\nimport * as i11 from \"primeng/button\";\nimport * as i12 from \"primeng/inputtext\";\nimport * as i13 from \"primeng/dropdown\";\nimport * as i14 from \"@coreui/angular-chartjs\";\nimport * as i15 from \"primeng/badge\";\nimport * as i16 from \"@fortawesome/angular-fontawesome\";\nimport * as i17 from \"primeng/card\";\nimport * as i18 from \"primeng/tag\";\nimport * as i19 from \"primeng/ripple\";\nimport * as i20 from \"primeng/paginator\";\nfunction IndexComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"div\", 8);\n    i0.ɵɵelement(3, \"fa-icon\", 9);\n    i0.ɵɵelementStart(4, \"span\", 10);\n    i0.ɵɵtext(5, \"Active Energy Systems\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 11);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"p-tag\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 13);\n    i0.ɵɵelement(10, \"fa-icon\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r0.stations.length);\n  }\n}\nfunction IndexComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"div\", 8);\n    i0.ɵɵelement(3, \"fa-icon\", 15);\n    i0.ɵɵelementStart(4, \"span\", 10);\n    i0.ɵɵtext(5, \"Combined Power Performance\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 11);\n    i0.ɵɵtext(7, \"33% (4.124kW)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"p-tag\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 17);\n    i0.ɵɵelement(10, \"fa-icon\", 18);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction IndexComponent_ng_template_9_p_badge_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-badge\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", ctx_r4.totalItems);\n  }\n}\nfunction IndexComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20);\n    i0.ɵɵelement(2, \"fa-icon\", 21);\n    i0.ɵɵelementStart(3, \"h2\", 22);\n    i0.ɵɵtext(4, \"Energy Stations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, IndexComponent_ng_template_9_p_badge_5_Template, 1, 1, \"p-badge\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 24)(7, \"p-button\", 25);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_9_Template_p_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.clearFilters());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p-button\", 26);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_9_Template_p_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.refreshData());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.totalItems > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"loading\", ctx_r2.isRefreshing);\n  }\n}\nfunction IndexComponent_ng_template_10_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 37);\n    i0.ɵɵelement(2, \"fa-icon\", 38);\n    i0.ɵɵelementStart(3, \"p\", 39);\n    i0.ɵɵtext(4, \"No stations found matching your criteria.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p-button\", 40);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_10_div_8_Template_p_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.clearFilters());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction IndexComponent_ng_template_10_div_9_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 43)(2, \"div\", 20);\n    i0.ɵɵelement(3, \"p-tag\", 44)(4, \"p-tag\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 46);\n    i0.ɵɵelement(6, \"fa-icon\", 47);\n    i0.ɵɵelementStart(7, \"span\", 48);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const station_r13 = i0.ɵɵnextContext().$implicit;\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", station_r13.provider);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", (station_r13.status == null ? null : station_r13.status.toLowerCase()) || \"unknown\")(\"severity\", (station_r13.status == null ? null : station_r13.status.toLowerCase()) === \"online\" ? \"success\" : \"warning\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r14.getStationLastUpdate(station_r13.id));\n  }\n}\nfunction IndexComponent_ng_template_10_div_9_ng_template_3_c_chart_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"c-chart\", 75);\n  }\n  if (rf & 2) {\n    const station_r13 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"data\", ctx_r17.getStationsRealTimeData(station_r13))(\"options\", ctx_r17.lineOptions);\n  }\n}\nfunction IndexComponent_ng_template_10_div_9_ng_template_3_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵelement(1, \"fa-icon\", 77);\n    i0.ɵɵelementStart(2, \"p\", 78);\n    i0.ɵɵtext(3, \"No chart data\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction IndexComponent_ng_template_10_div_9_ng_template_3_p_badge_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-badge\", 79);\n  }\n  if (rf & 2) {\n    const station_r13 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", \"Inv: \" + ctx_r19.getStationsInverters(station_r13.id));\n  }\n}\nfunction IndexComponent_ng_template_10_div_9_ng_template_3_p_badge_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-badge\", 80);\n  }\n  if (rf & 2) {\n    const station_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"value\", \"MMPT: \" + station_r13.mmpt);\n  }\n}\nfunction IndexComponent_ng_template_10_div_9_ng_template_3_p_badge_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-badge\", 81);\n  }\n  if (rf & 2) {\n    const station_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"value\", \"Str: \" + station_r13.string);\n  }\n}\nfunction IndexComponent_ng_template_10_div_9_ng_template_3_p_badge_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-badge\", 82);\n  }\n  if (rf & 2) {\n    const station_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"value\", \"PVN: \" + station_r13.pvn);\n  }\n}\nfunction IndexComponent_ng_template_10_div_9_ng_template_3_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵelement(1, \"fa-icon\", 83);\n    i0.ɵɵelementStart(2, \"p\", 78);\n    i0.ɵɵtext(3, \"No equipment data\");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c0 = a1 => [\"/app/station\", a1];\nfunction IndexComponent_ng_template_10_div_9_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50)(2, \"div\", 51)(3, \"h3\", 52)(4, \"a\", 53);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 54);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 55)(9, \"div\", 56)(10, \"div\", 57);\n    i0.ɵɵelement(11, \"fa-icon\", 58);\n    i0.ɵɵelementStart(12, \"span\", 59);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 60);\n    i0.ɵɵelement(15, \"fa-icon\", 61);\n    i0.ɵɵelementStart(16, \"span\", 62);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(18, \"div\", 55)(19, \"div\", 63);\n    i0.ɵɵtemplate(20, IndexComponent_ng_template_10_div_9_ng_template_3_c_chart_20_Template, 1, 2, \"c-chart\", 64)(21, IndexComponent_ng_template_10_div_9_ng_template_3_div_21_Template, 4, 0, \"div\", 65);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 50)(23, \"div\", 66);\n    i0.ɵɵtemplate(24, IndexComponent_ng_template_10_div_9_ng_template_3_p_badge_24_Template, 1, 1, \"p-badge\", 67)(25, IndexComponent_ng_template_10_div_9_ng_template_3_p_badge_25_Template, 1, 1, \"p-badge\", 68)(26, IndexComponent_ng_template_10_div_9_ng_template_3_p_badge_26_Template, 1, 1, \"p-badge\", 69)(27, IndexComponent_ng_template_10_div_9_ng_template_3_p_badge_27_Template, 1, 1, \"p-badge\", 70)(28, IndexComponent_ng_template_10_div_9_ng_template_3_div_28_Template, 4, 0, \"div\", 71);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 55)(30, \"div\", 56)(31, \"div\", 72);\n    i0.ɵɵelement(32, \"i\", 73);\n    i0.ɵɵelementStart(33, \"span\", 74);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const station_r13 = i0.ɵɵnextContext().$implicit;\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(13, _c0, station_r13.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", station_r13.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(station_r13.location || \"Location not specified\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r15.getStationsSumData(station_r13.id), \"kW\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", station_r13.irradiance, \"kWh/m\\u00B2\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.getStationsRealTimeData(station_r13));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r15.getStationsRealTimeData(station_r13));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.getStationsInverters(station_r13.id) > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", station_r13.mmpt && station_r13.mmpt > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", station_r13.string && station_r13.string > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", station_r13.pvn && station_r13.pvn > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r15.hasEquipmentData(station_r13));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", station_r13.temperature, \"\\u00B0C\");\n  }\n}\nfunction IndexComponent_ng_template_10_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"p-card\", 41);\n    i0.ɵɵtemplate(2, IndexComponent_ng_template_10_div_9_ng_template_2_Template, 9, 4, \"ng-template\", 5)(3, IndexComponent_ng_template_10_div_9_ng_template_3_Template, 35, 15, \"ng-template\", 3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const station_r13 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", \"station-status-\" + (station_r13.status == null ? null : station_r13.status.toLowerCase()));\n  }\n}\nconst _c1 = () => [5, 10, 20, 50];\nfunction IndexComponent_ng_template_10_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"p-paginator\", 85);\n    i0.ɵɵlistener(\"onPageChange\", function IndexComponent_ng_template_10_div_10_Template_p_paginator_onPageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r30.onPageChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"rows\", ctx_r10.itemsPerPage)(\"totalRecords\", ctx_r10.totalItems)(\"first\", ctx_r10.currentPage * ctx_r10.itemsPerPage)(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(5, _c1))(\"showCurrentPageReport\", true);\n  }\n}\nfunction IndexComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 24)(2, \"p-dropdown\", 29);\n    i0.ɵɵlistener(\"ngModelChange\", function IndexComponent_ng_template_10_Template_p_dropdown_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.selectedProvider = $event);\n    })(\"onChange\", function IndexComponent_ng_template_10_Template_p_dropdown_onChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.onProviderChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-dropdown\", 30);\n    i0.ɵɵlistener(\"ngModelChange\", function IndexComponent_ng_template_10_Template_p_dropdown_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.selectedStatus = $event);\n    })(\"onChange\", function IndexComponent_ng_template_10_Template_p_dropdown_onChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.onStatusChange($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"span\", 31);\n    i0.ɵɵelement(5, \"i\", 32);\n    i0.ɵɵelementStart(6, \"input\", 33);\n    i0.ɵɵlistener(\"ngModelChange\", function IndexComponent_ng_template_10_Template_input_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.searchTerm = $event);\n    })(\"input\", function IndexComponent_ng_template_10_Template_input_input_6_listener($event) {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.onSearchChange($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 0);\n    i0.ɵɵtemplate(8, IndexComponent_ng_template_10_div_8_Template, 6, 0, \"div\", 34)(9, IndexComponent_ng_template_10_div_9_Template, 4, 1, \"div\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, IndexComponent_ng_template_10_div_10_Template, 2, 6, \"div\", 36);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"options\", ctx_r3.sortOptionsCountry)(\"ngModel\", ctx_r3.selectedProvider)(\"showClear\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"options\", ctx_r3.sortOptionsStatus)(\"ngModel\", ctx_r3.selectedStatus)(\"showClear\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.searchTerm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.paginatedStations.length === 0 && !ctx_r3.isRefreshing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.paginatedStations);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.totalItems > ctx_r3.itemsPerPage);\n  }\n}\nexport let IndexComponent = /*#__PURE__*/(() => {\n  class IndexComponent {\n    constructor(layoutService, stationsService, providersService, messageService, fb, router, cacheService, dateUtils) {\n      this.layoutService = layoutService;\n      this.stationsService = stationsService;\n      this.providersService = providersService;\n      this.messageService = messageService;\n      this.fb = fb;\n      this.router = router;\n      this.cacheService = cacheService;\n      this.dateUtils = dateUtils;\n      this.stations = [];\n      this.filteredStations = [];\n      this.paginatedStations = [];\n      this.sortOptionsCountry = [];\n      this.sortOptionsStatus = [];\n      this.sortOrder = 0;\n      this.sortField = '';\n      // Filtering properties\n      this.searchTerm = '';\n      this.selectedProvider = '';\n      this.selectedStatus = '';\n      // Pagination properties\n      this.currentPage = 0;\n      this.itemsPerPage = 5;\n      this.totalItems = 0;\n      // Loading state\n      this.isRefreshing = false;\n      this.sourceCities = [];\n      this.targetCities = [];\n      this.orderCities = [];\n      this.stationsData = new Map();\n      this.stationsRawData = new Map();\n      this.stationsSumData = new Map();\n      this.subscription = this.layoutService.configUpdate$.pipe(debounceTime(25)).subscribe(config => {\n        //   this.initChart();\n      });\n    }\n    ngOnInit() {\n      this.getUserProviders();\n      this.barOptions = {\n        maintainAspectRatio: false,\n        plugins: {\n          legend: {\n            display: false\n          }\n        },\n        scales: {\n          x: {\n            display: false\n          },\n          y: {\n            display: false\n          }\n        }\n      };\n      this.lineOptions = {\n        responsive: true,\n        maintainAspectRatio: false,\n        interaction: {\n          intersect: false,\n          mode: 'index'\n        },\n        elements: {\n          line: {\n            tension: 0.4,\n            borderWidth: 2\n          },\n          point: {\n            radius: 0,\n            hoverRadius: 4,\n            hitRadius: 10\n          }\n        },\n        plugins: {\n          legend: {\n            display: false\n          },\n          tooltip: {\n            enabled: true,\n            mode: 'index',\n            intersect: false,\n            position: 'nearest',\n            external: null,\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            titleColor: '#ffffff',\n            bodyColor: '#ffffff',\n            borderColor: 'rgba(255, 255, 255, 0.1)',\n            borderWidth: 1,\n            cornerRadius: 8,\n            displayColors: false,\n            padding: 12,\n            caretPadding: 6,\n            caretSize: 5,\n            titleFont: {\n              size: 14,\n              weight: 'bold'\n            },\n            bodyFont: {\n              size: 13\n            },\n            callbacks: {\n              title: function (context) {\n                if (context && context.length > 0) {\n                  return 'Time: ' + context[0].label;\n                }\n                return 'Time: --:--';\n              },\n              label: function (context) {\n                const label = context.dataset.label || '';\n                const value = context.parsed.y;\n                return `${label}: ${value.toFixed(2)} kW`;\n              }\n            }\n          }\n        },\n        scales: {\n          x: {\n            display: false,\n            grid: {\n              display: false\n            }\n          },\n          y: {\n            display: false,\n            grid: {\n              display: false\n            }\n          }\n        },\n        animation: {\n          duration: 750,\n          easing: 'easeInOutQuart'\n        }\n      };\n    }\n    getUserProviders() {\n      this.isRefreshing = true;\n      this.providersService.getUserProviders().then(providersData => {\n        if (providersData.length > 0) {\n          this.stationsService.getUserStations().then(stationsData => {\n            this.cacheService.setStations(stationsData);\n            this.stations = stationsData;\n            console.log(\"stations set\");\n            console.log(stationsData);\n            // Initialize filter options\n            this.initializeFilterOptions();\n            // Apply filters and pagination\n            this.applyFilters();\n            // Φορτώνουμε τα δεδομένα για κάθε σταθμό\n            this.loadAllStationsData();\n            this.isRefreshing = false;\n          });\n        } else {\n          this.router.navigate(['/app/providers']);\n        }\n      }).catch(error => {\n        console.error('Error loading providers:', error);\n        this.isRefreshing = false;\n      });\n    }\n    // Νέα μέθοδος για φόρτωση δεδομένων όλων των σταθμών\n    loadAllStationsData() {\n      if (!this.stations || this.stations.length === 0) return;\n      // Φορτώνουμε τα δεδομένα για κάθε σταθμό\n      this.stations.forEach(station => {\n        this.loadStationData(station);\n      });\n    }\n    // Νέα μέθοδος για φόρτωση δεδομένων ενός σταθμού\n    loadStationData(station) {\n      if (!station) return;\n      const now = new Date();\n      const formattedStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0).toISOString();\n      const formattedEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0).toISOString();\n      let request = {\n        devIds: station.deviceIds,\n        devTypeId: 1,\n        startDateTime: formattedStartDate,\n        endDateTime: formattedEndDate,\n        separated: false,\n        searchType: null,\n        stationId: station.id\n      };\n      this.stationsService.getStationHistoricData(request).then(data => {\n        if (data && data.data) {\n          // Filter data to not show beyond current time\n          const filteredData = this.filterDataByCurrentTime(data.data);\n          const documentStyle = getComputedStyle(document.documentElement);\n          const lineData = {\n            labels: filteredData.map((e, index) => index % 2 === 0 ? this.dateUtils.formatTimeForChart(e.dateTime) : ''),\n            datasets: [{\n              label: 'Active Power',\n              data: filteredData.map(e => e.activePower),\n              fill: false,\n              backgroundColor: documentStyle.getPropertyValue('--primary-500'),\n              borderColor: documentStyle.getPropertyValue('--primary-500'),\n              tension: .4\n            }, {\n              label: 'Total Input Power',\n              data: filteredData.map(e => e.totalInputPower),\n              fill: false,\n              backgroundColor: documentStyle.getPropertyValue('--primary-200'),\n              borderColor: documentStyle.getPropertyValue('--primary-200'),\n              tension: .4\n            }]\n          };\n          // Αποθηκεύουμε τα δεδομένα στο Map με κλειδί το ID του σταθμού\n          this.stationsData.set(station.id, lineData);\n          this.stationsRawData.set(station.id, filteredData);\n          this.stationsSumData.set(station.id, data.sum);\n        }\n      }).catch(error => {\n        console.error(`Error loading data for station ${station.id}:`, error);\n      });\n    }\n    // Τροποποιημένη μέθοδος που επιστρέφει τα προ-φορτωμένα δεδομένα\n    getStationsRealTimeData(station) {\n      // Επιστρέφουμε τα δεδομένα από το Map αν υπάρχουν\n      if (station && station.id && this.stationsData.has(station.id)) {\n        return this.stationsData.get(station.id);\n      }\n      // Αν δεν έχουν φορτωθεί ακόμα, επιστρέφουμε null\n      return null;\n    }\n    getStationsSumData(stationId) {\n      return this.stationsSumData.get(stationId);\n    }\n    getStationsInverters(stationId) {\n      var data = this.stationsRawData.get(stationId);\n      if (!data || data.length === 0) {\n        return 0;\n      } else return new Set(data.map(item => item.name)).size;\n    }\n    getStationLastUpdate(stationId) {\n      const data = this.stationsRawData.get(stationId);\n      if (!data || data.length === 0) return \"-\";\n      const latest = data.reduce((latestSoFar, current) => {\n        return new Date(current.dateTime).getTime() > new Date(latestSoFar.dateTime).getTime() ? current : latestSoFar;\n      });\n      return new Date(latest.dateTime).toLocaleString(\"en-GB\", {\n        hour: '2-digit',\n        minute: '2-digit',\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric',\n        timeZone: 'Europe/Athens'\n      });\n    }\n    onSortChange(event) {\n      const value = event.value;\n      if (value.indexOf('!') === 0) {\n        this.sortOrder = -1;\n        this.sortField = value.substring(1, value.length);\n      } else {\n        this.sortOrder = 1;\n        this.sortField = value;\n      }\n    }\n    onFilter(dv, event) {\n      dv.filter(event.target.value);\n    }\n    // Initialize filter options based on available stations\n    initializeFilterOptions() {\n      // Get unique providers\n      const providers = [...new Set(this.stations.map(station => station.provider))];\n      this.sortOptionsCountry = providers.map(provider => ({\n        label: provider,\n        value: provider\n      }));\n      // Get unique statuses\n      const statuses = [...new Set(this.stations.map(station => station.status))];\n      this.sortOptionsStatus = statuses.map(status => ({\n        label: status,\n        value: status\n      }));\n    }\n    // Apply all filters and update pagination\n    applyFilters() {\n      let filtered = [...this.stations];\n      // Apply search filter\n      if (this.searchTerm) {\n        const searchLower = this.searchTerm.toLowerCase();\n        filtered = filtered.filter(station => station.name.toLowerCase().includes(searchLower) || station.provider.toLowerCase().includes(searchLower) || station.location && station.location.toLowerCase().includes(searchLower));\n      }\n      // Apply provider filter\n      if (this.selectedProvider) {\n        filtered = filtered.filter(station => station.provider === this.selectedProvider);\n      }\n      // Apply status filter\n      if (this.selectedStatus) {\n        filtered = filtered.filter(station => station.status === this.selectedStatus);\n      }\n      this.filteredStations = filtered;\n      this.totalItems = filtered.length;\n      this.currentPage = 0; // Reset to first page\n      this.updatePagination();\n    }\n    // Update pagination based on current page\n    updatePagination() {\n      const startIndex = this.currentPage * this.itemsPerPage;\n      const endIndex = startIndex + this.itemsPerPage;\n      this.paginatedStations = this.filteredStations.slice(startIndex, endIndex);\n    }\n    // Handle search input change\n    onSearchChange(event) {\n      this.searchTerm = event.target.value;\n      this.applyFilters();\n    }\n    // Handle provider filter change\n    onProviderChange(event) {\n      this.selectedProvider = event.value || '';\n      this.applyFilters();\n    }\n    // Handle status filter change\n    onStatusChange(event) {\n      this.selectedStatus = event.value || '';\n      this.applyFilters();\n    }\n    // Handle pagination change\n    onPageChange(event) {\n      this.currentPage = event.page;\n      this.itemsPerPage = event.rows;\n      this.updatePagination();\n    }\n    // Refresh data\n    refreshData() {\n      this.getUserProviders();\n    }\n    // Clear all filters\n    clearFilters() {\n      this.searchTerm = '';\n      this.selectedProvider = '';\n      this.selectedStatus = '';\n      this.applyFilters();\n    }\n    // Check if station has equipment data\n    hasEquipmentData(station) {\n      const inverters = this.getStationsInverters(station.id || '');\n      return inverters > 0 || station.mmpt && station.mmpt > 0 || station.string && station.string > 0 || station.pvn && station.pvn > 0;\n    }\n    filterDataByCurrentTime(data) {\n      const now = new Date();\n      const currentTime = now.getTime();\n      return data.filter(item => {\n        const itemDateTime = new Date(item.dateTime);\n        const itemTime = itemDateTime.getTime();\n        // Only include data points that are not in the future\n        return itemTime <= currentTime;\n      });\n    }\n    initMap() {\n      this.mapSrc = \"https://www.digitaltrends.com/wp-content/uploads/2020/02/custom-map.jpg?fit=720%2C479&p=1\";\n    }\n    // initChart() {\n    //     const documentStyle = getComputedStyle(document.documentElement);\n    //     const textColor = documentStyle.getPropertyValue('--text-color');\n    //     const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n    //     const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\n    //     this.chartData = {\n    //         labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\n    //         datasets: [\n    //             {\n    //                 label: 'First Dataset',\n    //                 data: [65, 59, 80, 81, 56, 55, 40],\n    //                 fill: false,\n    //                 backgroundColor: documentStyle.getPropertyValue('--bluegray-700'),\n    //                 borderColor: documentStyle.getPropertyValue('--bluegray-700'),\n    //                 tension: .4\n    //             },\n    //             {\n    //                 label: 'Second Dataset',\n    //                 data: [28, 48, 40, 19, 86, 27, 90],\n    //                 fill: false,\n    //                 backgroundColor: documentStyle.getPropertyValue('--green-600'),\n    //                 borderColor: documentStyle.getPropertyValue('--green-600'),\n    //                 tension: .4\n    //             }\n    //         ]\n    //     };\n    //     this.chartOptions = {\n    //         plugins: {\n    //             legend: {\n    //                 labels: {\n    //                     color: textColor\n    //                 }\n    //             }\n    //         },\n    //         scales: {\n    //             x: {\n    //                 ticks: {\n    //                     color: textColorSecondary\n    //                 },\n    //                 grid: {\n    //                     color: surfaceBorder,\n    //                     drawBorder: false\n    //                 }\n    //             },\n    //             y: {\n    //                 ticks: {\n    //                     color: textColorSecondary\n    //                 },\n    //                 grid: {\n    //                     color: surfaceBorder,\n    //                     drawBorder: false\n    //                 }\n    //             }\n    //         }\n    //     };\n    // }\n    ngOnDestroy() {\n      if (this.subscription) {\n        this.subscription.unsubscribe();\n      }\n    }\n    static #_ = this.ɵfac = function IndexComponent_Factory(t) {\n      return new (t || IndexComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.StationsService), i0.ɵɵdirectiveInject(i3.ProvidersService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.FormBuilder), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.CacheService), i0.ɵɵdirectiveInject(i8.DateUtilsService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: IndexComponent,\n      selectors: [[\"ng-component\"]],\n      decls: 11,\n      vars: 0,\n      consts: [[1, \"grid\"], [1, \"col-12\", \"md:col-6\", \"lg:col-6\"], [1, \"h-full\"], [\"pTemplate\", \"content\"], [1, \"col-12\"], [\"pTemplate\", \"header\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\"], [1, \"flex-1\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"mb-3\"], [\"icon\", \"solar-panel\", 1, \"text-primary\", \"text-lg\"], [1, \"text-600\", \"font-medium\"], [1, \"text-900\", \"font-bold\", \"text-3xl\"], [\"value\", \"Online\", \"severity\", \"success\", 1, \"mt-2\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-primary-50\", \"border-round-lg\", 2, \"width\", \"4rem\", \"height\", \"4rem\"], [\"icon\", \"solar-panel\", 1, \"text-primary\", \"text-2xl\"], [\"icon\", \"bolt\", 1, \"text-orange-500\", \"text-lg\"], [\"value\", \"Optimal\", \"severity\", \"warning\", 1, \"mt-2\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-orange-50\", \"border-round-lg\", 2, \"width\", \"4rem\", \"height\", \"4rem\"], [\"icon\", \"bolt\", 1, \"text-orange-500\", \"text-2xl\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"p-3\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"icon\", \"list\", 1, \"text-primary\", \"text-xl\"], [1, \"text-2xl\", \"font-semibold\", \"m-0\"], [\"severity\", \"info\", 3, \"value\", 4, \"ngIf\"], [1, \"flex\", \"gap-2\"], [\"icon\", \"pi pi-filter-slash\", \"severity\", \"secondary\", \"size\", \"small\", \"pTooltip\", \"Clear all filters\", 3, \"click\"], [\"icon\", \"pi pi-refresh\", \"severity\", \"info\", \"size\", \"small\", \"pTooltip\", \"Refresh data\", 3, \"loading\", \"click\"], [\"severity\", \"info\", 3, \"value\"], [1, \"flex\", \"flex-column\", \"md:flex-row\", \"md:justify-content-between\", \"gap-3\", \"mb-4\"], [\"placeholder\", \"Filter by Provider\", 1, \"w-full\", \"md:w-auto\", 3, \"options\", \"ngModel\", \"showClear\", \"ngModelChange\", \"onChange\"], [\"placeholder\", \"Filter by Status\", 1, \"w-full\", \"md:w-auto\", 3, \"options\", \"ngModel\", \"showClear\", \"ngModelChange\", \"onChange\"], [1, \"p-input-icon-left\"], [1, \"pi\", \"pi-search\"], [\"type\", \"search\", \"pInputText\", \"\", \"placeholder\", \"Search stations...\", 1, \"w-full\", \"md:w-auto\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"class\", \"col-12\", 4, \"ngIf\"], [\"class\", \"col-12\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"mt-4\", 4, \"ngIf\"], [1, \"text-center\", \"py-6\"], [\"icon\", \"inbox\", 1, \"text-6xl\", \"text-300\", \"mb-3\"], [1, \"text-lg\", \"text-600\"], [\"label\", \"Clear Filters\", \"icon\", \"pi pi-filter-slash\", \"severity\", \"secondary\", \"size\", \"small\", 3, \"click\"], [\"pRipple\", \"\", 1, \"mb-3\", 3, \"ngClass\"], [1, \"bg-primary-50\", \"p-3\", \"border-round-top\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\"], [\"severity\", \"info\", \"icon\", \"pi pi-server\", 3, \"value\"], [3, \"value\", \"severity\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"text-600\"], [\"icon\", \"clock\", 1, \"text-sm\"], [1, \"text-sm\"], [1, \"grid\", \"align-items-center\"], [1, \"col-12\", \"md:col-6\", \"lg:col-3\"], [1, \"flex\", \"flex-column\", \"gap-2\"], [1, \"text-xl\", \"font-bold\", \"m-0\"], [1, \"text-primary\", \"no-underline\", \"hover:underline\", 3, \"routerLink\"], [1, \"text-600\", \"text-sm\"], [1, \"col-12\", \"md:col-6\", \"lg:col-2\"], [1, \"text-center\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-1\", \"mb-1\"], [\"icon\", \"bolt\", 1, \"text-orange-500\"], [1, \"font-bold\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-1\"], [\"icon\", \"sun\", 1, \"text-yellow-500\"], [1, \"text-sm\", \"text-600\"], [1, \"text-center\", \"chart-container\"], [\"type\", \"line\", \"class\", \"mx-auto chart-section\", \"height\", \"50\", \"width\", \"120\", 3, \"data\", \"options\", 4, \"ngIf\"], [\"class\", \"text-center p-2\", 4, \"ngIf\"], [1, \"flex\", \"flex-wrap\", \"gap-2\", \"justify-content-center\"], [\"severity\", \"contrast\", \"size\", \"small\", 3, \"value\", 4, \"ngIf\"], [\"severity\", \"success\", \"size\", \"small\", 3, \"value\", 4, \"ngIf\"], [\"severity\", \"info\", \"size\", \"small\", 3, \"value\", 4, \"ngIf\"], [\"severity\", \"warning\", \"size\", \"small\", 3, \"value\", 4, \"ngIf\"], [\"class\", \"text-center\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-2\"], [1, \"pi\", \"pi-cloud\", \"text-blue-500\", \"text-xl\"], [1, \"text-xl\", \"font-semibold\"], [\"type\", \"line\", \"height\", \"50\", \"width\", \"120\", 1, \"mx-auto\", \"chart-section\", 3, \"data\", \"options\"], [1, \"text-center\", \"p-2\"], [\"icon\", \"chart-line\", 1, \"text-300\", \"text-2xl\", \"mb-1\"], [1, \"text-xs\", \"text-600\", \"mt-1\", \"mb-0\"], [\"severity\", \"contrast\", \"size\", \"small\", 3, \"value\"], [\"severity\", \"success\", \"size\", \"small\", 3, \"value\"], [\"severity\", \"info\", \"size\", \"small\", 3, \"value\"], [\"severity\", \"warning\", \"size\", \"small\", 3, \"value\"], [\"icon\", \"cog\", 1, \"text-300\", \"text-lg\"], [1, \"mt-4\"], [\"currentPageReportTemplate\", \"Showing {first} to {last} of {totalRecords} stations\", 3, \"rows\", \"totalRecords\", \"first\", \"rowsPerPageOptions\", \"showCurrentPageReport\", \"onPageChange\"]],\n      template: function IndexComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"p-card\", 2);\n          i0.ɵɵtemplate(3, IndexComponent_ng_template_3_Template, 11, 1, \"ng-template\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 1)(5, \"p-card\", 2);\n          i0.ɵɵtemplate(6, IndexComponent_ng_template_6_Template, 11, 0, \"ng-template\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 4)(8, \"p-card\");\n          i0.ɵɵtemplate(9, IndexComponent_ng_template_9_Template, 9, 2, \"ng-template\", 5)(10, IndexComponent_ng_template_10_Template, 11, 10, \"ng-template\", 3);\n          i0.ɵɵelementEnd()()();\n        }\n      },\n      dependencies: [i9.NgClass, i9.NgForOf, i9.NgIf, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel, i6.RouterLink, i10.Tooltip, i4.PrimeTemplate, i11.Button, i12.InputText, i13.Dropdown, i14.ChartjsComponent, i15.Badge, i16.FaIconComponent, i17.Card, i18.Tag, i19.Ripple, i20.Paginator],\n      styles: [\".chart-container[_ngcontent-%COMP%]{position:relative;overflow:visible}c-chart[_ngcontent-%COMP%]{position:relative;display:block}c-chart[_ngcontent-%COMP%]   canvas[_ngcontent-%COMP%]{position:relative!important}[_nghost-%COMP%]     .chartjs-tooltip{position:absolute!important;pointer-events:none;z-index:1000}[_nghost-%COMP%]     .p-card-content{overflow:visible}[_nghost-%COMP%]     canvas{position:relative!important}.station-card[_ngcontent-%COMP%]   .chart-section[_ngcontent-%COMP%]{position:relative;overflow:visible;min-height:80px}.station-card[_ngcontent-%COMP%]   .chart-section[_ngcontent-%COMP%]   c-chart[_ngcontent-%COMP%]{position:relative;z-index:1}@media (max-width: 768px){c-chart[_ngcontent-%COMP%]{height:40px!important;width:100px!important}}\"]\n    });\n  }\n  return IndexComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}