{"ast": null, "code": "import { debounceTime } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../service/product.service\";\nimport * as i2 from \"src/app/layout/service/app.layout.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/button\";\nfunction IndexComponent_ng_template_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"p-dropdown\", 17);\n    i0.ɵɵlistener(\"onChange\", function IndexComponent_ng_template_27_Template_p_dropdown_onChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSortChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 18);\n    i0.ɵɵelement(3, \"i\", 19);\n    i0.ɵɵelementStart(4, \"input\", 20);\n    i0.ɵɵlistener(\"input\", function IndexComponent_ng_template_27_Template_input_input_4_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r6 = i0.ɵɵnextContext();\n      const _r0 = i0.ɵɵreference(26);\n      return i0.ɵɵresetView(ctx_r6.onFilter(_r0, $event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(5, \"p-dataViewLayoutOptions\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"options\", ctx_r1.sortOptions);\n  }\n}\nfunction IndexComponent_ng_template_28_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 22);\n    i0.ɵɵelement(2, \"img\", 23);\n    i0.ɵɵelementStart(3, \"div\", 24)(4, \"div\", 25);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 26);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"p-rating\", 27);\n    i0.ɵɵelementStart(9, \"div\", 28);\n    i0.ɵɵelement(10, \"i\", 29);\n    i0.ɵɵelementStart(11, \"span\", 30);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 31)(14, \"span\", 32);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"p-button\", 33);\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", \"assets/demo/images/product/\" + product_r9.image, i0.ɵɵsanitizeUrl)(\"alt\", product_r9.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r9.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r9.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", product_r9.rating)(\"readonly\", true)(\"cancel\", false);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(product_r9.category);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"$\", product_r9.price, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", product_r9.inventoryStatus === \"OUTOFSTOCK\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(\"product-badge status-\" + product_r9.inventoryStatus.toLowerCase());\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(product_r9.inventoryStatus);\n  }\n}\nfunction IndexComponent_ng_template_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, IndexComponent_ng_template_28_div_0_Template, 19, 13, \"div\", 21);\n  }\n  if (rf & 2) {\n    const products_r7 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngForOf\", products_r7);\n  }\n}\nfunction IndexComponent_ng_template_29_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 37)(2, \"div\", 38)(3, \"div\", 39);\n    i0.ɵɵelement(4, \"i\", 29);\n    i0.ɵɵelementStart(5, \"span\", 30);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"span\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 40);\n    i0.ɵɵelement(10, \"img\", 41);\n    i0.ɵɵelementStart(11, \"div\", 42);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 43);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"p-rating\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 45)(17, \"span\", 46);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"p-button\", 47);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r12 = ctx.$implicit;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(product_r12.category);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(\"product-badge status-\" + product_r12.inventoryStatus.toLowerCase());\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(product_r12.inventoryStatus);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", \"assets/demo/images/product/\" + product_r12.image, i0.ɵɵsanitizeUrl)(\"alt\", product_r12.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r12.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r12.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", product_r12.rating)(\"readonly\", true)(\"cancel\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"$\", product_r12.price, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", product_r12.inventoryStatus === \"OUTOFSTOCK\");\n  }\n}\nfunction IndexComponent_ng_template_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtemplate(1, IndexComponent_ng_template_29_div_1_Template, 20, 13, \"div\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const products_r10 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", products_r10);\n  }\n}\nconst _c0 = () => ({\n  width: \"2.5rem\",\n  height: \"2.5rem\"\n});\nexport class IndexComponent {\n  constructor(productService, layoutService) {\n    this.productService = productService;\n    this.layoutService = layoutService;\n    this.subscription = this.layoutService.configUpdate$.pipe(debounceTime(25)).subscribe(config => {\n      this.initChart();\n    });\n  }\n  ngOnInit() {\n    this.initChart();\n    this.productService.getProductsSmall().then(data => this.products = data);\n    this.items = [{\n      label: 'Add New',\n      icon: 'pi pi-fw pi-plus'\n    }, {\n      label: 'Remove',\n      icon: 'pi pi-fw pi-minus'\n    }];\n  }\n  initChart() {\n    const documentStyle = getComputedStyle(document.documentElement);\n    const textColor = documentStyle.getPropertyValue('--text-color');\n    const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n    const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\n    this.chartData = {\n      labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\n      datasets: [{\n        label: 'First Dataset',\n        data: [65, 59, 80, 81, 56, 55, 40],\n        fill: false,\n        backgroundColor: documentStyle.getPropertyValue('--bluegray-700'),\n        borderColor: documentStyle.getPropertyValue('--bluegray-700'),\n        tension: .4\n      }, {\n        label: 'Second Dataset',\n        data: [28, 48, 40, 19, 86, 27, 90],\n        fill: false,\n        backgroundColor: documentStyle.getPropertyValue('--green-600'),\n        borderColor: documentStyle.getPropertyValue('--green-600'),\n        tension: .4\n      }]\n    };\n    this.chartOptions = {\n      plugins: {\n        legend: {\n          labels: {\n            color: textColor\n          }\n        }\n      },\n      scales: {\n        x: {\n          ticks: {\n            color: textColorSecondary\n          },\n          grid: {\n            color: surfaceBorder,\n            drawBorder: false\n          }\n        },\n        y: {\n          ticks: {\n            color: textColorSecondary\n          },\n          grid: {\n            color: surfaceBorder,\n            drawBorder: false\n          }\n        }\n      }\n    };\n  }\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n  static #_ = this.ɵfac = function IndexComponent_Factory(t) {\n    return new (t || IndexComponent)(i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i2.LayoutService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: IndexComponent,\n    selectors: [[\"ng-component\"]],\n    decls: 30,\n    vars: 9,\n    consts: [[1, \"grid\"], [1, \"col-12\", \"lg:col-6\", \"xl:col-6\"], [1, \"card\", \"mb-0\"], [1, \"flex\", \"justify-content-between\", \"mb-3\"], [1, \"block\", \"text-500\", \"font-medium\", \"mb-3\"], [1, \"text-900\", \"font-medium\", \"text-xl\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-blue-100\", \"border-round\", 3, \"ngStyle\"], [1, \"pi\", \"pi-circle-fill\", \"text-blue-500\", \"text-xl\"], [1, \"pi\", \"pi-sun\", \"text-blue-500\", \"text-xl\"], [1, \"col-12\"], [1, \"card\"], [\"filterBy\", \"name\", \"layout\", \"grid\", 3, \"value\", \"paginator\", \"rows\", \"sortField\", \"sortOrder\"], [\"dv\", \"\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"listItem\"], [\"pTemplate\", \"gridItem\"], [1, \"flex\", \"flex-column\", \"md:flex-row\", \"md:justify-content-between\", \"gap-2\"], [\"placeholder\", \"Sort By Price\", 3, \"options\", \"onChange\"], [1, \"p-input-icon-left\"], [1, \"pi\", \"pi-search\"], [\"type\", \"search\", \"pInputText\", \"\", \"placeholder\", \"Search by Name\", 3, \"input\"], [\"class\", \"col-12\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"flex-column\", \"md:flex-row\", \"align-items-center\", \"p-3\", \"w-full\"], [1, \"my-4\", \"md:my-0\", \"w-9\", \"md:w-10rem\", \"shadow-2\", \"mr-5\", 3, \"src\", \"alt\"], [1, \"flex-1\", \"flex\", \"flex-column\", \"align-items-center\", \"text-center\", \"md:text-left\"], [1, \"font-bold\", \"text-2xl\"], [1, \"mb-2\"], [\"styleClass\", \"mb-2\", 3, \"ngModel\", \"readonly\", \"cancel\"], [1, \"flex\", \"align-items-center\", \"mt-2\"], [1, \"pi\", \"pi-tag\", \"mr-2\"], [1, \"font-semibold\"], [1, \"flex\", \"flex-row\", \"md:flex-column\", \"justify-content-between\", \"w-full\", \"md:w-auto\", \"align-items-center\", \"md:align-items-end\", \"mt-5\", \"md:mt-0\"], [1, \"text-2xl\", \"font-semibold\", \"mb-2\", \"align-self-center\", \"md:align-self-end\"], [\"icon\", \"pi pi-shopping-cart\", \"label\", \"Add to Cart\", \"styleClass\", \"mb-2 p-button-sm\", 3, \"disabled\"], [1, \"grid\", \"grid-nogutter\"], [\"class\", \"col-12 md:col-4\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"md:col-4\"], [1, \"card\", \"m-3\", \"border-1\", \"surface-border\"], [1, \"flex\", \"flex-wrap\", \"gap-2\", \"align-items-center\", \"justify-content-between\", \"mb-2\"], [1, \"flex\", \"align-items-center\"], [1, \"flex\", \"flex-column\", \"align-items-center\", \"text-center\", \"mb-3\"], [1, \"w-9\", \"shadow-2\", \"my-3\", \"mx-0\", 3, \"src\", \"alt\"], [1, \"text-2xl\", \"font-bold\"], [1, \"mb-3\"], [3, \"ngModel\", \"readonly\", \"cancel\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"text-2xl\", \"font-semibold\"], [\"icon\", \"pi pi-shopping-cart\", 3, \"disabled\"]],\n    template: function IndexComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\")(5, \"span\", 4);\n        i0.ɵɵtext(6, \"Active Energy Systems\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"div\", 5);\n        i0.ɵɵtext(8, \"44\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(9, \"div\", 6);\n        i0.ɵɵelement(10, \"i\", 7);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(11, \"div\", 1)(12, \"div\", 2)(13, \"div\", 3)(14, \"div\")(15, \"span\", 4);\n        i0.ɵɵtext(16, \"Combined Power Permormance\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"div\", 5);\n        i0.ɵɵtext(18, \"33% (4.124kW) \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(19, \"div\", 6);\n        i0.ɵɵelement(20, \"i\", 8);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(21, \"div\", 9)(22, \"div\", 10)(23, \"h5\");\n        i0.ɵɵtext(24, \"DataView\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"p-dataView\", 11, 12);\n        i0.ɵɵtemplate(27, IndexComponent_ng_template_27_Template, 6, 1, \"ng-template\", 13)(28, IndexComponent_ng_template_28_Template, 1, 1, \"ng-template\", 14)(29, IndexComponent_ng_template_29_Template, 2, 1, \"ng-template\", 15);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(7, _c0));\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(8, _c0));\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"value\", ctx.products)(\"paginator\", true)(\"rows\", 9)(\"sortField\", ctx.sortField)(\"sortOrder\", ctx.sortOrder);\n      }\n    },\n    dependencies: [i3.NgForOf, i3.NgStyle, i4.NgControlStatus, i4.NgModel, i5.PrimeTemplate, i6.Button],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["debounceTime", "i0", "ɵɵelementStart", "ɵɵlistener", "IndexComponent_ng_template_27_Template_p_dropdown_onChange_1_listener", "$event", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "onSortChange", "ɵɵelementEnd", "ɵɵelement", "IndexComponent_ng_template_27_Template_input_input_4_listener", "ctx_r6", "_r0", "ɵɵreference", "onFilter", "ɵɵadvance", "ɵɵproperty", "ctx_r1", "sortOptions", "ɵɵtext", "product_r9", "image", "ɵɵsanitizeUrl", "name", "ɵɵtextInterpolate", "description", "rating", "category", "ɵɵtextInterpolate1", "price", "inventoryStatus", "ɵɵclassMap", "toLowerCase", "ɵɵtemplate", "IndexComponent_ng_template_28_div_0_Template", "products_r7", "product_r12", "IndexComponent_ng_template_29_div_1_Template", "products_r10", "IndexComponent", "constructor", "productService", "layoutService", "subscription", "configUpdate$", "pipe", "subscribe", "config", "initChart", "ngOnInit", "getProductsSmall", "then", "data", "products", "items", "label", "icon", "documentStyle", "getComputedStyle", "document", "documentElement", "textColor", "getPropertyValue", "textColorSecondary", "surfaceBorder", "chartData", "labels", "datasets", "fill", "backgroundColor", "borderColor", "tension", "chartOptions", "plugins", "legend", "color", "scales", "x", "ticks", "grid", "drawBorder", "y", "ngOnDestroy", "unsubscribe", "_", "ɵɵdirectiveInject", "i1", "ProductService", "i2", "LayoutService", "_2", "selectors", "decls", "vars", "consts", "template", "IndexComponent_Template", "rf", "ctx", "IndexComponent_ng_template_27_Template", "IndexComponent_ng_template_28_Template", "IndexComponent_ng_template_29_Template", "ɵɵpureFunction0", "_c0", "sortField", "sortOrder"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\index\\index.component.ts", "D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\index\\index.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { Product } from '../../api/product';\r\nimport { ProductService } from '../../service/product.service';\r\nimport { Subscription, debounceTime } from 'rxjs';\r\nimport { LayoutService } from 'src/app/layout/service/app.layout.service';\r\nimport { SelectItem } from 'primeng/api';\r\nimport { DataView } from 'primeng/dataview';\r\n\r\n@Component({\r\n    templateUrl: './index.component.html',\r\n})\r\nexport class IndexComponent implements OnInit, OnDestroy {\r\n\r\n    items!: MenuItem[];\r\n\r\n    products!: Product[];\r\n\r\n    chartData: any;\r\n\r\n    chartOptions: any;\r\n\r\n    subscription!: Subscription;\r\n\r\n    constructor(private productService: ProductService, public layoutService: LayoutService) {\r\n        this.subscription = this.layoutService.configUpdate$\r\n        .pipe(debounceTime(25))\r\n        .subscribe((config) => {\r\n            this.initChart();\r\n        });\r\n    }\r\n\r\n    ngOnInit() {\r\n        this.initChart();\r\n        this.productService.getProductsSmall().then(data => this.products = data);\r\n\r\n        this.items = [\r\n            { label: 'Add New', icon: 'pi pi-fw pi-plus' },\r\n            { label: 'Remove', icon: 'pi pi-fw pi-minus' }\r\n        ];\r\n    }\r\n\r\n    initChart() {\r\n        const documentStyle = getComputedStyle(document.documentElement);\r\n        const textColor = documentStyle.getPropertyValue('--text-color');\r\n        const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\r\n        const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\r\n\r\n        this.chartData = {\r\n            labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\r\n            datasets: [\r\n                {\r\n                    label: 'First Dataset',\r\n                    data: [65, 59, 80, 81, 56, 55, 40],\r\n                    fill: false,\r\n                    backgroundColor: documentStyle.getPropertyValue('--bluegray-700'),\r\n                    borderColor: documentStyle.getPropertyValue('--bluegray-700'),\r\n                    tension: .4\r\n                },\r\n                {\r\n                    label: 'Second Dataset',\r\n                    data: [28, 48, 40, 19, 86, 27, 90],\r\n                    fill: false,\r\n                    backgroundColor: documentStyle.getPropertyValue('--green-600'),\r\n                    borderColor: documentStyle.getPropertyValue('--green-600'),\r\n                    tension: .4\r\n                }\r\n            ]\r\n        };\r\n\r\n        this.chartOptions = {\r\n            plugins: {\r\n                legend: {\r\n                    labels: {\r\n                        color: textColor\r\n                    }\r\n                }\r\n            },\r\n            scales: {\r\n                x: {\r\n                    ticks: {\r\n                        color: textColorSecondary\r\n                    },\r\n                    grid: {\r\n                        color: surfaceBorder,\r\n                        drawBorder: false\r\n                    }\r\n                },\r\n                y: {\r\n                    ticks: {\r\n                        color: textColorSecondary\r\n                    },\r\n                    grid: {\r\n                        color: surfaceBorder,\r\n                        drawBorder: false\r\n                    }\r\n                }\r\n            }\r\n        };\r\n    }\r\n\r\n    ngOnDestroy() {\r\n        if (this.subscription) {\r\n            this.subscription.unsubscribe();\r\n        }\r\n    }\r\n}\r\n", "<div class=\"grid\">\r\n    <div class=\"col-12 lg:col-6 xl:col-6\">\r\n        <div class=\"card mb-0\">\r\n            <div class=\"flex justify-content-between mb-3\">\r\n                <div>\r\n                    <span class=\"block text-500 font-medium mb-3\">Active Energy Systems</span>\r\n                    <div class=\"text-900 font-medium text-xl\">44</div>\r\n                </div>\r\n                <div class=\"flex align-items-center justify-content-center bg-blue-100 border-round\" [ngStyle]=\"{width: '2.5rem', height: '2.5rem'}\">\r\n                    <i class=\"pi pi-circle-fill text-blue-500 text-xl\"></i>\r\n                </div>\r\n            </div>\r\n            <!-- <span class=\"text-green-500 font-medium\">24 new </span>\r\n            <span class=\"text-500\">since last visit</span> -->\r\n        </div>\r\n    </div>\r\n    <div class=\"col-12 lg:col-6 xl:col-6\">\r\n        <div class=\"card mb-0\">\r\n            <div class=\"flex justify-content-between mb-3\">\r\n                <div>\r\n                    <span class=\"block text-500 font-medium mb-3\">Combined Power Permormance</span>\r\n                    <div class=\"text-900 font-medium text-xl\">33% (4.124kW) </div>\r\n                </div>\r\n                <div class=\"flex align-items-center justify-content-center bg-blue-100 border-round\" [ngStyle]=\"{width: '2.5rem', height: '2.5rem'}\">\r\n                    <i class=\"pi pi-sun text-blue-500 text-xl\"></i>\r\n                </div>\r\n            </div>\r\n            <!-- <span class=\"text-green-500 font-medium\">24 new </span>\r\n            <span class=\"text-500\">since last visit</span> -->\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"col-12\">\r\n\t\t<div class=\"card\">\r\n\t\t\t<h5>DataView</h5>\r\n\t\t\t<p-dataView #dv [value]=\"products\" [paginator]=\"true\" [rows]=\"9\" filterBy=\"name\" [sortField]=\"sortField\" [sortOrder]=\"sortOrder\" layout=\"grid\">\r\n\t\t\t\t<ng-template pTemplate=\"header\">\r\n\t\t\t\t\t<div class=\"flex flex-column md:flex-row md:justify-content-between gap-2\">\r\n\t\t\t\t\t\t<p-dropdown [options]=\"sortOptions\" placeholder=\"Sort By Price\" (onChange)=\"onSortChange($event)\"></p-dropdown>\r\n\t\t\t\t\t\t<span class=\"p-input-icon-left\">\r\n                            <i class=\"pi pi-search\"></i>\r\n                            <input type=\"search\" pInputText placeholder=\"Search by Name\" (input)=\"onFilter(dv, $event)\">\r\n                        </span>\t\r\n\t\t\t\t\t\t<p-dataViewLayoutOptions></p-dataViewLayoutOptions>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</ng-template>\r\n\r\n\t\t\t\t<ng-template let-products pTemplate=\"listItem\">\r\n\t\t\t\t\t<div class=\"col-12\" *ngFor=\"let product of products\">\r\n\t\t\t\t\t\t<div class=\"flex flex-column md:flex-row align-items-center p-3 w-full\">\r\n\t\t\t\t\t\t\t<img [src]=\"'assets/demo/images/product/' + product.image\" [alt]=\"product.name\" class=\"my-4 md:my-0 w-9 md:w-10rem shadow-2 mr-5\"/>\r\n\t\t\t\t\t\t\t<div class=\"flex-1 flex flex-column align-items-center text-center md:text-left\">\r\n\t\t\t\t\t\t\t\t<div class=\"font-bold text-2xl\">{{product.name}}</div>\r\n\t\t\t\t\t\t\t\t<div class=\"mb-2\">{{product.description}}</div>\r\n\t\t\t\t\t\t\t\t<p-rating [ngModel]=\"product.rating\" [readonly]=\"true\" [cancel]=\"false\" styleClass=\"mb-2\"></p-rating>\r\n\t\t\t\t\t\t\t\t<div class=\"flex align-items-center mt-2\">\r\n\t\t\t\t\t\t\t\t\t<i class=\"pi pi-tag mr-2\"></i>\r\n\t\t\t\t\t\t\t\t\t<span class=\"font-semibold\">{{product.category}}</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"flex flex-row md:flex-column justify-content-between w-full md:w-auto align-items-center md:align-items-end mt-5 md:mt-0\">\r\n\t\t\t\t\t\t\t\t<span class=\"text-2xl font-semibold mb-2 align-self-center md:align-self-end\">${{product.price}}</span>\r\n\t\t\t\t\t\t\t\t<p-button icon=\"pi pi-shopping-cart\" label=\"Add to Cart\" [disabled]=\"product.inventoryStatus === 'OUTOFSTOCK'\" styleClass=\"mb-2 p-button-sm\"></p-button>\r\n\t\t\t\t\t\t\t\t<span [class]=\"'product-badge status-' + product.inventoryStatus.toLowerCase()\">{{product.inventoryStatus}}</span>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</ng-template>\r\n\r\n\t\t\t\t<ng-template let-products pTemplate=\"gridItem\">\r\n\t\t\t\t\t<div class=\"grid grid-nogutter\">\r\n\t\t\t\t\t<div class=\"col-12 md:col-4\" *ngFor=\"let product of products\">\r\n\t\t\t\t\t\t<div class=\"card m-3 border-1 surface-border\">\r\n\t\t\t\t\t\t\t<div class=\"flex flex-wrap gap-2 align-items-center justify-content-between mb-2\">\r\n\t\t\t\t\t\t\t\t<div class=\"flex align-items-center\">\r\n\t\t\t\t\t\t\t\t\t<i class=\"pi pi-tag mr-2\"></i>\r\n\t\t\t\t\t\t\t\t\t<span class=\"font-semibold\">{{product.category}}</span>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t<span [class]=\"'product-badge status-' + product.inventoryStatus.toLowerCase()\">{{product.inventoryStatus}}</span>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"flex flex-column align-items-center text-center mb-3\">\r\n\t\t\t\t\t\t\t\t<img [src]=\"'assets/demo/images/product/' + product.image\" [alt]=\"product.name\" class=\"w-9 shadow-2 my-3 mx-0\"/>\r\n\t\t\t\t\t\t\t\t<div class=\"text-2xl font-bold\">{{product.name}}</div>\r\n\t\t\t\t\t\t\t\t<div class=\"mb-3\">{{product.description}}</div>\r\n\t\t\t\t\t\t\t\t<p-rating [ngModel]=\"product.rating\" [readonly]=\"true\" [cancel]=\"false\"></p-rating>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"flex align-items-center justify-content-between\">\r\n\t\t\t\t\t\t\t\t<span class=\"text-2xl font-semibold\">${{product.price}}</span>\r\n\t\t\t\t\t\t\t\t<p-button icon=\"pi pi-shopping-cart\" [disabled]=\"product.inventoryStatus === 'OUTOFSTOCK'\"></p-button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</ng-template>\r\n\t\t\t</p-dataView>\r\n\t\t</div>\r\n\t</div>\r\n</div>"], "mappings": "AAIA,SAAuBA,YAAY,QAAQ,MAAM;;;;;;;;;;;ICiC5CC,EAAA,CAAAC,cAAA,cAA2E;IACVD,EAAA,CAAAE,UAAA,sBAAAC,sEAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAYR,EAAA,CAAAS,WAAA,CAAAF,MAAA,CAAAG,YAAA,CAAAN,MAAA,CAAoB;IAAA,EAAC;IAACJ,EAAA,CAAAW,YAAA,EAAa;IAC/GX,EAAA,CAAAC,cAAA,eAAgC;IACVD,EAAA,CAAAY,SAAA,YAA4B;IAC5BZ,EAAA,CAAAC,cAAA,gBAA4F;IAA/BD,EAAA,CAAAE,UAAA,mBAAAW,8DAAAT,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAQ,MAAA,GAAAd,EAAA,CAAAQ,aAAA;MAAA,MAAAO,GAAA,GAAAf,EAAA,CAAAgB,WAAA;MAAA,OAAShB,EAAA,CAAAS,WAAA,CAAAK,MAAA,CAAAG,QAAA,CAAAF,GAAA,EAAAX,MAAA,CAAoB;IAAA,EAAC;IAA3FJ,EAAA,CAAAW,YAAA,EAA4F;IAElHX,EAAA,CAAAY,SAAA,8BAAmD;IACpDZ,EAAA,CAAAW,YAAA,EAAM;;;;IANOX,EAAA,CAAAkB,SAAA,GAAuB;IAAvBlB,EAAA,CAAAmB,UAAA,YAAAC,MAAA,CAAAC,WAAA,CAAuB;;;;;IAUpCrB,EAAA,CAAAC,cAAA,aAAqD;IAEnDD,EAAA,CAAAY,SAAA,cAAmI;IACnIZ,EAAA,CAAAC,cAAA,cAAiF;IAChDD,EAAA,CAAAsB,MAAA,GAAgB;IAAAtB,EAAA,CAAAW,YAAA,EAAM;IACtDX,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAsB,MAAA,GAAuB;IAAAtB,EAAA,CAAAW,YAAA,EAAM;IAC/CX,EAAA,CAAAY,SAAA,mBAAqG;IACrGZ,EAAA,CAAAC,cAAA,cAA0C;IACzCD,EAAA,CAAAY,SAAA,aAA8B;IAC9BZ,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAsB,MAAA,IAAoB;IAAAtB,EAAA,CAAAW,YAAA,EAAO;IAGzDX,EAAA,CAAAC,cAAA,eAAsI;IACvDD,EAAA,CAAAsB,MAAA,IAAkB;IAAAtB,EAAA,CAAAW,YAAA,EAAO;IACvGX,EAAA,CAAAY,SAAA,oBAAwJ;IACxJZ,EAAA,CAAAC,cAAA,YAAgF;IAAAD,EAAA,CAAAsB,MAAA,IAA2B;IAAAtB,EAAA,CAAAW,YAAA,EAAO;;;;IAb9GX,EAAA,CAAAkB,SAAA,GAAqD;IAArDlB,EAAA,CAAAmB,UAAA,wCAAAI,UAAA,CAAAC,KAAA,EAAAxB,EAAA,CAAAyB,aAAA,CAAqD,QAAAF,UAAA,CAAAG,IAAA;IAEzB1B,EAAA,CAAAkB,SAAA,GAAgB;IAAhBlB,EAAA,CAAA2B,iBAAA,CAAAJ,UAAA,CAAAG,IAAA,CAAgB;IAC9B1B,EAAA,CAAAkB,SAAA,GAAuB;IAAvBlB,EAAA,CAAA2B,iBAAA,CAAAJ,UAAA,CAAAK,WAAA,CAAuB;IAC/B5B,EAAA,CAAAkB,SAAA,GAA0B;IAA1BlB,EAAA,CAAAmB,UAAA,YAAAI,UAAA,CAAAM,MAAA,CAA0B;IAGP7B,EAAA,CAAAkB,SAAA,GAAoB;IAApBlB,EAAA,CAAA2B,iBAAA,CAAAJ,UAAA,CAAAO,QAAA,CAAoB;IAI6B9B,EAAA,CAAAkB,SAAA,GAAkB;IAAlBlB,EAAA,CAAA+B,kBAAA,MAAAR,UAAA,CAAAS,KAAA,KAAkB;IACvChC,EAAA,CAAAkB,SAAA,GAAqD;IAArDlB,EAAA,CAAAmB,UAAA,aAAAI,UAAA,CAAAU,eAAA,kBAAqD;IACxGjC,EAAA,CAAAkB,SAAA,GAAyE;IAAzElB,EAAA,CAAAkC,UAAA,2BAAAX,UAAA,CAAAU,eAAA,CAAAE,WAAA,GAAyE;IAACnC,EAAA,CAAAkB,SAAA,GAA2B;IAA3BlB,EAAA,CAAA2B,iBAAA,CAAAJ,UAAA,CAAAU,eAAA,CAA2B;;;;;IAf9GjC,EAAA,CAAAoC,UAAA,IAAAC,4CAAA,oBAkBM;;;;IAlBkCrC,EAAA,CAAAmB,UAAA,YAAAmB,WAAA,CAAW;;;;;IAuBnDtC,EAAA,CAAAC,cAAA,cAA8D;IAI1DD,EAAA,CAAAY,SAAA,YAA8B;IAC9BZ,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAsB,MAAA,GAAoB;IAAAtB,EAAA,CAAAW,YAAA,EAAO;IAExDX,EAAA,CAAAC,cAAA,WAAgF;IAAAD,EAAA,CAAAsB,MAAA,GAA2B;IAAAtB,EAAA,CAAAW,YAAA,EAAO;IAEnHX,EAAA,CAAAC,cAAA,cAAkE;IACjED,EAAA,CAAAY,SAAA,eAAgH;IAChHZ,EAAA,CAAAC,cAAA,eAAgC;IAAAD,EAAA,CAAAsB,MAAA,IAAgB;IAAAtB,EAAA,CAAAW,YAAA,EAAM;IACtDX,EAAA,CAAAC,cAAA,eAAkB;IAAAD,EAAA,CAAAsB,MAAA,IAAuB;IAAAtB,EAAA,CAAAW,YAAA,EAAM;IAC/CX,EAAA,CAAAY,SAAA,oBAAmF;IACpFZ,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAA6D;IACvBD,EAAA,CAAAsB,MAAA,IAAkB;IAAAtB,EAAA,CAAAW,YAAA,EAAO;IAC9DX,EAAA,CAAAY,SAAA,oBAAsG;IACvGZ,EAAA,CAAAW,YAAA,EAAM;;;;IAbwBX,EAAA,CAAAkB,SAAA,GAAoB;IAApBlB,EAAA,CAAA2B,iBAAA,CAAAY,WAAA,CAAAT,QAAA,CAAoB;IAE3C9B,EAAA,CAAAkB,SAAA,GAAyE;IAAzElB,EAAA,CAAAkC,UAAA,2BAAAK,WAAA,CAAAN,eAAA,CAAAE,WAAA,GAAyE;IAACnC,EAAA,CAAAkB,SAAA,GAA2B;IAA3BlB,EAAA,CAAA2B,iBAAA,CAAAY,WAAA,CAAAN,eAAA,CAA2B;IAGtGjC,EAAA,CAAAkB,SAAA,GAAqD;IAArDlB,EAAA,CAAAmB,UAAA,wCAAAoB,WAAA,CAAAf,KAAA,EAAAxB,EAAA,CAAAyB,aAAA,CAAqD,QAAAc,WAAA,CAAAb,IAAA;IAC1B1B,EAAA,CAAAkB,SAAA,GAAgB;IAAhBlB,EAAA,CAAA2B,iBAAA,CAAAY,WAAA,CAAAb,IAAA,CAAgB;IAC9B1B,EAAA,CAAAkB,SAAA,GAAuB;IAAvBlB,EAAA,CAAA2B,iBAAA,CAAAY,WAAA,CAAAX,WAAA,CAAuB;IAC/B5B,EAAA,CAAAkB,SAAA,GAA0B;IAA1BlB,EAAA,CAAAmB,UAAA,YAAAoB,WAAA,CAAAV,MAAA,CAA0B;IAGC7B,EAAA,CAAAkB,SAAA,GAAkB;IAAlBlB,EAAA,CAAA+B,kBAAA,MAAAQ,WAAA,CAAAP,KAAA,KAAkB;IAClBhC,EAAA,CAAAkB,SAAA,GAAqD;IAArDlB,EAAA,CAAAmB,UAAA,aAAAoB,WAAA,CAAAN,eAAA,kBAAqD;;;;;IAlB7FjC,EAAA,CAAAC,cAAA,cAAgC;IAChCD,EAAA,CAAAoC,UAAA,IAAAI,4CAAA,oBAoBM;IACNxC,EAAA,CAAAW,YAAA,EAAM;;;;IArB2CX,EAAA,CAAAkB,SAAA,GAAW;IAAXlB,EAAA,CAAAmB,UAAA,YAAAsB,YAAA,CAAW;;;;;;;AD3DjE,OAAM,MAAOC,cAAc;EAYvBC,YAAoBC,cAA8B,EAASC,aAA4B;IAAnE,KAAAD,cAAc,GAAdA,cAAc;IAAyB,KAAAC,aAAa,GAAbA,aAAa;IACpE,IAAI,CAACC,YAAY,GAAG,IAAI,CAACD,aAAa,CAACE,aAAa,CACnDC,IAAI,CAACjD,YAAY,CAAC,EAAE,CAAC,CAAC,CACtBkD,SAAS,CAAEC,MAAM,IAAI;MAClB,IAAI,CAACC,SAAS,EAAE;IACpB,CAAC,CAAC;EACN;EAEAC,QAAQA,CAAA;IACJ,IAAI,CAACD,SAAS,EAAE;IAChB,IAAI,CAACP,cAAc,CAACS,gBAAgB,EAAE,CAACC,IAAI,CAACC,IAAI,IAAI,IAAI,CAACC,QAAQ,GAAGD,IAAI,CAAC;IAEzE,IAAI,CAACE,KAAK,GAAG,CACT;MAAEC,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAkB,CAAE,EAC9C;MAAED,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAmB,CAAE,CACjD;EACL;EAEAR,SAASA,CAAA;IACL,MAAMS,aAAa,GAAGC,gBAAgB,CAACC,QAAQ,CAACC,eAAe,CAAC;IAChE,MAAMC,SAAS,GAAGJ,aAAa,CAACK,gBAAgB,CAAC,cAAc,CAAC;IAChE,MAAMC,kBAAkB,GAAGN,aAAa,CAACK,gBAAgB,CAAC,wBAAwB,CAAC;IACnF,MAAME,aAAa,GAAGP,aAAa,CAACK,gBAAgB,CAAC,kBAAkB,CAAC;IAExE,IAAI,CAACG,SAAS,GAAG;MACbC,MAAM,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;MACxEC,QAAQ,EAAE,CACN;QACIZ,KAAK,EAAE,eAAe;QACtBH,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QAClCgB,IAAI,EAAE,KAAK;QACXC,eAAe,EAAEZ,aAAa,CAACK,gBAAgB,CAAC,gBAAgB,CAAC;QACjEQ,WAAW,EAAEb,aAAa,CAACK,gBAAgB,CAAC,gBAAgB,CAAC;QAC7DS,OAAO,EAAE;OACZ,EACD;QACIhB,KAAK,EAAE,gBAAgB;QACvBH,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;QAClCgB,IAAI,EAAE,KAAK;QACXC,eAAe,EAAEZ,aAAa,CAACK,gBAAgB,CAAC,aAAa,CAAC;QAC9DQ,WAAW,EAAEb,aAAa,CAACK,gBAAgB,CAAC,aAAa,CAAC;QAC1DS,OAAO,EAAE;OACZ;KAER;IAED,IAAI,CAACC,YAAY,GAAG;MAChBC,OAAO,EAAE;QACLC,MAAM,EAAE;UACJR,MAAM,EAAE;YACJS,KAAK,EAAEd;;;OAGlB;MACDe,MAAM,EAAE;QACJC,CAAC,EAAE;UACCC,KAAK,EAAE;YACHH,KAAK,EAAEZ;WACV;UACDgB,IAAI,EAAE;YACFJ,KAAK,EAAEX,aAAa;YACpBgB,UAAU,EAAE;;SAEnB;QACDC,CAAC,EAAE;UACCH,KAAK,EAAE;YACHH,KAAK,EAAEZ;WACV;UACDgB,IAAI,EAAE;YACFJ,KAAK,EAAEX,aAAa;YACpBgB,UAAU,EAAE;;;;KAI3B;EACL;EAEAE,WAAWA,CAAA;IACP,IAAI,IAAI,CAACvC,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAACwC,WAAW,EAAE;;EAEvC;EAAC,QAAAC,CAAA,G;qBA7FQ7C,cAAc,EAAA1C,EAAA,CAAAwF,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA1F,EAAA,CAAAwF,iBAAA,CAAAG,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAdnD,cAAc;IAAAoD,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZ3BpG,EAAA,CAAAC,cAAA,aAAkB;QAKgDD,EAAA,CAAAsB,MAAA,4BAAqB;QAAAtB,EAAA,CAAAW,YAAA,EAAO;QAC1EX,EAAA,CAAAC,cAAA,aAA0C;QAAAD,EAAA,CAAAsB,MAAA,SAAE;QAAAtB,EAAA,CAAAW,YAAA,EAAM;QAEtDX,EAAA,CAAAC,cAAA,aAAqI;QACjID,EAAA,CAAAY,SAAA,YAAuD;QAC3DZ,EAAA,CAAAW,YAAA,EAAM;QAMlBX,EAAA,CAAAC,cAAA,cAAsC;QAIwBD,EAAA,CAAAsB,MAAA,kCAA0B;QAAAtB,EAAA,CAAAW,YAAA,EAAO;QAC/EX,EAAA,CAAAC,cAAA,cAA0C;QAAAD,EAAA,CAAAsB,MAAA,sBAAc;QAAAtB,EAAA,CAAAW,YAAA,EAAM;QAElEX,EAAA,CAAAC,cAAA,cAAqI;QACjID,EAAA,CAAAY,SAAA,YAA+C;QACnDZ,EAAA,CAAAW,YAAA,EAAM;QAOlBX,EAAA,CAAAC,cAAA,cAAoB;QAEjBD,EAAA,CAAAsB,MAAA,gBAAQ;QAAAtB,EAAA,CAAAW,YAAA,EAAK;QACjBX,EAAA,CAAAC,cAAA,0BAA+I;QAC9ID,EAAA,CAAAoC,UAAA,KAAAkE,sCAAA,0BASc,KAAAC,sCAAA,+BAAAC,sCAAA;QAiDfxG,EAAA,CAAAW,YAAA,EAAa;;;QAtFqFX,EAAA,CAAAkB,SAAA,GAA+C;QAA/ClB,EAAA,CAAAmB,UAAA,YAAAnB,EAAA,CAAAyG,eAAA,IAAAC,GAAA,EAA+C;QAe/C1G,EAAA,CAAAkB,SAAA,IAA+C;QAA/ClB,EAAA,CAAAmB,UAAA,YAAAnB,EAAA,CAAAyG,eAAA,IAAAC,GAAA,EAA+C;QAYjI1G,EAAA,CAAAkB,SAAA,GAAkB;QAAlBlB,EAAA,CAAAmB,UAAA,UAAAkF,GAAA,CAAA7C,QAAA,CAAkB,4CAAA6C,GAAA,CAAAM,SAAA,eAAAN,GAAA,CAAAO,SAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}