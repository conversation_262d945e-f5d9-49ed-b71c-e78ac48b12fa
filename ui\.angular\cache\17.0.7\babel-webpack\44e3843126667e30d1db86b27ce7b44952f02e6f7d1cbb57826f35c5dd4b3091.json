{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../service/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"../../../service/error.service\";\nimport * as i6 from \"../../../service/enhanced-message.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/checkbox\";\nimport * as i10 from \"primeng/inputtext\";\nimport * as i11 from \"primeng/password\";\nfunction RegisterComponent_small_18_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"First name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_18_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"First name must be at least 2 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_18_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"First name cannot exceed 50 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 33);\n    i0.ɵɵtemplate(1, RegisterComponent_small_18_span_1_Template, 2, 0, \"span\", 34)(2, RegisterComponent_small_18_span_2_Template, 2, 0, \"span\", 34)(3, RegisterComponent_small_18_span_3_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r0.registerForm.get(\"firstName\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r0.registerForm.get(\"firstName\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.registerForm.get(\"firstName\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"maxlength\"]);\n  }\n}\nfunction RegisterComponent_small_23_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Last name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_23_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Last name must be at least 2 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_23_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Last name cannot exceed 50 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 33);\n    i0.ɵɵtemplate(1, RegisterComponent_small_23_span_1_Template, 2, 0, \"span\", 34)(2, RegisterComponent_small_23_span_2_Template, 2, 0, \"span\", 34)(3, RegisterComponent_small_23_span_3_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r1.registerForm.get(\"lastName\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r1.registerForm.get(\"lastName\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r1.registerForm.get(\"lastName\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"maxlength\"]);\n  }\n}\nfunction RegisterComponent_small_28_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Username is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_28_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Username must be at least 3 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_28_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Username can only contain letters, numbers, and underscores\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 33);\n    i0.ɵɵtemplate(1, RegisterComponent_small_28_span_1_Template, 2, 0, \"span\", 34)(2, RegisterComponent_small_28_span_2_Template, 2, 0, \"span\", 34)(3, RegisterComponent_small_28_span_3_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r2.registerForm.get(\"username\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r2.registerForm.get(\"username\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r2.registerForm.get(\"username\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"pattern\"]);\n  }\n}\nfunction RegisterComponent_small_33_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_33_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Please enter a valid email address\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 33);\n    i0.ɵɵtemplate(1, RegisterComponent_small_33_span_1_Template, 2, 0, \"span\", 34)(2, RegisterComponent_small_33_span_2_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r3.registerForm.get(\"email\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r3.registerForm.get(\"email\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"email\"]);\n  }\n}\nfunction RegisterComponent_small_38_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_38_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Password must be at least 8 characters\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_38_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Password must contain uppercase, lowercase, number and special character\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 33);\n    i0.ɵɵtemplate(1, RegisterComponent_small_38_span_1_Template, 2, 0, \"span\", 34)(2, RegisterComponent_small_38_span_2_Template, 2, 0, \"span\", 34)(3, RegisterComponent_small_38_span_3_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r4.registerForm.get(\"password\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r4.registerForm.get(\"password\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r4.registerForm.get(\"password\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"pattern\"]);\n  }\n}\nfunction RegisterComponent_small_43_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Please confirm your password\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_43_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Passwords do not match\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_small_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 33);\n    i0.ɵɵtemplate(1, RegisterComponent_small_43_span_1_Template, 2, 0, \"span\", 34)(2, RegisterComponent_small_43_span_2_Template, 2, 0, \"span\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r5.registerForm.get(\"confirmPassword\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.registerForm.errors == null ? null : ctx_r5.registerForm.errors[\"passwordMismatch\"]);\n  }\n}\nfunction RegisterComponent_small_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 33);\n    i0.ɵɵtext(1, \" You must accept the terms and conditions \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterComponent_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36);\n    i0.ɵɵelement(2, \"span\", 37);\n    i0.ɵɵelementStart(3, \"span\", 38);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r7.errorMessage);\n  }\n}\nexport let RegisterComponent = /*#__PURE__*/(() => {\n  class RegisterComponent {\n    constructor(fb, authService, router, messageService, errorService, enhancedMessage) {\n      this.fb = fb;\n      this.authService = authService;\n      this.router = router;\n      this.messageService = messageService;\n      this.errorService = errorService;\n      this.enhancedMessage = enhancedMessage;\n      this.errorMessage = '';\n      this.isLoading = false;\n      this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n      this.initializeForm();\n    }\n    ngOnDestroy() {\n      this.destroy$.next();\n      this.destroy$.complete();\n    }\n    initializeForm() {\n      this.registerForm = this.fb.group({\n        firstName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n        lastName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],\n        username: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(20), Validators.pattern(/^[a-zA-Z0-9_]+$/) // Only alphanumeric and underscore\n        ]],\n\n        email: ['', [Validators.required, Validators.email, this.emailDomainValidator]],\n        password: ['', [Validators.required, Validators.minLength(8), this.strongPasswordValidator]],\n        confirmPassword: ['', [Validators.required]],\n        acceptTerms: [false, [Validators.requiredTrue]]\n      }, {\n        validators: this.passwordMatchValidator\n      });\n      // Clear error message when form values change\n      this.registerForm.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(() => {\n        if (this.errorMessage) {\n          this.errorMessage = '';\n        }\n      });\n    }\n    // Custom validator for strong password\n    strongPasswordValidator(control) {\n      const value = control.value;\n      if (!value) return null;\n      const hasUpperCase = /[A-Z]/.test(value);\n      const hasLowerCase = /[a-z]/.test(value);\n      const hasNumeric = /[0-9]/.test(value);\n      const hasSpecialChar = /[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(value);\n      const valid = hasUpperCase && hasLowerCase && hasNumeric && hasSpecialChar;\n      return valid ? null : {\n        pattern: true\n      };\n    }\n    // Custom validator for email domain\n    emailDomainValidator(control) {\n      const value = control.value;\n      if (!value) return null;\n      const blockedDomains = ['tempmail.com', '10minutemail.com', 'guerrillamail.com'];\n      const domain = value.split('@')[1];\n      return blockedDomains.includes(domain) ? {\n        blockedDomain: true\n      } : null;\n    }\n    // Custom validator for password match\n    passwordMatchValidator(form) {\n      const password = form.get('password')?.value;\n      const confirmPassword = form.get('confirmPassword')?.value;\n      if (!password || !confirmPassword) return null;\n      return password === confirmPassword ? null : {\n        passwordMismatch: true\n      };\n    }\n    register() {\n      if (this.registerForm.invalid) {\n        this.markFormGroupTouched();\n        return;\n      }\n      this.isLoading = true;\n      this.errorMessage = '';\n      const {\n        firstName,\n        lastName,\n        username,\n        email,\n        password\n      } = this.registerForm.value;\n      this.authService.register(username, password, email, firstName, lastName).pipe(takeUntil(this.destroy$)).subscribe({\n        next: () => {\n          this.isLoading = false;\n          this.enhancedMessage.showSuccess('Account Created!', 'Your account has been created successfully. Please sign in.');\n          // Small delay for better UX\n          setTimeout(() => {\n            this.router.navigate(['/auth/login']);\n          }, 1500);\n        },\n        error: error => {\n          this.isLoading = false;\n          this.handleRegistrationError(error);\n        }\n      });\n    }\n    handleRegistrationError(error) {\n      let errorMessage = 'Registration failed. Please try again.';\n      if (error.status === 409) {\n        errorMessage = 'Username or email already exists. Please choose different credentials.';\n      } else if (error.status === 400) {\n        errorMessage = 'Invalid registration data. Please check your information and try again.';\n      } else if (error.status === 422) {\n        errorMessage = 'Validation failed. Please ensure all fields are filled correctly.';\n      } else if (error.status === 0) {\n        errorMessage = 'Unable to connect to server. Please check your internet connection.';\n      } else if (error.status >= 500) {\n        errorMessage = 'Server error. Please try again later.';\n      }\n      this.errorMessage = errorMessage;\n      // Also add to global error service\n      this.errorService.addError(error);\n      // Show enhanced toast message\n      this.enhancedMessage.showError('Registration Failed', errorMessage);\n    }\n    markFormGroupTouched() {\n      Object.keys(this.registerForm.controls).forEach(key => {\n        const control = this.registerForm.get(key);\n        control?.markAsTouched();\n      });\n    }\n    redirectLogin() {\n      this.router.navigate(['/auth/login']);\n    }\n    showTerms() {\n      this.enhancedMessage.showInfo('Terms of Service', 'Terms of Service page will be available soon.');\n    }\n    showPrivacy() {\n      this.enhancedMessage.showInfo('Privacy Policy', 'Privacy Policy page will be available soon.');\n    }\n    static #_ = this.ɵfac = function RegisterComponent_Factory(t) {\n      return new (t || RegisterComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ErrorService), i0.ɵɵdirectiveInject(i6.EnhancedMessageService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RegisterComponent,\n      selectors: [[\"app-register\"]],\n      decls: 62,\n      vars: 28,\n      consts: [[1, \"auth-container\"], [1, \"auth-background\"], [1, \"bg-element\", \"bg-element-1\"], [1, \"bg-element\", \"bg-element-2\"], [1, \"bg-element\", \"bg-element-3\"], [1, \"auth-content\"], [1, \"auth-card\"], [1, \"text-center\", \"mb-4\"], [\"src\", \"assets/layout/images/logo-dark.png\", \"alt\", \"SolarKapital\", \"height\", \"50\", 1, \"mb-3\"], [1, \"text-900\", \"text-2xl\", \"font-semibold\", \"mb-2\"], [1, \"text-600\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"mb-4\"], [\"for\", \"firstName\", 1, \"block\", \"text-900\", \"font-medium\", \"mb-2\"], [\"id\", \"firstName\", \"type\", \"text\", \"formControlName\", \"firstName\", \"placeholder\", \"Enter your first name\", \"pInputText\", \"\", 1, \"w-full\", \"p-3\"], [\"class\", \"p-error\", 4, \"ngIf\"], [\"for\", \"lastName\", 1, \"block\", \"text-900\", \"font-medium\", \"mb-2\"], [\"id\", \"lastName\", \"type\", \"text\", \"formControlName\", \"lastName\", \"placeholder\", \"Enter your last name\", \"pInputText\", \"\", 1, \"w-full\", \"p-3\"], [\"for\", \"username\", 1, \"block\", \"text-900\", \"font-medium\", \"mb-2\"], [\"id\", \"username\", \"type\", \"text\", \"formControlName\", \"username\", \"placeholder\", \"Choose a username\", \"pInputText\", \"\", 1, \"w-full\", \"p-3\"], [\"for\", \"email\", 1, \"block\", \"text-900\", \"font-medium\", \"mb-2\"], [\"id\", \"email\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email address\", \"pInputText\", \"\", 1, \"w-full\", \"p-3\"], [\"for\", \"password\", 1, \"block\", \"text-900\", \"font-medium\", \"mb-2\"], [\"id\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"Create a strong password\", \"styleClass\", \"w-full\", \"inputStyleClass\", \"w-full p-3\", 3, \"toggleMask\", \"feedback\"], [\"for\", \"confirmPassword\", 1, \"block\", \"text-900\", \"font-medium\", \"mb-2\"], [\"id\", \"confirmPassword\", \"formControlName\", \"confirmPassword\", \"placeholder\", \"Confirm your password\", \"styleClass\", \"w-full\", \"inputStyleClass\", \"w-full p-3\", 3, \"toggleMask\", \"feedback\"], [1, \"flex\", \"align-items-center\"], [\"id\", \"acceptTerms\", \"formControlName\", \"acceptTerms\", \"styleClass\", \"mr-2\", 3, \"binary\"], [\"for\", \"acceptTerms\", 1, \"text-900\"], [1, \"font-medium\", \"no-underline\", \"cursor-pointer\", 2, \"color\", \"var(--primary-color)\", 3, \"click\"], [\"class\", \"p-message p-message-error mb-4\", 4, \"ngIf\"], [\"type\", \"submit\", \"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Create Account\", 1, \"w-full\", \"p-3\", \"text-xl\", \"mb-4\", 3, \"disabled\", \"loading\"], [1, \"text-center\"], [1, \"p-error\"], [4, \"ngIf\"], [1, \"p-message\", \"p-message-error\", \"mb-4\"], [1, \"p-message-wrapper\"], [1, \"p-message-icon\", \"pi\", \"pi-exclamation-triangle\"], [1, \"p-message-text\"]],\n      template: function RegisterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7);\n          i0.ɵɵelement(8, \"img\", 8);\n          i0.ɵɵelementStart(9, \"h2\", 9);\n          i0.ɵɵtext(10, \"Create Account\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p\", 10);\n          i0.ɵɵtext(12, \"Join us to manage your solar energy systems\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"form\", 11);\n          i0.ɵɵlistener(\"ngSubmit\", function RegisterComponent_Template_form_ngSubmit_13_listener() {\n            return ctx.register();\n          });\n          i0.ɵɵelementStart(14, \"div\", 12)(15, \"label\", 13);\n          i0.ɵɵtext(16, \"First Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"input\", 14);\n          i0.ɵɵtemplate(18, RegisterComponent_small_18_Template, 4, 3, \"small\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 12)(20, \"label\", 16);\n          i0.ɵɵtext(21, \"Last Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"input\", 17);\n          i0.ɵɵtemplate(23, RegisterComponent_small_23_Template, 4, 3, \"small\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"div\", 12)(25, \"label\", 18);\n          i0.ɵɵtext(26, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(27, \"input\", 19);\n          i0.ɵɵtemplate(28, RegisterComponent_small_28_Template, 4, 3, \"small\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 12)(30, \"label\", 20);\n          i0.ɵɵtext(31, \"Email Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(32, \"input\", 21);\n          i0.ɵɵtemplate(33, RegisterComponent_small_33_Template, 3, 2, \"small\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"div\", 12)(35, \"label\", 22);\n          i0.ɵɵtext(36, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(37, \"p-password\", 23);\n          i0.ɵɵtemplate(38, RegisterComponent_small_38_Template, 4, 3, \"small\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"div\", 12)(40, \"label\", 24);\n          i0.ɵɵtext(41, \"Confirm Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(42, \"p-password\", 25);\n          i0.ɵɵtemplate(43, RegisterComponent_small_43_Template, 3, 2, \"small\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"div\", 12)(45, \"div\", 26);\n          i0.ɵɵelement(46, \"p-checkbox\", 27);\n          i0.ɵɵelementStart(47, \"label\", 28);\n          i0.ɵɵtext(48, \" I agree to the \");\n          i0.ɵɵelementStart(49, \"a\", 29);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_a_click_49_listener() {\n            return ctx.showTerms();\n          });\n          i0.ɵɵtext(50, \"Terms of Service\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(51, \" and \");\n          i0.ɵɵelementStart(52, \"a\", 29);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_a_click_52_listener() {\n            return ctx.showPrivacy();\n          });\n          i0.ɵɵtext(53, \"Privacy Policy\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(54, RegisterComponent_small_54_Template, 2, 0, \"small\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(55, RegisterComponent_div_55_Template, 5, 1, \"div\", 30);\n          i0.ɵɵelement(56, \"button\", 31);\n          i0.ɵɵelementStart(57, \"div\", 32)(58, \"span\", 10);\n          i0.ɵɵtext(59, \"Already have an account? \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"a\", 29);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_a_click_60_listener() {\n            return ctx.redirectLogin();\n          });\n          i0.ɵɵtext(61, \" Sign in here \");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_4_0;\n          let tmp_5_0;\n          let tmp_6_0;\n          let tmp_7_0;\n          let tmp_8_0;\n          let tmp_9_0;\n          let tmp_12_0;\n          let tmp_13_0;\n          let tmp_16_0;\n          let tmp_18_0;\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"formGroup\", ctx.registerForm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"p-invalid\", ((tmp_1_0 = ctx.registerForm.get(\"firstName\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.registerForm.get(\"firstName\")) == null ? null : tmp_1_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx.registerForm.get(\"firstName\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.registerForm.get(\"firstName\")) == null ? null : tmp_2_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"p-invalid\", ((tmp_3_0 = ctx.registerForm.get(\"lastName\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.registerForm.get(\"lastName\")) == null ? null : tmp_3_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.registerForm.get(\"lastName\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.registerForm.get(\"lastName\")) == null ? null : tmp_4_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"p-invalid\", ((tmp_5_0 = ctx.registerForm.get(\"username\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.registerForm.get(\"username\")) == null ? null : tmp_5_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.registerForm.get(\"username\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.registerForm.get(\"username\")) == null ? null : tmp_6_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"p-invalid\", ((tmp_7_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_7_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.registerForm.get(\"email\")) == null ? null : tmp_8_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"p-invalid\", ((tmp_9_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_9_0.invalid) && ((tmp_9_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_9_0.touched));\n          i0.ɵɵproperty(\"toggleMask\", true)(\"feedback\", true);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_12_0.invalid) && ((tmp_12_0 = ctx.registerForm.get(\"password\")) == null ? null : tmp_12_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"p-invalid\", ((tmp_13_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_13_0.invalid) && ((tmp_13_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_13_0.touched));\n          i0.ɵɵproperty(\"toggleMask\", true)(\"feedback\", false);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_16_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_16_0.invalid) && ((tmp_16_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_16_0.touched) || (ctx.registerForm.errors == null ? null : ctx.registerForm.errors[\"passwordMismatch\"]) && ((tmp_16_0 = ctx.registerForm.get(\"confirmPassword\")) == null ? null : tmp_16_0.touched));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"binary\", true);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_18_0 = ctx.registerForm.get(\"acceptTerms\")) == null ? null : tmp_18_0.invalid) && ((tmp_18_0 = ctx.registerForm.get(\"acceptTerms\")) == null ? null : tmp_18_0.touched));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.registerForm.invalid || ctx.isLoading)(\"loading\", ctx.isLoading);\n        }\n      },\n      dependencies: [i7.NgIf, i8.ButtonDirective, i9.Checkbox, i10.InputText, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i11.Password],\n      styles: [\".auth-container[_ngcontent-%COMP%]{min-height:100vh;display:flex;align-items:center;justify-content:center;position:relative;overflow:hidden;background:var(--surface-ground);padding:1rem}.auth-background[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;z-index:1;opacity:.1}.auth-background[_ngcontent-%COMP%]   .bg-element[_ngcontent-%COMP%]{position:absolute;border-radius:50%;background:var(--primary-color);animation:_ngcontent-%COMP%_float 6s ease-in-out infinite}.auth-background[_ngcontent-%COMP%]   .bg-element.bg-element-1[_ngcontent-%COMP%]{width:200px;height:200px;top:10%;left:10%;animation-delay:0s}.auth-background[_ngcontent-%COMP%]   .bg-element.bg-element-2[_ngcontent-%COMP%]{width:150px;height:150px;top:60%;right:10%;animation-delay:2s}.auth-background[_ngcontent-%COMP%]   .bg-element.bg-element-3[_ngcontent-%COMP%]{width:100px;height:100px;bottom:20%;left:60%;animation-delay:4s}@keyframes _ngcontent-%COMP%_float{0%,to{transform:translateY(0)}50%{transform:translateY(-20px)}}.auth-content[_ngcontent-%COMP%]{position:relative;z-index:2;width:100%;max-width:500px}.auth-card[_ngcontent-%COMP%]{background:var(--surface-card);border-radius:16px;padding:2rem;box-shadow:0 4px 12px #0000001a;border:1px solid var(--surface-border)}.p-message-error[_ngcontent-%COMP%]{border-radius:8px;margin-bottom:1rem}.p-message-error[_ngcontent-%COMP%]   .p-message-wrapper[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}[_nghost-%COMP%]     .p-password-panel{background:var(--surface-overlay);border:1px solid var(--surface-border);border-radius:8px;box-shadow:0 4px 12px #0000001a;padding:1rem;margin-top:.5rem}[_nghost-%COMP%]     .p-password-panel .p-password-meter{margin-bottom:.5rem}[_nghost-%COMP%]     .p-password-panel .p-password-meter .p-password-strength{height:4px;border-radius:2px;transition:all .3s ease}[_nghost-%COMP%]     .p-password-panel .p-password-info{font-size:.75rem;color:var(--text-color-secondary);line-height:1.4}@media (max-width: 768px){.auth-content[_ngcontent-%COMP%]{padding:1rem;max-width:100%}.auth-card[_ngcontent-%COMP%]{padding:1.5rem;margin:1rem}}\"]\n    });\n  }\n  return RegisterComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}