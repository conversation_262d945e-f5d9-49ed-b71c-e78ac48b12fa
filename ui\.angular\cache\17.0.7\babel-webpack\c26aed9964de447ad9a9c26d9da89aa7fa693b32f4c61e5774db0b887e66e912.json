{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../service/stations.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../service/cache.service\";\nimport * as i4 from \"../../service/weather.service\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"../../service/date-utils.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"primeng/chart\";\nimport * as i10 from \"primeng/tooltip\";\nimport * as i11 from \"primeng/table\";\nimport * as i12 from \"primeng/button\";\nimport * as i13 from \"primeng/inputtext\";\nimport * as i14 from \"primeng/dropdown\";\nimport * as i15 from \"primeng/calendar\";\nimport * as i16 from \"@fortawesome/angular-fontawesome\";\nimport * as i17 from \"primeng/card\";\nimport * as i18 from \"primeng/tag\";\nimport * as i19 from \"primeng/divider\";\nimport * as i20 from \"primeng/toast\";\nimport * as i21 from \"../../../shared/components/modern-breadcrumb/modern-breadcrumb.component\";\nfunction ViewStationComponent_div_0_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11);\n    i0.ɵɵelement(2, \"fa-icon\", 12);\n    i0.ɵɵelementStart(3, \"h3\", 13);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.selectedStation.name);\n  }\n}\nfunction ViewStationComponent_div_0_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15);\n    i0.ɵɵelement(2, \"fa-icon\", 16);\n    i0.ɵɵelementStart(3, \"span\", 17);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(5, \"p-divider\");\n    i0.ɵɵelementStart(6, \"div\", 18)(7, \"div\", 19)(8, \"div\", 20)(9, \"div\", 21);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 22);\n    i0.ɵɵtext(12, \"Inverters\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 19)(14, \"div\", 20)(15, \"div\", 23);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 22);\n    i0.ɵɵtext(18, \"Total Devices\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelement(19, \"p-divider\");\n    i0.ɵɵelementStart(20, \"div\", 24)(21, \"h4\", 25);\n    i0.ɵɵtext(22, \"Monitoring Views\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"p-button\", 26);\n    i0.ɵɵlistener(\"click\", function ViewStationComponent_div_0_ng_template_6_Template_p_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.showGeneralOverview());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"p-button\", 27);\n    i0.ɵɵlistener(\"click\", function ViewStationComponent_div_0_ng_template_6_Template_p_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.showInvertMonitoring());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"p-button\", 28);\n    i0.ɵɵlistener(\"click\", function ViewStationComponent_div_0_ng_template_6_Template_p_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.showStringMonitoring());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedStation.address);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.inverters.length);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.devices.length);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"severity\", ctx_r2.showGeneral ? \"primary\" : \"secondary\")(\"text\", !ctx_r2.showGeneral);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"severity\", ctx_r2.showInvert ? \"primary\" : \"secondary\")(\"text\", !ctx_r2.showInvert);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"severity\", ctx_r2.showString ? \"primary\" : \"secondary\")(\"text\", !ctx_r2.showString);\n  }\n}\nfunction ViewStationComponent_div_0_p_card_7_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 11);\n    i0.ɵɵelement(2, \"fa-icon\", 30);\n    i0.ɵɵelementStart(3, \"h4\", 31);\n    i0.ɵɵtext(4, \"Performance Summary\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ViewStationComponent_div_0_p_card_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 32)(2, \"div\", 33)(3, \"div\", 11);\n    i0.ɵɵelement(4, \"fa-icon\", 34);\n    i0.ɵɵelementStart(5, \"span\", 35);\n    i0.ɵɵtext(6, \"Day Power\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"span\", 36);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 32)(10, \"div\", 33)(11, \"div\", 11);\n    i0.ɵɵelement(12, \"fa-icon\", 37);\n    i0.ɵɵelementStart(13, \"span\", 35);\n    i0.ɵɵtext(14, \"Total Power\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"span\", 36);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 32)(18, \"div\", 33)(19, \"div\", 11);\n    i0.ɵɵelement(20, \"fa-icon\", 38);\n    i0.ɵɵelementStart(21, \"span\", 35);\n    i0.ɵɵtext(22, \"Day Income\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"span\", 36);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 32)(26, \"div\", 33)(27, \"div\", 11);\n    i0.ɵɵelement(28, \"fa-icon\", 39);\n    i0.ɵɵelementStart(29, \"span\", 35);\n    i0.ɵɵtext(30, \"Month Power\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"span\", 36);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 32)(34, \"div\", 33)(35, \"div\", 11);\n    i0.ɵɵelement(36, \"fa-icon\", 40);\n    i0.ɵɵelementStart(37, \"span\", 35);\n    i0.ɵɵtext(38, \"Day Grid Energy\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"span\", 36);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(41, \"div\", 32)(42, \"div\", 33)(43, \"div\", 11);\n    i0.ɵɵelement(44, \"fa-icon\", 41);\n    i0.ɵɵelementStart(45, \"span\", 35);\n    i0.ɵɵtext(46, \"Day Use Energy\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"span\", 36);\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(49, \"div\", 2)(50, \"div\", 33)(51, \"div\", 11);\n    i0.ɵɵelement(52, \"fa-icon\", 42);\n    i0.ɵɵelementStart(53, \"span\", 35);\n    i0.ɵɵtext(54, \"Total Income\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"span\", 43);\n    i0.ɵɵtext(56);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r12.sumData.dayPower, \" kW\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r12.sumData.totalPower, \" kW\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\\u20AC\", ctx_r12.sumData.dayIncome, \"\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r12.sumData.monthPower, \" kW\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r12.sumData.dayOnGridEnergy, \" kWh\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r12.sumData.dayUseEnergy, \" kWh\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\\u20AC\", ctx_r12.sumData.totalIncome, \"\");\n  }\n}\nfunction ViewStationComponent_div_0_p_card_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-card\");\n    i0.ɵɵtemplate(1, ViewStationComponent_div_0_p_card_7_ng_template_1_Template, 5, 0, \"ng-template\", 6)(2, ViewStationComponent_div_0_p_card_7_ng_template_2_Template, 57, 7, \"ng-template\", 7);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewStationComponent_div_0_div_8_p_card_1_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 11);\n    i0.ɵɵelement(2, \"fa-icon\", 47);\n    i0.ɵɵelementStart(3, \"h4\", 48);\n    i0.ɵɵtext(4, \"Weather Forecast\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ViewStationComponent_div_0_div_8_p_card_1_ng_template_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 51)(2, \"h5\", 25);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"img\", 52);\n    i0.ɵɵelementStart(5, \"p\", 53);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 54);\n    i0.ɵɵelement(8, \"fa-icon\", 55);\n    i0.ɵɵelementStart(9, \"span\", 56);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"fa-icon\", 57);\n    i0.ɵɵelementStart(12, \"span\", 56);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 58);\n    i0.ɵɵelement(15, \"fa-icon\", 59);\n    i0.ɵɵelementStart(16, \"span\", 22);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const day_r19 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(day_r19.day);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", day_r19.icon, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(day_r19.description);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", day_r19.minTemperatureCelsius, \"\\u00B0\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", day_r19.maxTemperatureCelsius, \"\\u00B0\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", day_r19.windKph, \"km/h\");\n  }\n}\nfunction ViewStationComponent_div_0_div_8_p_card_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, ViewStationComponent_div_0_div_8_p_card_1_ng_template_2_div_1_Template, 18, 6, \"div\", 49);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r17.weatherData.weatherInfo);\n  }\n}\nfunction ViewStationComponent_div_0_div_8_p_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-card\", 5);\n    i0.ɵɵtemplate(1, ViewStationComponent_div_0_div_8_p_card_1_ng_template_1_Template, 5, 0, \"ng-template\", 6)(2, ViewStationComponent_div_0_div_8_p_card_1_ng_template_2_Template, 2, 1, \"ng-template\", 7);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewStationComponent_div_0_div_8_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 33)(2, \"div\", 11);\n    i0.ɵɵelement(3, \"fa-icon\", 60);\n    i0.ɵɵelementStart(4, \"h4\", 31);\n    i0.ɵɵtext(5, \"Energy Performance\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"p-tag\", 61);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ViewStationComponent_div_0_div_8_ng_template_5_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"label\", 64);\n    i0.ɵɵtext(2, \"Select Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-calendar\", 70);\n    i0.ɵɵlistener(\"ngModelChange\", function ViewStationComponent_div_0_div_8_ng_template_5_div_5_Template_p_calendar_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r23.selectedDate = $event);\n    })(\"onSelect\", function ViewStationComponent_div_0_div_8_ng_template_5_div_5_Template_p_calendar_onSelect_3_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r25.onDateChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r20.selectedDate)(\"showIcon\", true);\n  }\n}\nfunction ViewStationComponent_div_0_div_8_ng_template_5_p_chart_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-chart\", 71);\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"data\", ctx_r21.lineData)(\"options\", ctx_r21.lineOptions)(\"height\", 400);\n  }\n}\nfunction ViewStationComponent_div_0_div_8_ng_template_5_p_chart_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-chart\", 72);\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"data\", ctx_r22.barData)(\"options\", ctx_r22.lineOptions)(\"height\", 400);\n  }\n}\nfunction ViewStationComponent_div_0_div_8_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"div\", 63)(2, \"label\", 64);\n    i0.ɵɵtext(3, \"Time Frame\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p-dropdown\", 65);\n    i0.ɵɵlistener(\"ngModelChange\", function ViewStationComponent_div_0_div_8_ng_template_5_Template_p_dropdown_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r26.selectedTimeFrame = $event);\n    })(\"onChange\", function ViewStationComponent_div_0_div_8_ng_template_5_Template_p_dropdown_onChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r28 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r28.onTimeFrameChange($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, ViewStationComponent_div_0_div_8_ng_template_5_div_5_Template, 4, 2, \"div\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 67);\n    i0.ɵɵtemplate(7, ViewStationComponent_div_0_div_8_ng_template_5_p_chart_7_Template, 1, 3, \"p-chart\", 68)(8, ViewStationComponent_div_0_div_8_ng_template_5_p_chart_8_Template, 1, 3, \"p-chart\", 69);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"options\", ctx_r15.timeFrames)(\"ngModel\", ctx_r15.selectedTimeFrame)(\"showClear\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.selectedTimeFrame === \"day\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.searchType == null);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r15.searchType != null);\n  }\n}\nfunction ViewStationComponent_div_0_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, ViewStationComponent_div_0_div_8_p_card_1_Template, 3, 0, \"p-card\", 45);\n    i0.ɵɵelement(2, \"br\");\n    i0.ɵɵelementStart(3, \"p-card\");\n    i0.ɵɵtemplate(4, ViewStationComponent_div_0_div_8_ng_template_4_Template, 7, 0, \"ng-template\", 6)(5, ViewStationComponent_div_0_div_8_ng_template_5_Template, 9, 6, \"ng-template\", 7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.weatherData);\n  }\n}\nfunction ViewStationComponent_div_0_div_9_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"div\", 33)(2, \"div\", 11);\n    i0.ɵɵelement(3, \"fa-icon\", 74);\n    i0.ɵɵelementStart(4, \"h4\", 75);\n    i0.ɵɵtext(5, \"Inverter Power Monitoring\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"p-tag\", 76);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ViewStationComponent_div_0_div_9_ng_template_3_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"label\", 64);\n    i0.ɵɵtext(2, \"Select Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-calendar\", 70);\n    i0.ɵɵlistener(\"ngModelChange\", function ViewStationComponent_div_0_div_9_ng_template_3_div_5_Template_p_calendar_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r38);\n      const ctx_r37 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r37.selectedDate = $event);\n    })(\"onSelect\", function ViewStationComponent_div_0_div_9_ng_template_3_div_5_Template_p_calendar_onSelect_3_listener($event) {\n      i0.ɵɵrestoreView(_r38);\n      const ctx_r39 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r39.onDateChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r31.selectedDate)(\"showIcon\", true);\n  }\n}\nfunction ViewStationComponent_div_0_div_9_ng_template_3_p_chart_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-chart\", 71);\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"data\", ctx_r32.lineData)(\"options\", ctx_r32.lineOptions)(\"height\", 300);\n  }\n}\nfunction ViewStationComponent_div_0_div_9_ng_template_3_p_chart_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-chart\", 72);\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"data\", ctx_r33.barData)(\"options\", ctx_r33.lineOptions)(\"height\", 300);\n  }\n}\nfunction ViewStationComponent_div_0_div_9_ng_template_3_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 91)(2, \"div\", 92);\n    i0.ɵɵelement(3, \"fa-icon\", 93);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Device Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"p-sortIcon\", 94);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"th\", 95)(8, \"div\", 92);\n    i0.ɵɵelement(9, \"fa-icon\", 96);\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11, \"Time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"p-sortIcon\", 97);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"th\", 98)(14, \"div\", 92);\n    i0.ɵɵelement(15, \"fa-icon\", 99);\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17, \"Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"p-sortIcon\", 100);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"th\", 101)(20, \"div\", 92);\n    i0.ɵɵelement(21, \"fa-icon\", 102);\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23, \"Active Power (kW)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(24, \"p-sortIcon\", 103);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"th\", 104)(26, \"div\", 92);\n    i0.ɵɵelement(27, \"fa-icon\", 105);\n    i0.ɵɵelementStart(28, \"span\");\n    i0.ɵɵtext(29, \"Total Input Power (kW)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"p-sortIcon\", 106);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"th\", 107)(32, \"div\", 92);\n    i0.ɵɵelement(33, \"fa-icon\", 108);\n    i0.ɵɵelementStart(34, \"span\");\n    i0.ɵɵtext(35, \"Efficiency (%)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(36, \"p-sortIcon\", 109);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"th\", 110)(38, \"div\", 92);\n    i0.ɵɵelement(39, \"fa-icon\", 111);\n    i0.ɵɵelementStart(40, \"span\");\n    i0.ɵɵtext(41, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(42, \"p-sortIcon\", 112);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ViewStationComponent_div_0_div_9_ng_template_3_ng_template_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"div\", 11);\n    i0.ɵɵelement(3, \"fa-icon\", 113);\n    i0.ɵɵelementStart(4, \"span\", 114);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"td\")(7, \"div\", 11);\n    i0.ɵɵelement(8, \"fa-icon\", 115);\n    i0.ɵɵelementStart(9, \"span\", 114);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"td\")(12, \"span\", 114);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"td\")(15, \"div\", 11);\n    i0.ɵɵelement(16, \"fa-icon\", 116);\n    i0.ɵɵelementStart(17, \"span\", 117);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"td\")(20, \"div\", 11);\n    i0.ɵɵelement(21, \"fa-icon\", 118);\n    i0.ɵɵelementStart(22, \"span\", 119);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"td\");\n    i0.ɵɵelement(25, \"p-tag\", 120);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"td\");\n    i0.ɵɵelement(27, \"p-tag\", 121);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dataPoint_r40 = ctx.$implicit;\n    const ctx_r36 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(dataPoint_r40.name || \"N/A\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(dataPoint_r40.time);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(dataPoint_r40.date);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(dataPoint_r40.activePower.toFixed(2));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(dataPoint_r40.totalInputPower.toFixed(2));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", dataPoint_r40.efficiency + (dataPoint_r40.efficiency !== \"N/A\" ? \"%\" : \"\"))(\"severity\", dataPoint_r40.efficiency === \"N/A\" ? \"secondary\" : ctx_r36.parseFloat(dataPoint_r40.efficiency) > 90 ? \"success\" : ctx_r36.parseFloat(dataPoint_r40.efficiency) > 75 ? \"info\" : ctx_r36.parseFloat(dataPoint_r40.efficiency) > 50 ? \"warning\" : \"danger\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", dataPoint_r40.status)(\"severity\", dataPoint_r40.status === \"Optimal\" ? \"success\" : dataPoint_r40.status === \"Good\" ? \"info\" : dataPoint_r40.status === \"Low\" ? \"warning\" : dataPoint_r40.status === \"Minimal\" ? \"warning\" : \"danger\");\n  }\n}\nconst _c0 = () => [\"name\", \"time\", \"date\", \"activePower\", \"totalInputPower\", \"efficiency\", \"status\"];\nconst _c1 = () => [10, 20, 30];\nfunction ViewStationComponent_div_0_div_9_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"div\", 63)(2, \"label\", 64);\n    i0.ɵɵtext(3, \"Time Frame\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p-dropdown\", 65);\n    i0.ɵɵlistener(\"ngModelChange\", function ViewStationComponent_div_0_div_9_ng_template_3_Template_p_dropdown_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r41 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r41.selectedTimeFrame = $event);\n    })(\"onChange\", function ViewStationComponent_div_0_div_9_ng_template_3_Template_p_dropdown_onChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r43 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r43.onTimeFrameChange($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, ViewStationComponent_div_0_div_9_ng_template_3_div_5_Template, 4, 2, \"div\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 77);\n    i0.ɵɵtemplate(7, ViewStationComponent_div_0_div_9_ng_template_3_p_chart_7_Template, 1, 3, \"p-chart\", 68)(8, ViewStationComponent_div_0_div_9_ng_template_3_p_chart_8_Template, 1, 3, \"p-chart\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 78);\n    i0.ɵɵelement(10, \"p-toast\");\n    i0.ɵɵelementStart(11, \"div\", 79)(12, \"div\", 80)(13, \"div\", 11);\n    i0.ɵɵelement(14, \"fa-icon\", 81);\n    i0.ɵɵelementStart(15, \"h5\", 82);\n    i0.ɵɵtext(16, \"Inverter Performance Data\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 83)(18, \"span\", 84);\n    i0.ɵɵelement(19, \"i\", 85);\n    i0.ɵɵelementStart(20, \"input\", 86);\n    i0.ɵɵlistener(\"input\", function ViewStationComponent_div_0_div_9_ng_template_3_Template_input_input_20_listener($event) {\n      i0.ɵɵrestoreView(_r42);\n      const _r34 = i0.ɵɵreference(23);\n      const ctx_r44 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r44.onGlobalFilter(_r34, $event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"p-button\", 87);\n    i0.ɵɵlistener(\"click\", function ViewStationComponent_div_0_div_9_ng_template_3_Template_p_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r45 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r45.exportChartData());\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(22, \"p-table\", 88, 89);\n    i0.ɵɵtemplate(24, ViewStationComponent_div_0_div_9_ng_template_3_ng_template_24_Template, 43, 0, \"ng-template\", 6)(25, ViewStationComponent_div_0_div_9_ng_template_3_ng_template_25_Template, 28, 9, \"ng-template\", 90);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"options\", ctx_r30.timeFrames)(\"ngModel\", ctx_r30.selectedTimeFrame)(\"showClear\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r30.selectedTimeFrame === \"day\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r30.searchType == null);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r30.searchType != null);\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"value\", ctx_r30.chartTableData)(\"rows\", 10)(\"globalFilterFields\", i0.ɵɵpureFunction0(13, _c0))(\"paginator\", true)(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(14, _c1))(\"showCurrentPageReport\", true)(\"rowHover\", true);\n  }\n}\nfunction ViewStationComponent_div_0_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"p-card\");\n    i0.ɵɵtemplate(2, ViewStationComponent_div_0_div_9_ng_template_2_Template, 7, 0, \"ng-template\", 6)(3, ViewStationComponent_div_0_div_9_ng_template_3_Template, 26, 15, \"ng-template\", 7);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ViewStationComponent_div_0_div_10_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 122)(1, \"div\", 33)(2, \"div\", 11);\n    i0.ɵɵelement(3, \"fa-icon\", 123);\n    i0.ɵɵelementStart(4, \"h4\", 124);\n    i0.ɵɵtext(5, \"String Current Monitoring\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"p-tag\", 125);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ViewStationComponent_div_0_div_10_ng_template_3_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 130);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 91)(4, \"div\", 92);\n    i0.ɵɵelement(5, \"fa-icon\", 131);\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7, \"String Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"p-sortIcon\", 94);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"th\", 132)(10, \"div\", 92);\n    i0.ɵɵelement(11, \"fa-icon\", 102);\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13, \"Current (A)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"p-sortIcon\", 133);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"th\", 134)(16, \"div\", 92);\n    i0.ɵɵelement(17, \"fa-icon\", 135);\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtext(19, \"Voltage (V)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"p-sortIcon\", 136);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"th\", 137)(22, \"div\", 92);\n    i0.ɵɵelement(23, \"fa-icon\", 105);\n    i0.ɵɵelementStart(24, \"span\");\n    i0.ɵɵtext(25, \"Power (W)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"p-sortIcon\", 138);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"th\", 139)(28, \"div\", 92);\n    i0.ɵɵelement(29, \"fa-icon\", 140);\n    i0.ɵɵelementStart(30, \"span\");\n    i0.ɵɵtext(31, \"Total Energy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(32, \"p-sortIcon\", 141);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"th\", 142)(34, \"div\", 92);\n    i0.ɵɵelement(35, \"fa-icon\", 108);\n    i0.ɵɵelementStart(36, \"span\");\n    i0.ɵɵtext(37, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(38, \"p-sortIcon\", 143);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ViewStationComponent_div_0_div_10_ng_template_3_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 144);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\")(4, \"div\", 11);\n    i0.ɵɵelement(5, \"fa-icon\", 145);\n    i0.ɵɵelementStart(6, \"span\", 114);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"td\")(9, \"span\", 119);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"td\")(12, \"span\", 114);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"td\")(15, \"span\", 117);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"td\")(18, \"span\", 114);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"td\")(21, \"div\", 11);\n    i0.ɵɵelement(22, \"p-tag\", 121);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const invert_r51 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", invert_r51);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(invert_r51.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", invert_r51.nominaloutput, \" A\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", invert_r51.capacity, \" V\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", invert_r51.acpower, \" W\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", invert_r51.totalenergy, \" kWh\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", invert_r51.performance > 80 ? \"Normal\" : invert_r51.performance > 60 ? \"Warning\" : \"Critical\")(\"severity\", invert_r51.performance > 80 ? \"success\" : invert_r51.performance > 60 ? \"warning\" : \"danger\");\n  }\n}\nconst _c2 = () => [\"name\", \"nominaloutput\", \"capacity\", \"acpower\", \"totalenergy\", \"performance\"];\nfunction ViewStationComponent_div_0_div_10_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r53 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 77);\n    i0.ɵɵelement(1, \"p-chart\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 78);\n    i0.ɵɵelement(3, \"p-toast\");\n    i0.ɵɵelementStart(4, \"div\", 79)(5, \"div\", 80)(6, \"div\", 11);\n    i0.ɵɵelement(7, \"fa-icon\", 81);\n    i0.ɵɵelementStart(8, \"h5\", 82);\n    i0.ɵɵtext(9, \"String Performance Data\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 83)(11, \"span\", 84);\n    i0.ɵɵelement(12, \"i\", 85);\n    i0.ɵɵelementStart(13, \"input\", 126);\n    i0.ɵɵlistener(\"input\", function ViewStationComponent_div_0_div_10_ng_template_3_Template_input_input_13_listener($event) {\n      i0.ɵɵrestoreView(_r53);\n      const _r48 = i0.ɵɵreference(16);\n      const ctx_r52 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r52.onGlobalFilter(_r48, $event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"p-button\", 127);\n    i0.ɵɵlistener(\"click\", function ViewStationComponent_div_0_div_10_ng_template_3_Template_p_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r53);\n      const _r48 = i0.ɵɵreference(16);\n      return i0.ɵɵresetView(_r48.exportCSV());\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"p-table\", 128, 129);\n    i0.ɵɵlistener(\"selectionChange\", function ViewStationComponent_div_0_div_10_ng_template_3_Template_p_table_selectionChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r53);\n      const ctx_r55 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r55.selectedProducts = $event);\n    });\n    i0.ɵɵtemplate(17, ViewStationComponent_div_0_div_10_ng_template_3_ng_template_17_Template, 39, 0, \"ng-template\", 6)(18, ViewStationComponent_div_0_div_10_ng_template_3_ng_template_18_Template, 23, 8, \"ng-template\", 90);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r47 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"data\", ctx_r47.lineData)(\"options\", ctx_r47.lineOptions)(\"height\", 300);\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"value\", ctx_r47.invertpower)(\"columns\", ctx_r47.cols)(\"rows\", 10)(\"globalFilterFields\", i0.ɵɵpureFunction0(12, _c2))(\"paginator\", true)(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(13, _c1))(\"showCurrentPageReport\", true)(\"selection\", ctx_r47.selectedProducts)(\"rowHover\", true);\n  }\n}\nfunction ViewStationComponent_div_0_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"p-card\");\n    i0.ɵɵtemplate(2, ViewStationComponent_div_0_div_10_ng_template_2_Template, 7, 0, \"ng-template\", 6)(3, ViewStationComponent_div_0_div_10_ng_template_3_Template, 19, 14, \"ng-template\", 7);\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c3 = () => ({\n  label: \"Stations\",\n  routerLink: \"/app/index\",\n  icon: \"solar-panel\"\n});\nconst _c4 = a0 => ({\n  label: a0,\n  icon: \"building\"\n});\nconst _c5 = (a0, a1) => [a0, a1];\nfunction ViewStationComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n    i0.ɵɵelement(2, \"app-modern-breadcrumb\", 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 4)(4, \"p-card\", 5);\n    i0.ɵɵtemplate(5, ViewStationComponent_div_0_ng_template_5_Template, 5, 1, \"ng-template\", 6)(6, ViewStationComponent_div_0_ng_template_6_Template, 26, 9, \"ng-template\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, ViewStationComponent_div_0_p_card_7_Template, 3, 0, \"p-card\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, ViewStationComponent_div_0_div_8_Template, 6, 1, \"div\", 9)(9, ViewStationComponent_div_0_div_9_Template, 4, 0, \"div\", 9)(10, ViewStationComponent_div_0_div_10_Template, 4, 0, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"items\", i0.ɵɵpureFunction2(8, _c5, i0.ɵɵpureFunction0(5, _c3), i0.ɵɵpureFunction1(6, _c4, ctx_r0.selectedStation.name)));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.sumData);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showGeneral);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showInvert);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showString);\n  }\n}\nexport let ViewStationComponent = /*#__PURE__*/(() => {\n  class ViewStationComponent {\n    constructor(stationsService, route, cacheService, weatherService, messageService, dateUtils) {\n      this.stationsService = stationsService;\n      this.route = route;\n      this.cacheService = cacheService;\n      this.weatherService = weatherService;\n      this.messageService = messageService;\n      this.dateUtils = dateUtils;\n      this.stations = [];\n      this.devices = [];\n      this.inverters = [];\n      this.searchType = null;\n      this.selectedDate = new Date(); // default σημερινή ημερομηνία\n      this.sortOptionsCountry = [];\n      this.sortOptionsStatus = [];\n      this.sortOrder = 0;\n      this.sortField = '';\n      this.sourceCities = [];\n      this.targetCities = [];\n      this.orderCities = [];\n      this.showGeneral = true;\n      this.showInvert = false;\n      this.showString = false;\n      this.invertpower = [];\n      this.chartTableData = []; // Data for the table based on chart\n      this.invert = {};\n      this.rowsPerPageOptions = [5, 10, 20];\n      this.cols = [];\n    }\n    ngOnInit() {\n      let stations = this.cacheService.getStations();\n      console.log(stations);\n      //console.log(this.stations)\n      this.route.paramMap.subscribe(params => {\n        this.selectedStation = stations.find(s => s.id == params.get('id'));\n        //this.selectedStation = params.get('id');\n        console.log('Station ID:', this.selectedStation);\n      });\n      this.getStationDevices();\n      this.getStationSumData();\n      this.getWeather();\n      //this.initCharts();\n      ///duummyy\n      // this.initDays();\n      //dummy - will be populated with real data from charts\n      // this.stationsService.getInvertPower().then(data => this.invertpower = data);\n      this.cols = [];\n      this.timeFrames = [{\n        id: 'day',\n        label: 'Day'\n      }, {\n        id: 'month',\n        label: 'Month'\n      }, {\n        id: 'year',\n        label: 'Year'\n      }, {\n        id: 'last30days',\n        label: 'Last 30 days'\n      }, {\n        id: 'last365days',\n        label: 'Last 365 days'\n      }, {\n        id: 'thisYear',\n        label: 'This year (1/1 - today)'\n      }, {\n        id: 'fromBeginning',\n        label: 'From the beginning'\n      }];\n      this.selectedTimeFrame = \"day\";\n    }\n    // initDays(){\n    //     this.days = [\n    //         {\n    //             \"day\":\"Tuesday\",\n    //             \"temp\":23,\n    //             \"kati\":3.20,\n    //             \"image\":\"https://cdn-icons-png.flaticon.com/512/979/979585.png\",\n    //             \"alert\":\"No alerts\"\n    //         },\n    //         {\n    //             \"day\":\"Today\",\n    //             \"temp\":23,\n    //             \"kati\":3.20,\n    //             \"image\":\"https://cdn-icons-png.flaticon.com/512/5213/5213385.png\",\n    //             \"alert\":\"No alerts\"\n    //         },\n    //         {\n    //             \"day\":\"Thursday\",\n    //             \"temp\":22,\n    //             \"kati\":3.20,\n    //             \"image\":\"https://cdn-icons-png.flaticon.com/512/5213/5213385.png\",\n    //             \"alert\":\"No alerts\"\n    //         },\n    //         {\n    //             \"day\":\"Friday\",\n    //             \"temp\":23,\n    //             \"kati\":3.20,\n    //             \"image\":\"https://cdn-icons-png.flaticon.com/512/5213/5213385.png\",\n    //             \"alert\":\"No alerts\"\n    //         },\n    //         {\n    //             \"day\":\"Saturday\",\n    //             \"temp\":23,\n    //             \"kati\":3.20,\n    //             \"image\":\"https://cdn-icons-png.flaticon.com/512/1163/1163657.png\",\n    //             \"alert\":\"Light Hail Probability\"\n    //         },\n    //         {\n    //             \"day\":\"Sunday\",\n    //             \"temp\":21,\n    //             \"kati\":3.20,\n    //             \"image\":\"https://cdn-icons-png.flaticon.com/512/979/979585.png\",\n    //             \"alert\":\"No alerts\"\n    //         },\n    //         {\n    //             \"day\":\"Monday\",\n    //             \"temp\":23,\n    //             \"kati\":3.20,\n    //             \"image\":\"https://cdn-icons-png.flaticon.com/512/12607/12607703.png\",\n    //             \"alert\":\"No alerts\"\n    //         }\n    //     ]\n    // }\n    initCharts(separated, searchType) {\n      const documentStyle = getComputedStyle(document.documentElement);\n      const textColor = documentStyle.getPropertyValue('--text-color');\n      const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n      const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\n      if (separated) {\n        // Βρίσκουμε τις μοναδικές ημερομηνίες με σειρά\n        const uniqueDates = Array.from(new Set(this.energyData.data.map(d => d.dateTime)));\n        const uniqueDateDescriptions = Array.from(new Set(this.energyData.data.map(d => d.dateDescription)));\n        // Βρίσκουμε τους μοναδικούς inverters\n        const uniqueInverters = Array.from(new Set(this.energyData.data.map(d => d.name)));\n        // Δημιουργούμε datasets, ένα για κάθε inverter\n        const barDatasets = uniqueInverters.map(inv => {\n          const color = this.getRandomColor();\n          const data = uniqueDates.map(date => {\n            const entry = this.energyData.data.find(d => d.name === inv && d.dateTime === date);\n            return entry ? entry.activePower : 0;\n          });\n          return {\n            label: inv,\n            data: data,\n            backgroundColor: color\n          };\n        });\n        this.barData = {\n          labels: uniqueDateDescriptions.map(dt => dt),\n          datasets: barDatasets\n        };\n        // const uniqueLineDates = Array.from(new Set(this.energyData.data.map(d => d.dateTime)));\n        // const uniqueLineDateDescriptions = Array.from(new Set(this.energyData.data.map(d => d.dateDescription)));\n        // const uniqueLineInverters = Array.from(new Set(this.energyData.data.map(d => d.name)));\n        // const lineDatasets = uniqueLineInverters.map(inv => {\n        //     const color = this.getRandomColor();\n        //     const data = uniqueLineDates.map(date => {\n        //         const entry = this.energyData.data.find(d => d.name === inv && d.dateTime === date);\n        //         return entry ? entry.activePower : 0;\n        //     });\n        //     return {\n        //         label: inv,\n        //         data: data,\n        //         borderColor: color,\n        //         fill: false,\n        //         tension: 0.3, // προαιρετικά για πιο ομαλές καμπύλες\n        //     };\n        // });\n        // this.lineData = {\n        //     labels: uniqueLineDateDescriptions, // ετικέτες στον X-άξονα\n        //     datasets: lineDatasets\n        // };\n        var datasets = [];\n        // const uniqueInverters = Array.from(new Set(this.energyData.data.map(d => d.name)));\n        const activePowerByName = {};\n        this.energyData.data.forEach(d => {\n          if (!activePowerByName[d.name]) {\n            activePowerByName[d.name] = [];\n          }\n          activePowerByName[d.name].push(d.activePower);\n        });\n        uniqueInverters.forEach(inv => {\n          var color = this.getRandomColor();\n          datasets.push({\n            label: inv,\n            data: activePowerByName[inv],\n            fill: false,\n            backgroundColor: color,\n            borderColor: color,\n            tension: .4\n          });\n        });\n        const firstName = this.energyData.data[0].name; // Παίρνουμε το πρώτο name\n        const dateTimesForFirstName = this.energyData.data.filter(d => d.name === firstName) // Φιλτράρουμε μόνο τα αντικείμενα με το ίδιο name\n        .map(d => d.dateTime); // Παίρνουμε μόνο τα dateTime\n        this.lineData = {\n          labels: dateTimesForFirstName.map((dt, index) => index % 2 === 0 ? this.dateUtils.formatTimeForChart(dt) : ''),\n          datasets: datasets\n        };\n        // this.barData = {\n        //     labels: this.energyData.data.map(item => item.name), // Το property \"name\" για τον X-άξονα\n        //     datasets: datasets\n        // };\n      } else {\n        this.lineData = {\n          labels: this.energyData.data.map((e, index) => index % 2 === 0 ? this.dateUtils.formatTimeForChart(e.dateTime) : ''),\n          datasets: [{\n            label: 'Active Power',\n            data: this.energyData.data.map(e => e.activePower),\n            fill: false,\n            backgroundColor: documentStyle.getPropertyValue('--primary-500'),\n            borderColor: documentStyle.getPropertyValue('--primary-500'),\n            tension: .4\n          }, {\n            label: 'Total Input Power',\n            data: this.energyData.data.map(e => e.totalInputPower),\n            fill: false,\n            backgroundColor: documentStyle.getPropertyValue('--primary-200'),\n            borderColor: documentStyle.getPropertyValue('--primary-200'),\n            tension: .4\n          }]\n        };\n        this.barData = {\n          labels: this.energyData.data.map(item => item.name),\n          datasets: [{\n            label: 'Active Power',\n            data: this.energyData.data.map(e => e.activePower),\n            fill: false,\n            backgroundColor: documentStyle.getPropertyValue('--primary-500'),\n            borderColor: documentStyle.getPropertyValue('--primary-500'),\n            tension: .4\n          }, {\n            label: 'Total Input Power',\n            data: this.energyData.data.map(e => e.totalInputPower),\n            fill: false,\n            backgroundColor: documentStyle.getPropertyValue('--primary-200'),\n            borderColor: documentStyle.getPropertyValue('--primary-200'),\n            tension: .4\n          }]\n        };\n      }\n      this.lineOptions = {\n        responsive: true,\n        maintainAspectRatio: false,\n        interaction: {\n          intersect: false,\n          mode: 'index'\n        },\n        elements: {\n          point: {\n            radius: 3,\n            hoverRadius: 6,\n            hitRadius: 15\n          }\n        },\n        plugins: {\n          legend: {\n            labels: {\n              fontColor: textColor\n            }\n          },\n          tooltip: {\n            enabled: true,\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            titleColor: '#ffffff',\n            bodyColor: '#ffffff',\n            borderColor: 'rgba(255, 255, 255, 0.1)',\n            borderWidth: 1,\n            cornerRadius: 8,\n            displayColors: true,\n            padding: 12,\n            titleFont: {\n              size: 14,\n              weight: 'bold'\n            },\n            bodyFont: {\n              size: 13\n            },\n            callbacks: {\n              title: context => {\n                if (context && context.length > 0) {\n                  const dataIndex = context[0].dataIndex;\n                  // Get the original datetime from energyData\n                  if (this.energyData && this.energyData.data && this.energyData.data[dataIndex]) {\n                    const dateTime = this.energyData.data[dataIndex].dateTime;\n                    return `Time: ${this.dateUtils.formatDateTimeForTooltip(dateTime)}`;\n                  }\n                  return `Time: ${context[0].label}`;\n                }\n                return 'Time: --:--';\n              },\n              label: context => {\n                const label = context.dataset.label || '';\n                const value = context.parsed.y;\n                return `${label}: ${value.toFixed(2)} kW`;\n              },\n              afterBody: context => {\n                if (context && context.length > 0) {\n                  const dataIndex = context[0].dataIndex;\n                  if (this.energyData && this.energyData.data && this.energyData.data[dataIndex]) {\n                    const data = this.energyData.data[dataIndex];\n                    const date = new Date(data.dateTime);\n                    return ['', `Date: ${date.toLocaleDateString()}`, `Total Input Power: ${data.totalInputPower?.toFixed(2) || 'N/A'} kW`];\n                  }\n                }\n                return [];\n              }\n            }\n          }\n        },\n        scales: {\n          x: {\n            ticks: {\n              color: textColorSecondary,\n              maxRotation: 0,\n              minRotation: 0\n            },\n            grid: {\n              color: surfaceBorder,\n              drawBorder: false\n            }\n          },\n          y: {\n            ticks: {\n              color: textColorSecondary\n            },\n            grid: {\n              color: surfaceBorder,\n              drawBorder: false\n            }\n          }\n        }\n      };\n      // Enhanced Bar Chart Options with Tooltips\n      this.barOptions = {\n        responsive: true,\n        maintainAspectRatio: false,\n        interaction: {\n          intersect: false,\n          mode: 'index'\n        },\n        plugins: {\n          legend: {\n            labels: {\n              fontColor: textColor\n            }\n          },\n          tooltip: {\n            enabled: true,\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            titleColor: '#ffffff',\n            bodyColor: '#ffffff',\n            borderColor: 'rgba(255, 255, 255, 0.1)',\n            borderWidth: 1,\n            cornerRadius: 8,\n            displayColors: true,\n            padding: 12,\n            titleFont: {\n              size: 14,\n              weight: 'bold'\n            },\n            bodyFont: {\n              size: 13\n            },\n            callbacks: {\n              title: context => {\n                if (context && context.length > 0) {\n                  const dataIndex = context[0].dataIndex;\n                  // For bar charts, show the label (date/time or inverter name)\n                  if (this.energyData && this.energyData.data && this.energyData.data[dataIndex]) {\n                    const data = this.energyData.data[dataIndex];\n                    if (data.dateTime) {\n                      return `Time: ${this.dateUtils.formatDateTimeForTooltip(data.dateTime)}`;\n                    }\n                    return `Inverter: ${data.name || context[0].label}`;\n                  }\n                  return context[0].label;\n                }\n                return 'Data Point';\n              },\n              label: context => {\n                const label = context.dataset.label || '';\n                const value = context.parsed.y;\n                return `${label}: ${value.toFixed(2)} kW`;\n              },\n              afterBody: context => {\n                if (context && context.length > 0) {\n                  const dataIndex = context[0].dataIndex;\n                  if (this.energyData && this.energyData.data && this.energyData.data[dataIndex]) {\n                    const data = this.energyData.data[dataIndex];\n                    const additionalInfo = [];\n                    if (data.dateTime) {\n                      const date = new Date(data.dateTime);\n                      additionalInfo.push(`Date: ${date.toLocaleDateString()}`);\n                    }\n                    if (data.totalInputPower !== undefined) {\n                      additionalInfo.push(`Total Input Power: ${data.totalInputPower.toFixed(2)} kW`);\n                    }\n                    if (data.name) {\n                      additionalInfo.push(`Device: ${data.name}`);\n                    }\n                    return additionalInfo.length > 0 ? ['', ...additionalInfo] : [];\n                  }\n                }\n                return [];\n              }\n            }\n          }\n        },\n        scales: {\n          x: {\n            ticks: {\n              color: textColorSecondary,\n              maxRotation: 45,\n              minRotation: 0\n            },\n            grid: {\n              color: surfaceBorder,\n              drawBorder: false\n            }\n          },\n          y: {\n            ticks: {\n              color: textColorSecondary\n            },\n            grid: {\n              color: surfaceBorder,\n              drawBorder: false\n            }\n          }\n        }\n      };\n    }\n    getStationDevices() {\n      let request = {\n        stationId: this.selectedStation.id\n      };\n      this.stationsService.getStationDevices(request).then(data => {\n        console.log(data);\n        this.devices = data;\n        this.inverters = this.devices.filter(device => device.type === \"StringInverter\");\n        console.log(\"DEBV\" + this.devices);\n        this.inverterIds = this.devices.filter(device => device.type === \"StringInverter\") // Φιλτράρει μόνο τα StringInverter\n        .map(device => device.id) // Παίρνει μόνο τα id\n        .join(\",\"); // Τα ενώνει με κόμματα\n        this.getHistoryEnergy(this.inverterIds);\n      });\n    }\n    getStationSumData() {\n      let request = {\n        stationId: this.selectedStation.id\n      };\n      this.stationsService.getStationSumData(request).then(data => this.sumData = data);\n    }\n    getWeather() {\n      let request = {\n        coordinates: this.selectedStation.longitude + \",\" + this.selectedStation.latitude\n      };\n      this.weatherService.getWeather(request).then(data => this.weatherData = data);\n    }\n    getHistoryEnergy(inverterIds, separated = false, dateTimeFrom = null, dateTimeTo = null, searchType = null) {\n      var now = new Date();\n      if (dateTimeFrom != null) var formattedStartDate = dateTimeFrom.toISOString();else var formattedStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0).toISOString();\n      if (dateTimeTo != null) var formattedEndDate = dateTimeTo.toISOString();else var formattedEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0).toISOString();\n      // var todayMidnightUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 0, 0, 0));\n      // console.log(dateTimeFrom)\n      // if (dateTimeFrom != null) {\n      //     now = new Date(dateTimeFrom);\n      //     todayMidnightUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 0, 0, 0));\n      // }\n      // var formattedStartDate = dateTimeFrom.toISOString();\n      // console.log(formattedStartDate);\n      // var tomorrowMidnightUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate() + 1, 0, 0, 0));\n      // if (dateTimeTo != null) {\n      //     now = new Date(dateTimeTo);\n      //     tomorrowMidnightUTC = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()+1, 0, 0, 0));\n      // }\n      let request = {\n        devIds: inverterIds != \"\" ? inverterIds : null,\n        devTypeId: 1,\n        startDateTime: formattedStartDate,\n        endDateTime: formattedEndDate,\n        separated: separated,\n        searchType: searchType,\n        stationId: this.selectedStation.id\n      };\n      this.stationsService.getStationHistoricData(request).then(data => {\n        this.energyData = data;\n        console.log(this.energyData);\n        if (this.energyData.data.length > 0) {\n          // Filter data to not show beyond current time\n          this.energyData.data = this.filterDataByCurrentTime(this.energyData.data);\n          this.initCharts(separated, searchType);\n          this.updateChartTableData(); // Update table data when chart data changes\n        } else {\n          this.messageService.add({\n            severity: 'warn',\n            summary: 'No Data',\n            detail: 'No Data to display!'\n          });\n        }\n      });\n    }\n    showGeneralOverview() {\n      var reload = true;\n      if (this.showGeneral) reload = false;\n      this.showGeneral = true;\n      this.showInvert = false;\n      this.showString = false;\n      if (reload) {\n        // Reload the original general overview data (non-separated)\n        this.getHistoryEnergy(this.inverterIds, false);\n      }\n    }\n    showInvertMonitoring() {\n      var reload = true;\n      if (this.showInvert) reload = false;\n      this.showGeneral = false;\n      this.showInvert = true;\n      this.showString = false;\n      if (reload) this.getHistoryEnergy(this.inverterIds, true);\n    }\n    showStringMonitoring() {\n      this.showGeneral = false;\n      this.showInvert = false;\n      this.showString = true;\n    }\n    getRandomColor() {\n      const rootStyles = getComputedStyle(document.documentElement);\n      const cssVariables = Object.keys(rootStyles).map(key => rootStyles[key]).filter(value => value.startsWith('--')) // Φιλτράρουμε μόνο τις CSS variables\n      .map(varName => rootStyles.getPropertyValue(varName).trim()) // Παίρνουμε τις τιμές τους\n      .filter(value => {\n        // Ελέγχουμε αν το όνομα της μεταβλητής περιέχει έναν αριθμό μεγαλύτερο του 200\n        const match = value.match(/(\\d+)/); // Αντιστοιχεί στον αριθμό που υπάρχει στο όνομα\n        return match && parseInt(match[0], 10) > 200; // Φιλτράρει μόνο χρώματα με αριθμό > 200\n      });\n      // Συνάρτηση που επιστρέφει τυχαίο χρώμα από τη λίστα\n      return cssVariables[Math.floor(Math.random() * cssVariables.length)];\n    }\n    onDateChange(event) {\n      console.log('Ημερομηνία επιλέχθηκε:', event);\n      // Create Athens timezone dates\n      const athensDate = this.dateUtils.toAthensTime(event);\n      var datefrom = new Date(athensDate.setHours(0, 0, 0, 0));\n      const pastDate = new Date(datefrom);\n      pastDate.setDate(pastDate.getDate() + 1);\n      this.getHistoryEnergy(this.inverterIds, !this.showGeneral, datefrom, pastDate, this.searchType);\n    }\n    onTimeFrameChange(event) {\n      console.log(event);\n      let currentDate = new Date();\n      console.log(currentDate);\n      let datefrom;\n      let dateto = new Date(currentDate);\n      switch (event.value) {\n        case 'day':\n          // Για την περίπτωση του \"Day\", το datefrom είναι σήμερα\n          datefrom = new Date(currentDate.setHours(0, 0, 0, 0)); // Ορίζουμε το time στα 00:00:00\n          this.searchType = null;\n          if (event.value === 'day') {\n            this.selectedDate = new Date(); // reset σε σημερινή ημερομηνία\n          }\n\n          break;\n        case 'month':\n          // Για την περίπτωση του \"Month\", το datefrom είναι η 1η μέρα του τρέχοντος μήνα\n          datefrom = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);\n          datefrom.setHours(0, 0, 0, 0);\n          this.searchType = \"monthly\";\n          break;\n        case 'year':\n          // Για την περίπτωση του \"Year\", το datefrom είναι η 1η Ιανουαρίου του τρέχοντος έτους\n          datefrom = new Date(currentDate.getFullYear(), 0, 1);\n          datefrom.setHours(0, 0, 0, 0);\n          this.searchType = \"yearly\";\n          break;\n        case 'last30days':\n          // Για την περίπτωση του \"Last 30 days\", το datefrom είναι 30 μέρες πριν\n          const pastDate = new Date(currentDate);\n          pastDate.setDate(pastDate.getDate() - 30);\n          pastDate.setHours(0, 0, 0, 0);\n          datefrom = pastDate;\n          this.searchType = \"monthly\";\n          break;\n        case 'last365days':\n          // Για την περίπτωση του \"Last 365 days\", το datefrom είναι 365 μέρες πριν\n          const pastDate2 = new Date(currentDate);\n          pastDate2.setDate(pastDate2.getDate() - 365);\n          pastDate2.setHours(0, 0, 0, 0);\n          datefrom = pastDate2;\n          this.searchType = \"yearly\";\n          break;\n        case 'thisYear':\n          // Για την περίπτωση του \"This year (1/1 - today)\", το datefrom είναι η 1η Ιανουαρίου του τρέχοντος έτους\n          datefrom = new Date(currentDate.getFullYear(), 0, 1);\n          datefrom.setHours(0, 0, 0, 0);\n          this.searchType = \"yearly\";\n          break;\n        case 'fromBeginning':\n          // Για την περίπτωση του \"From the beginning\", το datefrom μπορεί να είναι η αρχή της εφαρμογής ή άλλη προεπιλεγμένη ημερομηνία\n          // Εδώ χρησιμοποιούμε την αρχική ημερομηνία της εφαρμογής ή άλλη ημερομηνία\n          datefrom = new Date(2000, 0, 1); // Ή οποιαδήποτε άλλη ημερομηνία αρχής\n          this.searchType = \"lifetime\";\n          break;\n        default:\n          // Αν δεν είναι καμία από τις παραπάνω επιλογές, ορίζουμε μια προεπιλεγμένη τιμή\n          datefrom = new Date(currentDate.setHours(0, 0, 0, 0));\n          break;\n      }\n      // Καλούμε τη συνάρτηση getHistoryEnergy με τα ορίσματα\n      this.getHistoryEnergy(this.inverterIds, !this.showGeneral, datefrom, dateto, this.searchType);\n    }\n    onGlobalFilter(table, event) {\n      table.filterGlobal(event.target.value, 'contains');\n    }\n    filterDataByCurrentTime(data) {\n      const now = new Date();\n      const currentTime = now.getTime();\n      return data.filter(item => {\n        const itemDateTime = new Date(item.dateTime);\n        const itemTime = itemDateTime.getTime();\n        // Only include data points that are not in the future\n        return itemTime <= currentTime;\n      });\n    }\n    updateChartTableData() {\n      if (this.energyData && this.energyData.data) {\n        this.chartTableData = this.energyData.data.map((item, index) => {\n          return {\n            id: index + 1,\n            name: item.name || `Device-${index + 1}`,\n            time: this.dateUtils.formatTimeForTable(item.dateTime),\n            date: this.dateUtils.formatDateForTable(item.dateTime),\n            dateTime: item.dateTime,\n            activePower: item.activePower || 0,\n            totalInputPower: item.totalInputPower || 0,\n            efficiency: item.activePower && item.totalInputPower ? (item.activePower / item.totalInputPower * 100).toFixed(1) : 'N/A',\n            status: this.getStatusFromPower(item.activePower || 0)\n          };\n        });\n        // Also update invertpower array with latest data from each device\n        this.updateInvertPowerFromChartData();\n      }\n    }\n    updateInvertPowerFromChartData() {\n      if (this.energyData && this.energyData.data) {\n        // Group data by device name and get the latest entry for each\n        const deviceMap = new Map();\n        this.energyData.data.forEach(item => {\n          const deviceName = item.name || 'Unknown Device';\n          const itemTime = new Date(item.dateTime).getTime();\n          if (!deviceMap.has(deviceName) || new Date(deviceMap.get(deviceName).dateTime).getTime() < itemTime) {\n            deviceMap.set(deviceName, item);\n          }\n        });\n        // Convert to invertpower format\n        this.invertpower = Array.from(deviceMap.values()).map((item, index) => ({\n          id: (index + 1).toString().padStart(4, '0'),\n          name: item.name || `Device-${index + 1}`,\n          nominaloutput: Math.round(item.totalInputPower || 0),\n          capacity: Math.round((item.totalInputPower || 0) * 0.95),\n          acpower: item.activePower || 0,\n          totalenergy: Math.round((item.activePower || 0) * 24),\n          performance: item.activePower && item.totalInputPower ? Math.round(item.activePower / item.totalInputPower * 100) : 0\n        }));\n      }\n    }\n    getStatusFromPower(power) {\n      if (power > 80) return 'Optimal';\n      if (power > 50) return 'Good';\n      if (power > 20) return 'Low';\n      if (power > 0) return 'Minimal';\n      return 'Offline';\n    }\n    getStatusSeverity(status) {\n      switch (status) {\n        case 'Optimal':\n          return 'success';\n        case 'Good':\n          return 'info';\n        case 'Low':\n          return 'warning';\n        case 'Minimal':\n          return 'warning';\n        case 'Offline':\n          return 'danger';\n        default:\n          return 'secondary';\n      }\n    }\n    exportChartData() {\n      if (!this.chartTableData || this.chartTableData.length === 0) {\n        this.messageService.add({\n          severity: 'warn',\n          summary: 'No Data',\n          detail: 'No chart data available to export.'\n        });\n        return;\n      }\n      // Prepare CSV data\n      const headers = ['Device Name', 'Time', 'Date', 'Active Power (kW)', 'Total Input Power (kW)', 'Efficiency (%)', 'Status'];\n      const csvData = this.chartTableData.map(row => [row.name, row.time, row.date, row.activePower.toFixed(2), row.totalInputPower.toFixed(2), row.efficiency, row.status]);\n      // Create CSV content\n      const csvContent = [headers.join(','), ...csvData.map(row => row.join(','))].join('\\n');\n      // Create and download file\n      const blob = new Blob([csvContent], {\n        type: 'text/csv;charset=utf-8;'\n      });\n      const link = document.createElement('a');\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', `inverter-data-${new Date().toISOString().split('T')[0]}.csv`);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      this.messageService.add({\n        severity: 'success',\n        summary: 'Export Complete',\n        detail: 'Chart data has been exported to CSV successfully.'\n      });\n    }\n    ngOnDestroy() {\n      if (this.subscription) {\n        this.subscription.unsubscribe();\n      }\n    }\n    static #_ = this.ɵfac = function ViewStationComponent_Factory(t) {\n      return new (t || ViewStationComponent)(i0.ɵɵdirectiveInject(i1.StationsService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.CacheService), i0.ɵɵdirectiveInject(i4.WeatherService), i0.ɵɵdirectiveInject(i5.MessageService), i0.ɵɵdirectiveInject(i6.DateUtilsService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ViewStationComponent,\n      selectors: [[\"ng-component\"]],\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"grid p-fluid\", 4, \"ngIf\"], [1, \"grid\", \"p-fluid\"], [1, \"col-12\"], [3, \"items\"], [1, \"col-12\", \"lg:col-3\"], [1, \"mb-3\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"content\"], [4, \"ngIf\"], [\"class\", \"col-12 lg:col-9\", 4, \"ngIf\"], [1, \"bg-primary-50\", \"p-3\", \"border-round-top\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"icon\", \"solar-panel\", 1, \"text-primary\", \"text-xl\"], [1, \"m-0\", \"text-primary\"], [1, \"space-y-3\"], [1, \"flex\", \"align-items-start\", \"gap-2\"], [\"icon\", \"map-marker-alt\", 1, \"text-600\", \"mt-1\"], [1, \"text-600\", \"text-sm\", 2, \"word-break\", \"break-word\"], [1, \"grid\"], [1, \"col-6\"], [1, \"text-center\"], [1, \"text-2xl\", \"font-bold\", \"text-primary\"], [1, \"text-xs\", \"text-600\"], [1, \"text-2xl\", \"font-bold\", \"text-orange-500\"], [1, \"space-y-2\"], [1, \"text-900\", \"font-semibold\", \"mb-2\"], [\"label\", \"General Overview\", \"icon\", \"pi pi-chart-line\", \"size\", \"small\", 1, \"w-full\", \"justify-content-start\", 3, \"severity\", \"text\", \"click\"], [\"label\", \"Inverter Monitoring\", \"icon\", \"pi pi-cog\", \"size\", \"small\", 1, \"w-full\", \"justify-content-start\", 3, \"severity\", \"text\", \"click\"], [\"label\", \"String Monitoring\", \"icon\", \"pi pi-sitemap\", \"size\", \"small\", 1, \"w-full\", \"justify-content-start\", 3, \"severity\", \"text\", \"click\"], [1, \"bg-green-50\", \"p-3\", \"border-round-top\"], [\"icon\", \"chart-bar\", 1, \"text-green-600\", \"text-lg\"], [1, \"m-0\", \"text-green-800\"], [1, \"col-12\", \"mb-2\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\"], [\"icon\", \"bolt\", 1, \"text-orange-500\"], [1, \"text-sm\", \"text-600\"], [1, \"font-bold\"], [\"icon\", \"chart-line\", 1, \"text-blue-500\"], [\"icon\", \"euro-sign\", 1, \"text-green-500\"], [\"icon\", \"calendar-alt\", 1, \"text-purple-500\"], [\"icon\", \"plug\", 1, \"text-cyan-500\"], [\"icon\", \"battery-half\", 1, \"text-yellow-500\"], [\"icon\", \"coins\", 1, \"text-green-600\"], [1, \"font-bold\", \"text-green-600\"], [1, \"col-12\", \"lg:col-9\"], [\"class\", \"mb-3\", 4, \"ngIf\"], [1, \"bg-blue-50\", \"p-3\", \"border-round-top\"], [\"icon\", \"cloud-sun\", 1, \"text-blue-600\", \"text-lg\"], [1, \"m-0\", \"text-blue-800\"], [\"class\", \"col-12 md:col-6 lg:col-3 xl:col\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"md:col-6\", \"lg:col-3\", \"xl:col\"], [1, \"text-center\", \"p-3\", \"border-round\", \"hover:bg-blue-50\", \"transition-colors\", \"transition-duration-150\"], [\"height\", \"50\", 1, \"mb-2\", 3, \"src\"], [1, \"text-600\", \"text-sm\", \"mb-2\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-2\", \"mb-1\"], [\"icon\", \"arrow-down\", 1, \"text-blue-500\", \"text-xs\"], [1, \"text-sm\"], [\"icon\", \"arrow-up\", 1, \"text-red-500\", \"text-xs\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-1\"], [\"icon\", \"wind\", 1, \"text-cyan-500\", \"text-xs\"], [\"icon\", \"chart-line\", 1, \"text-green-600\", \"text-lg\"], [\"value\", \"Live Data\", \"severity\", \"success\", \"icon\", \"pi pi-circle-fill\"], [1, \"flex\", \"flex-column\", \"md:flex-row\", \"gap-3\", \"mb-4\"], [1, \"flex-1\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-600\", \"mb-2\"], [\"placeholder\", \"Select TimeFrame\", \"optionLabel\", \"label\", \"optionValue\", \"id\", 1, \"w-full\", 3, \"options\", \"ngModel\", \"showClear\", \"ngModelChange\", \"onChange\"], [\"class\", \"flex-1\", 4, \"ngIf\"], [1, \"border-round\", \"border-1\", \"surface-border\", \"p-3\"], [\"type\", \"line\", 3, \"data\", \"options\", \"height\", 4, \"ngIf\"], [\"type\", \"bar\", 3, \"data\", \"options\", \"height\", 4, \"ngIf\"], [\"dateFormat\", \"dd/mm/yy\", 1, \"w-full\", 3, \"ngModel\", \"showIcon\", \"ngModelChange\", \"onSelect\"], [\"type\", \"line\", 3, \"data\", \"options\", \"height\"], [\"type\", \"bar\", 3, \"data\", \"options\", \"height\"], [1, \"bg-orange-50\", \"p-3\", \"border-round-top\"], [\"icon\", \"cog\", 1, \"text-orange-600\", \"text-lg\"], [1, \"m-0\", \"text-orange-800\"], [\"value\", \"Real-time\", \"severity\", \"warning\", \"icon\", \"pi pi-circle-fill\"], [1, \"border-round\", \"border-1\", \"surface-border\", \"p-3\", \"mb-4\"], [1, \"border-round\", \"border-1\", \"surface-border\"], [1, \"bg-gray-50\", \"p-3\", \"border-round-top\", \"border-bottom-1\", \"surface-border\"], [1, \"flex\", \"flex-column\", \"md:flex-row\", \"md:justify-content-between\", \"md:align-items-center\", \"gap-3\"], [\"icon\", \"table\", 1, \"text-600\"], [1, \"m-0\", \"text-900\"], [1, \"flex\", \"gap-2\"], [1, \"p-input-icon-left\"], [1, \"pi\", \"pi-search\"], [\"pInputText\", \"\", \"type\", \"text\", \"placeholder\", \"Search inverters...\", 1, \"w-full\", \"sm:w-auto\", 3, \"input\"], [\"icon\", \"pi pi-download\", \"severity\", \"help\", \"size\", \"small\", \"pTooltip\", \"Export Chart Data to CSV\", 3, \"click\"], [\"responsiveLayout\", \"scroll\", \"currentPageReportTemplate\", \"Showing {first} to {last} of {totalRecords} data points\", \"dataKey\", \"id\", \"styleClass\", \"p-datatable-sm\", 3, \"value\", \"rows\", \"globalFilterFields\", \"paginator\", \"rowsPerPageOptions\", \"showCurrentPageReport\", \"rowHover\"], [\"dt\", \"\"], [\"pTemplate\", \"body\"], [\"pSortableColumn\", \"name\"], [1, \"flex\", \"align-items-center\", \"gap-1\"], [\"icon\", \"microchip\", 1, \"text-xs\"], [\"field\", \"name\"], [\"pSortableColumn\", \"time\"], [\"icon\", \"clock\", 1, \"text-xs\"], [\"field\", \"time\"], [\"pSortableColumn\", \"date\"], [\"icon\", \"calendar\", 1, \"text-xs\"], [\"field\", \"date\"], [\"pSortableColumn\", \"activePower\"], [\"icon\", \"bolt\", 1, \"text-xs\"], [\"field\", \"activePower\"], [\"pSortableColumn\", \"totalInputPower\"], [\"icon\", \"plug\", 1, \"text-xs\"], [\"field\", \"totalInputPower\"], [\"pSortableColumn\", \"efficiency\"], [\"icon\", \"tachometer-alt\", 1, \"text-xs\"], [\"field\", \"efficiency\"], [\"pSortableColumn\", \"status\"], [\"icon\", \"info-circle\", 1, \"text-xs\"], [\"field\", \"status\"], [\"icon\", \"microchip\", 1, \"text-primary\"], [1, \"font-medium\"], [\"icon\", \"clock\", 1, \"text-secondary\"], [\"icon\", \"bolt\", 1, \"text-orange-600\"], [1, \"font-medium\", \"text-orange-600\"], [\"icon\", \"plug\", 1, \"text-blue-600\"], [1, \"font-medium\", \"text-blue-600\"], [3, \"value\", \"severity\"], [\"icon\", \"pi pi-circle-fill\", 3, \"value\", \"severity\"], [1, \"bg-purple-50\", \"p-3\", \"border-round-top\"], [\"icon\", \"sitemap\", 1, \"text-purple-600\", \"text-lg\"], [1, \"m-0\", \"text-purple-800\"], [\"value\", \"Live Data\", \"severity\", \"info\", \"icon\", \"pi pi-circle-fill\"], [\"pInputText\", \"\", \"type\", \"text\", \"placeholder\", \"Search strings...\", 1, \"w-full\", \"sm:w-auto\", 3, \"input\"], [\"icon\", \"pi pi-download\", \"severity\", \"help\", \"size\", \"small\", \"pTooltip\", \"Export to CSV\", 3, \"click\"], [\"responsiveLayout\", \"scroll\", \"currentPageReportTemplate\", \"Showing {first} to {last} of {totalRecords} strings\", \"selectionMode\", \"multiple\", \"dataKey\", \"id\", \"styleClass\", \"p-datatable-sm\", 3, \"value\", \"columns\", \"rows\", \"globalFilterFields\", \"paginator\", \"rowsPerPageOptions\", \"showCurrentPageReport\", \"selection\", \"rowHover\", \"selectionChange\"], [\"dt2\", \"\"], [2, \"width\", \"3rem\"], [\"icon\", \"link\", 1, \"text-xs\"], [\"pSortableColumn\", \"nominaloutput\"], [\"field\", \"nominaloutput\"], [\"pSortableColumn\", \"capacity\"], [\"icon\", \"battery-full\", 1, \"text-xs\"], [\"field\", \"capacity\"], [\"pSortableColumn\", \"acpower\"], [\"field\", \"acpower\"], [\"pSortableColumn\", \"totalenergy\"], [\"icon\", \"chart-area\", 1, \"text-xs\"], [\"field\", \"totalenergy\"], [\"pSortableColumn\", \"performance\"], [\"field\", \"performance\"], [3, \"value\"], [\"icon\", \"link\", 1, \"text-purple-600\"]],\n      template: function ViewStationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ViewStationComponent_div_0_Template, 11, 11, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedStation);\n        }\n      },\n      dependencies: [i7.NgForOf, i7.NgIf, i8.NgControlStatus, i8.NgModel, i9.UIChart, i10.Tooltip, i11.Table, i5.PrimeTemplate, i11.SortableColumn, i11.SortIcon, i11.TableCheckbox, i11.TableHeaderCheckbox, i12.Button, i13.InputText, i14.Dropdown, i15.Calendar, i16.FaIconComponent, i17.Card, i18.Tag, i19.Divider, i20.Toast, i21.ModernBreadcrumbComponent],\n      encapsulation: 2\n    });\n  }\n  return ViewStationComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}