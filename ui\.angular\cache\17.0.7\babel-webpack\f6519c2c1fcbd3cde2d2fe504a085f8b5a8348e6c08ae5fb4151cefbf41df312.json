{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst _c0 = () => [\"/\"];\nexport class NotfoundComponent {\n  static #_ = this.ɵfac = function NotfoundComponent_Factory(t) {\n    return new (t || NotfoundComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NotfoundComponent,\n    selectors: [[\"app-notfound\"]],\n    decls: 35,\n    vars: 6,\n    consts: [[1, \"surface-ground\", \"flex\", \"align-items-center\", \"justify-content-center\", \"min-h-screen\", \"min-w-screen\", \"overflow-hidden\"], [1, \"flex\", \"flex-column\", \"align-items-center\", \"justify-content-center\"], [\"src\", \"assets/demo/images/notfound/logo-blue.svg\", \"alt\", \"Sakai logo\", 1, \"mb-5\", \"w-6rem\", \"flex-shrink-0\"], [2, \"border-radius\", \"56px\", \"padding\", \"0.3rem\", \"background\", \"linear-gradient(180deg, rgba(33, 150, 243, 0.4) 10%, rgba(33, 150, 243, 0) 30%)\"], [1, \"w-full\", \"surface-card\", \"py-8\", \"px-5\", \"sm:px-8\", \"flex\", \"flex-column\", \"align-items-center\", 2, \"border-radius\", \"53px\"], [1, \"text-blue-500\", \"font-bold\", \"text-3xl\"], [1, \"text-900\", \"font-bold\", \"text-3xl\", \"lg:text-5xl\", \"mb-2\"], [1, \"text-600\", \"mb-5\"], [1, \"w-full\", \"flex\", \"align-items-center\", \"py-5\", \"border-300\", \"border-bottom-1\", 3, \"routerLink\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"bg-cyan-400\", \"border-round\", 2, \"height\", \"3.5rem\", \"width\", \"3.5rem\"], [1, \"text-50\", \"pi\", \"pi-fw\", \"pi-table\", \"text-2xl\"], [1, \"ml-4\", \"flex\", \"flex-column\"], [1, \"text-900\", \"lg:text-xl\", \"font-medium\", \"mb-0\", \"block\"], [1, \"text-600\", \"lg:text-xl\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"bg-orange-400\", \"border-round\", 2, \"height\", \"3.5rem\", \"width\", \"3.5rem\"], [1, \"pi\", \"pi-fw\", \"pi-question-circle\", \"text-50\", \"text-2xl\"], [1, \"text-900\", \"lg:text-xl\", \"font-medium\", \"mb-0\"], [1, \"w-full\", \"flex\", \"align-items-center\", \"mb-5\", \"py-5\", \"border-300\", \"border-bottom-1\", 3, \"routerLink\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"bg-indigo-400\", \"border-round\", 2, \"height\", \"3.5rem\", \"width\", \"3.5rem\"], [1, \"pi\", \"pi-fw\", \"pi-unlock\", \"text-50\", \"text-2xl\"]],\n    template: function NotfoundComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"img\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"span\", 5);\n        i0.ɵɵtext(6, \"404\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"h1\", 6);\n        i0.ɵɵtext(8, \"Not Found\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"div\", 7);\n        i0.ɵɵtext(10, \"Requested resource is not available.\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"a\", 8)(12, \"span\", 9);\n        i0.ɵɵelement(13, \"i\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"span\", 11)(15, \"span\", 12);\n        i0.ɵɵtext(16, \"Frequently Asked Questions\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"span\", 13);\n        i0.ɵɵtext(18, \"Ultricies mi quis hendrerit dolor.\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(19, \"a\", 8)(20, \"span\", 14);\n        i0.ɵɵelement(21, \"i\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"span\", 11)(23, \"span\", 16);\n        i0.ɵɵtext(24, \"Solution Center\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"span\", 13);\n        i0.ɵɵtext(26, \"Phasellus faucibus scelerisque eleifend.\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(27, \"a\", 17)(28, \"span\", 18);\n        i0.ɵɵelement(29, \"i\", 19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(30, \"span\", 11)(31, \"span\", 16);\n        i0.ɵɵtext(32, \"Permission Manager\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(33, \"span\", 13);\n        i0.ɵɵtext(34, \"Accumsan in nisl nisi scelerisque\");\n        i0.ɵɵelementEnd()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(11);\n        i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(3, _c0));\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(4, _c0));\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(5, _c0));\n      }\n    },\n    dependencies: [i1.RouterLink],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["NotfoundComponent", "_", "_2", "selectors", "decls", "vars", "consts", "template", "NotfoundComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0"], "sources": ["D:\\Greg\\sakai-ng-master\\sakai-ng-master\\src\\app\\demo\\components\\notfound\\notfound.component.ts", "D:\\Greg\\sakai-ng-master\\sakai-ng-master\\src\\app\\demo\\components\\notfound\\notfound.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n    selector: 'app-notfound',\n    templateUrl: './notfound.component.html',\n})\nexport class NotfoundComponent { }", "<div class=\"surface-ground flex align-items-center justify-content-center min-h-screen min-w-screen overflow-hidden\">\n    <div class=\"flex flex-column align-items-center justify-content-center\">\n        <img src=\"assets/demo/images/notfound/logo-blue.svg\" alt=\"Sakai logo\" class=\"mb-5 w-6rem flex-shrink-0\">\n        <div style=\"border-radius:56px; padding:0.3rem; background: linear-gradient(180deg, rgba(33, 150, 243, 0.4) 10%, rgba(33, 150, 243, 0) 30%);\">\n            <div class=\"w-full surface-card py-8 px-5 sm:px-8 flex flex-column align-items-center\" style=\"border-radius:53px\">\n                <span class=\"text-blue-500 font-bold text-3xl\">404</span>\n                <h1 class=\"text-900 font-bold text-3xl lg:text-5xl mb-2\">Not Found</h1>\n                <div class=\"text-600 mb-5\">Requested resource is not available.</div>\n                <a [routerLink]=\"['/']\" class=\"w-full flex align-items-center py-5 border-300 border-bottom-1\">\n                    <span class=\"flex justify-content-center align-items-center bg-cyan-400 border-round\" style=\"height:3.5rem; width:3.5rem;\">\n                        <i class=\"text-50 pi pi-fw pi-table text-2xl\"></i>\n                    </span>\n                    <span class=\"ml-4 flex flex-column\">\n                        <span class=\"text-900 lg:text-xl font-medium mb-0 block\">Frequently Asked Questions</span>\n                        <span class=\"text-600 lg:text-xl\">Ultricies mi quis hendrerit dolor.</span>\n                    </span>\n                </a>\n                <a [routerLink]=\"['/']\" class=\"w-full flex align-items-center py-5 border-300 border-bottom-1\">\n                    <span class=\"flex justify-content-center align-items-center bg-orange-400 border-round\" style=\"height:3.5rem; width:3.5rem;\">\n                        <i class=\"pi pi-fw pi-question-circle text-50 text-2xl\"></i>\n                    </span>\n                    <span class=\"ml-4 flex flex-column\">\n                        <span class=\"text-900 lg:text-xl font-medium mb-0\">Solution Center</span>\n                        <span class=\"text-600 lg:text-xl\">Phasellus faucibus scelerisque eleifend.</span>\n                    </span>\n                </a>\n                <a [routerLink]=\"['/']\" class=\"w-full flex align-items-center mb-5 py-5 border-300 border-bottom-1\">\n                    <span class=\"flex justify-content-center align-items-center bg-indigo-400 border-round\" style=\"height:3.5rem; width:3.5rem;\">\n                        <i class=\"pi pi-fw pi-unlock text-50 text-2xl\"></i>\n                    </span>\n                    <span class=\"ml-4 flex flex-column\">\n                        <span class=\"text-900 lg:text-xl font-medium mb-0\">Permission Manager</span>\n                        <span class=\"text-600 lg:text-xl\">Accumsan in nisl nisi scelerisque</span>\n                    </span>\n                </a>\n            </div>\n        </div>\n    </div>\n</div>"], "mappings": ";;;AAMA,OAAM,MAAOA,iBAAiB;EAAA,QAAAC,CAAA,G;qBAAjBD,iBAAiB;EAAA;EAAA,QAAAE,EAAA,G;UAAjBF,iBAAiB;IAAAG,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCN9BE,EAAA,CAAAC,cAAA,aAAqH;QAE7GD,EAAA,CAAAE,SAAA,aAAwG;QACxGF,EAAA,CAAAC,cAAA,aAA8I;QAEvFD,EAAA,CAAAG,MAAA,UAAG;QAAAH,EAAA,CAAAI,YAAA,EAAO;QACzDJ,EAAA,CAAAC,cAAA,YAAyD;QAAAD,EAAA,CAAAG,MAAA,gBAAS;QAAAH,EAAA,CAAAI,YAAA,EAAK;QACvEJ,EAAA,CAAAC,cAAA,aAA2B;QAAAD,EAAA,CAAAG,MAAA,4CAAoC;QAAAH,EAAA,CAAAI,YAAA,EAAM;QACrEJ,EAAA,CAAAC,cAAA,YAA+F;QAEvFD,EAAA,CAAAE,SAAA,aAAkD;QACtDF,EAAA,CAAAI,YAAA,EAAO;QACPJ,EAAA,CAAAC,cAAA,gBAAoC;QACyBD,EAAA,CAAAG,MAAA,kCAA0B;QAAAH,EAAA,CAAAI,YAAA,EAAO;QAC1FJ,EAAA,CAAAC,cAAA,gBAAkC;QAAAD,EAAA,CAAAG,MAAA,0CAAkC;QAAAH,EAAA,CAAAI,YAAA,EAAO;QAGnFJ,EAAA,CAAAC,cAAA,YAA+F;QAEvFD,EAAA,CAAAE,SAAA,aAA4D;QAChEF,EAAA,CAAAI,YAAA,EAAO;QACPJ,EAAA,CAAAC,cAAA,gBAAoC;QACmBD,EAAA,CAAAG,MAAA,uBAAe;QAAAH,EAAA,CAAAI,YAAA,EAAO;QACzEJ,EAAA,CAAAC,cAAA,gBAAkC;QAAAD,EAAA,CAAAG,MAAA,gDAAwC;QAAAH,EAAA,CAAAI,YAAA,EAAO;QAGzFJ,EAAA,CAAAC,cAAA,aAAoG;QAE5FD,EAAA,CAAAE,SAAA,aAAmD;QACvDF,EAAA,CAAAI,YAAA,EAAO;QACPJ,EAAA,CAAAC,cAAA,gBAAoC;QACmBD,EAAA,CAAAG,MAAA,0BAAkB;QAAAH,EAAA,CAAAI,YAAA,EAAO;QAC5EJ,EAAA,CAAAC,cAAA,gBAAkC;QAAAD,EAAA,CAAAG,MAAA,yCAAiC;QAAAH,EAAA,CAAAI,YAAA,EAAO;;;QAxB/EJ,EAAA,CAAAK,SAAA,IAAoB;QAApBL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAAO,eAAA,IAAAC,GAAA,EAAoB;QASpBR,EAAA,CAAAK,SAAA,GAAoB;QAApBL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAAO,eAAA,IAAAC,GAAA,EAAoB;QASpBR,EAAA,CAAAK,SAAA,GAAoB;QAApBL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAAO,eAAA,IAAAC,GAAA,EAAoB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}